namespace inRiver.Core.Persistance.Interfaces
{
    using System.Threading.Tasks;
    using inRiver.Core.Models.inRiver;

    public interface IJobPersistance
    {
        int InsertLongRunningJob(LongRunningJob job);

        void UpdateLongRunningJobState(int id, string state);

        void UpdateLongRunningJobStateAndMetadata(int id, string state, string metadata);

        Task UpdateLongRunningJobPercentCompletedAsync(int jobId, int percentCompleted);

        bool StartedJobExists(string jobType, string identifier = null, string identifierType = null);

        bool JobIsCancelled(int jobId);
    }
}
