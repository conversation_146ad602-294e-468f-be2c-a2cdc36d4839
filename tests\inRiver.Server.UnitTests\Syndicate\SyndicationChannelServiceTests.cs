namespace inRiver.Server.UnitTests.Syndicate
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading.Tasks;
    using FakeItEasy;
    using inRiver.Remoting.Dto;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Service;
    using Xunit;

    public class SyndicationChannelServiceTests
    {
        private readonly IDataPersistance dataContext;
        private readonly IRequestContext requestContext;
        private readonly SyndicationChannelService syndicationChannelService;

        public SyndicationChannelServiceTests()
        {
            IDataPersistance dataContext;
            this.dataContext = A.Fake<IDataPersistance>();
            this.requestContext = A.Fake<IRequestContext>();

            A.CallTo(() => this.requestContext.DataPersistance).Returns(this.dataContext);
            A.CallTo(() => this.requestContext.DataLanguage).Returns(new CultureInfo("en-US"));

            this.syndicationChannelService = new SyndicationChannelService(this.requestContext);
        }

        [Fact]
        public async Task GetChannelNameAsync_ChannelFound_ReturnsDisplayName()
        {
            // Arrange
            const int channelId = 123;
            var channel = new DtoEntity { DisplayName = new DtoField { Data = "Test Channel" } };

            A.CallTo(() => this.dataContext.GetEntitiesAsync(A<List<int>>.That.Contains(channelId)))
                .Returns(Task.FromResult((IEnumerable<DtoEntity>)new List<DtoEntity> { channel }));

            // Act
            var result = await this.syndicationChannelService.GetChannelNameAsync(channelId);

            // Assert
            Assert.Equal("Test Channel", result);
        }

        [Fact]
        public async Task GetChannelNameAsync_ChannelNotFound_ThrowsException()
        {
            // Arrange
            const int channelId = 999;

            A.CallTo(() => this.dataContext.GetEntitiesAsync(A<List<int>>._))
                .Returns(Task.FromResult((IEnumerable<DtoEntity>)new List<DtoEntity>()));

            // Act + Assert
            await Assert.ThrowsAsync<SyndicateException>(() => this.syndicationChannelService.GetChannelNameAsync(channelId));
        }

        [Fact]
        public async Task GetChannelNameAsync_DisplayNameDataNull_ReturnsChannelIdString()
        {
            // Arrange
            const int channelId = 456;
            var channel = new DtoEntity { DisplayName = new DtoField { Data = null } };
            A.CallTo(() => this.dataContext.GetEntitiesAsync(A<List<int>>._))
                .Returns(Task.FromResult((IEnumerable<DtoEntity>)new List<DtoEntity> { channel }));

            // Act
            var result = await this.syndicationChannelService.GetChannelNameAsync(channelId);

            // Assert
            Assert.Equal("456", result);
        }

        [Fact]
        public async Task GetChannelNameAsync_DisplayNameJson_ReturnsLocalizedName()
        {
            // Arrange
            const int channelId = 789;
            const string localeString = "{ \"stringMap\":{ \"en-US\":\"English Name\",\"fr-FR\":\"French Name\"}}";
            var channel = new DtoEntity { DisplayName = new DtoField { Data = localeString } };

            A.CallTo(() => this.dataContext.GetEntitiesAsync(A<List<int>>._))
                .Returns(Task.FromResult((IEnumerable<DtoEntity>)new List<DtoEntity> { channel }));
            A.CallTo(() => this.requestContext.DataLanguage).Returns(new CultureInfo("en-US"));

            // Act
            var result = await this.syndicationChannelService.GetChannelNameAsync(channelId);

            // Assert
            Assert.Equal("English Name", result);
        }

        [Fact]
        public async Task GetChannelNameAsync_DisplayNameJson_NoMatchingLocale_ReturnsChannelIdString()
        {
            // Arrange
            const int channelId = 789;
            const string localeString = "{ \"stringMap\":{\"fr-FR\":\"French Name\"}}";
            var channel = new DtoEntity { DisplayName = new DtoField { Data = localeString } };

            A.CallTo(() => this.dataContext.GetEntitiesAsync(A<List<int>>._))
                .Returns(Task.FromResult((IEnumerable<DtoEntity>)new List<DtoEntity> { channel }));
            A.CallTo(() => this.requestContext.DataLanguage).Returns(new CultureInfo("en-US"));

            // Act
            var result = await this.syndicationChannelService.GetChannelNameAsync(channelId);

            // Assert
            Assert.Equal("789", result);
        }
    }
}
