namespace inRiver.Server.Syndication
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Text.RegularExpressions;
    using inRiver.Server.Syndication.Script;
    using Microsoft.ClearScript.V8;
    using Newtonsoft.Json;

    public class ScriptManager
    {
        private const int MaxRestApiFunctionResultSizeInBytes = 1048576; // 1 MB

        // Context functions that work with REST API.
        // The list of functions is defined in the IRestApiScriptContext interface.
        private static readonly IList<string> ApiContextFunctionNames = new List<string>
        {
            "RestApiGet",
            "RestApiDelete",
            "RestApiPost",
            "RestApiPut",
            "RestApiPatch"
        };

        private static readonly Regex RestApiFunctionsRegex =
            new Regex($@"inriverctx\.{string.Join("|", ApiContextFunctionNames)}(.*)", RegexOptions.IgnoreCase);

        public static void ValidateScriptResult(string script, object result)
        {
            if (GetNumberOfApiCallsPerFunction(script) > 0 && !IsValidateRestApiScriptResult(result))
            {
                throw new Exception($"The response from a custom function containing REST API calls cannot exceed {MaxRestApiFunctionResultSizeInBytes / 1024 / 1024} MB.");
            }
        }

        private static int GetNumberOfApiCallsPerFunction(string converter)
        {
            var matches = RestApiFunctionsRegex.Matches(GetScriptCodeWithoutComments(converter));

            return matches.Count;
        }

        private static string GetScriptCodeWithoutComments(string converter)
            => Regex.Replace(converter ?? string.Empty, @"\/\*[\s\S]*?\*\/|\/\/.*", string.Empty);

        private static bool IsValidateRestApiScriptResult(object result)
            => Encoding.Default.GetByteCount(result?.ToString() ?? string.Empty) <= MaxRestApiFunctionResultSizeInBytes;

        static object GetFromScriptObject(V8ScriptEngine engine, dynamic result)
        {
            if (result != null && result is System.Dynamic.DynamicObject)
            {
                if (engine.Script.Array.isArray(result))
                {
                    var arrayValue = new List<object>();
                    ((System.Dynamic.DynamicObject)result).GetDynamicMemberNames().ToList().ForEach(p => arrayValue.Add(GetFromScriptObject(engine, result[p])));
                    return arrayValue;
                }
                else
                {
                    var dictionaryValue = new Dictionary<object, object>();
                    ((System.Dynamic.DynamicObject)result).GetDynamicMemberNames().ToList().ForEach(p => dictionaryValue.Add(p, GetFromScriptObject(engine, result[p])));
                    return dictionaryValue;
                }
            }
            else
            {
                return result;
            }
        }

        private static object ExecuteInternal(string converter, string jsonArgs, string jsonValues, IScriptContext scriptContext)
        {
            using var engine = new V8ScriptEngine(V8ScriptEngineFlags.EnableDebugging);
            if (scriptContext?.IsValid() ?? false)
            {
                engine.AddHostObject("inriverctx", scriptContext);
            }

            engine.Execute(converter);
            var result = engine.Script.inRiver(jsonArgs, jsonValues);

            return GetFromScriptObject(engine, result);
        }

        public static object Execute(string converter, object[] args, object[] values, IScriptContext scriptContext)
        {
            string jsonArgs = args != null ? JsonConvert.SerializeObject(args) : null;
            string jsonValues = values != null ? JsonConvert.SerializeObject(values) : null;

            // create internal entry point which calls main
            converter +=
                "function inRiver(args, values) " +
                "{ " +
                "   return main(JSON.parse(args), JSON.parse(values));" +
                "}";

            var result = ExecuteInternal(converter, jsonArgs, jsonValues, scriptContext);
            if (scriptContext != null)
            {
                scriptContext.SetInRiverEntity(null);
            }

            return result;
        }
    }
}
