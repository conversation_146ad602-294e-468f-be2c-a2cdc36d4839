namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Api.Data.Client;   
    using inRiver.Remoting.Query;
    using Newtonsoft.Json;

    /// <summary>
    /// Contains Field related operations.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public List<int> ExecuteDataQuery(Query query)
        {
            var payload = JsonConvert.SerializeObject(query);
            var json = InRiverDataApiClient.ExecuteDataQuery(this.GetAuthInfo(), payload);
            var entityIds = JsonConvert.DeserializeObject<List<int>>(json);
            return entityIds;
        }

        public override List<int> LinkSearch(LinkQuery linkQuery)
        {
            var payload = JsonConvert.SerializeObject(linkQuery);
            var json = InRiverDataApiClient.LinkSearch(this.GetAuthInfo(), payload);
            var entityIds = JsonConvert.DeserializeObject<List<int>>(json);
            return entityIds;
        }

        public override List<Core.Models.inRiver.Queries.MultiValueSearchResult> MultiValueSearch(List<string> valuesList)
        {
            var distinctValues = valuesList.Distinct().ToList();
            var results = InRiverDataApiClient.MultiValueSearch(this.GetAuthInfo(), distinctValues);
            var multiValues = results.Select(multiValueSearchResult => new Core.Models.inRiver.Queries.MultiValueSearchResult
            {
                EntityId = multiValueSearchResult.EntityId,
                EntityTypeId = multiValueSearchResult.EntityTypeId,
                FieldTypeId = multiValueSearchResult.FieldTypeId,
                QueryValue = multiValueSearchResult.QueryValue
            }).ToList();

            var diff = distinctValues
                .Where(value => !results.Any(result => result.QueryValue.ToUpperInvariant() == value.ToUpperInvariant()))
                .Select(x => new Core.Models.inRiver.Queries.MultiValueSearchResult
                {
                    QueryValue = x
                });

            return multiValues.Concat(diff).ToList();
        }

#pragma warning disable RCS1163 // Unused parameter: joinOperator (not used in base class either)
        public override List<int> Search(Remoting.Query.Criteria criteria, Join? joinOperator = null)
#pragma warning enable RCS1163 
        {
            var query = new Query()
            {
                Criteria = new List<Criteria>() { criteria },
            };

            return ExecuteDataQuery(query);
        }
    }
}
