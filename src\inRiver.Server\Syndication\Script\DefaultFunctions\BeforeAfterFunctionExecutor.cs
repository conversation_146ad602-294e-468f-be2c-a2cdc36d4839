namespace inRiver.Server.Syndication.Script.DefaultFunctions
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;

    public class BeforeAfterFunctionExecutor
    {
        private readonly RequestContext context;

        private readonly string cvlId;

        private readonly string separator;

        private readonly string language;

        private readonly bool isLocaleString;

        private readonly object mainValue;

        private readonly string beforeValue;

        private readonly string afterValue;

        private readonly bool isEnum;

        /// <summary>
        /// Initializes a new instance of the <see cref="BeforeAfterFunctionExecutor"/> class.
        /// </summary>
        /// <param name="context">Current request context.</param>
        /// <param name="transformationManager">
        /// Transformation manager class.
        /// transformationManager.Args[0] - CVL id;
        /// transformationManager.Args[1] - Selected separator;
        /// transformationManager.Args[2] - Selected language;
        /// transformationManager.Args[3] - Selected field is a LocaleString type;
        /// transformationManager.Values[0] - "Before" value;
        /// transformationManager.Values[1] - "After" value.
        /// </param>
        /// <param name="mainValue">Current field value.</param>
        /// <param name="isEnum">Target field is enumeration.</param>
        public BeforeAfterFunctionExecutor(
            RequestContext context,
            TransformationManager transformationManager,
            object mainValue,
            bool isEnum)
        {
            this.context = context;
            this.cvlId = transformationManager.Args[0];
            this.separator = transformationManager.Args[1];
            this.language = transformationManager.Args[2];
            _ = bool.TryParse(transformationManager.Args[3], out this.isLocaleString);
            this.mainValue = mainValue;
            this.beforeValue = transformationManager.Values[0] ?? string.Empty;
            this.afterValue = transformationManager.Values[1] ?? string.Empty;
            this.isEnum = isEnum;
        }

        public string Execute()
        {
            if (this.mainValue == null)
            {
                return null;
            }

            var mappedFieldValues = this.GetMappedFieldValues();
            if (mappedFieldValues == null)
            {
                return null;
            }

            mappedFieldValues = mappedFieldValues.Where(value => !string.IsNullOrEmpty(value));
            if (!mappedFieldValues.Any())
            {
                return null;
            }

            var beforeAfterFunctionValues = new List<string>();
            beforeAfterFunctionValues.Add(this.beforeValue);
            beforeAfterFunctionValues.AddRange(mappedFieldValues);
            beforeAfterFunctionValues.Add(this.afterValue);

            return string.Join(this.separator, beforeAfterFunctionValues);
        }

        private IEnumerable<string> GetMappedFieldValues()
        {
            if (this.isEnum)
            {
                return this.mainValue.ToString().Split(';');
            }

            if (this.cvlId != null)
            {
                return this.GetCvlValues();
            }

            if (this.isLocaleString)
            {
                var localeStringMainValue = TransformationManager.GetLocaleStringMainValue(this.mainValue, this.language);
                return localeStringMainValue == null
                    ? null
                    : new List<string> { localeStringMainValue.ToString() };
            }

            return new List<string> { this.mainValue.ToString() };
        }

        private IEnumerable<string> GetCvlValues()
        {
            if (!ExportManager.CvlValuesDictionary.TryGetValue(this.cvlId, out var cvlValuesDictionary))
            {
                var cvlValues = this.context.DataPersistance.GetCVLValuesForCVL(this.cvlId) ?? new List<CVLValue>();
                cvlValuesDictionary = cvlValues
                    .GroupBy(cvl => cvl.Key)
                    .ToDictionary(x => x.Key, x => x.FirstOrDefault());
                _ = ExportManager.CvlValuesDictionary.TryAdd(this.cvlId, cvlValuesDictionary);
            }

            var keys = this.mainValue.ToString().Split(';');

            return keys.Select(key => {
                if (!cvlValuesDictionary.TryGetValue(key, out var cvlValue))
                {
                    throw new SyndicateException($"BeforeAfter function - Invalid CVL key: {key}, CVL id: {cvlId}.");
                }

                return cvlValue.Value is LocaleString localeString
                    ? localeString[new CultureInfo(this.language)]
                    : cvlValue.Value?.ToString();
            });
        }
    }
}
