namespace inRiver.Server.Completeness.Criteria
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;

    public class RelationsCompleteCritera
    {
        private readonly RequestContext context;

        public RelationsCompleteCritera(RequestContext context)
        {
            this.context = context;
        }

        private const string SettingsKey = "LinkTypeId";

        public string Name => "Relations Complete";

        public List<string> SettingsKeys => new List<string> { SettingsKey };

        public int GetCriteriaCompletenessPercentage(int entityId, List<CompletenessRuleSetting> settings)
        {
            CompletenessRuleSetting setting = settings.FirstOrDefault(s => s.Key == SettingsKey);

            string linkTypeId = setting?.Value;

            if (string.IsNullOrEmpty(linkTypeId))
            {
                return 0;
            }

            List<DtoLink> links = this.context.DataPersistance.GetOutboundLinksForEntityAndLinkType(entityId, linkTypeId);

            if (links.Count == 0)
            {
                return 100;
            }

            int completeLinks = 0;

            foreach (DtoLink link in links)
            {
                if (link.Target.Completeness == 100)
                {
                    completeLinks++;
                }
            }

            decimal result = (completeLinks * 100m) / links.Count;

            return (int)Math.Round(result);
        }

        public async Task<int> GetCriteriaCompletenessPercentageAsync(int entityId, IEnumerable<CompletenessRuleSetting> settings)
        {
            var setting = settings.FirstOrDefault(s => s.Key == SettingsKey);

            var linkTypeId = setting?.Value;

            if (string.IsNullOrEmpty(linkTypeId))
            {
                return 0;
            }

            var links = await this.context.DataPersistance.GetOutboundLinksForEntityAndLinkTypeAsync(entityId, linkTypeId);

            if (!links.Any())
            {
                return 100;
            }

            var completeLinks = 0;

            foreach (var link in links)
            {
                if (link.Target.Completeness == 100)
                {
                    completeLinks++;
                }
            }

            var result = (completeLinks * 100m) / links.Count();

            return (int)Math.Round(result);
        }
    }
}
