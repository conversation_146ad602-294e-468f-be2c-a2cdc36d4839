namespace inRiver.Server.Managers
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Globalization;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Error;
    using inRiver.Server.Request;
    using Newtonsoft.Json;
    using Serilog;

    public class SearchManager
    {
        private readonly RequestContext context;

        public SearchManager(RequestContext context)
        {
            this.context = context;
        }

        private delegate void AsyncAddToQuickSearchIndex(DtoEntity entity, RequestContext context);

        private delegate void AsyncUpdateQuickSearchIndex(DtoEntity entity, RequestContext context);

        public void AddToSearchIndex(DtoEntity entity)
        {
            AsyncAddToQuickSearchIndex caller = this.AddToQuickSearchIndex;
            Task.Run(() => caller.Invoke(entity, this.context));
        }

        public void UpdateSearchIndex(DtoEntity entity)
        {
            AsyncUpdateQuickSearchIndex caller = this.UpdateQuickSearchIndex;
            Task.Run(() => caller.Invoke(entity, this.context));
        }

        #region Private Methods

        private void DeleteFromQuickSearchIndex(int entityId, RequestContext threadContext)
        {
            using (SqlConnection connection = new SqlConnection(threadContext.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "DELETE FROM QuickSearch Where EntityId = @EntityId";
                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    var message = $"An unexpected error occurred when deleting entity {entityId} from the quick search index";
                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }
        }

        public void UpdateQuickSearchIndex(DtoEntity entity, RequestContext threadContext)
        {
            this.DeleteFromQuickSearchIndex(entity.Id, threadContext);
            this.AddToQuickSearchIndex(entity, threadContext);
        }

        private void AddToQuickSearchIndex(DtoEntity entity, RequestContext threadContext)
        {
            if (entity == null)
            {
                return;
            }

            if (entity.DisplayName?.Data != null)
            {
                string cvlId = this.GetCvlIdForFieldType(entity.DisplayName.FieldTypeId, threadContext);

                if (!string.IsNullOrEmpty(cvlId))
                {
                    foreach (string key in entity.DisplayName.Data.Split(new[] { ";" },
                        StringSplitOptions.RemoveEmptyEntries))
                    {
                        this.AddCVLValue(entity.Id, cvlId, key, 4, threadContext);
                    }
                }
                else
                {
                    this.AddQuickSearchValue(entity.Id, entity.DisplayName.Data, 4, threadContext);
                }
            }

            if (entity.DisplayDescription?.Data != null)
            {
                string cvlId = this.GetCvlIdForFieldType(entity.DisplayDescription.FieldTypeId, threadContext);

                if (!string.IsNullOrEmpty(cvlId))
                {
                    foreach (string key in entity.DisplayDescription.Data.Split(new[] { ";" },
                        StringSplitOptions.RemoveEmptyEntries))
                    {
                        this.AddCVLValue(entity.Id, cvlId, key, 2, threadContext);
                    }
                }
                else
                {
                    this.AddQuickSearchValue(entity.Id, entity.DisplayDescription.Data, 2, threadContext);
                }
            }
        }

        private void AddCVLValue(int entityId, string cvlId, string cvlKey, int boost, RequestContext threadContext)
        {
            CVLValue cvl = threadContext.DataPersistance.GetCVLValueByKey(cvlKey, cvlId);

            if (cvl == null)
            {
                return;
            }

            List<string> values = new List<string>();

            if (cvl.Value is string)
            {
                string s = cvl.Value.ToString();

                if (string.IsNullOrEmpty(s))
                {
                    return;
                }

                if (!values.Contains(s))
                {
                    values.Add(s);
                }
            }

            if (cvl.Value is LocaleString)
            {
                LocaleString ls = cvl.Value as LocaleString;

                if (LocaleString.IsNullOrEmpty(ls))
                {
                    return;
                }

                foreach (CultureInfo cultureInfo in ls.Languages)
                {
                    string value = ls[cultureInfo];
                    if (string.IsNullOrEmpty(value))
                    {
                        continue;
                    }

                    if (!values.Contains(value))
                    {
                        values.Add(value);
                    }
                }
            }

            if (!values.Any())
            {
                return;
            }

            using (SqlConnection connection = new SqlConnection(threadContext.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    for (int i = 0; i < values.Count; i++)
                    {
                        command.CommandText +=
                            $"INSERT INTO QuickSearch (EntityId, Value, Boost) VALUES({entityId}, @Value{i}, {boost}); ";

                        command.Parameters.AddWithValue($"@Value{i}", values[i]);
                    }

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    threadContext.Log(LogLevel.Error,
                        "An unknown server error occurred when add cvl value to quick search index", ex);
                }
            }
        }

        private string GetCvlIdForFieldType(string fieldTypeId, RequestContext threadContext)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return null;
            }

            string cvlId = null;

            using (SqlConnection connection = new SqlConnection(threadContext.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT CVLId FROM FieldType WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", fieldTypeId);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read() && !reader.IsDBNull(0))
                        {
                            cvlId = reader.GetString(0);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    var message = $"An unexpected error occurred when checking cvl id for field type {fieldTypeId}";
                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }

            return cvlId;
        }

        private void AddQuickSearchValue(int entityId, string value, int boost, RequestContext threadContext)
        {
            if (value.StartsWith("{\"stringMap\":{\"", StringComparison.InvariantCultureIgnoreCase))
            {
                this.AddLocaleStringValue(entityId, value, boost, threadContext);

                return;
            }

            using (SqlConnection connection = new SqlConnection(threadContext.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText =
                        $"INSERT INTO QuickSearch (EntityId, Value, Boost) Values ( @EntityId, @Value, {boost})";
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@Value", value);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    var message = $"An unexpected error occurred when adding value {value} to quick search index for entity {entityId}";
                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }
        }

        private void AddLocaleStringValue(int entityId, string value, int boost, RequestContext threadContext)
        {
            LocaleString ls = JsonConvert.DeserializeObject<LocaleString>(value);

            if (LocaleString.IsNullOrEmpty(ls))
            {
                return;
            }

            List<string> values = new List<string>();

            foreach (CultureInfo ci in ls.Languages)
            {
                if (string.IsNullOrEmpty(ls[ci]))
                {
                    continue;
                }

                if (values.Contains(ls[ci]))
                {
                    continue;
                }

                values.Add(ls[ci]);
            }

            if (!values.Any())
            {
                return;
            }

            using (SqlConnection connection = new SqlConnection(threadContext.ConnectionString))
            {
                try
                {
                    SqlCommand cmd = connection.CreateCommand();

                    StringBuilder sb = new StringBuilder("INSERT INTO QuickSearch (EntityId, [Value], Boost) VALUES ");

                    int i = 0;

                    foreach (string val in values)
                    {
                        sb.Append($"({entityId}, @Value{i}, {boost}),");

                        cmd.Parameters.AddWithValue($"@Value{i}", val);

                        i++;
                    }

                    sb = sb.Remove(sb.Length - 1, 1);

                    cmd.CommandText = sb.ToString();

                    connection.Open();
                    cmd.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    threadContext.Log(LogLevel.Error,
                        "An unexpected error occurred when adding locale string to quick search index", ex);
                }
            }
        }

        #endregion
    }
}
