namespace LongRunningJobActor.Abstraction
{
    using System;
    using System.Threading.Tasks;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;

    public interface ISyndicationService
    {
        public Task RunSyndicateInternalAsync(EnvironmentContextData environmentContextData, int longRunningJobId, SyndicationModel syndicationModel, string actorId, string stackGroup, string username);

        Task RunReviewInternalAsync(EnvironmentContextData environmentContextData, SyndicationModel syndicationModel, string stackGroup, Guid reviewId);
    }
}
