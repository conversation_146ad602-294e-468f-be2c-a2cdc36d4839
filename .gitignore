## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
[Dd]eployment/
[Aa]zureDev/
[Aa]zureQaEu/
[Aa]zureQaUs/
x64/
x86/
bld/
[Bb]in/
[Oo]bj/
[Dd]evelopment-LocalUse/
[Dd]evelopment-LocalEuw/
[Dd]evelopment-DevSql2016/

# Visual Studio 2015 cache/options directory
.vs/
# Uncomment if you have tasks that create the project's static files in wwwroot
#wwwroot/

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c
[Dd]evelopment-LocalUsePS/
[Dd]evelopment-LocalEuwPS/
[Dd]evelopment-DevSql2016PS/

# DNX
project.lock.json
project.fragment.lock.json
artifacts/

*_i.c
*_p.c
*_i.h
*.ilk
*.meta
*.obj
*.pch
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# TFS 2012 Local Workspace
$tf/

# Guidance Automation Toolkit
*.gpState

# ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# TODO: Comment the next line if you want to checkin your web deploy settings
# but database connection strings (with potential passwords) will be unencrypted
#*.pubxml
#*.publishproj

# Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
PublishScripts/

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/packages/*
# except build/, which is used as an MSBuild target.
!**/packages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/packages/repositories.config
# NuGet v3's project.json files produces more ignoreable files
*.nuget.props
*.nuget.targets

# Microsoft Azure Build Output
csx/
*.build.csdef

# Microsoft Azure Emulator
ecf/
rcf/

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.pfx
*.publishsettings
node_modules/
orleans.codegen.cs
*.min.css
*.min.js


/Server/inRiver.ImageService/ReleaseBinaries
/Server/inRiver.Server.CustomValueList.GroupTasks/ReleaseBinaries
/Server/inRiver.Server.CustomValueList.Users/ReleaseBinaries
.gitconfig
project.lock.json
_references.js
Common/inRiver.Configuration.Core/project.lock.json
Common/inRiver.Core/project.lock.json
ControlCenter/inRiver.ControlCenter/project.lock.json
ControlCenter/inRiver.ControlCenter/wwwroot/_references.js
ServiceFabric/DefaultSubscriberService/project.lock.json
ServiceFabric/Clients/BackOffice/project.lock.json
ServiceFabric/DefaultAssetService/project.lock.json
ServiceFabric/DefaultJobService/project.lock.json
ServiceFabric/DefaultLogService/project.lock.json
ServiceFabric/DefaultSubscriberService/project.lock.json
ServiceFabric/DefaultTokenService/project.lock.json
ServiceFabric/Clients/BackOffice/project.lock.json
ServiceFabric/Actors/CheckDisplayNamesJobActor/JobCheckDisplayNamesActor.cs.orig
ServiceFabric/Actors/CheckMainPicturesJobActor/JobCheckMainPicturesActor.cs.orig
ServiceFabric/Actors/NotifyMeJobActor/JobNotifyMeActor.cs.orig
ServiceFabric/Actors/RebuildQuickSearchIndexJobActor/JobRebuildQuickSearchIndexActor.cs.orig
ServiceFabric/Actors/UpdateLinkRuleDefinitionsJobActor/JobUpdateLinkRuleDefinitionsActor.cs.orig
ServiceFabric/Clients/BackOffice/project.lock.json
ServiceFabric/Clients/BackOffice/project.lock.json
ServiceFabric/Clients/BackOffice/project.lock.json
*.orig
ServiceFabric/Clients/ControlCenter/wwwroot/app/templates/navbar/NavigationTemplate.min.html
ServiceFabric/Clients/BackOffice/project.lock.json
Portal/inRiver.Portal/inRiver.Portal.csproj.user
ServiceFabric/Clients/ControlCenter/wwwroot/_generated/
xConnect/inRiver.xConnect.ContentStore/inRiver.xConnect.ContentStore.csproj.user
xConnect/inRiver.xConnect.SupplierOnboarding/inRiver.xConnect.SupplierOnboarding.csproj.user

#Ignore transform changes in Local.1Node.xml and in Local.5Node.xml  Remove the following ignore, when we wish to make real changes
src/LongRunningJob/PublishProfiles/Local.1Node.xml
src/LongRunningJob/PublishProfiles/Local.5Node.xml