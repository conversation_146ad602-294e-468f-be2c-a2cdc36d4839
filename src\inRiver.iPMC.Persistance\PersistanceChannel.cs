namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Data;
    using System.Data.SqlClient;
    using System.Threading;
    using inRiver.Log;

    public class PersistanceChannel : BasePersistance, IPersistanceChannel
    {
        public PersistanceChannel(string connectionString, 
            ICommonLogging logInstance,
            IContentSegmentPermissionProvider contentSegmentProvider) 
            : base(connectionString, logInstance, contentSegmentProvider)
        {
        }

        public void CreateChannelStructure(int channelId, string entityTypeId, string customerSafeName, string environmentSafeName, CancellationToken cancellationToken)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = "exec CreateChannelStructureEnhanced_NewLayer @ChannelId, @EntityTypeId";
                    command.Parameters.AddWithValue("@ChannelId", channelId);
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                    command.CommandTimeout = Convert.ToInt32(72000);
                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }
                    LogInstance.Error($"An unexpected error occurred when create the channel structure for id: {channelId} ({customerSafeName}/{environmentSafeName})", ex, string.Empty, string.Empty);
                    throw;
                }
            }
        }

        public bool IsExcludedByChannelFilter(int channelId, int entityId, string linkTypeId, string customerSafeName, string environmentSafeName)
        {
            bool isExcluded = true;
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    using (var command =
                         new SqlCommand("IsExcludedByChannelFilterEnhanced_NewLayer", connection)
                         {
                             CommandType = CommandType.StoredProcedure
                         })
                    {
                        command.Parameters.AddWithValue("@channelId", channelId);
                        command.Parameters.AddWithValue("@entityId", entityId);
                        command.Parameters.AddWithValue("@linkTypeId", linkTypeId);
                        command.Parameters.Add("@excluded", SqlDbType.Bit, 1);
                        command.Parameters["@excluded"].Direction = ParameterDirection.Output;

                        connection.Open();
                        command.ExecuteNonQuery();
                        isExcluded = Convert.ToBoolean(command.Parameters["@excluded"].Value);
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }
                    LogInstance.Error($"An unexpected error occurred in IsExcludedByChannelFilter channelId: {channelId} entityId: {entityId}. ({customerSafeName}/{environmentSafeName})", ex, string.Empty, string.Empty);
                    throw;
                }
            }

            return isExcluded;
        }
    }
}
