namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Linq;
    using System.Text;
    using inriver.Expressions.Client.Constants;
    using Inriver.Expressions.Dto;

    public partial class inRiverPersistance
    {
        public Dictionary<string, Dictionary<string, DtoExpression>> GetExpressionsForEntity(int entityId)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT [Id], [Data], [Status], [EntityId], [Target], [TargetType], [RefType], [RefValue], [StatusMessage], [LanguageVersion] " +
                        "FROM [dbo].[Expression] WHERE [EntityId] = @EntityId";
                    _ = command.Parameters.AddWithValue("@EntityId", entityId);

                    var expressions = new Dictionary<string, Dictionary<string, DtoExpression>>()
                    {
                        { ExpressionTargetType.ENTITYMETADATA, new Dictionary<string, DtoExpression>() },
                        { ExpressionTargetType.FIELDTYPEID, new Dictionary<string, DtoExpression>() }
                    };
                    var reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        bool? status;
                        if (reader.IsDBNull(2))
                        {
                            status = null;
                        }
                        else
                        {
                            status = reader.GetBoolean(2);
                        }

                        var expression = new DtoExpression
                        {
                            Id = reader.GetInt32(0),
                            Data = reader.GetString(1),
                            EntityId = reader.GetInt32(3),
                            Status = status,
                            Target = reader.GetString(4),
                            TargetType = reader.GetString(5),
                            RefType = reader.IsDBNull(6) ? null : reader.GetString(6),
                            RefValue = reader.IsDBNull(7) ? null : reader.GetString(7),
                            StatusMessage = reader.IsDBNull(8) ? null : reader.GetString(8),
                            LanguageVersion = reader.GetDouble(9),
                        };

                        if (!expressions.ContainsKey(expression.TargetType))
                        {
                            expressions[expression.TargetType] = new Dictionary<string, DtoExpression>();
                        }

                        expressions[expression.TargetType][expression.Target] = expression;
                    }

                    return expressions;
                }
            }
        }

        public Dictionary<string, Dictionary<string, DtoExpression>> GetExpressionsForEntityType(string entityTypeId)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"SELECT [Id], [Data], [Status], [EntityId], [Target], [TargetType], [RefType], [RefValue], [StatusMessage], [LanguageVersion] 
                                            FROM [dbo].[Expression] WHERE [RefType] = 'ENTITYTYPE' AND [RefValue] = @EntityTypeId";
                    _ = command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    var expressions = new Dictionary<string, Dictionary<string, DtoExpression>>
                    {
                        { ExpressionTargetType.ENTITYMETADATA, new Dictionary<string, DtoExpression>() },
                        { ExpressionTargetType.FIELDTYPEID, new Dictionary<string, DtoExpression>() }
                    };

                    var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        bool? status;
                        if (reader.IsDBNull(2))
                        {
                            status = null;
                        }
                        else
                        {
                            status = reader.GetBoolean(2);
                        }

                        var expression = new DtoExpression
                        {
                            Id = reader.GetInt32(0),
                            Data = reader.GetString(1),
                            Status = status,
                            Target = reader.GetString(4),
                            TargetType = reader.GetString(5),
                            RefType = reader.IsDBNull(6) ? null : reader.GetString(6),
                            RefValue = reader.IsDBNull(7) ? null : reader.GetString(7),
                            StatusMessage = reader.IsDBNull(8) ? null : reader.GetString(8),
                            LanguageVersion = reader.GetDouble(9),
                        };

                        if (!expressions.ContainsKey(expression.TargetType))
                        {
                            expressions[expression.TargetType] = new Dictionary<string, DtoExpression>();
                        }

                        expressions[expression.TargetType][expression.Target] = expression;
                    }


                    return expressions;
                }
            }
        }

        public List<DtoExpression> GetExpressionsForEntityTypesByTarget(List<string> entityTypeIds, string target)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = $@"SELECT [Id], [Data], [Status], [EntityId], [Target], [TargetType], [RefType], [RefValue], [StatusMessage], [LanguageVersion] 
                                            FROM [dbo].[Expression] WHERE [RefType] = 'ENTITYTYPE' AND [Target] = @target AND [RefValue] IN ({string.Join(",", entityTypeIds.Select(x => $"@p_{x}"))})";
                    _ = command.Parameters.AddWithValue("@target", target);
                    entityTypeIds.ForEach(x => command.Parameters.AddWithValue($"@p_{x}", x));

                    var expressions = new List<DtoExpression>();

                    var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        bool? status;
                        if (reader.IsDBNull(2))
                        {
                            status = null;
                        }
                        else
                        {
                            status = reader.GetBoolean(2);
                        }

                        var expression = new DtoExpression
                        {
                            Id = reader.GetInt32(0),
                            Data = reader.GetString(1),
                            Status = status,
                            Target = reader.GetString(4),
                            TargetType = reader.GetString(5),
                            RefType = reader.IsDBNull(6) ? null : reader.GetString(6),
                            RefValue = reader.IsDBNull(7) ? null : reader.GetString(7),
                            StatusMessage = reader.IsDBNull(8) ? null : reader.GetString(8),
                            LanguageVersion = reader.GetDouble(9),
                        };

                        expressions.Add(expression);
                    }

                    return expressions;
                }
            }
        }

        public int GetFirstLinkedEntity(int entityId, string direction, string linkTypeIds, bool inactive)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                using (var command = connection.CreateCommand())
                {
                    if (direction.Equals("Inbound", StringComparison.OrdinalIgnoreCase))
                    {
                        command.CommandText = @"
                    WITH LinkTypeIdsCte AS 
                    (
	                    SELECT value, ordinal FROM STRING_SPLIT(@LinkTypeIds, ',', 1)
                    ),
                    CTE AS (
	                    SELECT [SourceEntityId], [TargetEntityId], [LinkTypeId], 1 AS [Level]
	                    FROM [dbo].[Link] 
	                    WHERE [TargetEntityId] = @EntityId AND LinkTypeId = @StartLinkType AND [Inactive] = @Inactive
                    UNION ALL
	                    SELECT l.[SourceEntityId], l.[TargetEntityId], l.[LinkTypeId], c.[Level] + 1
	                    FROM [dbo].[Link] l
	                    INNER JOIN CTE c ON l.TargetEntityId = c.SourceEntityId
	                    INNER JOIN LinkTypeIdsCte ltc ON ltc.value = l.LinkTypeId AND ltc.ordinal = c.[Level] + 1
                        WHERE l.[Inactive] = 0 
                    )
                    SELECT TOP(1) SourceEntityId FROM CTE WHERE LinkTypeId = @TargetLinkType";
                    }
                    else
                    {
                        command.CommandText = @"
                    WITH LinkTypeIdsCte AS 
                     (
	                    SELECT value, ordinal FROM STRING_SPLIT(@LinkTypeIds, ',', 1)
                     ),
                    CTE AS (
	                    SELECT [SourceEntityId], [TargetEntityId], [LinkTypeId], 1 AS [Level]
	                    FROM [dbo].[Link] 
	                    WHERE [SourceEntityId] = @EntityId AND LinkTypeId = @StartLinkType AND [Inactive] = @Inactive 
                    UNION ALL
	                    SELECT l.[SourceEntityId], l.[TargetEntityId], l.[LinkTypeId], c.[Level] + 1
	                    FROM [dbo].[Link] l
	                    INNER JOIN CTE c ON c.TargetEntityId = l.SourceEntityId
	                    INNER JOIN LinkTypeIdsCte ltc ON ltc.value = l.LinkTypeId AND ltc.ordinal = c.[Level] + 1
                        WHERE l.[Inactive] = 0
                    )
                    SELECT TOP(1) TargetEntityId FROM CTE WHERE LinkTypeId = @TargetLinkType";
                    }

                    var linkTypeIdsArr = linkTypeIds.Split(',');
                    _ = command.Parameters.AddWithValue("@EntityId", entityId);
                    _ = command.Parameters.AddWithValue("@LinkTypeIds", linkTypeIds);
                    _ = command.Parameters.AddWithValue("@StartLinkType", linkTypeIdsArr[0]);
                    _ = command.Parameters.AddWithValue("@TargetLinkType", linkTypeIdsArr[linkTypeIdsArr.Length - 1]);
                    _ = command.Parameters.AddWithValue("@Inactive", inactive);
                    connection.Open();
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return reader.GetInt32(0);
                        }
                    }

                    return -1;

                }
            }
        }

        public void UpsertExpressions(List<DtoExpression> expressions)
        {
            if (expressions == null || expressions.Count == 0)
            {
                return;
            }

            using (var dataTable = new DataTable())
            {
                _ = dataTable.Columns.Add("Data", typeof(string));
                _ = dataTable.Columns.Add("Status", typeof(bool));
                _ = dataTable.Columns.Add("EntityId", typeof(int));
                _ = dataTable.Columns.Add("Target", typeof(string));
                _ = dataTable.Columns.Add("TargetType", typeof(string));
                _ = dataTable.Columns.Add("StatusMessage", typeof(string));
                _ = dataTable.Columns.Add("LanguageVersion", typeof(double));
                _ = dataTable.Columns.Add("RefType", typeof(string));
                _ = dataTable.Columns.Add("RefValue", typeof(string));

                foreach (var expr in expressions)
                {
                    if (expr.Data.Length > 100000)
                    {
                        throw new ArgumentException("Expression data is too large");
                    }
                    else if ((!expr.EntityId.HasValue || expr.EntityId <= 0) && expr.RefValue == null && expr.RefType == null)
                    {
                        throw new ArgumentException("Orphaned expression detected");
                    }
                    else if (expr.EntityId.HasValue && (expr.RefValue != null || expr.RefType != null))
                    {
                        throw new ArgumentException("Expression has both EntityId and RefType/RefValue set");
                    }

                    _ = dataTable.Rows.Add(
                        expr.Data,
                        expr.Status,
                        expr.EntityId,
                        expr.Target,
                        expr.TargetType,
                        expr.StatusMessage,
                        expr.LanguageVersion,
                        expr.RefType,
                        expr.RefValue);
                }

                using (var connection = new SqlConnection(this.ConnectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        var tableName = $"TmpTable_Expression";
                        command.CommandText = $@"CREATE TABLE #{tableName} (
                        [Data] [nvarchar](max) NOT NULL,
                        [Status] [bit] NULL,
                        [EntityId] [int] NULL,
                        [Target] [nvarchar](64) NULL,
                        [TargetType] [nvarchar](128) NULL,
                        [StatusMessage] [nvarchar](max) NULL,
                        [LanguageVersion] [float] NOT NULL,
                        [RefType] [nvarchar](128) NULL,
                        [RefValue] [nvarchar](128) NULL)";
                        _ = command.ExecuteNonQuery();
                        using (var bulkcopy = new SqlBulkCopy(connection))
                        {
                            bulkcopy.BulkCopyTimeout = 3000;
                            bulkcopy.DestinationTableName = $"#{tableName}";
                            bulkcopy.WriteToServer(dataTable);
                        }

                        command.CommandText = $@"
                        MERGE [dbo].[Expression] AS target USING (
                            SELECT Data, Status, EntityId, Target, TargetType, StatusMessage, LanguageVersion, RefType, RefValue FROM #{tableName}
                        ) AS source ([Data], [Status], [EntityId], [Target], [TargetType], [StatusMessage], [LanguageVersion], [RefType], [RefValue])
                        ON (
                            (target.[EntityId] = source.[EntityId] OR (target.[EntityId] IS NULL AND source.[EntityId] IS NULL)) 
                            AND target.[Target] = source.[Target] 
                            AND target.[TargetType] = source.[TargetType] 
                            AND (target.[RefType] = source.[RefType] OR (target.[RefType] IS NULL AND source.[RefType] IS NULL))
                            AND (target.[RefValue] = source.[RefValue] OR (target.[RefValue] IS NULL AND source.[RefValue] IS NULL))
                        )
                        WHEN MATCHED THEN
                            UPDATE SET [Data] = source.[Data], [Status] = source.[Status], [StatusMessage] = source.[StatusMessage], [LanguageVersion] = source.[LanguageVersion]
                        WHEN NOT MATCHED THEN
                            INSERT ([Data], [Status], [EntityId], [Target], [TargetType], [StatusMessage], [LanguageVersion], [RefType], [RefValue])
                            VALUES (source.[Data], source.[Status], source.[EntityId], source.[Target], source.[TargetType], source.[StatusMessage], source.[LanguageVersion], source.[RefType], source.[RefValue]);
                        DROP TABLE #{tableName};";

                        _ = command.ExecuteNonQuery();
                    }
                }
            }
        }

        public void DeleteExpressions(List<int> ids)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM [dbo].[Expression] WHERE Id IN (SELECT [Value] FROM STRING_SPLIT(@IdList, ','))";
                    _ = command.Parameters.AddWithValue("@IdList", string.Join(",", ids));
                    _ = command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteExpressionsIfExists(List<int> entityIds, string target)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM [dbo].[Expression] WHERE [EntityId] IN (SELECT [Value] FROM STRING_SPLIT(@IdList, ',')) AND [Target] = @Target";
                    _ = command.Parameters.AddWithValue("@IdList", string.Join(",", entityIds));
                    _ = command.Parameters.AddWithValue("@Target", target);
                    _ = command.ExecuteNonQuery();
                }
            }
        }

        public DtoExpression GetExpressionForEntityByTarget(int entityId, string target)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT [Id], [Data], [Status], [EntityId], [Target], [TargetType], [RefType], [RefValue], [StatusMessage], [LanguageVersion]
                        FROM [dbo].[Expression]
                        WHERE [EntityId] = @EntityId AND [Target] = @Target";
                    _ = command.Parameters.AddWithValue("@EntityId", entityId);
                    _ = command.Parameters.AddWithValue("@Target", target);

                    var reader = command.ExecuteReader();
                    if (reader.Read())
                    {
                        bool? status;
                        if (reader.IsDBNull(2))
                        {
                            status = null;
                        }
                        else
                        {
                            status = reader.GetBoolean(2);
                        }

                        return new DtoExpression
                        {
                            Id = reader.GetInt32(0),
                            Data = reader.GetString(1),
                            EntityId = reader.GetInt32(3),
                            Status = status,
                            Target = reader.GetString(4),
                            TargetType = reader.GetString(5),
                            RefType = reader.IsDBNull(6) ? null : reader.GetString(6),
                            RefValue = reader.IsDBNull(7) ? null : reader.GetString(7),
                            StatusMessage = reader.IsDBNull(8) ? null : reader.GetString(8),
                            LanguageVersion = reader.GetDouble(9),
                        };
                    }

                    return null;
                }
            }
        }

        private static IEnumerable<IEnumerable<T>> Batch<T>(IEnumerable<T> items, int maxItems)
        {
            return
                items.Select((item, inx) => new { item, inx })
                    .GroupBy(x => x.inx / maxItems)
                    .Select(g => g.Select(x => x.item));
        }
    }
}
