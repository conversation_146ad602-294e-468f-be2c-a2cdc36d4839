namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Remoting.Dto;
    using inRiver.Server.Request;

    /// <summary>
    /// Contains the base functionality for the IPMCServer3DLPersistanceAdapter.
    /// The IPMCServer3DLPersistanceAdapter class is split into other small classes
    /// based on domain.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public IPMCServer3DLPersistanceAdapter(RequestContext context, IDataPersistance origInRiverPersistance)
            : base(context, origInRiverPersistance)
        {
        }

        private ApiClientAuthenticationInfo GetAuthInfo() =>
            ApiClientAuthenticationInfo.CreateFromUsername(this._context.Username, this._context.EnvironmentId);
    }
}
