namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Globalization;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Telemetry.Constants;
    using Xunit;

    public class SyndicationJobResultServiceTests
    {
        [Fact]
        public void UpdateNumberOfFunctions_FieldsAreEmpty_ShouldNotThrowException()
        {
            // Arrange
            const int batchNumberOfEntities = 10;
            var jobResultService = new SyndicationJobResultService();
            var mapContainer = new MapContainer
            {
                MapFields = null,
                MapResourceFields = null,
            };

            // Act
            Action act = () => jobResultService.UpdateNumberOfFunctions(batchNumberOfEntities, mapContainer);

            // Assert
            act.Should().NotThrow();
        }

        [Fact]
        public void UpdateNumberOfFunctions_ContainerHasFieldsWithFunctions_ShouldSetRightNumberOfFunctions()
        {
            // Arrange
            const int batchNumberOfEntities = 5;
            var jobResultService = new SyndicationJobResultService();
            var mapContainer = new MapContainer
            {
                MapFields = new List<MapField>
                {
                    new MapField
                    {
                        Script = "default function 1",
                        IsCustom = false,
                    },
                    new MapField
                    {
                        Script = "default function 2",
                        IsCustom = false,
                    },
                    new MapField
                    {
                        Script = "custom function 1",
                        IsCustom = true,
                    }
                },
                MapResourceFields = new List<MapResourceField>
                {
                    new MapResourceField
                    {
                        Script = "resource custom function 1"
                    },
                    new MapResourceField
                    {
                        Script = "resource custom function 2"
                    },
                    new MapResourceField
                    {
                        Script = null,
                    }
                },
            };

            // Act
            jobResultService.UpdateNumberOfFunctions(batchNumberOfEntities, mapContainer);

            // Assert
            var (numberOfDefaultFunctions, numberOfCustomFunctions) = GetNumberOfFunctionsFromMetric(jobResultService);
            Assert.Equal(15, numberOfCustomFunctions);
            Assert.Equal(10, numberOfDefaultFunctions);
        }

        private static (int NumberOfDefaultFunctions, int NumberOfCustomFunctions) GetNumberOfFunctionsFromMetric(ISyndicationJobResultService jobResultService)
        {
            var stopwatch = A.Fake<Stopwatch>();
            var metrics = jobResultService.GetResultMetrics(stopwatch);
            var hasNumberOfDefaultFunctionsMetric = metrics.TryGetValue(Dimension.NumberOfDefaultFunctions, out var numberOfDefaultFunctions);
            var numberOfDefaultFunctionsInt = hasNumberOfDefaultFunctionsMetric && numberOfDefaultFunctions != "-" ? int.Parse(numberOfDefaultFunctions, new CultureInfo("en-US")) : 0;

            var hasNumberOfCustomFunctionsMetric = metrics.TryGetValue(Dimension.NumberOfCustomFunctions, out var numberOfCustomFunctions);
            var numberOfCustomFunctionsInt = hasNumberOfCustomFunctionsMetric && numberOfCustomFunctions != "-" ? int.Parse(numberOfCustomFunctions, new CultureInfo("en-US")) : 0;

            return (NumberOfDefaultFunctions: numberOfDefaultFunctionsInt, NumberOfCustomFunctions: numberOfCustomFunctionsInt);
        }
    }
}
