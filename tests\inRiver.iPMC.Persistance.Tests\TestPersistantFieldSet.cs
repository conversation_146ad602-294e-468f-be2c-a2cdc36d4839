namespace inRiver.iPMC.Persistance.Tests
{
    using System.Collections.Generic;
    using Constants;
    using Xunit;

    [Collection("Persistance Collection")]
    public class TestPersistantFieldSet : IClassFixture<PersistanceFixture>
    {
        private static PersistanceFixture _persistanceFixture;

        public TestPersistantFieldSet(PersistanceFixture persistanceFixture)
        {
            if (_persistanceFixture == null)
            {
                _persistanceFixture = persistanceFixture;
            }
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void TestGetFieldSet(bool includeFieldTypes)
        {
            var fieldSet = _persistanceFixture.MockPersistentFieldSet.GetFieldSet(KnownDbRecords.FieldSetId, includeFieldTypes);

            Assert.NotNull(fieldSet);

            Assert.IsType<FieldSet>(fieldSet);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void TestGetAllFieldSets(bool includeFieldTypes)
        {
            var fieldSets = _persistanceFixture.MockPersistentFieldSet.GetAllFieldSets(includeFieldTypes);

            Assert.NotNull(fieldSets);

            Assert.IsType<List<FieldSet>>(fieldSets);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void TestGetFieldSetsForEntityType(bool includeFieldTypes)
        {
            var fieldSets = _persistanceFixture.MockPersistentFieldSet.GetFieldSetsForEntityType(KnownDbRecords.EntityTypeId, includeFieldTypes);

            Assert.NotNull(fieldSets);

            Assert.IsType< List<FieldSet>>(fieldSets);
        }

        [Fact]
        public void TestGetFieldTypesForFieldSet()
        {
            var fieldSets = _persistanceFixture.MockPersistentFieldSet.GetFieldTypesForFieldSet(KnownDbRecords.FieldSetId);

            Assert.NotNull(fieldSets);

            Assert.IsType< List<string>>(fieldSets);
        }
    }
}
