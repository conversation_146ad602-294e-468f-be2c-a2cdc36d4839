namespace inRiver.Server.UnitTests.Syndicate
{
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Server.Syndication.Script.Api;
    using Newtonsoft.Json;
    using Xunit;

    public class MaskReplacementServiceTests
    {
        private const int EntityId = 1;

        [Theory]

        // requestPath contains {sys_id} mask
        [InlineData(1, "/{sys_id}/fields", "/1/fields", true)]
        [InlineData(1, "{sys_id}/fields", "1/fields", true)]
        [InlineData(1, "{sys_{sys_id}id}/fields", "{sys_1id}/fields", true)]
        [InlineData(1, "{{{{sys_id}}/fields", "{{{1}/fields", true)]

        // requestPath does not contains {sys_id} mask
        [InlineData(1, "{SYS_ID}/fields", "1/fields", false)]
        [InlineData(1, "{SYS_id}/fields", "1/fields", false)]
        [InlineData(1, "10/fields", "10/fields", false)]
        [InlineData(1, "{sysid}/fields", "{sysid}/fields", false)]
        public void Replace_ShouldReturnCorrectRequestPathAndMasIsUsedValue(int entitySystemId, string requestPath, string result, bool expectedMaskIsUsedValue)
        {
            // Arrange
            var maskReplacementService = A.Fake<MaskReplacementService>(options
                => options.WithArgumentsForConstructor(() => new MaskReplacementService()));

            // Act
            var (replacedRequestPath, _, maskIsUsed) = maskReplacementService.Replace(entitySystemId, requestPath, null);

            // Assert
            replacedRequestPath.Should().BeEquivalentTo(result);
            maskIsUsed.Should().Be(expectedMaskIsUsedValue);
        }

        [Theory]
        [InlineData(1, "{ 'field1': '{sysid}' }", "{ 'field1': '{sysid}' }", false)]
        [InlineData(1, "{ 'field1': [10, '{sys_id}'] }", "{ 'field1': [10, 1] }", true)]
        [InlineData(1, "{ 'field1': [10, 11] }", "{ 'field1': [10, 1] }", false)]
        public void Replace_ShouldReturnCorrectBody(int entitySystemId, string bodyString, string result, bool expectedMaskIsUsedValue)
        {
            // Arrange
            var maskReplacementService = A.Fake<MaskReplacementService>(options
                => options.WithArgumentsForConstructor(() => new MaskReplacementService()));
            var body = JsonConvert.DeserializeObject<object>(bodyString);

            // Act
            var (_, replacedObject, maskIsUsed) = maskReplacementService.Replace(entitySystemId, requestPath: string.Empty, body);

            // Assert
            var resultBodyObject = JsonConvert.DeserializeObject(result);
            replacedObject.Should().BeEquivalentTo(resultBodyObject);
            maskIsUsed.Should().Be(expectedMaskIsUsedValue);
        }

        [Fact]
        public void Replace_RequestPathAndBodyHaveMasks_ShouldReplaceAllMasks()
        {
            // Arrange
            const string requestPath = "v1/fields/{sys_id}";
            var maskReplacementService = A.Fake<MaskReplacementService>(options
                => options.WithArgumentsForConstructor(() => new MaskReplacementService()));
            const string bodyString = @"
                                {
                                    'field1': [10, '{sys_id}']
                                }
                            ";
            var body = JsonConvert.DeserializeObject<object>(bodyString);

            // Act
            var (replacedPath, replacedBody, _) = maskReplacementService.Replace(EntityId, requestPath, body);

            // Assert
            replacedPath.Should().BeEquivalentTo($"v1/fields/{EntityId}");
            replacedBody.Should().BeEquivalentTo(body);
        }
    }
}
