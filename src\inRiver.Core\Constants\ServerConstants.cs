namespace inRiver.Core.Constants
{
    // Re<PERSON><PERSON>per disable InconsistentNaming
    public static class ServerConstants
    {
        public const string MASTER_LANGUAGE = "MASTER_LANGUAGE";

        public const string CHANNEL_FILTER = "CHANNEL_FILTER";

        public const string SKU_FIELD = "SKU_FIELD";

        public const string SKU_SCHEMA = "SKU_SCHEMA";

        public const string MAX_NUMBER_OF_INLINE_CVL_VALUES = "MAX_NUMBER_OF_INLINE_CVL_VALUES";

        public const string ENCRYPTED_SYNDICATION_ENDPOINT_ALIASES = "ENCRYPTED_SYNDICATION_ENDPOINT_ALIASES";
    }

    // ReSharper enable InconsistentNaming
}
