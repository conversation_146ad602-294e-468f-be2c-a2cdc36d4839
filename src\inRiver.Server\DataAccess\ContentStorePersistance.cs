namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using DocumentFormat.OpenXml.Office2010.Excel;
    using inRiver.Server.Error;
    using Serilog;

    public partial class inRiverPersistance
    {
        const string FieldTypeErrorMessage = "An unexpected error occurred when updating exclude field types for content store [{0}]";
        const string EntityTypeErrorMessage = "An unexpected error occurred when updating exclude entity types for content store [{0}]";
        const string LinkTypeErrorMessage = "An unexpected error occurred when updating exclude link types for content store [{0}]";

        public void ExcludeFieldTypes(int contentStoreId, string fieldTypes)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "UPDATE xConnectContentStore SET ExcludeFieldTypes = @ExcludeFieldTypes " +
                                          "WHERE Id = @ContentStoreId";

                    command.Parameters.AddWithValue("@ContentStoreId", contentStoreId);
                    command.Parameters.AddWithValue("@ExcludeFieldTypes", fieldTypes);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, string.Format(FieldTypeErrorMessage, contentStoreId));
                    throw ErrorUtility.GetDataAccessException(string.Format(FieldTypeErrorMessage, contentStoreId), ex);
                }
            }
        }

        public void ExcludeEntityTypes(int contentStoreId, string entityTypes)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "UPDATE xConnectContentStore SET ExcludeEntityTypes = @ExcludeFieldTypes " +
                                          "WHERE Id = @ContentStoreId";

                    command.Parameters.AddWithValue("@ContentStoreId", contentStoreId);
                    command.Parameters.AddWithValue("@ExcludeFieldTypes", entityTypes);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, string.Format(EntityTypeErrorMessage, contentStoreId));
                    throw ErrorUtility.GetDataAccessException(string.Format(FieldTypeErrorMessage, contentStoreId), ex);
                }
            }
        }

        public void ExcludeLinkTypes(int contentStoreId, string linkTypes)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "UPDATE xConnectContentStore SET ExcludeLinkTypes = @ExcludeFieldTypes " +
                                          "WHERE Id = @ContentStoreId";

                    command.Parameters.AddWithValue("@ContentStoreId", contentStoreId);
                    command.Parameters.AddWithValue("@ExcludeFieldTypes", linkTypes);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, string.Format(LinkTypeErrorMessage, contentStoreId));
                    throw ErrorUtility.GetDataAccessException(string.Format(FieldTypeErrorMessage, contentStoreId), ex);
                }
            }
        }

        public Dictionary<int, string> GetAllExcludeFieldTypes()
        {
            var excludeFieldTypes = new Dictionary<int, string>();

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, ExcludeFieldTypes FROM xConnectContentStore";
                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var id = reader.GetInt32(0);
                            var fieldTypes = reader.IsDBNull(1) ? null : reader.GetString(1);
                            excludeFieldTypes.Add(id, fieldTypes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occurred when getting ExcludeFieldTypes.");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when getting ExcludeFieldTypes.", ex);
                }
            }

            return excludeFieldTypes;
        }

        public Dictionary<int, string> GetAllExcludeEntityTypes()
        {
            var excludeFieldTypes = new Dictionary<int, string>();

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, ExcludeEntityTypes FROM xConnectContentStore";
                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var id = reader.GetInt32(0);
                            var fieldTypes = reader.IsDBNull(1) ? null : reader.GetString(1);
                            excludeFieldTypes.Add(id, fieldTypes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occurred when getting ExcludeEntityTypes.");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when getting ExcludeEntityTypes.", ex);
                }
            }

            return excludeFieldTypes;
        }

        public Dictionary<int, string> GetAllExcludeLinkTypes()
        {
            var excludeFieldTypes = new Dictionary<int, string>();

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, ExcludeLinkTypes FROM xConnectContentStore";
                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var id = reader.GetInt32(0);
                            var fieldTypes = reader.IsDBNull(1) ? null : reader.GetString(1);
                            excludeFieldTypes.Add(id, fieldTypes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occurred when getting ExcludeLinkTypes.");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when getting ExcludeLinkTypes.", ex);
                }
            }

            return excludeFieldTypes;
        }
    }
}
