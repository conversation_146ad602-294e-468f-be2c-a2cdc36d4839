namespace inRiver.Server.Syndication.Mapping
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using Core.Http;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Extension;
    using inRiver.Server.Service;
    using inRiver.Server.Syndication.Constants;
    using inRiver.Server.Syndication.Enums;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Newtonsoft.Json.Linq;

    public class MapManager
    {
        private readonly IDataPersistance dataContext;

        private readonly IResourceExportService resourceExportService;

        private readonly IOutputAdapterHttpClient outputAdapterHttpClient;

        public MapManager(IDataPersistance dataContext, int mappingId, IResourceExportService resourceExportService, SyndicationMappingSource mappingSource, string environmentGid)
        {
            this.dataContext = dataContext;
            this.MappingId = mappingId;
            this.MappingSource = mappingSource;
            this.resourceExportService = resourceExportService;
            this.outputAdapterHttpClient = StaticServiceProvider.GetRequiredService<IOutputAdapterHttpClient>();
            this.EnvironmentGid = environmentGid;
        }

        public string WorkareaEntityTypeId { get; private set; }

        public string FirstRelatedEntityTypeId { get; private set; }

        public string FirstLinkEntityTypeId { get; private set; }

        public string SecondRelatedEntityTypeId { get; private set; }

        public string SecondLinkEntityTypeId { get; private set; }

        public string OutputEntityTypeId { get; private set; }

        public int MappingId { get; }

        public SyndicationMappingSource MappingSource { get; }

        public string EnvironmentGid { get; }

        public bool FirstRelatedIsSet => !string.IsNullOrWhiteSpace(this.FirstRelatedEntityTypeId);

        public bool SecondRelatedIsSet => !string.IsNullOrWhiteSpace(this.SecondRelatedEntityTypeId);

        public MapContainer MapContainer { get; private set; }

        private bool IsMapped(string fieldTypeId) =>
            this.MapContainer.MapFields.Any(x => x.Source.FieldTypeId == fieldTypeId)
            || (this.resourceExportService.IsResourceExportAllowed && this.MapContainer.MapResourceFields.Any(x => x.FieldTypeId == fieldTypeId));

        public bool IsImageMapped(string entityTypeId) => this.IsMapped(entityTypeId + ResourceFieldTypes.Images) ||
                                                          this.IsMapped(entityTypeId + ResourceFieldTypes.Image) ||
                                                          this.IsMapped(entityTypeId + ResourceFieldTypes.ImageUrl);

        public static InRiverEntity GetInRiverEntity(Entity entity) =>
            new InRiverEntity
            {
                Id = entity.Id,
                EntityTypeId = entity.EntityType.Id,
                DisplayName = entity.DisplayName?.Data?.ToString(),
                MainPictureId = entity.MainPictureId,
                FieldSetId = entity.FieldSetId,
                Fields = entity.Fields.Select(f => new InRiverField()
                {
                    Data = f.Data,
                    FieldType = new InRiverFieldType()
                    {
                        FieldTypeId = f.FieldType.Id,
                        DataType = f.FieldType.DataType
                    }
                }).ToList()
            };

        public static JArray GetImagesWithLinksAsJson(List<DtoLink> entityResourceLinks, List<Entity> resources, List<CultureInfo> languages)
        {
            var jArray = new JArray();
            foreach (var resource in resources)
            {
                var imageJson = GetImageAsJson(resource, languages);

                var link = entityResourceLinks.First(l => l.Target.Id == resource.Id);
                imageJson.First.AddAfterSelf(new JProperty("Index", link.Index));
                imageJson.First.AddAfterSelf(new JProperty("LinkTypeId", link.LinkTypeId));

                jArray.Add(imageJson);
            }

            return jArray;
        }

        public static JObject GetImageAsJson(Entity resource, List<CultureInfo> languages)
        {
            var filename = resource.DisplayName?.Data?.ToString();
            var extension = Path.GetExtension(filename);

            var jObject = new JObject
            {
                { "Id", resource.MainPictureId },
                { "Url", resource.MainPictureUrl },
                { "EntityId", resource.Id },
                { "Filename", filename },
                { "Extension", extension }
            };

            foreach (var field in resource.Fields)
            {
                var value = field.Data;
                if (field.FieldType.DataType == DataType.LocaleString && field.Data != null)
                {
                    value = ((LocaleString)field.Data).ToDictionary(languages);
                }

                jObject.Add(field.FieldType.Id, value == null ? null : JToken.FromObject(value));
            }

            return jObject;
        }

        public void Load()
        {
            var isMappingSourceOutputAdapter = this.MappingSource == SyndicationMappingSource.OutputAdapter;
            var isMappingSourceOutputAdapterDsa = this.MappingSource == SyndicationMappingSource.OutputAdapterDsa;

            var syndicationMapping = isMappingSourceOutputAdapter
                    ? this.GetSyndicationMappingFromOutputAdapter(this.EnvironmentGid, this.MappingId)
                    : isMappingSourceOutputAdapterDsa
                        ? this.GetSyndicationMappingFromOutputAdapterDsa(this.EnvironmentGid, this.MappingId)
                        : this.GetSyndicationMapping(this.MappingId);

            var mapType = isMappingSourceOutputAdapter || isMappingSourceOutputAdapterDsa
                ? new MapFormat()
                : this.GetMapType(syndicationMapping.FormatId);

            this.WorkareaEntityTypeId = syndicationMapping.WorkareaEntityTypeId;
            this.FirstRelatedEntityTypeId = syndicationMapping.FirstRelatedEntityTypeId;
            this.SecondRelatedEntityTypeId = syndicationMapping.SecondRelatedEntityTypeId;
            this.OutputEntityTypeId = syndicationMapping.OutputEntityTypeId ?? this.WorkareaEntityTypeId;

            if (syndicationMapping.IsResourceExportConfigured)
            {
                this.resourceExportService.SetResourceExportAsConfigured();
            }

            if (!string.IsNullOrWhiteSpace(syndicationMapping.FirstLinkEntityTypeId))
            {
                this.FirstLinkEntityTypeId = syndicationMapping.FirstLinkEntityTypeId;
            }
            else
            {
                // Fallback for mappings created before we made related entity type optional
                // and before we forced the user to select link entity type
                this.FirstLinkEntityTypeId = this.GetLinkTypes(this.FirstRelatedEntityTypeId, this.WorkareaEntityTypeId).FirstOrDefault()?.Id;
            }

            if (!string.IsNullOrWhiteSpace(syndicationMapping.SecondLinkEntityTypeId))
            {
                this.SecondLinkEntityTypeId = syndicationMapping.SecondLinkEntityTypeId;
            }

            // For constants, EntityTypeId is null, so we put the in main EntityType for now
            foreach (var row in syndicationMapping.SyndicationMappingFields)
            {
                if (string.IsNullOrEmpty(row.EntityTypeId))
                {
                    row.EntityTypeId = this.WorkareaEntityTypeId;
                }
            }

            this.MapContainer = new MapContainer()
            {
                EntityTypeId = this.OutputEntityTypeId,
                MapType = mapType,
            };

            this.MapContainer.MapFields = syndicationMapping.SyndicationMappingFields.GroupBy(x => new
            {
                x.EntityTypeId,
                x.FieldTypeId,
                x.MapFieldTypeId,
                x.MapPath,
                x.Enumerations,
                x.MappingFieldGroupId,
                x.UnitType,
                x.UnitCvl,
                x.UnitDefaultValue,
                x.UnitValue,
                x.Converter,
                x.Script,
                x.Args,
                x.IsCustom,
                x.SortOrder,
                x.MappingFormatFieldId
            })
            .Select(x => new MapField
            {
                Source = new Source()
                {
                    EntityTypeId = x.Key.EntityTypeId,
                    FieldTypeId = x.Key.FieldTypeId
                },
                MapFieldTypeId = x.Key.MapFieldTypeId,
                MapPath = x.Key.MapPath,
                MappingFieldGroupId = x.Key.MappingFieldGroupId,
                UnitType = x.Key.UnitType,
                UnitCvl = x.Key.UnitCvl,
                UnitDefaultValue = x.Key.UnitDefaultValue,
                UnitValue = x.Key.UnitValue,
                Converter = x.Key.Converter,
                Script = x.Key.Script,
                IsCustom = x.Key.IsCustom,
                Args = x.Key.Args,
                SortOrder = x.Key.SortOrder,
                MapFieldType = (from t in x
                                where x.Key.MapFieldTypeId == t.MapFieldTypeId
                                select new MapFieldType
                                {
                                    DataType = t.DataType,
                                    FieldDataType = t.FieldDataType,
                                    Mandatory = t.Mandatory,
                                    Unique = t.Unique,
                                    MaxLength = t.MaxLength,
                                    Recommended = t.Recommended,
                                    DefaultValue = t.DefaultValue,
                                    MetaData = t.MetaData
                                }).ToList().First(),
                Enumerations = x.Key.Enumerations,
                MappingFormatFieldId = x.Key.MappingFormatFieldId
            }).ToList();

            this.MapContainer.MapResourceFields = syndicationMapping.SyndicationMappingResourceFields.Select(x => new MapResourceField
            {
                Id = x.Id,
                Args = x.Args,
                ConverterId = x.ConverterId,
                FieldDataType = x.FieldDataType,
                FieldTypeId = x.FieldTypeId,
                Script = x.Script
            }).ToList();
        }

        private MapFormat GetMapType(int id) => this.dataContext.GetMapFormat(id);

        private SyndicationMapping GetSyndicationMapping(int mappingId) => this.dataContext.GetMappingDetails(mappingId);

        private SyndicationMapping GetSyndicationMappingFromOutputAdapterDsa(string environmentGid, int mappingId)
        {
            if (string.IsNullOrEmpty(environmentGid))
            {
                throw new ArgumentNullException("environmentGid is invalid.");
            }

            var mappingDto = this.outputAdapterHttpClient.GetDsaMapping(environmentGid, mappingId, default).Result;
            var syndicationMapping = mappingDto.ConvertToSyndicationMapping();

            this.UpdateScriptAndConverter(syndicationMapping);

            return syndicationMapping;
        }

        private SyndicationMapping GetSyndicationMappingFromOutputAdapter(string environmentGid, int mappingId)
        {
            if (string.IsNullOrEmpty(environmentGid))
            {
                throw new ArgumentNullException("environmentGid is invalid.");
            }

            var mappingDto = this.outputAdapterHttpClient.GetMapping(environmentGid, mappingId, default).Result;

            var syndicationMapping = mappingDto.ConvertToSyndicationMapping();

            this.UpdateScriptAndConverter(syndicationMapping);

            return syndicationMapping;
        }

        private void UpdateScriptAndConverter(SyndicationMapping syndicationMapping)
        {
            syndicationMapping.SyndicationMappingFields.ForEach(x =>
            {
                if (x.ConverterId == 0)
                {
                    return;
                }

                var syndicationMappingFunction = this.dataContext.GetMappingFunction((int)x.ConverterId);
                x.Script = syndicationMappingFunction.Script;
                x.Converter = syndicationMappingFunction.Name;
            });
        }

        private IEnumerable<LinkType> GetLinkTypes(string sourceEntityTypeId, string targetEntityTypeId) =>
            this.dataContext.GetLinkTypes(sourceEntityTypeId, targetEntityTypeId);
    }
}
