namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;

    public interface IPersistanceFieldSet
    {
        List<FieldSet> GetAllFieldSets(bool includeFieldTypes = false);

        FieldSet GetFieldSet(string id, bool includeFieldTypes = false);

        List<FieldSet> GetFieldSetsForEntityType(string entityTypeId, bool includeFieldTypes = false);

        List<string> GetFieldTypesForFieldSet(string fieldSetId);
    }
}
