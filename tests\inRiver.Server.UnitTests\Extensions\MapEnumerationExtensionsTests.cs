namespace inRiver.Server.UnitTests.Extensions
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using FluentAssertions;
    using inRiver.Server.Syndication;
    using Xunit;

    public class MapEnumerationExtensionsTests
    {
        [Theory]
        [InlineData("red color", "red")]
        [InlineData("blue color;red color", "red;blue")]
        [InlineData("blue color;green color;red color", "red;blue;green")]
        [InlineData("purple color", null)]
        [InlineData("red", null)]
        [InlineData("", null)]
        [InlineData(null, null)]

        private void GetMatchingEnumValuesString_ShouldReturnExpectedValues(string foundData, string expected)
        {
            // Arrange
            var enumerations = new List<MapEnumeration>
            {
                new MapEnumeration { EnumValue = "red", FieldValue = "red color" },
                new MapEnumeration { EnumValue = "blue", FieldValue = "blue color" },
                new MapEnumeration { EnumValue = "green", FieldValue = "green color" },
            };
            //// Act
            var result = enumerations.GetMatchingEnumValuesString(foundData);

            //// Assert
            result.Should().Be(expected);
        }
    }
}
