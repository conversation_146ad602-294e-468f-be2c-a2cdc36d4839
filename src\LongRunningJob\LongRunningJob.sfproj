﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" InitialTargets="">
  <Import Project="..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.props" Condition="Exists('..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.props')" />
  <PropertyGroup Label="Globals">
    <ProjectGuid>334dd99c-cb79-465f-b6f5-edbe6430a289</ProjectGuid>
    <ProjectVersion>2.5</ProjectVersion>
    <MinToolsVersion>1.5</MinToolsVersion>
    <SupportedMSBuildNuGetPackageVersion>1.6.7</SupportedMSBuildNuGetPackageVersion>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Development-LocalEuw|x64">
      <Configuration>Development-LocalEuw</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development-LocalUse|x64">
      <Configuration>Development-LocalUse</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <None Include="ApplicationPackageRoot\ApplicationManifest.xml" />
    <None Include="ApplicationParameters\Local.1Node.Development-LocalEuw.xml" />
    <None Include="ApplicationParameters\Local.5Node.Development-LocalEuw.xml" />
    <None Include="ApplicationParameters\Local.1Node.Development-LocalUse.xml" />
    <None Include="ApplicationParameters\Local.5Node.Development-LocalUse.xml" />
    <None Include="PublishProfiles\Local.5Node.Template.xml" />
    <None Include="PublishProfiles\Local.1Node.Template.xml" />
    <None Include="PublishProfiles\Local.1Node.xml" />
    <None Include="PublishProfiles\Local.5Node.xml" />
    <None Include="Scripts\Deploy-FabricApplication.ps1" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ApplicationParameters\ApplicationParameters.template.xml" />
    <Content Include="packages.config" />
    <Content Include="PublishProfiles\PublishProfiles.template.xml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LongRunningJobWorkerService\LongRunningJobWorkerService.csproj" />
    <ProjectReference Include="..\LongRunningJobActor\LongRunningJobActor.csproj" />
    <ProjectReference Include="..\LongRunningJobService\LongRunningJobService.csproj" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />
  <PropertyGroup>
    <ApplicationProjectTargetsPath>$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Service Fabric Tools\Microsoft.VisualStudio.Azure.Fabric.ApplicationProject.targets</ApplicationProjectTargetsPath>
  </PropertyGroup>
  <Import Project="$(ApplicationProjectTargetsPath)" Condition="Exists('$(ApplicationProjectTargetsPath)')" />
  <Import Project="..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets" Condition="Exists('..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets')" />
  <Target Name="ValidateMSBuildFiles">
    <Error Condition="!Exists('..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.props')" Text="Unable to find the '..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.props' file. Please restore the 'Microsoft.VisualStudio.Azure.Fabric.MSBuild' Nuget package." />
    <Error Condition="!Exists('..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets')" Text="Unable to find the '..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.6.7\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets' file. Please restore the 'Microsoft.VisualStudio.Azure.Fabric.MSBuild' Nuget package." />
  </Target>
  <!-- Run the build/CreateConfigFiles script to create the local config files -->
  <Target Name="UpdateProfile" BeforeTargets="UpdateServiceFabricApplicationManifest" Condition="$(Configuration.Contains('Development-'))">
    <Exec Command="%WINDIR%\System32\WindowsPowerShell\v1.0\powershell.exe -NonInteractive -ExecutionPolicy Unrestricted -NoProfile -Command &quot;&amp; { &amp;'../../build/CreateLocalConfigFiles.ps1' '$(Configuration)'}&quot;" WorkingDirectory="$(ProjectDir)" />
  </Target>
</Project>