namespace LongRunningJobService.Middleware
{
    using System.Threading.Tasks;
    using global::LongRunningJobService.Constants;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Models;
    using Microsoft.AspNetCore.Http;

    public class EnvironmentContextMiddleware
    {
        private readonly RequestDelegate next;
        private readonly IEnvironmentContextAccessor environmentContextAccessor;
        private readonly ICustomerEnvironmentRepository customerEnvironmentRepository;

        public EnvironmentContextMiddleware(RequestDelegate next, IEnvironmentContextAccessor environmentContextAccessor, ICustomerEnvironmentRepository customerEnvironmentRepository)
        {
            this.next = next;
            this.environmentContextAccessor = environmentContextAccessor;
            this.customerEnvironmentRepository = customerEnvironmentRepository;
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            var (customerSafeName, environmentSafeName) = GetCustomerEnvironmentSafeNames(httpContext);

            if (!string.IsNullOrEmpty(customerSafeName) && !string.IsNullOrEmpty(environmentSafeName))
            {
                var customerEnvironmentContextData = await this.customerEnvironmentRepository.GetAsync(customerSafeName, environmentSafeName);
                var environmentContext = new EnvironmentContext(
                    customerEnvironmentContextData.EnvironmentId,
                    customerEnvironmentContextData.CustomerSafeName,
                    customerEnvironmentContextData.EnvironmentSafeName,
                    customerEnvironmentContextData.ConnectionString,
                    customerEnvironmentContextData.StorageAccountConnectionString);

                this.environmentContextAccessor.EnvironmentContext = environmentContext;
            }

            await this.next(httpContext);
        }

        private static (string CustomerSafeName, string EnvironmentSafeName) GetCustomerEnvironmentSafeNames(
            HttpContext httpContext) =>
            (httpContext.Request.RouteValues[RequestPathParts.CustomerSafeName] as string,
                httpContext.Request.RouteValues[RequestPathParts.EnvironmentSafeName] as string);
    }
}
