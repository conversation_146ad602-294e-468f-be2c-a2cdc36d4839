namespace LongRunningJob.Core.UnitTests
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Models.inRiver;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Models;
    using Xunit;

    public class JobRunnerTests
    {
        [Theory]
        [InlineData(LongRunningJobsStatus.Finished)]
        [InlineData(LongRunningJobsStatus.FinishedWithErrors)]
        public async Task RunAsync_SuccessfulJobStateResult_ShouldReturnTrue(string jobState)
        {
            var commandDispatcherFake = A.Fake<ICommandDispatcher<Result>>();
            A.<PERSON>o(() => commandDispatcherFake.DispatchAsync(A<ICommand>.Ignored, CancellationToken.None)).Returns(new Result { JobState = jobState });
            var jobRunner = new JobRunner(<PERSON><PERSON>Du<PERSON><ILongRunningJobRepository>(), commandDispatcher<PERSON><PERSON>, <PERSON><PERSON><ICommandFactory>());

            var result = await jobRunner.RunAsync(new LongRunningJob(), CancellationToken.None);

            result.Should().BeTrue();
        }

        [Theory]
        [InlineData(LongRunningJobsStatus.Error)]
        [InlineData(LongRunningJobsStatus.Running)]
        [InlineData(LongRunningJobsStatus.Queued)]
        [InlineData(LongRunningJobsStatus.Unknown)]
        public async Task RunAsync_UnSuccessfulJobStateResult_ShouldReturnFalse(string jobState)
        {
            var commandDispatcherFake = A.Fake<ICommandDispatcher<Result>>();
            A.CallTo(() => commandDispatcherFake.DispatchAsync(A<ICommand>.Ignored, CancellationToken.None)).Returns(new Result { JobState = jobState });
            var jobRunner = new JobRunner(A.Dummy<ILongRunningJobRepository>(), commandDispatcherFake, A.Dummy<ICommandFactory>());

            var result = await jobRunner.RunAsync(new LongRunningJob(), CancellationToken.None);

            result.Should().BeFalse();
        }

        [Fact]
        public async Task RunAsync_ThrownException_ShouldReturnFalse()
        {
            var commandDispatcherFake = A.Fake<ICommandDispatcher<Result>>();
            A.CallTo(() => commandDispatcherFake.DispatchAsync(A<ICommand>.Ignored, CancellationToken.None)).Throws<Exception>();
            var longRunningJobRepositoryFake = A.Fake<ILongRunningJobRepository>();
            var jobRunner = new JobRunner(longRunningJobRepositoryFake, commandDispatcherFake, A.Dummy<ICommandFactory>());

            var result = await jobRunner.RunAsync(new LongRunningJob(), CancellationToken.None);

            result.Should().BeFalse();
        }

        [Fact]
        public async Task RunAsync_ThrownException_ShouldSetJobStateToError()
        {
            var commandDispatcherFake = A.Fake<ICommandDispatcher<Result>>();
            A.CallTo(() => commandDispatcherFake.DispatchAsync(A<ICommand>.Ignored, CancellationToken.None)).Throws<Exception>();
            var longRunningJobRepositoryFake = A.Fake<ILongRunningJobRepository>();
            var jobRunner = new JobRunner(longRunningJobRepositoryFake, commandDispatcherFake, A.Dummy<ICommandFactory>());

            await jobRunner.RunAsync(new LongRunningJob(), CancellationToken.None);

            A.CallTo(() => longRunningJobRepositoryFake.UpdateStateAsync(A<int>.Ignored, LongRunningJobsStatus.Error)).MustHaveHappened();
        }
    }
}
