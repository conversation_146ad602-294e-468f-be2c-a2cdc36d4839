namespace inRiver.Core.Models.inRiver.ExcelImport
{
    using System.Collections.Generic;

    public class FileImportConfigurationModel
    {
        public string Id { get; set; }

        public string Filename { get; set; }

        public string FileType { get; set; }

        public string EntityTypeId { get; set; }

        public List<EntityType> EntityTypes { get; set; }

        public List<FieldType> FieldTypes { get; set; }

        public string CurrentLanguage { get; set; }

        public List<Language> Languages { get; set; }

        public bool ShowEmptyColumns { get; set; }

        public bool ClearEmptyValues { get; set; }

        public List<FileImportColumnModel> Columns { get; set; }

        public string MappingName { get; set; }

        public int MappingId { get; set; }

        public bool HasSpecificationRelation { get; set; }

        public int DefaultSegmentId { get; set; }

        public string BatchId { get; set; }

        public bool RunValidation { get; set; } = true;

        public bool RunImport { get; set; } = true;

        public bool RunServerExtensions { get; set; } = true;

        public bool RunEntityListeners { get; set; } = true;

        public bool RunChannelListeners { get; set; } = true;

        public bool RunPostProcessing { get; set; } = true;

    }
}
