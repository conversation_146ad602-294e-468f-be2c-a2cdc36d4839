namespace inRiver.Server.Completeness.Criteria
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;

    public class FieldNotEmptyCritera
    {
        private readonly RequestContext context;

        public FieldNotEmptyCritera(RequestContext context)
        {
            this.context = context;
        }

        private const string SettingsKey = "FieldTypeId";

        public string Name => "Field Not Empty";

        public List<string> SettingsKeys => new List<string> { SettingsKey };

        public int GetCriteriaCompletenessPercentage(int entityId, List<CompletenessRuleSetting> settings)
        {
            CompletenessRuleSetting setting = settings.FirstOrDefault(s => s.Key == SettingsKey);

            if (setting == null)
            {
                return 0;
            }

            string fieldTypeId = setting.Value;

            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return 0;
            }

            object data = this.context.DataPersistance.GetFieldValue(entityId, fieldTypeId);

            if (data == null)
            {
                return 0;
            }

            if (string.IsNullOrEmpty(data.ToString()))
            {
                return 0;
            }

            if (data.GetType() == typeof(LocaleString))
            {
                if (LocaleString.IsNullOrEmpty((LocaleString)data))
                {
                    return 0;
                }
            }

            return 100;
        }

        public async Task<int> GetCriteriaCompletenessPercentageAsync(int entityId, IEnumerable<CompletenessRuleSetting> settings)
        {
            var setting = settings.FirstOrDefault(s => s.Key == SettingsKey);

            if (setting == null)
            {
                return 0;
            }

            var fieldTypeId = setting.Value;

            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return 0;
            }

            var data = await this.context.DataPersistance.GetFieldValueAsync(entityId, fieldTypeId);

            if (data == null)
            {
                return 0;
            }

            if (string.IsNullOrEmpty(data.ToString()))
            {
                return 0;
            }

            if (data.GetType() == typeof(LocaleString))
            {
                if (LocaleString.IsNullOrEmpty((LocaleString)data))
                {
                    return 0;
                }
            }

            return 100;
        }
    }
}
