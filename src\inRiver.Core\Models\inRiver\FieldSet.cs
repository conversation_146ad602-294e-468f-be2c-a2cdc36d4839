﻿namespace inRiver.Core.Models.inRiver
{
    using System.Collections.Generic;

    public class FieldSet
        : IIdentifierAsStringInterface
    {
        public FieldSet()
        {
            this.FieldTypes = new List<string>();
        }

        public string Id { get; set; }

        public LocaleString Name { get; set; }

        public LocaleString Description { get; set; }

        public string EntityTypeId { get; set; }

        public List<string> FieldTypes { get; set; }

        public override string ToString()
        {
            if (string.IsNullOrWhiteSpace(this.Id))
            {
                return base.ToString();
            }

            return this.Id;
        }
    }
}
