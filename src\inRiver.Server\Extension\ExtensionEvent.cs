namespace inRiver.Server.Extension
{
    internal struct ExtensionEvent
    {
        public static string OnAdd => "OnAdd";

        public static string OnUpdate => "OnUpdate";

        public static string OnDelete => "OnDelete";

        public static string OnLink => "OnLink";

        public static string OnLinkUpdate => "OnLinkUpdate";

        public static string OnUnlink => "OnUnlink";

        public static string OnLock => "OnLock";

        public static string OnUnlock => "OnUnlock";

        public static string OnCreateVersion => "OnCreateVersion";
    }
}
