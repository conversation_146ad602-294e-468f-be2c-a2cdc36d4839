namespace LongRunningJobService.Code
{
    using System.Threading.Tasks;
    using global::LongRunningJobService.Abstractions;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Enum;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Constants;
    using LongRunningJob.Core.Models;

    public class LongRunningJobInitializer : ILongRunningJobInitializer
    {
        private readonly IJobModelRepository jobModelRepository;
        private readonly ILongRunningJobRepository jobRepository;
        private readonly IJobWorkerServiceSpawner jobWorkerServiceSpawner;

        public LongRunningJobInitializer(IJobModelRepository jobModelRepository, ILongRunningJobRepository jobRepository, IJobWorkerServiceSpawner jobWorkerServiceSpawner)
        {
            this.jobModelRepository = jobModelRepository;
            this.jobRepository = jobRepository;
            this.jobWorkerServiceSpawner = jobWorkerServiceSpawner;
        }

        public async Task InitialiseExcelExportJobAsync(string username, ExcelExportModel excelExportModel)
        {
            var longRunningJobId = await this.CreateLongRunningJobAsync(LongRunningJobsJobType.ExcelExport, username, LongRunningJobScope.Personal, username);
            if (longRunningJobId.HasValue)
            {
                var excelExportJobModel = new ExcelExportJobModel
                {
                    ExcelExportModel = excelExportModel,
                    UserName = username
                };

                await this.jobModelRepository.SaveJobModelAsync(excelExportJobModel, longRunningJobId.Value);

                await this.jobWorkerServiceSpawner.SpawnAsync(longRunningJobId.Value);
            }
        }

        private async Task<int?> CreateLongRunningJobAsync(string jobType, string identifier, LongRunningJobScope scope, string startedBy = null)
        {
            if (await this.jobRepository.StartedJobExistsAsync(jobType, identifier))
            {
                return null;
            }

            return await this.jobRepository.InsertLongRunningJobAsync(new LongRunningJob
            {
                JobType = jobType,
                Identifier = identifier,
                State = LongRunningJobsStatus.Queued,
                Scope = scope,
                StartedBy = startedBy
            });
        }
    }
}
