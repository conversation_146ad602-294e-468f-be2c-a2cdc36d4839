namespace LongRunningJobService.Middlewares
{
    using System;
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Http;

    /// <summary>
    /// Adds an X-ServiceFabric header when responding with 404.
    /// Required by ServiceFabric when using the internal reverse proxy.
    /// https://docs.microsoft.com/en-us/azure/service-fabric/service-fabric-reverseproxy#special-handling-for-port-sharing-services
    /// </summary>
    public class ServiceFabricResourceNotFoundMiddleware
    {
        private readonly RequestDelegate next;

        public ServiceFabricResourceNotFoundMiddleware(RequestDelegate next)
        {
            this.next = next;
        }

        public Task InvokeAsync(HttpContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            context.Response.OnStarting(
                () => {
                    if (context.Response.StatusCode == (int)HttpStatusCode.NotFound)
                    {
                        context.Response.Headers.Add("X-ServiceFabric", "ResourceNotFound");
                    }

                    return Task.CompletedTask;
                });

            return this.next(context);
        }
    }
}
