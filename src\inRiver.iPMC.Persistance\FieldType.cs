namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;

    public class FieldType
    {
        public FieldType()
        {
            this.TrackChanges = true;
        }

        public FieldType(string id, string entityTypeId, string dataType, string categoryId)
        {
            this.Id = id;
            this.EntityTypeId = entityTypeId;
            this.DataType = dataType;
            this.CategoryId = categoryId;
            this.TrackChanges = true;
        }

        public string Id { get; set; }

        public LocaleString Name { get; set; }

        public LocaleString Description { get; set; }

        public string EntityTypeId { get; set; }

        public string DataType { get; set; }

        public bool Mandatory { get; set; }

        public bool Unique { get; set; }

        public int Index { get; set; }

        public string CategoryId { get; set; }

        public string DefaultValue { get; set; }

        public bool Hidden { get; set; }

        public bool ReadOnly { get; set; }

        public bool IsDisplayName { get; set; }

        public bool IsDisplayDescription { get; set; }

        public Dictionary<string, string> Settings { get; set; }

        public bool ExcludeFromDefaultView { get; set; }

        public string CVLId { get; set; }

        public bool Multivalue { get; set; }

        public bool TrackChanges { get; set; }

        public bool ExpressionSupport { get; set; }

        public override string ToString() => string.IsNullOrWhiteSpace(this.Id) ? base.ToString() : this.Id;
    }
}
