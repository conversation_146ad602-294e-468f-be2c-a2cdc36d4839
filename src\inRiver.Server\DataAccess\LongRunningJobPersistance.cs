namespace inRiver.Server.DataAccess
{
    using System;
    using System.Data;
    using System.Data.SqlClient;
    using Serilog;

    partial class inRiverPersistance
    {
        public string GetStorageAccountConnectionString(int environmentId)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(this.ReadonlyConfigConnectionString))
                {
                    using (SqlCommand command = connection.CreateCommand())
                    {
                        connection.Open();
                        command.CommandText = "SELECT StorageAccountConnectionString FROM Environment WHERE Id = @Id";
                        command.Parameters.Add("@Id", SqlDbType.Int).Value = environmentId;

                        object result = command.ExecuteScalar();
                        if (null != result)
                            return Convert.ToString(result);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetStorageAccountConnectionString caught an unexpected exception");
                throw;
            }

            return null;
        }
    }
}
