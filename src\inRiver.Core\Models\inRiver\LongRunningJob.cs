namespace inRiver.Core.Models.inRiver
{
    using System;
    using Enum;

    public class LongRunningJob
    {
        public int Id { get; set; }

        public LongRunningJobScope Scope { get; set; } = LongRunningJobScope.Internal;

        public string JobType { get; set; }

        public string Identifier { get; set; }

        /// <summary>
        /// IdentifierType will be null for all the long running jobs.
        /// If Syndication Id is 0 meaning no syndication exists then syndication should be done on SyndicationFormat Id,
        /// in this case FileFormatId is set in Identifier and this IdentifierType specifies what data we have in Identifier.
        /// </summary>
        public string IdentifierType { get; set; }

        public string State { get; set; }

        public DateTime Date { get; }

        public int PercentCompleted { get; set; }

        public string Metadata { get; set; }

        public string StartedBy { get; set; } = null;
    }
}
