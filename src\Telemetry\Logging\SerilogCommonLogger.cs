namespace Telemetry.Logging
{
    using System;
    using System.Runtime.CompilerServices;
    using inRiver.Log;
    using Serilog.Events;

    public class SerilogCommonLogger : ICommonLogging
    {
        public static SerilogCommonLogger Instance => new SerilogCommonLogger();

        public void Log(
            LogLevel logLevel,
            string message,
            string module,
            string error,
            string stackTrace,
            string username,
            string memberName = "",
            string filePath = "",
            int lineNo = 0,
            LogOrigin logOrigin = LogOrigin.Undefined) =>
            Serilog.Log
                .ForContext(nameof(module), module)
                .ForContext(nameof(username), username)
                .ForContext(nameof(memberName), memberName)
                .ForContext(nameof(filePath), filePath)
                .ForContext(nameof(lineNo), lineNo)
                .ForContext(nameof(logOrigin), logOrigin)
                .Write(Map(logLevel), message);

        public void Information(
            string message,
            string module,
            string username,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNo = 0,
            LogOrigin logOrigin = LogOrigin.Undefined) =>
            this.Log(LogLevel.Information, message, module, string.Empty, string.Empty, username, memberName, filePath, lineNo, logOrigin);

        public void Debug(
            string message,
            string module,
            string username,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNo = 0,
            LogOrigin logOrigin = LogOrigin.Undefined) =>
            this.Log(LogLevel.Debug, message, module, string.Empty, string.Empty, username, memberName, filePath, lineNo, logOrigin);

        public void Warning(
            string message,
            string module,
            string username,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNo = 0,
            LogOrigin logOrigin = LogOrigin.Undefined) =>
            this.Log(LogLevel.Warning, message, module, string.Empty, string.Empty, username, memberName, filePath, lineNo, logOrigin);

        public void Error(
            string message,
            Exception ex,
            string module,
            string username,
            bool showStacktrace = true,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNo = 0,
            LogOrigin logOrigin = LogOrigin.Undefined) =>
            Serilog.Log
                .ForContext(nameof(module), module)
                .ForContext(nameof(username), username)
                .ForContext(nameof(showStacktrace), showStacktrace)
                .ForContext(nameof(memberName), memberName)
                .ForContext(nameof(filePath), filePath)
                .ForContext(nameof(lineNo), lineNo)
                .ForContext(nameof(logOrigin), logOrigin)
                .Error(ex, message, module);

        public void Verbose(
            string message,
            string module,
            string username,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNo = 0,
            LogOrigin logOrigin = LogOrigin.Undefined) =>
            this.Log(LogLevel.Verbose, message, module, string.Empty, string.Empty, username, memberName, filePath, lineNo, logOrigin);

        private static LogEventLevel Map(LogLevel logLevel)
        {
            switch (logLevel)
            {
                case LogLevel.Error:
                    return LogEventLevel.Error;
                case LogLevel.Warning:
                    return LogEventLevel.Warning;
                case LogLevel.Information:
                    return LogEventLevel.Information;
                case LogLevel.Debug:
                    return LogEventLevel.Debug;
                case LogLevel.Verbose:
                default:
                    return LogEventLevel.Verbose;
            }
        }
    }
}
