namespace inRiver.Server.Syndication.Service
{
    using inRiver.Server.Syndication.Export;

    public static class ExportSerializationSettingsService
    {
        public static void ConfigureSettings(SyndicationModel syndicationModel, bool applyDsaMapping)
        {
            if (syndicationModel.DsaMappingId.HasValue && syndicationModel.RunDsaSyndication && !applyDsaMapping)
            {
                ExportSerializationSettings.SerializeMapFieldType = true;
            }
            else
            {
                ExportSerializationSettings.SerializeMapFieldType = false;
            }
        }
    }
}
