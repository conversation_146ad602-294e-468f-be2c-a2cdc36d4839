namespace inRiver.iPMC.Persistance.Tests
{
    using Xunit;

    [Collection("Persistance Collection")]
    public class TestPersistantEntityType : IClassFixture<PersistanceFixture>
    {
        private static PersistanceFixture _persistanceFixture;

        public TestPersistantEntityType(PersistanceFixture persistanceFixture)
        {
            if (_persistanceFixture == null)
            {
                _persistanceFixture = persistanceFixture;
            }
        }

        [Theory]
        [InlineData(false, false, false)]
        [InlineData(true, false, false)]
        [InlineData(false, true, false)]
        [InlineData(false, false, true)]
        [InlineData(true, true, true)]
        public void TestGetEntityTypes(bool loadFieldTypes, bool loadLinkTypes, bool loadFieldsets)
        {
            var allEntityTypes = _persistanceFixture.MockPersistentEntityType.GetAllEntityTypes(
                loadFieldTypes: loadFieldTypes,
                loadLinkTypes: loadLinkTypes,
                loadFieldsets: loadFieldsets);

            Assert.NotNull(allEntityTypes);
        }

    }
}
