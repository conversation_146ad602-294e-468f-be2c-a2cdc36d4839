# https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/code-style-rule-options
# https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/language-rules

################ top-most EditorConfig file
root = true

# Don't use tabs for indentation.
[*]
charset = utf-8
end_of_line = crlf
indent_style = space
insert_final_newline = true
trim_trailing_whitespace = true
indent_size = 4

################ Solution Files
[*.sln]
indent_style = tab

################ XML Project Files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj,sfproj}]
indent_size = 2

################ Configuration Files
[*.{json,xml,yml,config,props,targets,nuspec,resx,ruleset,vsixmanifest,vsct}]
indent_size = 2

################ Markdown Files
[*.md]
trim_trailing_whitespace = false

################ Web Files
[*.{js,jsx,ts,tsx,vue,htm,html,css,scss,less}]
indent_size = 4
max_line_length = 120

################ Script Files
[*.{ps1,psm1,sh}]
indent_size = 2

#
################ Dotnet Code Style Settings
#
[*.{cs,csx,vb,vbx}]
indent_size = 4

# NET style rules

# this preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0003-ide0009
dotnet_style_qualification_for_event = true:error
dotnet_style_qualification_for_field = true:error
dotnet_style_qualification_for_method = true:error
dotnet_style_qualification_for_property = true:error

# Use language keywords instead of framework type names for type references. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0049
dotnet_style_predefined_type_for_locals_parameters_members = true:error
dotnet_style_predefined_type_for_member_access = true:error

# .NET modifier preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/modifier-preferences#net-modifier-preferences
dotnet_style_require_accessibility_modifiers = always:error
csharp_preferred_modifier_order = public, private, protected, internal, static, extern, new, virtual, abstract, sealed, override, readonly, unsafe, volatile, async:silent: error
dotnet_style_readonly_field = true:error

# Parentheses preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0047-ide0048
dotnet_style_parentheses_in_arithmetic_binary_operators = always_for_clarity:suggestion
dotnet_style_parentheses_in_relational_binary_operators = always_for_clarity:suggestion
dotnet_style_parentheses_in_other_binary_operators = always_for_clarity:suggestion
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:suggestion

# .NET expression-level preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/expression-level-preferences#net-expression-level-preferences
dotnet_style_object_initializer = true:error
dotnet_style_collection_initializer = true:error
dotnet_style_explicit_tuple_names = true:error
dotnet_style_prefer_inferred_tuple_names = true:error
dotnet_style_prefer_inferred_anonymous_type_member_names = true:error
dotnet_style_prefer_auto_properties = true:error
dotnet_style_prefer_conditional_expression_over_assignment = true:error
dotnet_style_prefer_conditional_expression_over_return = true:error
dotnet_style_prefer_compound_assignment = true:error
dotnet_style_prefer_simplified_interpolation = true:error
dotnet_style_prefer_simplified_boolean_expressions = true:error

# .NET null-checking preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/null-checking-preferences#net-null-checking-preferences
dotnet_style_coalesce_expression = true:error
dotnet_style_null_propagation = true:error
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:error



# C# Style rules. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/language-rules
[*.{cs,csx,cake,cshtml}]

# 'var' preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0003-ide0009
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_elsewhere = true:suggestion
csharp_style_var_when_type_is_apparent = true:error

# Expression-bodied members. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/expression-bodied-members
csharp_style_expression_bodied_accessors = when_on_single_line:error
csharp_style_expression_bodied_constructors = false:error
csharp_style_expression_bodied_indexers = true:error
csharp_style_expression_bodied_methods = when_on_single_line:error
csharp_style_expression_bodied_operators = true:error
csharp_style_expression_bodied_properties = true:error
csharp_style_expression_bodied_lambdas = true:error

# Pattern matching preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/pattern-matching-preferences
csharp_style_pattern_matching_over_as_with_null_check = true:error
csharp_style_pattern_matching_over_is_with_cast_check = true:error
csharp_style_prefer_switch_expression = true:error
csharp_style_prefer_pattern_matching = true:error
csharp_style_prefer_not_pattern = true:suggestion

# Expression-level preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/expression-level-preferences#c-expression-level-preferences
csharp_style_inlined_variable_declaration = true:error
csharp_prefer_simple_default_expression = true:error
csharp_style_pattern_local_over_anonymous_function = true:error
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_style_prefer_index_operator = true:suggestion
csharp_style_prefer_range_operator = true:suggestion
csharp_style_implicit_object_creation_when_type_is_apparent = true:suggestion
csharp_style_unused_value_assignment_preference = discard_variable:error

# "Null" checking preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/null-checking-preferences#c-null-checking-preferences
csharp_style_conditional_delegate_call = true:error
csharp_style_throw_expression = true:error

# Code-block preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/code-block-preferences
csharp_prefer_braces = true:error
csharp_prefer_simple_using_statement = true:suggestion

# 'using' directive preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0065
csharp_using_directive_placement = inside_namespace:error

# Modifier preferences
csharp_prefer_static_local_function = true:error
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async:error



# C# formatting rules. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#c-formatting-rules
dotnet_sort_system_directives_first = true:error
dotnet_separate_import_directive_groups = false:error
s
# New Line Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#new-line-options
csharp_new_line_before_catch = true:error
csharp_new_line_before_else = false:error
csharp_new_line_before_finally = true:error
csharp_new_line_before_members_in_anonymous_types = true:error
csharp_new_line_before_members_in_object_initializers = true:error
csharp_new_line_between_query_expression_clauses = false:error
csharp_new_line_before_open_brace = methods, properties, control_blocks, object_collection_array_initializers, types

# Indentation Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#indentation-options
csharp_indent_block_contents = true:error
csharp_indent_braces = false:error
csharp_indent_case_contents = true:error
csharp_indent_case_contents_when_block = false:error
csharp_indent_labels = no_change:error
csharp_indent_switch_labels = true:error

# Spacing Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#spacing-options
csharp_space_after_cast = false:error
csharp_space_after_colon_in_inheritance_clause = true:error
csharp_space_after_comma = true:error
csharp_space_after_dot = false:error
csharp_space_after_keywords_in_control_flow_statements = true:error
csharp_space_after_semicolon_in_for_statement = true:error
csharp_space_around_binary_operators = before_and_after:error
csharp_space_around_declaration_statements = do_not_ignore:error
csharp_space_before_colon_in_inheritance_clause = true:error
csharp_space_before_comma = false:error
csharp_space_before_dot = false:error
csharp_space_before_semicolon_in_for_statement = false:error
csharp_space_before_open_square_brackets = false:error
csharp_space_between_empty_square_brackets = false:error
csharp_space_between_method_declaration_name_and_open_parenthesis = false:error
csharp_space_between_method_declaration_parameter_list_parentheses = false:error
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false:error
csharp_space_between_method_call_name_and_opening_parenthesis = false:error
csharp_space_between_method_call_parameter_list_parentheses = false:error
csharp_space_between_method_call_empty_parameter_list_parentheses = false:error
csharp_space_between_parentheses = expressions:error
csharp_space_between_square_brackets = false:error

# Wrap Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#wrap-options
csharp_preserve_single_line_blocks = true:error
csharp_preserve_single_line_statements = false:error

# Modifier preferences
csharp_prefer_static_local_function = true:error

# Parameter preferences
dotnet_code_quality_unused_parameters = all:error

# Naming rules. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/naming-rules


# Naming Symbols
# constant_fields - Define constant fields
dotnet_naming_symbols.constant_fields.applicable_kinds = field
dotnet_naming_symbols.constant_fields.required_modifiers = const
# non_private_readonly_fields - Define public, internal and protected readonly fields
dotnet_naming_symbols.non_private_readonly_fields.applicable_accessibilities = public, internal, protected
dotnet_naming_symbols.non_private_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.non_private_readonly_fields.required_modifiers = readonly
# static_readonly_fields - Define static and readonly fields
dotnet_naming_symbols.static_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.static_readonly_fields.required_modifiers = static, readonly
# private_readonly_fields - Define private readonly fields
dotnet_naming_symbols.private_readonly_fields.applicable_accessibilities = private
dotnet_naming_symbols.private_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.private_readonly_fields.required_modifiers = readonly
# public_internal_fields - Define public and internal fields
dotnet_naming_symbols.public_internal_fields.applicable_accessibilities = public, internal
dotnet_naming_symbols.public_internal_fields.applicable_kinds = field
# private_protected_fields - Define private and protected fields
dotnet_naming_symbols.private_protected_fields.applicable_accessibilities = private, protected
dotnet_naming_symbols.private_protected_fields.applicable_kinds = field
# public_symbols - Define any public symbol
dotnet_naming_symbols.public_symbols.applicable_accessibilities = public, internal, protected, protected_internal
dotnet_naming_symbols.public_symbols.applicable_kinds = method, property, event, delegate
# parameters - Defines any parameter
dotnet_naming_symbols.parameters.applicable_kinds = parameter
# non_interface_types - Defines class, struct, enum and delegate types
dotnet_naming_symbols.non_interface_types.applicable_kinds = class, struct, enum, delegate
# interface_types - Defines interfaces
dotnet_naming_symbols.interface_types.applicable_kinds = interface

# Naming Styles
# camel_case - Define the camelCase style
dotnet_naming_style.camel_case.capitalization = camel_case
# pascal_case - Define the Pascal_case style
dotnet_naming_style.pascal_case.capitalization = pascal_case
# first_upper - The first character must start with an upper-case character
dotnet_naming_style.first_upper.capitalization = first_word_upper
# prefix_interface_interface_with_i - Interfaces must be PascalCase and the first character of an interface must be an 'I'
dotnet_naming_style.prefix_interface_interface_with_i.capitalization = pascal_case
dotnet_naming_style.prefix_interface_interface_with_i.required_prefix = I

# Naming Rules
# Constant fields must be PascalCase
dotnet_naming_rule.constant_fields_must_be_pascal_case.severity = error
dotnet_naming_rule.constant_fields_must_be_pascal_case.symbols = constant_fields
dotnet_naming_rule.constant_fields_must_be_pascal_case.style = pascal_case
# Public, internal and protected readonly fields must be PascalCase
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.severity = error
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.symbols = non_private_readonly_fields
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.style = pascal_case
# Static readonly fields must be PascalCase
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.severity = error
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.symbols = static_readonly_fields
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.style = pascal_case
# Private readonly fields must be camelCase
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.severity = error
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.symbols = private_readonly_fields
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.style = camel_case
# Public and internal fields must be PascalCase
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.severity = error
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.symbols = public_internal_fields
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.style = pascal_case
# Private and protected fields must be camelCase
dotnet_naming_rule.private_protected_fields_must_be_camel_case.severity = error
dotnet_naming_rule.private_protected_fields_must_be_camel_case.symbols = private_protected_fields
dotnet_naming_rule.private_protected_fields_must_be_camel_case.style = camel_case

# Public members must be capitalized
dotnet_naming_rule.public_members_must_be_capitalized.severity = error
dotnet_naming_rule.public_members_must_be_capitalized.symbols = public_symbols
dotnet_naming_rule.public_members_must_be_capitalized.style = first_upper
# Parameters must be camelCase
dotnet_naming_rule.parameters_must_be_camel_case.severity = error
dotnet_naming_rule.parameters_must_be_camel_case.symbols = parameters
dotnet_naming_rule.parameters_must_be_camel_case.style = camel_case
# Class, struct, enum and delegates must be PascalCase
dotnet_naming_rule.non_interface_types_must_be_pascal_case.severity = error
dotnet_naming_rule.non_interface_types_must_be_pascal_case.symbols = non_interface_types
dotnet_naming_rule.non_interface_types_must_be_pascal_case.style = pascal_case
# Interfaces must be PascalCase and start with an 'I'
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.severity = error
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.symbols = interface_types
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.style = prefix_interface_interface_with_i

# Async methods should have "Async" suffix
dotnet_naming_rule.async_methods_end_in_async.symbols = any_async_methods
dotnet_naming_rule.async_methods_end_in_async.style = end_in_async
dotnet_naming_rule.async_methods_end_in_async.severity = suggestion

dotnet_naming_symbols.any_async_methods.applicable_kinds = method
dotnet_naming_symbols.any_async_methods.applicable_accessibilities = *
dotnet_naming_symbols.any_async_methods.required_modifiers = async

dotnet_naming_style.end_in_async.required_prefix =
dotnet_naming_style.end_in_async.required_suffix = Async
dotnet_naming_style.end_in_async.capitalization = pascal_case
dotnet_naming_style.end_in_async.word_separator =

# IDE0005: Using directive is unnecessary.
dotnet_diagnostic.IDE0005.severity = warning

# IDE0010: Add missing cases to switch statement
dotnet_diagnostic.IDE0010.severity = error

# IDE0016: Use throw expression
dotnet_diagnostic.IDE0016.severity = error

# IDE0050: Convert anonymous type to tuple
dotnet_diagnostic.IDE0050.severity = none

# IDE0055: Fix formatting
dotnet_diagnostic.IDE0055.severity = error

# IDE0064: Make struct fields writable
dotnet_diagnostic.IDE0064.severity = error

# IDE0070: Use 'System.HashCode.Combine'
dotnet_diagnostic.IDE0070.severity = error

# IDE0072: Add missing cases to switch expression
dotnet_diagnostic.IDE0072.severity = error

# IDE0001: Simplify Names
dotnet_diagnostic.IDE0001.severity = warning

# IDE0082: Convert typeof to nameof
dotnet_diagnostic.IDE0082.severity = error

# IDE1006: Naming rule violation
dotnet_diagnostic.IDE1006.severity = error


# CA1002: Do not expose generic lists
dotnet_diagnostic.CA1002.severity = error

# CA1008: Enums should have zero value
dotnet_diagnostic.CA1008.severity = none

# CA1031: Do not catch general exception types
dotnet_diagnostic.CA1031.severity = suggestion

# CA1034: Nested types should not be visible
dotnet_diagnostic.CA1034.severity = error

# CA1051: Do not declare visible instance fields
dotnet_diagnostic.CA1051.severity = error

# CA1052: Static holder types should be Static or NotInheritable
dotnet_diagnostic.CA1052.severity = error

# CA1054: URI-like parameters should not be strings
dotnet_diagnostic.CA1054.severity = suggestion

# CA1062: Validate arguments of public methods
dotnet_diagnostic.CA1062.severity = suggestion

# CA1304: Specify CultureInfo
dotnet_diagnostic.CA1304.severity = error

# CA1305: Specify IFormatProvider
dotnet_diagnostic.CA1305.severity = error

# CA1307: Specify StringComparison for clarity
dotnet_diagnostic.CA1307.severity = error

# CA1308: Normalize strings to uppercase
dotnet_diagnostic.CA1308.severity = error

# CA1309: Use ordinal string comparison
dotnet_diagnostic.CA1309.severity = error

# CA1310: Specify StringComparison for correctness
dotnet_diagnostic.CA1310.severity = error

# CA1507: Use nameof to express symbol names
dotnet_diagnostic.CA1507.severity = error

# CA1508 Remove or refactor the condition(s) to avoid dead code
dotnet_diagnostic.CA1508.severity = error

# CA1716: Identifiers should not match keywords
dotnet_diagnostic.CA1716.severity = warning

# CA1717: Only FlagsAttribute enums should have plural names
dotnet_diagnostic.CA1717.severity = error

# CA1724: Type Names Should Not Match Namespaces
dotnet_diagnostic.CA1724.severity = error

# CA1725: Parameter names should match base declaration
dotnet_diagnostic.CA1725.severity = error

# CA1801: Review unused parameters
dotnet_diagnostic.CA1801.severity = error

# CA1802: Use literals where appropriate
dotnet_diagnostic.CA1802.severity = error

# CA1806: Do not ignore method results
dotnet_diagnostic.CA1806.severity = error

# CA1812: Avoid uninstantiated internal classes
dotnet_diagnostic.CA1812.severity = error

# CA1813: Avoid unsealed attributes
dotnet_diagnostic.CA1813.severity = error

# CA1815: Override equals and operator equals on value types
dotnet_diagnostic.CA1815.severity = error

# CA1819: Properties should not return arrays
dotnet_diagnostic.CA1819.severity = suggestion

# CA1820: Test for empty strings using string length
dotnet_diagnostic.CA1820.severity = error

# CA1822: Member does not access instance data and can be marked as static
dotnet_diagnostic.CA1822.severity = error

# CA1829: Use Length/Count property instead of Count() when available
dotnet_diagnostic.CA1829.severity = error

# CA2000: Call System.IDisposable.Dispose on object created by 'MethodName' before all references to it are out of scope
dotnet_diagnostic.CA2000.severity = error

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = none
# Not needed for projects targeting .net core. See https://itnext.io/a-deep-dive-into-configureawait-65f52b9605c2

# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = error

# CA2227: Collection properties should be read only
dotnet_diagnostic.CA2227.severity = error

# CA2234: Pass system uri objects instead of strings
dotnet_diagnostic.CA2234.severity = warning

# CA2237: Mark ISerializable types with serializable
dotnet_diagnostic.CA2237.severity = error

# CA5401: Symmetric encryption uses non-default initialization vector, which could be potentially repeatable
dotnet_diagnostic.CA5401.severity = error

# CS0114: Member hides inherited member; missing override keyword
dotnet_diagnostic.CS0114.severity = error

# CS0168: Variable is declared but never used
dotnet_diagnostic.CS0168.severity = warning

# CS0618: Type or member is obsolete
dotnet_diagnostic.CS0618.severity = error

# CS1998: Async method lacks 'await' operators and will run synchronously
dotnet_diagnostic.CS1998.severity = error





# StyleCop rules

# SA0001 XML comment analysis is disabled due to project configuration.
dotnet_diagnostic.SA0001.severity = none

# SA1003: Symbols should be spaced correctly
dotnet_diagnostic.SA1003.severity = error

# SA1005: Single line comments should begin with single space
dotnet_diagnostic.SA1005.severity = error

# SA1009: Closing parenthesis should be spaced correctly
dotnet_diagnostic.SA1009.severity = error

# SA1013: Closing braces should be spaced correctly
dotnet_diagnostic.SA1013.severity = error

# SA1025: Code should not contain multiple whitespace in a row
dotnet_diagnostic.SA1025.severity = error

# SA1028 'public' members should come before 'private' members.
dotnet_diagnostic.SA1028.severity = error

# SA1101: Prefix local calls with this - this is handled by dotnet_style_qualification_for_*
dotnet_diagnostic.SA1101.severity = silent

# SA1106: Code should not contain empty statements
dotnet_diagnostic.SA1106.severity = error

# SA1108: Block statements should not contain embedded comments
dotnet_diagnostic.SA1108.severity = suggestion

# SA1116: Split parameters should start on line after declaration
dotnet_diagnostic.SA1116.severity = error

# SA1117: Parameters should be on same line or separate lines
dotnet_diagnostic.SA1117.severity = error

# SA1118: Parameter should not span multiple lines
dotnet_diagnostic.SA1118.severity = warning

# SA1120: Comments should contain text
dotnet_diagnostic.SA1120.severity = error

# SA1122: Use string.Empty for empty strings
dotnet_diagnostic.SA1122.severity = warning

# SA1128: Put constructor initializers on their own line
dotnet_diagnostic.SA1128.severity = error

# SA1129: Do not use default value type constructor
dotnet_diagnostic.SA1129.severity = error

# SA1131: When a comparison is made between a variable and a literal, the variable should be placed on the left-hand-side to maximize readability.
dotnet_diagnostic.SA1131.severity = error

# SA1133 Each attribute should be placed in its own set of square brackets.
dotnet_diagnostic.SA1133.severity = error

# SA1135: Using directives should be qualified
dotnet_diagnostic.SA1135.severity = error

# SA1136: Enum values should be on separate lines
dotnet_diagnostic.SA1136.severity = error

# SA1137: Elements should have the same indentation
dotnet_diagnostic.SA1137.severity = warning

# SA1200: Using directives should be placed correctly
dotnet_diagnostic.SA1200.severity = error

# SA1201: Elements must appear in the correct order
dotnet_diagnostic.SA1201.severity = error

# SA1202 'public' members should come before 'private' members.
dotnet_diagnostic.SA1202.severity = error

# SA1203: Constants should appear before fields
dotnet_diagnostic.SA1203.severity = error

# SA1204 Static members should appear before non-static members.
dotnet_diagnostic.SA1204.severity = error

# SA1208 Using directive for 'System.*' should appear before directive for 'NonSystem.*'
dotnet_diagnostic.SA1208.severity = error

# SA1210: Using directives should be ordered alphabetically by namespace
dotnet_diagnostic.SA1210.severity = warning

# SA1212: Property accessors should follow order
dotnet_diagnostic.SA1212.severity = error

# SA1214: Readonly fields should appear before non-readonly fields
dotnet_diagnostic.SA1214.severity = warning

# SA1300 Element 'inRiver' should begin with an uppercase letter.
dotnet_diagnostic.SA1300.severity = suggestion

# SA1303: Const field names should begin with upper-case letter
dotnet_diagnostic.SA1303.severity = error

# SA1306: Field names should begin with lower-case letter
dotnet_diagnostic.SA1306.severity = error

# SA1307: Accessible fields should begin with upper-case letter
dotnet_diagnostic.SA1307.severity = error

# SA1309 Field should not begin with an underscore.
dotnet_diagnostic.SA1309.severity = error

# SA1312: Variable names should begin with lower-case letter
dotnet_diagnostic.SA1312.severity = error

# SA1401: Fields should be private
dotnet_diagnostic.SA1401.severity = error

# SA1402 File may only contain a single type.
dotnet_diagnostic.SA1402.severity = error

# SA1405:Debug.Assert should provide message text
dotnet_diagnostic.SA1405.severity = error

# SA1411 Attribute constructor should not use unnecessary parenthesis.
dotnet_diagnostic.SA1411.severity = error

# SA1413 Use trailing comma in multi-line initializers.
dotnet_diagnostic.SA1413.severity = none

# SA1500 Braces for multi-line statements should not share line.
dotnet_diagnostic.SA1500.severity = none

# SA1505 An opening brace should not be followed by a blank line.
dotnet_diagnostic.SA1505.severity = error

# SA1507 Code should not contain multiple blank lines in a row.
dotnet_diagnostic.SA1507.severity = error

# SA1508 A closing brace should not be preceded by a blank line.
dotnet_diagnostic.SA1508.severity = error

# SA1509:Opening braces should not be preceded by blank line
dotnet_diagnostic.SA1509.severity = error

# SA1513 Closing brace should be followed by blank line.
dotnet_diagnostic.SA1513.severity = error

# SA1515: Single-line comment should be preceded by blank line
dotnet_diagnostic.SA1515.severity = error

# SA1516 Elements should be separated by blank line.
dotnet_diagnostic.SA1516.severity = error

# SA1517:Code should not contain blank lines at start of file
dotnet_diagnostic.SA1517.severity = error

# SA1518:Use line endings correctly at end of file
dotnet_diagnostic.SA1518.severity = warning

# SA1629 Documentation text should end with a period
dotnet_diagnostic.SA1629.severity = error

# SA1600: Elements should be documented
dotnet_diagnostic.SA1600.severity = none

# SA1602: Enumeration items should be documented
dotnet_diagnostic.SA1602.severity = suggestion

# SA1633 The file header is missing or not located at the top of the file
dotnet_diagnostic.SA1633.severity = none

# SA1649: File name should match first type name
dotnet_diagnostic.SA1649.severity = error

# CA2100: Review SQL queries for security vulnerabilities
dotnet_diagnostic.CA2100.severity = warning
