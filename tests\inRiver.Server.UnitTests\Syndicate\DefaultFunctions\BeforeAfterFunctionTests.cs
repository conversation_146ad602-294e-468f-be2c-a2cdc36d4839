namespace inRiver.Server.UnitTests.Syndicate.DefaultFunctions
{
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using FluentAssertions;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Script;
    using inRiver.Server.Syndication.Script.DefaultFunctions;
    using Xunit;

    public class BeforeAfterFunctionTests
    {
        public static readonly object[][] BeforeAfterFunctionValues =
        {
            new object[] { DefaultFunctionsData.LSDictionary, "null", "\",\"", "\"en\"", "\"true\"", "\"before\",\"after\"", false, "before,en value,after" },
            new object[] { DefaultFunctionsData.LSDictionary, "null", "\",\"", "\"\"", "\"true\"", "\"before\",\"after\"", false, null },
            new object[] { DefaultFunctionsData.LSDictionary, "null", "\",\"", "\"en\"", "\"true\"", "\"\",\"after\"", false, ",en value,after" },
            new object[] { DefaultFunctionsData.LSDictionary, "null", "\",\"", "\"en\"", "\"true\"", "\"before\",\"\"", false, "before,en value," },
            new object[] { DefaultFunctionsData.LSDictionary, "null", "\",\"", "\"fr\"", "\"true\"", "\"before\",\"after\"", false, null },
            new object[] { "String value", "null", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before,String value,after" },
            new object[] { "String value", "null", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before,String value,after" },
            new object[] { "String value", "null", "\"\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "beforeString valueafter" },
            new object[] { "String value", "null", "\" \"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before String value after" },
            new object[] { "String value", "null", "\"\\t\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before	String value	after" },
            new object[] { "String value", "null", "\"\\n\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before\nString value\nafter" },
            new object[] { "2020-10-06 18:00:00", "null", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before,2020-10-06 18:00:00,after" },
            new object[] { "<xml><node>123</node></xml>", "null", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before,<xml><node>123</node></xml>,after" },
            new object[] { null, "null", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, null },
            new object[] { "black", "\"stringCVL\"", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before,Black,after" },
            new object[] { "blue", "\"stringCVL\"", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, null },
            new object[] { "black;blue", "\"stringCVL\"", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before,Black,after" },
            new object[] { "black", "\"localeStringCVL\"", "\",\"", "\"en\"", "\"true\"", "\"before\",\"after\"", false, "before,en black,after" },
            new object[] { "green", "\"localeStringCVL\"", "\",\"", "\"sv\"", "\"true\"", "\"before\",\"after\"", false, null },
            new object[] { "black;green", "\"localeStringCVL\"", "\",\"", "\"en\"", "\"true\"", "\"before\",\"after\"", false, "before,en black,en green,after" },
            new object[] { "black;green", "\"localeStringCVL\"", "\",\"", "\"fr\"", "\"true\"", "\"before\",\"after\"", false, null },
            new object[] { "black;green", "\"localeStringCVL\"", "\",\"", "\"sv\"", "\"true\"", "\"before\",\"after\"", false, "before,sv black,after" },
            new object[] { "black;green", "null", "\",\"", "\"sv\"", "\"false\"", "\"before\",\"after\"", false, "before,black;green,after" },
            new object[] { "1", "null", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", false, "before,1,after" },
            new object[] { "customEnumValue", "\"stringCVL\"", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", true, "before,customEnumValue,after" },
            new object[] { "customEnumValue1;customEnumValue2", "\"stringCVL\"", "\",\"", "\"\"", "\"false\"", "\"before\",\"after\"", true, "before,customEnumValue1,customEnumValue2,after" },
        };

        [Theory]
        [MemberData(nameof(BeforeAfterFunctionValues))]
        public void Execute_ShouldReturnCorrectValue(
            object mainValue,
            string cvlId,
            string separator,
            string language,
            string isLocaleString,
            string values,
            bool isEnum,
            string expectedResult)
        {
            // Arrange
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            var json = GetTransformationJson(cvlId, separator, language, isLocaleString, values);
            var transformationManager = new TransformationManager(json, context: null);

            var beforeAfterFunctionExecutor = new BeforeAfterFunctionExecutor(
                DefaultFunctionsData.RequestContext,
                transformationManager,
                mainValue,
                isEnum);

            // Act
            var result = beforeAfterFunctionExecutor.Execute();

            // Assert
            result.Should().Be(expectedResult);
        }

        private static string GetTransformationJson(string cvlId, string separator, string language, string isLocaleString, string values)
        {
            var args = $"[{cvlId},{separator},{language},{isLocaleString}]";
            return $"{{\"transformations\":[{{\"function\":{{\"name\":\"BeforeAfter\",\"args\":{args},\"values\":[{values}]}}}}]}}";
        }
    }
}
