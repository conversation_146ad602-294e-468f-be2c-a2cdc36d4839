namespace inRiver.Core.Enum
{
    public enum ApplyExpressionOverwriteMode
    {
        /// <summary>
        /// Overwrites all values.
        /// </summary>
        All,

        /// <summary>
        /// Only overwrites empty values.
        /// </summary>
        EmptyOnly,

        /// <summary>
        /// Only overwrites values that are expressions.
        /// </summary>
        ExpressionsOnly
    }
}
