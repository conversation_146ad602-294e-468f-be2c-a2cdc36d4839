using inRiver.Api.Data.Client;
using Inriver.StackEssentials.Abstractions;
using Inriver.StackEssentials.Config;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Services.AppAuthentication;
using Newtonsoft.Json.Linq;
using PWDTK_DOTNET45;
using System;

namespace inRiver.Core.Persistance.ThirdDataLayer
{
    public static class DataApiInitializer
    {
        private const string MASTERKEY = "1024bitMasterKey";
        private static byte[] internalJWTSecret;

        private static Lazy<string> lazySerializedInternalJWTSecret;
        public static string SerializedInternalJWTSecret => lazySerializedInternalJWTSecret?.Value;

        public static void Init(string dataApiUrl, string dataJobServiceUrl, string keyVaultBaseUrl, string stackConfigSecretName, string clientCloudRoleName, bool isLocalDev = false, bool preferFaultExceptions = false)
        {
            // Set up key vault client
            var azureServiceTokenProvider = isLocalDev ? new AzureServiceTokenProvider("RunAs=Developer; DeveloperTool=VisualStudio") : new AzureServiceTokenProvider();
            using (var keyVaultClient = new KeyVaultClient(new KeyVaultClient.AuthenticationCallback(azureServiceTokenProvider.KeyVaultTokenCallback)))
            {
                // Load stack config
                var stackConfigSecret = keyVaultClient.GetSecretAsync(keyVaultBaseUrl, stackConfigSecretName).GetAwaiter().GetResult();
                var stackConfig = JObject.Parse(stackConfigSecret.Value);

                // Create crypto secrets
                var masterKey = stackConfig.Value<string>(MASTERKEY);
                if (string.IsNullOrEmpty(masterKey))
                {
                    throw new InvalidOperationException("Master key not found.");
                }

                internalJWTSecret = CreateInternalJWTSecret(masterKey);
                lazySerializedInternalJWTSecret = new Lazy<string>(CreateSerializedInternalJWTSecret);

                InRiverDataApiClient.Init(dataApiUrl, dataJobServiceUrl, internalJWTSecret, StackConfig.Instance.RedisConnectionString.UnScramble(), clientCloudRoleName, preferFaultExceptions);
            }
        }

        public static void InitWithSerializedJWTSecret(string dataApiUrl, string dataJobServiceUrl, string serializedInternalJWTSecret, string clientCloudRoleName)
        {
            if (string.IsNullOrEmpty(dataApiUrl) || string.IsNullOrEmpty(serializedInternalJWTSecret))
            {
                throw new ArgumentException("Invalid parameters");
            }

            internalJWTSecret = Convert.FromBase64String(ScrambledString.FromSerializedScrambledString(serializedInternalJWTSecret).UnScramble());
            lazySerializedInternalJWTSecret = new Lazy<string>(() => serializedInternalJWTSecret);

            InRiverDataApiClient.Init(dataApiUrl, dataJobServiceUrl, internalJWTSecret, StackConfig.Instance.RedisConnectionString.UnScramble(), clientCloudRoleName);
        }

        private static string CreateSerializedInternalJWTSecret()
        {
            if (internalJWTSecret == null)
            {
                throw new InvalidOperationException($"{nameof(internalJWTSecret)} is not initialized");
            }

            return new ScrambledString(Convert.ToBase64String(internalJWTSecret)).ToSerializedScrambledString();
        }

        private static byte[] CreateInternalJWTSecret(string masterKey)
        {
            if (Convert.FromBase64String(masterKey).Length < 128)
            {
                throw new ArgumentException("Master key is too short");
            }

            var internalJWTSecretSalt = Convert.FromBase64String("6T4f5QK0da81E3IuMBbfedRWQju7uzrFZfdrfYM59BftVIvVnKw8S6frpIT6BQnVv4Y1x4O6iMr1PEhPDOtIwms5xgkZXyltZkkPfrJCiGMNMg5x/hZ25xqNdxADV27kUimQtRoz+K/00wYgz4calW2HUZqxx+7mmDAFGNX7mKM=");
            var hashed = new Rfc2898(Convert.FromBase64String(masterKey), internalJWTSecretSalt, 6001).GetDerivedKeyBytes_PBKDF2_HMACSHA512(128);

            return hashed;
        }
    }
}
