namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;
    using System.Data.SqlClient;
    using inRiver.Log;

    public class PersistanceEntityType : BasePersistance, IPersistanceEntityType
    {
        private IPersistanceFieldType _persistanceFieldType;
        private IPersistanceFieldSet _persistanceFieldSet;

        public PersistanceEntityType(
            string connectionString,
            ICommonLogging logInstance,
            IPersistanceFieldType persistanceFieldType,
            IPersistanceFieldSet persistanceFieldSet,
            IContentSegmentPermissionProvider contentSegmentProvider
        )

            : base(connectionString, logInstance, contentSegmentProvider)
        {
            _persistanceFieldType = persistanceFieldType;
            _persistanceFieldSet = persistanceFieldSet;
        }


        public List<EntityType> GetAllEntityTypes(bool loadFieldTypes = false, bool loadLinkTypes = false,
            bool loadFieldsets = false)
        {
            var types = new List<EntityType>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, IsLinkEntityType FROM EntityType";

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var entityType = new EntityType
                        {
                            Id = reader.GetString(0)
                        };
                        var xml = reader.GetSqlXml(1);

                        entityType.Name = new LocaleString(xml);

                        entityType.IsLinkEntityType = reader.GetBoolean(2);

                        types.Add(entityType);
                    }
                }
            }

            var fieldTypes = new Dictionary<string, List<FieldType>>();
            var linkTypes = new List<LinkType>();
            var fieldSets = new List<FieldSet>();

            if (loadFieldTypes)
            {
                fieldTypes = _persistanceFieldType.GetAllFieldTypesGrouped();
            }

            if (loadLinkTypes)
            {
                linkTypes = GetAllLinkTypes();
            }

            if (loadFieldsets)
            {
                fieldSets = _persistanceFieldSet.GetAllFieldSets(true);
            }

            foreach (var entityType in types)
            {
                entityType.FieldTypes = fieldTypes.ContainsKey(entityType.Id) ? fieldTypes[entityType.Id] : new List<FieldType>();
                entityType.LinkTypes = linkTypes.FindAll(lt => lt.SourceEntityTypeId.Equals(entityType.Id) || lt.TargetEntityTypeId.Equals(entityType.Id));
                entityType.FieldSets = fieldSets.FindAll(fs => fs.EntityTypeId.Equals(entityType.Id));
            }

            return types;
        }

        public EntityType GetEntityType(string id, bool loadFieldTypes = false, bool loadLinkTypes = false,
            bool loadFieldsets = false)
        {
            EntityType entityType = null;

            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, IsLinkEntityType FROM EntityType WHERE Id = @Id";
                command.Parameters.AddWithValue("@Id", id);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.HasRows)
                    {
                        reader.Read();

                        entityType = new EntityType { Id = reader.GetString(0) };

                        var xml = reader.GetSqlXml(1);

                        entityType.Name = new LocaleString(xml);
                        entityType.IsLinkEntityType = reader.GetBoolean(2);
                    }
                }
            }

            if (entityType == null)
            {
                return null;
            }

            if (loadFieldTypes)
            {
                entityType.FieldTypes = _persistanceFieldType.GetFieldTypesForEntityType(entityType.Id, true);
            }

            if (loadLinkTypes)
            {
                entityType.LinkTypes = GetLinkTypesForEntityType(entityType.Id);
            }

            if (loadFieldsets)
            {
                entityType.FieldSets = _persistanceFieldSet.GetFieldSetsForEntityType(entityType.Id, true);
            }

            return entityType;
        }

        private List<LinkType> GetAllLinkTypes()
        {
            var linkTypes = new List<LinkType>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText =
                    "SELECT Id, SourceName, SourceEntityTypeId, TargetName, TargetEntityTypeId, LinkEntityTypeId, [Index] FROM LinkType";

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var linkType = new LinkType { Id = reader.GetString(0) };

                        var xmlSourceName = reader.GetSqlXml(1);
                        linkType.SourceName = new LocaleString(xmlSourceName);

                        linkType.SourceEntityTypeId = reader.GetString(2);

                        var xmlTargetName = reader.GetSqlXml(3);
                        linkType.TargetName = new LocaleString(xmlTargetName);

                        linkType.TargetEntityTypeId = reader.GetString(4);

                        if (!reader.IsDBNull(5))
                        {
                            linkType.LinkEntityTypeId = reader.GetString(5);
                        }

                        linkType.Index = reader.GetInt32(6);

                        linkTypes.Add(linkType);
                    }
                }
            }

            return linkTypes;
        }

        private List<LinkType> GetLinkTypesForEntityType(string entityTypeId)
        {
            var linkTypes = new List<LinkType>();

            if (string.IsNullOrWhiteSpace(entityTypeId))
            {
                return linkTypes;
            }

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText =
                    "SELECT Id, SourceName, SourceEntityTypeId, TargetName, TargetEntityTypeId, LinkEntityTypeId, [Index] FROM LinkType WHERE ((SourceEntityTypeId = @EntityTypeId) OR (TargetEntityTypeId = @EntityTypeId))";
                command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var linkType = new LinkType { Id = reader.GetString(0) };

                        var xmlSourceName = reader.GetSqlXml(1);
                        linkType.SourceName = new LocaleString(xmlSourceName);

                        linkType.SourceEntityTypeId = reader.GetString(2);

                        var xmlTargetName = reader.GetSqlXml(3);
                        linkType.TargetName = new LocaleString(xmlTargetName);

                        linkType.TargetEntityTypeId = reader.GetString(4);

                        if (!reader.IsDBNull(5))
                        {
                            linkType.LinkEntityTypeId = reader.GetString(5);
                        }

                        linkType.Index = reader.GetInt32(6);

                        linkTypes.Add(linkType);
                    }
                }
            }

            return linkTypes;
        }
    }
}
