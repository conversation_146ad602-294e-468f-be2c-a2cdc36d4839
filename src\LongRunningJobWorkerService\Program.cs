namespace LongRunningJobWorkerService
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Fabric;
    using System.Net.Http;
    using System.Threading;
    using global::LongRunningJobWorkerService.DependencyInjection;
    using inRiver.Api.Data.Client;
    using inRiver.Configuration.Core.Service;
    using inRiver.Core.Http;
    using inriver.Expressions.Client;
    using inRiver.Core.Persistance.ThirdDataLayer;
    using inRiver.Core.Services;
    using inRiver.Server.Configuration;
    using inRiver.Server.EventPublishing;
    using inRiver.Server.Service;
    using Inriver.StackEssentials;
    using Inriver.StackEssentials.Config;
    using Inriver.StackEssentials.DependencyInjection;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Models;
    using LongRunningJob.Core.Repositories;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;
    using Microsoft.Extensions.Options;
    using Microsoft.ServiceFabric.AspNetCore.Configuration;
    using Microsoft.ServiceFabric.Services.Runtime;
    using Serilog;
    using Serilog.Core;
    using Serilog.Events;
    using Microsoft.Extensions.Caching.Memory;
    using inRiver.Core.Models;

    internal static class Program
    {
        /// <summary>
        /// This is the entry point of the service host process.
        /// </summary>
        private static void Main()
        {
            try
            {
                // The ServiceManifest.XML file defines one or more service type names.
                // Registering a service maps a service type name to a .NET type.
                // When Service Fabric creates an instance of this service type,
                // an instance of the class is created in this host process.
                ServiceRuntime.RegisterServiceAsync(
                    "LongRunningJobWorkerServiceType",
                    ServiceFactory)
                    .GetAwaiter().GetResult();

                ServiceEventSource.Current.ServiceTypeRegistered(Process.GetCurrentProcess().Id, nameof(LongRunningJobWorkerService));

                // Prevents this host process from terminating so services keep running.
                Thread.Sleep(Timeout.Infinite);
            }
            catch (Exception e)
            {
                ServiceEventSource.Current.ServiceHostInitializationFailed(e.ToString());
                throw;
            }
        }

        private static LongRunningJobWorkerService ServiceFactory(StatelessServiceContext context)
        {
            var jobContext = new JobContext(context.ServiceName);
            var defaultConfigSection = GetDefaultConfigSection();

            var environmentContextAccessor = CreateEnvironmentContextAccessor(jobContext, defaultConfigSection);

            // There are still parts of the code that relies on the StackConfig.Instance being initialized.
            StackEssentialsInitializer.Init(defaultConfigSection["KeyVaultBaseUrl"], defaultConfigSection["StackConfigSecretName"]);

            ConfigureStaticConfigurationProvider(defaultConfigSection);

            var services = new ServiceCollection();

            services.AddStackConfig(defaultConfigSection);

            services.AddApplicationInsightsServices(defaultConfigSection);

            services.AddApplicationOptions(defaultConfigSection);

            services.AddRepositories();
            services.AddApplicationServices();

            services.AddSingleton<IEnvironmentContextAccessor>(environmentContextAccessor);

            services.AddResiliencePolicies();

            services.AddCachingDecorators();
            services.AddResilienceDecorators();

            var keyVaultBaseUrl = defaultConfigSection["KeyVaultBaseUrl"];

            var namedAuth0Options = new Dictionary<string, Auth0Options>();
            var auth0Options = new Auth0Options
            {
                BaseAddress = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-domain").GetAwaiter().GetResult(),
                ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-id").GetAwaiter().GetResult(),
                ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-secret").GetAwaiter().GetResult(),
                Audience = defaultConfigSection["OAuth:Audience"],
            };
            namedAuth0Options.Add("Auth0Client", auth0Options);

            try
            {
                var outputAdapterAuth0Options = new Auth0Options
                {
                    BaseAddress = defaultConfigSection["Auth0Domain"],
                    ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-id").GetAwaiter().GetResult(),
                    ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-secret").GetAwaiter().GetResult(),
                    Audience = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-audience").GetAwaiter().GetResult(),
                };
                namedAuth0Options.Add("OutputAdapterAuth0Client", outputAdapterAuth0Options);
            }
            catch
            {
            }

            services.AddSingleton<IDictionary<string, Auth0Options>>(namedAuth0Options);

            services.TryAddTransient<BearerTokenHandler>();

            services.AddTransient<IAccessTokenRetriever, AccessTokenRetriever>();

            _ = services.AddHttpClient<IAugmentaHttpClient, AugmentaHttpClient>(c =>
                    c.WithBaseAddress(defaultConfigSection["Augmenta:ApiBaseAddress"])
                        .WithAcceptJsonHeader()
                        .WithTimeout(TimeSpan.FromMinutes(5)))
                .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

            _ = services.AddHttpClient<IOutputAdapterHttpClient, OutputAdapterHttpClient>(c =>
                    c.WithBaseAddress(defaultConfigSection["OutputAdapter:ApiBaseAddress"])
                        .WithAcceptJsonHeader()
                        .WithTimeout(TimeSpan.FromMinutes(5)))
                .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

            _ = services.AddMemoryCache();

            _ = services.AddHttpClient("Auth0Client", (provider, client) => {
                var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["Auth0Client"].BaseAddress;
                client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
            });

            _ = services.AddHttpClient("OutputAdapterAuth0Client", (provider, client) => {
                var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["OutputAdapterAuth0Client"].BaseAddress;
                client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
            });

            var serviceProvider = services.BuildServiceProvider();

            ConfigureSerilog(defaultConfigSection, serviceProvider);

            StaticServiceProvider.Configure(serviceProvider);

            ConfigureDataApiService(defaultConfigSection);
            ConfigureExpressionClientService(defaultConfigSection);
            ConfigureMessagingService(defaultConfigSection);

            return new LongRunningJobWorkerService(
                context,
                serviceProvider.GetService<ILongRunningJobRepository>(),
                serviceProvider.GetService<TelemetryClient>(),
                serviceProvider.GetService<IJobRunner>(),
                jobContext);
        }

        private static void ConfigureStaticConfigurationProvider(IConfiguration configurationSection)
        {
            // Used by the legacy project inRiver.Server which is referenced by this application
            StaticConfigurationProvider.ChannelServiceUrl = configurationSection["ChannelServiceUrl"];
            StaticConfigurationProvider.ConnectServiceUrl = configurationSection["ConnectServiceUrl"];
        }

        private static IConfigurationSection GetDefaultConfigSection()
        {
            var configurationBuilder = new ConfigurationBuilder()
                .AddServiceFabricConfiguration(FabricRuntime.GetActivationContext(), (options) => options.IncludePackageName = false);
            var configuration = configurationBuilder.Build();
            var defaultConfigSection = configuration.GetSection("Configuration");
            return defaultConfigSection;
        }

        private static void ConfigureSerilog(IConfiguration configuration, IServiceProvider serviceProvider)
        {
            var loggingLevelSwitch = new LoggingLevelSwitch(Enum.TryParse(configuration["LogLevel"], out LogEventLevel level) ? level : LogEventLevel.Information);

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.ControlledBy(loggingLevelSwitch)
                .WriteTo.ApplicationInsights(serviceProvider.GetRequiredService<TelemetryConfiguration>(), TelemetryConverter.Traces)
                .Enrich.FromLogContext()
                .CreateLogger();
        }

        private static void ConfigureDataApiService(IConfiguration configurationSection) =>
            DataApiInitializer.Init(configurationSection["DataApiUrl"], configurationSection["DataJobServiceUrl"], configurationSection["KeyVaultBaseUrl"], configurationSection["StackConfigSecretName"], Constants.LogConstants.CloudRoleName);

        private static void ConfigureExpressionClientService(IConfiguration configurationSection) => ExpressionClient.Init(configurationSection["ExpressionWorkerServiceUrl"], Constants.LogConstants.CloudRoleName);

        private static void ConfigureMessagingService(IConfiguration configurationSection) =>
            MessagingServiceClient.Init(configurationSection["MessagingServiceUrl"]);

        private static EnvironmentContextAccessor CreateEnvironmentContextAccessor(JobContext jobContext, IConfiguration defaultConfigSection)
        {
            var stackConfigOptions = new StackConfigOptions
            {
                KeyVaultBaseUrl = defaultConfigSection["KeyVaultBaseUrl"],
                StackConfigSecretName = defaultConfigSection["StackConfigSecretName"]
            };

            var stackConfig = new StackConfig(new OptionsWrapper<StackConfigOptions>(stackConfigOptions));

            var customerEnvironmentRepository = new CustomerEnvironmentRepository(stackConfig);

            var environmentContextData = customerEnvironmentRepository.GetAsync(jobContext.CustomerSafename, jobContext.EnvironmentSafename)
                .GetAwaiter().GetResult();

            var environmentContextAccessor = new EnvironmentContextAccessor
            {
                EnvironmentContext = new EnvironmentContext(
                    environmentContextData.EnvironmentId,
                    environmentContextData.CustomerSafeName,
                    environmentContextData.EnvironmentSafeName,
                    environmentContextData.ConnectionString,
                    environmentContextData.StorageAccountConnectionString)
            };
            return environmentContextAccessor;
        }
    }
}
