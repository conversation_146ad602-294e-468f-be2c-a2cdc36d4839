﻿
namespace inRiver.Server.Completeness
{
    /// <summary>
    /// 
    /// </summary>
    public class CompletenessState
    {
        /// <summary>
        /// 
        /// </summary>
        public int EntityId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int DefinitionId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? GroupId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? RuleId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool Complete { get; set; }
    }
}
