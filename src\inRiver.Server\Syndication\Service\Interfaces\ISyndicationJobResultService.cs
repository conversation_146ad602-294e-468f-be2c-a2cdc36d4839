namespace inRiver.Server.Syndication.Service.Interfaces
{
    using System.Collections.Generic;
    using System.Diagnostics;
    using Core.Enum;
    using inRiver.Server.Syndication.Mapping;

    public interface ISyndicationJobResultService
    {
        IDictionary<string, string> GetResultMetrics(Stopwatch stopWatch);

        void UpdateNumberOfFunctions(int batchNumberOfEntities, MapContainer mapContainer);

        void SetNumberOfEntities(int newNumberOfEntities);

        void SetActorMethodStateAndJobStatus(ActorMethodState state, string newStatus);

        ActorMethodState GetActorMethodState();
        
        string GetStatusMetric();

        int GetNumberOfEntitiesMetric();
    }
}
