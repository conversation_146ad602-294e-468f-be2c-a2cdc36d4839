[*.cs]

# CA1707: Identifiers should not contain underscores
# Unit test methods should have underscore in the name
dotnet_diagnostic.CA1707.severity = none

# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = none

# IDE1006: Naming Styles
# It doesn't make sense to have Async suffix in unit test
dotnet_diagnostic.IDE1006.severity = none


############################################################
# SonarAnalyser rules #
############################################################

# async/await rules:

# 3433: Test method signatures should be correct
dotnet_diagnostic.3433.severity = error


############################################################
# Microsoft.VisualStudio.Threading.Analyzers rules #
############################################################

# VSTHRD200: Use "Async" suffix for async methods
dotnet_diagnostic.VSTHRD200.severity = none

# S3236: Caller information arguments should not be provided explicitly
dotnet_diagnostic.S3236.severity = none

# IDE0022: Use expression body for methods
csharp_style_expression_bodied_methods = when_on_single_line:none

