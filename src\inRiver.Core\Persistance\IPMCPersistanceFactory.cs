namespace inRiver.Core.Persistance
{
    using inRiver.Core.Models;
    using inRiver.Core.Persistance.ThirdDataLayer;
    using Telemetry.Logging;

    public static class IPMCPersistanceFactory
    {
        public static IinRiverPersistance GetInstance(string connectionString, ApiCaller caller, int entityModel, int environmentId)
        {
            inRiverPersistance defaultPersistance = new inRiverPersistance(connectionString, SerilogCommonLogger.Instance, caller, environmentId, true);

            IinRiverPersistance persistance = defaultPersistance;

            if (entityModel == 1)
            {
                persistance = new IPMCPersistanceAdaptor(defaultPersistance, connectionString, SerilogCommonLogger.Instance);
            }
            else if (entityModel == 2)
            {
                persistance = new iPMC3DLPersistanceAdapter(defaultPersistance, connectionString, SerilogCommonLogger.Instance, caller.Username);
            }

            return persistance;
        }
    }
}
