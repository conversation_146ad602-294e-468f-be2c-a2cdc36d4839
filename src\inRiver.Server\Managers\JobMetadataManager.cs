namespace inRiver.Server.Managers
{
    using System.Collections.Generic;
    using inRiver.Server.Managers.Interfaces;

    public class JobMetadataManager : IJobMetadataManager
    {
        private readonly IDictionary<string, object> metadata;

        public JobMetadataManager()
        {
            this.metadata = new Dictionary<string, object>();
        }

        public bool TryAdd(string key, object value) => this.metadata.TryAdd(key, value);

        public void Update(string key, object newValue)
        {
            if (this.metadata.TryGetValue(key, out var messageValue))
            {
                this.metadata[key] = messageValue + newValue?.ToString();
            }
            else
            {
                this.metadata.Add(key, newValue);
            }
        }

        public IDictionary<string, object> GetMetadata() => this.metadata;
    }
}
