namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Text.Json;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models;
    using inRiver.Api.Data.Client;
    using inRiver.Core.Http;
    using FakeItEasy;
    using inRiver.Log;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Enums;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Microsoft.Extensions.DependencyInjection;
    using inRiver.Server.Service;
    using Xunit;
    using DocumentFormat.OpenXml.Spreadsheet;

    public class ExportManagerTests
    {
        private const int MappingId = 23;

        private const int MappingFormatId = 1;

        private const string SourceEntityTypeId = "2";

        private const string TargetEntityTypeId = "3";

        private static readonly List<CultureInfo> Languages = new List<CultureInfo> { new CultureInfo("en-US") };

        public static ExportManagerTests Instance { get; } = new ExportManagerTests();

        private static RequestContext context
        {
            get {
                var persistance = new Fake<IDataPersistance>();
                persistance.CallsTo(x => x.GetMappingDetails(MappingId))
                    .Returns(FakePersistance.GetSyndicationMapping(MappingId));
                persistance.CallsTo(x => x.GetMapFormat(MappingFormatId))
                    .Returns(FakePersistance.GetMapType());
                persistance.CallsTo(x => x.GetLinkTypes(SourceEntityTypeId, TargetEntityTypeId))
                    .Returns(FakePersistance.GetLinkTypes());

                return new RequestContext
                {
                    DataPersistance = persistance.FakedObject,
                    Logging = new Fake<ICommonLogging>().FakedObject
                };
            }
        }

        private static List<ExportContainer> ExportContainers
        {
            get {
                var syndicationModel = new SyndicationModel
                {
                    IsResourceExportEnabled = false,
                    EnableSKU = false
                };
                var resourceExportService = A.Fake<IResourceExportService>();
                var jobMetadataManager = A.Fake<IJobMetadataManager>();
                var mapManager = new MapManager(context.DataPersistance, MappingId, resourceExportService, SyndicationMappingSource.Extension, "devenv");
                mapManager.Load();
                var exportManager = new ExportManager(mapManager.MapContainer, context, syndicationModel, resourceExportService, jobMetadataManager);
                var inRiverItems = FakePersistance.CreateFakeEntities("Item").ToList();

                return exportManager.GetExportContainers(entities: inRiverItems, languages: Languages, CancellationToken.None);
            }
        }

        private static List<ExportContainer> ExportContainersWithOutputAdapterMappingSource
        {
            get {
                var syndicationModel = new SyndicationModel
                {
                    IsResourceExportEnabled = false,
                    EnableSKU = false
                };
                var resourceExportService = A.Fake<IResourceExportService>();
                var jobMetadataManager = A.Fake<IJobMetadataManager>();
                var mapManager = new MapManager(context.DataPersistance, MappingId, resourceExportService, SyndicationMappingSource.OutputAdapter, "devenv");
                mapManager.Load();
                var exportManager = new ExportManager(mapManager.MapContainer, context, syndicationModel, resourceExportService, jobMetadataManager);
                var inRiverItems = FakePersistance.CreateFakeEntities("Item").ToList();

                return exportManager.GetExportContainers(entities: inRiverItems, languages: Languages, CancellationToken.None);
            }
        }

        [Fact]
        public void TestBasic()
        {
            Setup();

            var items = ExportContainers;

            Assert.Equal(3, items.Count);
            Assert.Equal(10, items[0].Fields.Count);

            Assert.Equal("id", items[0].Fields[0].Id);
            Assert.Equal("1", items[0].Fields[0].Data);
            Assert.Equal("size", items[0].Fields[2].Id);
            Assert.Equal("M", items[0].Fields[2].Data);

            Assert.Equal("id", items[1].Fields[0].Id);
            Assert.Equal("2", items[1].Fields[0].Data);
            Assert.Equal("size", items[1].Fields[2].Id);
            Assert.Equal("XL", items[1].Fields[2].Data);

            Assert.Equal("id", items[2].Fields[0].Id);
            Assert.Equal("3", items[2].Fields[0].Data);
            Assert.Equal("size", items[2].Fields[2].Id);
            Assert.Equal("L", items[2].Fields[2].Data);
        }

        [Fact]
        public void TestToUpper()
        {
            Setup();
            var items = ExportContainers;

            Assert.Equal("color", items[0].Fields[1].Id);
            Assert.Equal("BLUE", items[0].Fields[1].Data);

            Assert.Equal("title", items[0].Fields[4].Id);
            Assert.Equal("SKJORTA", items[0].Fields[4].Data);
        }

        [Fact]
        public void TestConcatenate()
        {
            Setup();
            var items = ExportContainers;

            Assert.Equal("description", items[0].Fields[3].Id);
            Assert.Equal("Skjorta Många fina färger", items[0].Fields[3].Data);
        }

        [Fact]
        public void TestConcatenateProductItem()
        {
            Setup();
            var items = ExportContainers;

            Assert.Equal("description", items[0].Fields[3].Id);
            Assert.Equal("Skjorta Många fina färger", items[0].Fields[3].Data);
        }

        [Fact]
        public void TestConstant()
        {
            Setup();
            var items = ExportContainers;

            Assert.Equal("color", items[0].Fields[5].Id);
            Assert.Equal("Purple", items[0].Fields[5].Data);
        }

        [Fact]
        public void TestEnum()
        {
            Setup();
            var items = ExportContainers;

            Assert.Equal("gender", items[0].Fields[6].Id);
            Assert.Equal("male", items[0].Fields[6].Data);

            Assert.Equal("gender", items[1].Fields[6].Id);
            Assert.Equal("female", items[1].Fields[6].Data);

            Assert.Equal("gender", items[2].Fields[6].Id);
            Assert.Equal("unisex", items[2].Fields[6].Data);
        }

        [Fact]
        public void TestUnitOfMeasure()
        {
            Setup();
            var items = ExportContainers;

            Assert.Equal("size", items[0].Fields[2].Id);
            Assert.Equal("unitOfMeasure", items[0].Fields[2].UnitType);
            Assert.Equal("mm", items[0].Fields[2].UnitValue);
        }

        [Fact]
        public void TestUnitDefaultValue()
        {
            Setup();
            var syndicationModel = new SyndicationModel
            {
                IsResourceExportEnabled = false,
                EnableSKU = false
            };
            var resourceExportService = A.Fake<IResourceExportService>();
            var jobMetadataManager = A.Fake<IJobMetadataManager>();
            var mapManager = new MapManager(context.DataPersistance, MappingId, resourceExportService, SyndicationMappingSource.Extension, "devenv");
            mapManager.Load();

            var exportManager = new ExportManager(mapManager.MapContainer, context, syndicationModel, resourceExportService, jobMetadataManager);
            var inRiverItems = FakePersistance.CreateFakeEntities("Item").ToList();
            var exportContainers = exportManager.GetExportContainers(entities: inRiverItems, languages: Languages, CancellationToken.None);

            Assert.Equal("mm", exportContainers[0].Fields[2].UnitValue);

            // make sure default value is used
            mapManager.MapContainer.MapFields[2].UnitValue = null;
            exportContainers = exportManager.GetExportContainers(entities: inRiverItems, languages: Languages, CancellationToken.None);

            Assert.Equal("cm", exportContainers[0].Fields[2].UnitValue);
        }

        [Fact]
        public void TestBasicWithOutputAdapterMappingSource()
        {
            Setup();
            var items = ExportContainersWithOutputAdapterMappingSource;

            Assert.Equal(3, items.Count);
            Assert.Equal(10, items[0].Fields.Count);

            Assert.Equal("id", items[0].Fields[0].Id);
            Assert.Equal("1", items[0].Fields[0].Data);
            Assert.Equal("size", items[0].Fields[2].Id);
            Assert.Equal("M", items[0].Fields[2].Data);

            Assert.Equal("id", items[1].Fields[0].Id);
            Assert.Equal("2", items[1].Fields[0].Data);
            Assert.Equal("size", items[1].Fields[2].Id);
            Assert.Equal("XL", items[1].Fields[2].Data);

            Assert.Equal("id", items[2].Fields[0].Id);
            Assert.Equal("3", items[2].Fields[0].Data);
            Assert.Equal("size", items[2].Fields[2].Id);
            Assert.Equal("L", items[2].Fields[2].Data);
        }

        private static void Setup()
        {
            var services = new ServiceCollection();

            var outputAdapterHttpClient = new Fake<IOutputAdapterHttpClient>();
            outputAdapterHttpClient.CallsTo(x => x.GetMapping("devenv", MappingId, default))
                .Returns(new MappingDto
                { Data = File.ReadAllText("TestFiles/ConversionHelperTests/mappingDtoData.json") });

            services.AddSingleton(outputAdapterHttpClient.FakedObject);

            StaticServiceProvider.Configure(services.BuildServiceProvider());
        }
    }
}
