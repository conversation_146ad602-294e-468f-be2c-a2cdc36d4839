﻿namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Core.Models.inRiver;

    /// <summary>
    /// FieldSet overrides.
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        public override FieldSet AddFieldSet(FieldSet fieldSet)
        {
            var result = _origInRiverPersistance.AddFieldSet(fieldSet);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override FieldSet UpdateFieldSet(FieldSet fieldSet)
        {
            var result = _origInRiverPersistance.UpdateFieldSet(fieldSet);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool DeleteFieldSet(string id)
        {
            var result = _origInRiverPersistance.DeleteFieldSet(id);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool DeleteAllFieldSets()
        {
            var result = _origInRiverPersistance.DeleteAllFieldSets();
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool DeleteFieldTypeFromFieldSet(string fieldSetId, string fieldTypeId)
        {
            var result = _origInRiverPersistance.DeleteFieldTypeFromFieldSet(fieldSetId, fieldTypeId);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool AddFieldTypeToFieldSet(string fieldSetId, string fieldTypeId)
        {
            var result = _origInRiverPersistance.AddFieldTypeToFieldSet(fieldSetId, fieldTypeId);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }
    }
}
