namespace inRiver.Server.DataAccess
{
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.iPMC.Persistance;
    using inRiver.Server.Request;

    public class ContentSegmentPermissionProvider : IContentSegmentPermissionProvider
    {
        private RequestContext _context;

        public ContentSegmentPermissionProvider(RequestContext context)
        {
            _context = context;
        }

        // implementation of method of IContentSegmentPermissionProvider
        public List<int> GetPermittedSegmentIds()
        {
            List<int> segmentIds = new List<int>();

            if (_context != null && _context.ContentSegmentIdToPermissions != null && _context.ContentSegmentIdToPermissions.Any())
            {
                segmentIds.AddRange(_context.ContentSegmentIdToPermissions.Keys.ToList());
            }

            return segmentIds;
        }
    }
}
