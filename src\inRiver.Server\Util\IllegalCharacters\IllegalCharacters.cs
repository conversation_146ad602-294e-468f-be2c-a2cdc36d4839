namespace inRiver.Server.Util.IllegalCharacters
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Error;

    internal class IllegalCharacters
    {
        #region An ASCII hex codes of Category "Control"

        public static readonly List<char> ControlCharacters = new List<char>()
        {
            '\u0000', // Null (NUL)
            '\u0001', // Start of Heading (SOH)
            '\u0002', // Start of Text (SOT)
            '\u0003', // End of Text (ETX)
            '\u0004', // End of Transmission (EOT)
            '\u0005', // Enquiry (ENQ)
            '\u0007', // Alert (BEL)
            '\u0008', // Backspace (BS)
            '\u0009', // Character Tabulation (HT, TAB)
            '\u000B', // Line Tabulation (VT)
            '\u000C', // Form Feed (FF)
            '\u000D', // Carriage Return (CR)
            '\u000E', // Locking-Shift One (SO)
            '\u000F', // Locking-Shift Zero (Sl)
            '\u0010', // Data Link Escape (DLE)
            '\u0011', // Device Control One (DC1)
            '\u0012', // Device Control Two (DC2)
            '\u0013', // Device Control Three (DC3)
            '\u0014', // Device Control Four (DC4)
            '\u0015', // Negative Acknowledge (NAK)
            '\u0016', // Synchronous Idle (SYN)
            '\u0017', // End of Transmission Block (ETB)
            '\u0018', // Cancel (CAN)
            '\u0019', // End of Medium (EOM)
            '\u001A', // Substitute (SUB)
            '\u001B', // Escape (ESC)
            '\u001C', // File Separator (FS)
            '\u001D', // Group Separator (GS)
            '\u001E', // Information Separator Two (RS)
            '\u001F', // Information Separator One (US)
            '\u007F', // Delete (DEL)
            '\u0080', // Padding Character (PAD)
            '\u0081', // High Octet Preset (HOP)
            '\u0082', // Break Permitted Here (BPH)
            '\u0083', // No Break Here (NBH)
            '\u0084', // Index (IND)
            '\u0085', // Next Line (NEL)
            '\u0086', // Start of Selected Area (SSA)
            '\u0088', // Character Tabulation Set (HTS)
            '\u0089', // Character Tabulation with Justification (HTJ)
            '\u008A', // Line Tabulation Set (VTS)
            '\u008B', // Partial Line Down (PLD)
            '\u008C', // Partial Line Backward (PLU)
            '\u008D', // Reverse Index (RI)
            '\u008E', // Single Shift Two (SS2)
            '\u008F', // Single Shift Three (SS3)
            '\u0090', // Device Control String (DCS)
            '\u0091', // Private Use One (PU1)
            '\u0092', // Private Use Two (PU2)
            '\u0093', // Set Transmit State (STS)
            '\u0094', // Cancel Character (CCH)
            '\u0095', // Message Waiting (MW)
            '\u0096', // Start of Guarded Area (SPA)
            '\u0097', // End of Guarded Area (EPA)
            '\u0098', // Start of String (SOS)
            '\u0099', // Single Graphic Character Introducer (SGC)
            '\u009A', // Single Character Introducer (SCI)
            '\u009B', // Control Sequence Introducer (CSI)
            '\u009C', // String Terminator (ST)
            '\u009D', // Operating System Command (OSC)
            '\u009E', // Privacy Message (PM)
            '\u009F', // Application Program Command (APC)
        };

        #endregion

        internal static void CheckField(Field field, string method)
        {
            string foundIllegalCharacter = string.Empty;

            if (field?.Data == null)
            {
                return;
            }

            if (field.FieldType.DataType != DataType.String && field.FieldType.DataType != DataType.LocaleString)
            {
                return;
            }

            if (field.FieldType.DataType == DataType.String)
            {
                if (CheckForControlCharacters(field.Data.ToString(), ref foundIllegalCharacter))
                {
                    ThrowIllegalCharacters(typeof(Field), method, $"[{field.FieldType.CategoryId}] [{field.FieldType.Id}]", foundIllegalCharacter);
                }
            }
            else if (field.FieldType.DataType == DataType.LocaleString)
            {
                if (field.FieldType.Settings != null && field.FieldType.Settings.ContainsKey("AdvancedTextObject") && field.FieldType.Settings["AdvancedTextObject"] == "1")
                {
                    return;
                }

                LocaleString ls = ((LocaleString)field.Data);

                if (LocaleString.IsNullOrEmpty(ls))
                {
                    return;
                }

                foreach (CultureInfo ci in ls.Languages)
                {
                    if (string.IsNullOrEmpty(ls[ci]))
                    {
                        continue;
                    }

                    if (CheckForControlCharacters(ls[ci], ref foundIllegalCharacter))
                    {
                        ThrowIllegalCharacters(typeof(Field), method, $"[{field.FieldType.CategoryId}] [{field.FieldType.Id}] [{ci.EnglishName}]", foundIllegalCharacter);
                    }
                }
            }
        }

        internal static bool CheckForControlCharacters(string stringToCheck, ref string foundIllegalCharacter)
        {
            if (string.IsNullOrEmpty(stringToCheck))
            {
                return false;
            }

            foundIllegalCharacter = string.Empty;

            foreach (var c in stringToCheck)
            {
                // Only check first 31 ASCIIs [00 to 1F].
                if (c < '\u0020')
                {
                    switch (c)
                    {
                        case '\u0000':
                            {
                                foundIllegalCharacter = "[ASCII: 0] [NULL]";
                                break;
                            }
                        case '\u0001':
                            {
                                foundIllegalCharacter = "[ASCII: 1] [Start of Heading]";
                                break;
                            }
                        case '\u0002':
                            {
                                foundIllegalCharacter = "[ASCII: 2] [Start of Text]";
                                break;
                            }
                        case '\u0003':
                            {
                                foundIllegalCharacter = "[ASCII: 3] [End of Text]";
                                break;
                            }
                        case '\u0004':
                            {
                                foundIllegalCharacter = "[ASCII: 4] [End of Transmission]";
                                break;
                            }
                        case '\u0005':
                            {
                                foundIllegalCharacter = "[ASCII: 5] [Enquiry]";
                                break;
                            }
                        case '\u0006':
                            {
                                foundIllegalCharacter = "[ASCII: 6] [Acknowledge]";
                                break;
                            }
                        case '\u0007':
                            {
                                foundIllegalCharacter = "[ASCII: 7] [Bell]";
                                break;
                            }
                        case '\u0008':
                            {
                                foundIllegalCharacter = "[ASCII: 8] [Backspace]";
                                break;
                            }
                        case '\u0009':
                            {
                                foundIllegalCharacter = "[ASCII: 9] [Horizontal Tabulation]";
                                break;
                            }
                        case '\u000B':
                            {
                                foundIllegalCharacter = "[ASCII: 11] [Vertical Tabulation]";
                                break;
                            }
                        case '\u000C':
                            {
                                foundIllegalCharacter = "[ASCII: 12] [Form Feed]";
                                break;
                            }
                        case '\u000E':
                            {
                                foundIllegalCharacter = "[ASCII: 14] [Shift Out]";
                                break;
                            }
                        case '\u000F':
                            {
                                foundIllegalCharacter = "[ASCII: 15] [Shift In]";
                                break;
                            }
                        case '\u0010':
                            {
                                foundIllegalCharacter = "[ASCII: 16] [Data Link Escape]";
                                break;
                            }
                        case '\u0011':
                            {
                                foundIllegalCharacter = "[ASCII: 17] [Device Control One]";
                                break;
                            }
                        case '\u0012':
                            {
                                foundIllegalCharacter = "[ASCII: 18] [Device Control Two]";
                                break;
                            }
                        case '\u0013':
                            {
                                foundIllegalCharacter = "[ASCII: 19] [Device Control Three]";
                                break;
                            }
                        case '\u0014':
                            {
                                foundIllegalCharacter = "[ASCII: 20] [Device Control Four]";
                                break;
                            }
                        case '\u0015':
                            {
                                foundIllegalCharacter = "[ASCII: 21] [Negative Acknowledge]";
                                break;
                            }
                        case '\u0016':
                            {
                                foundIllegalCharacter = "[ASCII: 22] [Synchronous Idle]";
                                break;
                            }
                        case '\u0017':
                            {
                                foundIllegalCharacter = "[ASCII: 23] [End of Transmission Block]";
                                break;
                            }
                        case '\u0018':
                            {
                                foundIllegalCharacter = "[ASCII: 24] [Cancel]";
                                break;
                            }
                        case '\u0019':
                            {
                                foundIllegalCharacter = "[ASCII: 25] [End of Medium]";
                                break;
                            }
                        case '\u001A':
                            {
                                foundIllegalCharacter = "[ASCII: 26] [Substitute]";
                                break;
                            }
                        case '\u001B':
                            {
                                foundIllegalCharacter = "[ASCII: 27] [Escape]";
                                break;
                            }
                        case '\u001C':
                            {
                                foundIllegalCharacter = "[ASCII: 28] [File Separator]";
                                break;
                            }
                        case '\u001D':
                            {
                                foundIllegalCharacter = "[ASCII: 29] [Group Separator]";
                                break;
                            }
                        case '\u001E':
                            {
                                foundIllegalCharacter = "[ASCII: 30] [Record Separator]";
                                break;
                            }
                        case '\u001F':
                            {
                                foundIllegalCharacter = "[ASCII: 31] [Unit Separator]";
                                break;
                            }
                    }
                }

                if (foundIllegalCharacter != string.Empty)
                {
                    return true;
                }
            }

            return false;
        }

        internal static string GetSanatizedFieldValue(string fieldValue)
        {
            var fieldValueWithForbiddenCharacters = ControlCharacters.Any(fieldValue.Contains);
            if (fieldValueWithForbiddenCharacters)
            {
                fieldValue = RemoveForbiddenCharactersFromField(fieldValue);
            }

            return fieldValue;
        }

        internal static string RemoveForbiddenCharactersFromField(string fieldValue)
        {
            var containedForbiddenCharacters = ControlCharacters.FindAll(x => fieldValue.Contains(x));
            var replacedFieldValue = fieldValue;

            foreach (var character in containedForbiddenCharacters)
            {
                replacedFieldValue = fieldValue.Replace(character.ToString(), string.Empty);
            }

            return replacedFieldValue;
        }

        private static void ThrowIllegalCharacters(Type type, string method, string parameterWithIllegalCharacter, string foundIllegalCharacter)
        {
            throw ErrorUtility.GetArgumentException(method, type.Name, $"'{parameterWithIllegalCharacter}' has illegal character '{foundIllegalCharacter}'");
        }
    }
}
