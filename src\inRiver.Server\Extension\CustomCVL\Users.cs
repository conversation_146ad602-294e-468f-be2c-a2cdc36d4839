namespace inRiver.Server.Extension.CustomCVL
{
    using System.Collections.Generic;
    using inRiver.Remoting.Extension;
    using inRiver.Remoting.Extension.Interface;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;

    public class Users : ICustomValueList
    {
        private readonly RequestContext requestContext;

        public inRiverContext Context { get; set; }

        public Dictionary<string, string> DefaultSettings => new Dictionary<string, string>();

        public string Id { get; set; }

        public string Test()
        {
            return string.Empty;
        }

        public Users(RequestContext requestContext)
        {
            this.requestContext = requestContext;
            this.Id = "Users";
        }

        public CVLValue GetCVLValueByKey(string key)
        {
            User user = this.requestContext.DataPersistance.GetShallowUser(key);

            if (user == null)
            {
                return null;
            }

            CVLValue value = new CVLValue();
            value.CVLId = this.Id;
            value.Key = user.Username;
            value.Index = 0;
            value.Value = $"{user.FirstName} {user.LastName} ({user.Username})";

            return value;
        }

        public List<CVLValue> GetAllCVLValues()
        {
            List<CVLValue> values = new List<CVLValue>();

            foreach (User user in this.requestContext.DataPersistance.GetAllShallowUsers())
            {
                CVLValue value = new CVLValue();
                value.CVLId = this.Id;
                value.Key = user.Username;
                value.Index = 0;
                value.Value = $"{user.FirstName} {user.LastName} ({user.Username})";

                values.Add(value);
            }

            values.Sort(new CVLValueComparer());

            int index = 0;

            values.ForEach(delegate (CVLValue value) { value.Index = index++; });

            return values;
        }
    }
}
