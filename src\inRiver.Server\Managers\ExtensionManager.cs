namespace inRiver.Server.Managers
{
    using System;
    using System.Collections.Generic;
    using System.Data.SqlClient;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Abstractions.Extensions;
    using Abstractions.Models;
    using Core.Http;
    using inRiver.Configuration.Core.Persistance;
    using inRiver.Configuration.Core.Repository;
    using inRiver.Core.Access;
    using inRiver.Core.Models;
    using inRiver.Core.Persistance;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Extension;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Completeness.Criteria;
    using inRiver.Server.Configuration;
    using inRiver.Server.Extension.CustomCVL;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Exceptions;
    using Microsoft.Extensions.Caching.Memory;
    using Newtonsoft.Json;
    using Remoting.Log;
    using Serilog;
    using Service;
    using Util;

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "Http clients should not be disposed.")]
    public class ExtensionManager
    {
        private readonly IinRiverPersistance persistance;
        private readonly RequestContext context;
        private readonly IAugmentaHttpClient augmentaHttpClient;
        private readonly SettingsUtilityWithMemoryCaching settingsUtility;

        public ExtensionManager(RequestContext context)
        {
            this.context = context ?? throw new ArgumentException("RequestContext can't be null");

            this.persistance = IPMCPersistanceFactory.GetInstance(
                context.ConnectionString,
                new ApiCaller { Module = context.Module, Username = context.Username },
                context.EntityModel,
                context.EnvironmentId);

            this.augmentaHttpClient = StaticServiceProvider.GetRequiredService<IAugmentaHttpClient>();
            var memoryCache = StaticServiceProvider.GetRequiredService<IMemoryCache>();

            this.settingsUtility = new SettingsUtilityWithMemoryCaching(this.context, memoryCache, nameof(ExtensionManager));
        }

        private bool IsAugmentaPreprocessingEnabled()
        {
            var settingValue = this.settingsUtility.GetEnvironmentSetting("AUGMENTA_PREPROCESSING_ENABLED");

            return bool.TryParse(settingValue ?? string.Empty, out var isEnabled) && isEnabled;
        }

        #region Server Extensions

        public Entity CallServerExtensionsForEntity(Entity entity, string extensionEvent, CancelUpdateArgument arg)
        {
            if (entity.LoadLevel > LoadLevel.DataOnly || entity.Links?.Count > 0)
            {
                throw new ArgumentException($"The LoadLevel of entity (id={entity.Id}) violates the max allowed level of DataOnly.");
            }

            if (this.IsAugmentaPreprocessingEnabled())
            {
                var preProcessedEntityResult = this.augmentaHttpClient.PreProcessAsync(
                        extensionEvent,
                        entity,
                        this.context.DataPersistance.GetAllEntityTypes(),
                        this.context.CustomerSafeName,
                        this.context.EnvironmentSafeName,
                        CancellationToken.None)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                if (preProcessedEntityResult.Cancel)
                {
                    arg.Cancel = preProcessedEntityResult.Cancel;
                    arg.Message = preProcessedEntityResult.Message;
                    return entity;
                }

                entity = preProcessedEntityResult.Entity;
            }

            if (this.GetExtensionIdsForEnvironmentAndType(ExtensionType.ServerExtension).Count == 0)
            {
                return entity;
            }

            var httpClient = StaticHttpClientFactory.CreateHttpClient(StaticConfigurationProvider.ConnectServiceUrl);
            httpClient.DefaultRequestHeaders.Add("X-Username", this.context.Username);


            //posting to Connect service
            var response = httpClient.PostAsJsonAsync($"api/EntityExtension/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}/{extensionEvent}/v2", DtoFactory.DtoFromEntity(entity)).GetAwaiter().GetResult();

            if (!response.IsSuccessStatusCode)
            {
                Serilog.Log.Warning("Could not call Server Extension for Entity at {requestUri}. Extension event {extensionEvent}. Received status code {statusCode}. EntityId: {entityId}",
                    response.RequestMessage.RequestUri, extensionEvent, response.StatusCode, entity.Id);

                return entity;
            }

            var result = response.Content.ReadAsAsync<EntityResult>().GetAwaiter().GetResult();
            if (result.Cancel)
            {
                arg.Cancel = result.Cancel;
                arg.Message = result.Message;
                return entity;
            }

            return DtoFactory.EntityFromDto(
                result.DtoEntity,
                this.context.DataPersistance.GetAllEntityTypes(),
                this.context.DataPersistance.GetAllLinkTypes());

        }

        public Link CallServerExtensionsForLink(Link link, string extensionEvent, CancelUpdateArgument arg)
        {
            if (this.IsAugmentaPreprocessingEnabled())
            {
                var preProcessedLinkResult = this.augmentaHttpClient.PreProcessAsync(
                        extensionEvent,
                        link,
                        this.context.CustomerSafeName,
                        this.context.EnvironmentSafeName,
                        CancellationToken.None)
                    .ConfigureAwait(false)
                    .GetAwaiter()
                    .GetResult();

                if (preProcessedLinkResult.Cancel)
                {
                    arg.Cancel = preProcessedLinkResult.Cancel;
                    arg.Message = preProcessedLinkResult.Message;
                    return link;
                }

                link = preProcessedLinkResult.Link;
            }

            if (this.GetExtensionIdsForEnvironmentAndType(ExtensionType.ServerExtension).Count == 0)
            {
                return link;
            }

            var httpClient = StaticHttpClientFactory.CreateHttpClient(StaticConfigurationProvider.ConnectServiceUrl);
            httpClient.DefaultRequestHeaders.Add("X-Username", this.context.Username);

            var response = httpClient.PostAsJsonAsync($"api/LinkExtension/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}/{extensionEvent}/v2", link.ToLinkModel()).GetAwaiter().GetResult();

            if (!response.IsSuccessStatusCode)
            {
                Serilog.Log.Warning("Could not call Server Extension for Link at {requestUri}. Extension event {extensionEvent}. Received status code {statusCode}. LinkId: {linkId}",
                    response.RequestMessage.RequestUri, extensionEvent, response.StatusCode, link.Id);

                return link;
            }

            var result = response.Content.ReadAsAsync<LinkResult>().GetAwaiter().GetResult();

            if (result.Cancel)
            {
                arg.Cancel = result.Cancel;
                arg.Message = result.Message;
                return link;
            }

            link.Inactive = result.Inactive;
            link.Index = result.Index;

            return link;
        }

        #endregion

        #region Syndication

        public async Task CallSyndicationOutputExtensionAsync(
            string extensionId,
            SyndicationExtensionData syndicationExtensionData,
            CancellationToken cancellationToken)
        {
            var httpClient = StaticHttpClientFactory.CreateHttpClient(StaticConfigurationProvider.ConnectServiceUrl);
            httpClient.DefaultRequestHeaders.Add("X-Username", this.context.Username);

            try
            {
                using var response = await httpClient.PostAsJsonAsync(
                    $"api/SyndicationOutputExtension/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}/{extensionId}",
                    syndicationExtensionData,
                    cancellationToken);
                if (!response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.GatewayTimeout)
                {
                    throw new TimeoutException($"Connect service haven't responded within expected time. ExtensionId: {extensionId}. Response status code: {response.StatusCode}.");
                }
                else if (!response.IsSuccessStatusCode)
                {
                    throw new SyndicateConnectException($"Connect service responded with an error. ExtensionId: {extensionId}. Response status code: {response.StatusCode}.");
                }
            }
            catch (HttpRequestException ex)
            {
                throw new SyndicateConnectException($"Failed to send request to Connect. ExtensionId: {extensionId}", ex);
            }
        }

        #endregion

        #region Custom CVL

        public List<CVLValue> GetAllCustomValuesForCVL(string id, CVL cvl)
        {
            if (id.Equals("Users"))
            {
                return new Users(this.context).GetAllCVLValues();
            }

            if (id.Equals("GroupTask"))
            {
                return new GroupTask(this.context).GetAllCVLValues();
            }


            var httpClient = StaticHttpClientFactory.CreateHttpClient(StaticConfigurationProvider.ConnectServiceUrl);
            httpClient.DefaultRequestHeaders.Add("X-Username", this.context.Username);

            var response = httpClient.GetAsync($"api/CustomValueList/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}/{id}");

            response.Wait();

            if (!response.Result.IsSuccessStatusCode)
            {
                return new List<CVLValue>();
            }

            var values = response.Result.Content.ReadAsAsync<List<CVLValue>>();

            foreach (CVLValue value in values.Result)
            {
                if (value.Value != null && cvl.DataType == DataType.LocaleString)
                {
                    value.Value = JsonConvert.DeserializeObject<LocaleString>(value.Value.ToString());
                }
            }

            return values.Result;
        }

        public CVLValue GetCustomCVLValueByKey(string id, string key, CVL cvl)
        {
            if (id.Equals("Users"))
            {
                return new Users(this.context).GetCVLValueByKey(key);
            }

            if (id.Equals("GroupTask"))
            {
                return new GroupTask(this.context).GetCVLValueByKey(key);
            }

            var httpClient = StaticHttpClientFactory.CreateHttpClient(StaticConfigurationProvider.ConnectServiceUrl);
            httpClient.DefaultRequestHeaders.Add("X-Username", this.context.Username);

            var response = httpClient.GetAsync($"api/CustomValueList/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}/{id}/{key}");

            response.Wait();

            if (response.Result.IsSuccessStatusCode)
            {
                var value = response.Result.Content.ReadAsAsync<CVLValue>();

                CVLValue cvlValue = value.Result;

                if (cvlValue.Value != null && cvl.DataType == DataType.LocaleString)
                {
                    cvlValue.Value = JsonConvert.DeserializeObject<LocaleString>(cvlValue.Value.ToString());
                }

                return cvlValue;
            }

            return null;
        }

        #endregion

        #region Custom Completeness

        public async Task<int?> GetCriteriaCompletenessPercentageAsync(int entityId, string type, List<CompletenessRuleSetting> settings)
        {
            if (type.Equals("inRiver.Server.Completeness.Criteria.ExpressionCriteria", StringComparison.Ordinal))
            {
                return new ExpressionCriteria(this.context).GetCriteriaCompletenessPercentage(entityId, settings);
            }

            if (type.Equals("inRiver.Server.Completeness.Criteria.ContainsValueCriteria", StringComparison.Ordinal))
            {
                return await new ContainsValueCriteria(this.context).GetCriteriaCompletenessPercentageAsync(entityId, settings);
            }

            if (type.Equals("inRiver.Server.Completeness.Criteria.ExactMatchCriteria", StringComparison.Ordinal))
            {
                return await new ExactMatchCriteria(this.context).GetCriteriaCompletenessPercentageAsync(entityId, settings);
            }

            if (type.Equals("inRiver.Server.Completeness.Criteria.FieldNotEmptyCritera", StringComparison.Ordinal))
            {
                return await new FieldNotEmptyCritera(this.context).GetCriteriaCompletenessPercentageAsync(entityId, settings);
            }

            if (type.Equals("inRiver.Server.Completeness.Criteria.LinkTypeExistsCriteria", StringComparison.Ordinal))
            {
                return await new LinkTypeExistsCriteria(this.context.DataPersistance).GetCriteriaCompletenessPercentageAsync(entityId, settings);
            }

            if (type.Equals("inRiver.Server.Completeness.Criteria.RelationsCompleteCritera", StringComparison.Ordinal))
            {
                return await new RelationsCompleteCritera(this.context).GetCriteriaCompletenessPercentageAsync(entityId, settings);
            }

            if (type.Equals("inRiver.Server.Completeness.Criteria.RelationsGroupCompleteCriteria", StringComparison.Ordinal))
            {
                return await new RelationsGroupCompleteCriteria(this.context).GetCriteriaCompletenessPercentageAsync(entityId, settings);
            }

            return await this.GetCriteriaCompletenessPercentageFromExtensionAsync(entityId, type, settings);
        }

        public int? GetCriteriaCompletenessPercentage(int entityId, string type, List<CompletenessRuleSetting> settings)
        {
            var result = this.GetCriteriaCompletenessPercentageAsync(entityId, type, settings);
            return result.GetAwaiter().GetResult();
        }

        private async Task<int?> GetCriteriaCompletenessPercentageFromExtensionAsync(int entityId, string type, List<CompletenessRuleSetting> settings)
        {
            try
            {
                var httpClient = StaticHttpClientFactory.CreateHttpClient(StaticConfigurationProvider.ConnectServiceUrl);
                httpClient.DefaultRequestHeaders.Add("X-Username", this.context.Username);

                var response = await httpClient.PostAsJsonAsync($"api/CompletenessRule/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}/{type}/{entityId}", settings);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsAsync<int>();
                }

                var content = await response.Content.ReadAsStringAsync();
                this.context.Log(
                    LogLevel.Warning,
                    $"Could not call Completeness Rule for Entity at {response.RequestMessage.RequestUri}. " +
                    $"Received status code {response.StatusCode}. " +
                    $"EntityId: {entityId}. " +
                    $"Response Content: {content}");
                Log.Warning(
                    "Could not call Completeness Rule for Entity at {RequestUri}. Received status code {StatusCode}. EntityId: {EntityId}. Response Content: {Content}",
                    response.RequestMessage.RequestUri, response.StatusCode, entityId, content);
            }
            catch (Exception ex)
            {
                this.context.Log(
                    LogLevel.Error,
                    $"Could not call Completeness Rule for Entity. EntityId: {entityId}. Exception: {ex.Message}", ex);
                Log.Error(ex, "Could not call Completeness Rule for Entity. EntityId: {EntityId}. Exception {Exception}", entityId, ex.Message);
            }
            return 0;
        }

        #endregion

        #region Persistance Methods

        public List<string> GetExtensionIdsForEnvironmentAndType(string type)
        {
            var ids = new List<string>();

            using (var connection = new SqlConnection(this.context.ReadOnlyConfigDatabaseConnectionString))
            {
                var command = connection.CreateCommand();

                command.CommandText = "SELECT Extension.ExtensionId FROM Extension " +
                    "WHERE Extension.EnvironmentId = @EnvironmentId AND Extension.ExtensionType = @ExtensionType AND Extension.Enabled = 1";

                command.Parameters.AddWithValue("@EnvironmentId", this.context.EnvironmentId);
                command.Parameters.AddWithValue("@ExtensionType", type);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    if (!reader.HasRows)
                    {
                        reader.Close();
                        connection.Close();

                        return new List<string>();
                    }

                    while (reader.Read())
                    {
                        ids.Add(reader.GetString(0));
                    }

                    reader.Close();
                }

                return ids;
            }
        }

        public Dictionary<string, string> GetExtensionSettings(string extensionId)
        {
            var settings = new Dictionary<string, string>();

            using (var connection = new SqlConnection(this.context.ReadOnlyConfigDatabaseConnectionString))
            {
                var command = connection.CreateCommand();

                command.CommandText = "SELECT ExtensionSetting.[Key], ExtensionSetting.[Value] " +
                    "FROM Extension INNER JOIN ExtensionSetting ON Extension.Id = ExtensionSetting.ExtensionId " +
                    "WHERE Extension.EnvironmentId = @EnvironmentId AND Extension.ExtensionId = @ExtensionId";

                command.Parameters.AddWithValue("@EnvironmentId", this.context.EnvironmentId);
                command.Parameters.AddWithValue("@ExtensionId", extensionId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        settings.Add(reader.GetString(0), reader.GetString(1));
                    }

                    reader.Close();
                }
            }

            return settings;
        }

        #endregion

        public bool ChannelIsPublished(int channelId)
        {
            // TODO: store in a separate table for better speed
            var field = this.persistance.GetField(channelId, "ChannelPublished");

            if (field?.Data == null)
            {
                return false;
            }

            if (field.Data.Equals("true", StringComparison.InvariantCultureIgnoreCase) || field.Data.Equals("1"))
            {
                return true;
            }

            return false;
        }
    }
}
