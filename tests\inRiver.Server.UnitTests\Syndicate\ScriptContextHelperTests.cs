namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using FluentAssertions;
    using inRiver.iPMC.Persistance;
    using inRiver.Server.Syndication.Script;
    using Xunit;

    public class ScriptContextHelperTests
    {
        public static readonly object[][] FieldValues =
        {
            new object[] { null, RelatedEntityId, string.Empty },
            new object[] { new List<SyndicationRelatedEntityFieldValue>(), RelatedEntityId, string.Empty },
            new object[] { new List<SyndicationRelatedEntityFieldValue> { CreateFieldValue(1111, "value1") }, null, "value1" },
            new object[] { new List<SyndicationRelatedEntityFieldValue>
            {
                CreateFieldValue(1111, "value1"),
                CreateFieldValue(2222, "value2")
            }, null, "\"[\"\"value1\"\",\"\"value2\"\"]\"" },
            new object[] { new List<SyndicationRelatedEntityFieldValue> {
                CreateFieldValue(1111, "value1"),
                CreateFieldValue(2222, "value2")
            }, "abc", string.Empty },
            new object[] { new List<SyndicationRelatedEntityFieldValue> {
                CreateFieldValue(1111, "value1"),
                CreateFieldValue(2222, "value2")
            }, RelatedEntityId, string.Empty },
            new object[] { new List<SyndicationRelatedEntityFieldValue> {
                CreateFieldValue(1111, "value1"),
                CreateFieldValue(1234, "value2")
            }, RelatedEntityId, "value2" }
        };

        public static readonly object[][] FieldValuesToValidate =
        {
            new object[] { "entityName", "field12", new[] { "12", "23" }, new[] { 0, 1 }, true },
            new object[] { string.Empty, "field12", new[] { "12", "23" }, new[] { 0, 1 }, false },
            new object[] { null, "field12345", new[] { "12", "23" }, new[] { 0, 1 }, false },
            new object[] { "entityNameEntityNameEntityNameEntityNameEntityNameEntityNameEntityName", "field12", new[] { "12", "23" }, new[] { 0, 1 }, false },
            new object[] { "entityName", string.Empty, new[] { "12", "23" }, new[] { 0, 1 }, false },
            new object[] { "entityName", "field12345field12345field12345field12345field12345field12345field12345", new[] { "12", "23" }, new[] { 0, 1 }, false },
            new object[] { "entityName", "field12345", null, new[] { 0, 1 }, false },
            new object[] { "entityName", "field12345", Array.Empty<string>(), new[] { 0, 1 }, false },
            new object[] { "entityName", "field12345", new[] { "12", "23" }, null, false },
            new object[] { "entityName", "field12345", new[] { "12", "23" }, Array.Empty<int>(), false },
            new object[] { "entityName", "field12345", new[] { "12", "23" }, new[] { 0 }, false },
            new object[] { "entityName", "field12345", new[] { "12", "23" }, new[] { 0, 3 }, false },
            new object[] { "entityName", "field12345", new[] { "12", "23" }, new[] { 3, 4 }, false }
        };

        private const string RelatedEntityId = "1234";

        [Theory]
        [MemberData(nameof(FieldValues))]
        public void TestGetRelatedEntityFieldValue(IList<SyndicationRelatedEntityFieldValue> fieldValues, string relatedEntityId, string result)
        {
            // Arrange & Act
            var resultValue = ScriptContextHelper.GetRelatedEntityFieldValue(fieldValues, relatedEntityId);

            // Assert
            resultValue.Should().BeEquivalentTo(result);
        }

        [Theory]
        [MemberData(nameof(FieldValuesToValidate))]
        public void TestIsRelatedEntityFieldsInputValid(string entityName, string field, string[] linkTypeIds, int[] entityLinkPosition, bool result)
        {
            // Arrange & Act
            var resultValue = ScriptContextHelper.IsRelatedEntityFieldsInputValid(entityName, field, linkTypeIds, entityLinkPosition);

            // Assert
            resultValue.Should().Be(result);
        }

        private static SyndicationRelatedEntityFieldValue CreateFieldValue(int entityId, string value)
            => new SyndicationRelatedEntityFieldValue { EntityId = entityId, FieldValue = value };
    }
}
