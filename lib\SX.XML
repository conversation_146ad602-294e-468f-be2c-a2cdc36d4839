<?xml version="1.0" encoding="UTF-8" standalone="yes" ?><doc><assembly><name>SX</name></assembly><members><member name="T:SmartXLS.OleObject">
            <summary>
            represent the embeded ole object
            </summary>
        </member><member name="M:SmartXLS.OleObject.convertWorkbookFromHidden">
            <summary>
            convert the embeded excel workbook to unhidden if possible
            </summary>
        </member><member name="P:SmartXLS.OleObject.FileNativeData">
            <summary>
            content data
            </summary>
        </member><member name="P:SmartXLS.OleObject.StorageName">
            <summary>
            storage name
            </summary>
        </member><member name="P:SmartXLS.OleObject.ObjectData">
            <summary>
            object data
            </summary>
        </member><member name="P:SmartXLS.OleObject.ObjectType">
            <summary>
            object type
            </summary>
        </member><member name="P:SmartXLS.OleObject.FileName">
            <summary>
            file name
            </summary>
        </member><member name="P:SmartXLS.OleObject.FilePath">
            <summary>
            file path
            </summary>
        </member><member name="P:SmartXLS.OleObject.OleObjectType">
            <summary>
            ole object type
            </summary>
        </member><member name="T:SmartXLS.Sheet">
            <summary>
            represent sheet for used in addin function
            </summary>
        </member><member name="M:SmartXLS.Sheet.getType(System.Int32,System.Int32)">
            <summary>
            get the cell type
            </summary>
            <param name="row1">row</param>
            <param name="col1">col</param>
            <returns>type 0-empty 1-number 2-text</returns>
        </member><member name="M:SmartXLS.Sheet.getNumber(System.Int32,System.Int32)">
            <summary>
            get the cell number value
            </summary>
            <param name="row1">row</param>
            <param name="col1">col</param>
            <returns>value</returns>
        </member><member name="M:SmartXLS.Sheet.getText(System.Int32,System.Int32)">
            <summary>
            get the cell text
            </summary>
            <param name="row1">row</param>
            <param name="col1">col</param>
            <returns>text</returns>
        </member><member name="T:SmartXLS.ShapeObj">
            <summary>
            This class is base class of drawing object.
            </summary>
        </member><member name="M:SmartXLS.ShapeObj.getFormat">
            <summary>
            Returns the Format object associated with this shape.
            </summary>
            <returns>shape format.</returns>
        </member><member name="M:SmartXLS.ShapeObj.setFormat">
            <summary>
            Sets the Format for this shape.
            </summary>
        </member><member name="M:SmartXLS.ShapeObj.getLeft">
            <summary>
            left edge of the shape
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.ShapeObj.getRight">
            <summary>
            right edge of the shape
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.ShapeObj.getTop">
            <summary>
            top edge of the shape
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.ShapeObj.getBottom">
            <summary>
            bottom edge of the shape
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.ShapeObj.setPosition(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            set the shape position
            </summary>
            <param name="x1">left position</param>
            <param name="y1">top position</param>
            <param name="x2">right position</param>
            <param name="y2">bottom position</param>
        </member><member name="M:SmartXLS.ShapeObj.getPos">
            <summary>
            shape pos
            </summary>
            <returns></returns>
        </member><member name="P:SmartXLS.ShapeObj.Name">
            <summary>
            shape name
            </summary>
        </member><member name="P:SmartXLS.ShapeObj.LayoutFromLeftTop">
            <summary>
            layout the shape from lefttop(if true) with it's default size
            </summary>
        </member><member name="P:SmartXLS.ShapeObj.ShapePos">
            <summary>
            shape position object
            </summary>
        </member><member name="T:SmartXLS.RangeStyle">
            <summary> this class present creating and returning information about cell formats.</summary>
        </member><member name="F:SmartXLS.RangeStyle.OrientationNone">
            <summary> No orientation, use normal text.</summary>
        </member><member name="F:SmartXLS.RangeStyle.OrientationTopToBottom">
            <summary> Text is stacked top to bottom.</summary>
        </member><member name="F:SmartXLS.RangeStyle.OrientationCounterClockwise">
            <summary> Text is rotated counter clockwise.</summary>
        </member><member name="F:SmartXLS.RangeStyle.OrientationClockwise">
            <summary> Text is rotated clockwise.</summary>
        </member><member name="F:SmartXLS.RangeStyle.UnderlineNone">
            <summary> No Underline</summary>
        </member><member name="F:SmartXLS.RangeStyle.UnderlineSingle">
            <summary> Single Underline</summary>
        </member><member name="F:SmartXLS.RangeStyle.UnderlineDouble">
            <summary> Double Underline</summary>
        </member><member name="M:SmartXLS.RangeStyle.useAllFormat">
            <summary> Sets all format settings are used.
            Normally the RangeStyle object will not use style settings unless the Rangestyle has been set with an explicit call to a set***() method.
            To copy a format from one range to another range you must call useAllFormats() in between the calls to getRangeStyle(...) and setRangeStyle(...).
            </summary> 
        </member><member name="M:SmartXLS.RangeStyle.resetFormat">
            <summary>
            reset the format settings,this will clear all setting had been set before
            </summary>
        </member><member name="M:SmartXLS.RangeStyle.getPaletteColor(System.Int32)">
            <summary>
            get the color in rgb value
            </summary>
            <param name="index">color paletter index</param>
            <returns>rgb value</returns>
        </member><member name="M:SmartXLS.RangeStyle.getGradientShadingStyle">
            <summary>
            Returns the gradient fill shading style.
            </summary>
            <returns>ExcelGradientStyle</returns>
        </member><member name="P:SmartXLS.RangeStyle.FormatType">
            <summary>
            number formatting type
            * 0-General
            * 1-Number
            * 2-Currency
            * 3-Date
            * 4-DateTime
            * 5-Percent
            * 6-Fraction
            * 7-Scientific
            * 8-String
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.CustomFormat">
            <summary> pattern string for number format (for example, #,##0.00)..</summary>
        </member><member name="P:SmartXLS.RangeStyle.MergeCells">
            <summary> the merge cells setting,true if cells are merged.</summary>
        </member><member name="P:SmartXLS.RangeStyle.Locked">
            <summary>
            the locked cell flag.
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.FontName">
            <summary> the name of the font.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontSize">
            <summary> the size of the font in twips.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontItalic">
            <summary> whether the font is italic.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontUnderline">
            <summary> 
            the underline attribute of the font.
            Underline Constants
            UnderlineNone UnderlineSingle UnderlineDouble
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.FontBold">
            <summary> whether the font is bold.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontColor">
            <summary> the color used to display the font,an integer representing the color as an RGB value.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontStrikeout">
            <summary> whether the font is strikeout.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontSubscript">
            <summary> whether the font is Subscript.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontSuperscript">
            <summary> whether the font is Superscript.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontShadow">
            <summary> whether the font is Shadow.</summary>
        </member><member name="P:SmartXLS.RangeStyle.FontOutline">
            <summary> whether the font is Outline.</summary>
        </member><member name="P:SmartXLS.RangeStyle.ShrinkToFit">
            <summary> whether ShrinkToFit is enabled.</summary>
        </member><member name="P:SmartXLS.RangeStyle.WordWrap">
            <summary> whether word wrap is enabled.</summary>
        </member><member name="P:SmartXLS.RangeStyle.Pattern">
            <summary> the pattern type.
            a number which correspond to the excel patterns.
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.PatternBG">
            <summary> the color used to display the pattern background.get/set in the format 0x00RRGGBB</summary>
        </member><member name="P:SmartXLS.RangeStyle.PatternFG">
            <summary> the color used to display the pattern foreground.get/set in the format 0x00RRGGBB</summary>
        </member><member name="P:SmartXLS.RangeStyle.HorizontalAlignment">
            <summary> 
            the type of horizontal alignment.
            one of the following shorts designating the type of horizontal alignment.
            Horizontal Alignment Constants HorizontalAlignmentGeneral 	HorizontalAlignmentLeft 	HorizontalAlignmentCenter
            HorizontalAlignmentRight 	HorizontalAlignmentFill 	HorizontalAlignmentJustify
            HorizontalAlignmentCenterAcrossCells
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.VerticalAlignment">
            <summary> the type of vertical alignment.
            one of the following vertical alignment types
            Vertical Alignment Constants VerticalAlignmentTop 	VerticalAlignmentCenter
            VerticalAlignmentBottom 	VerticalAlignmentJustify
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.TopBorder">
            <summary> 
            the border style for the top edge of the cell.
            short integer representing one of the following border styles:
            BorderNone             = 0
            BorderThin             = 1
            BorderMedium           = 2
            BorderDashed           = 3
            BorderDotted           = 4
            BorderThick            = 5
            BorderDouble           = 6
            BorderHair             = 7
            BorderMediumDash       = 8
            BorderDashDot          = 9
            BorderMediumDashDot    = 10
            BorderDashDotDot       = 11
            BorderMediumDashDotDot = 12
            BorderSlantedDashDot   = 13
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.TopBorderColor">
            <summary> 
            the color used to display the top edge of the cell.
            an int representing the color.
            The color is returned as a four-byte integer in the format 0x00RRGGBB.
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.BottomBorder">
            <summary> 
            Returns the border style for the bottom edge of the cell.
            a short integer representing one of the following border styles:
            BorderNone             = 0
            BorderThin             = 1
            BorderMedium           = 2
            BorderDashed           = 3
            BorderDotted           = 4
            BorderThick            = 5
            BorderDouble           = 6
            BorderHair             = 7
            BorderMediumDash       = 8
            BorderDashDot          = 9
            BorderMediumDashDot    = 10
            BorderDashDotDot       = 11
            BorderMediumDashDotDot = 12
            BorderSlantedDashDot   = 13
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.BottomBorderColor">
            <summary> 
            the color used to display the bottom edge of the cell.
            The color is returned as a four-byte integer in the format 0x00RRGGBB. 
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.LeftBorder">
            <summary> the border style for the left edge of the cell.
            a short integer representing one of the following border styles:
            BorderNone             = 0
            BorderThin             = 1
            BorderMedium           = 2
            BorderDashed           = 3
            BorderDotted           = 4
            BorderThick            = 5
            BorderDouble           = 6
            BorderHair             = 7
            BorderMediumDash       = 8
            BorderDashDot          = 9
            BorderMediumDashDot    = 10
            BorderDashDotDot       = 11
            BorderMediumDashDotDot = 12
            BorderSlantedDashDot   = 13
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.LeftBorderColor">
            <summary> 
            the color used to display the left edge of the cell.
            an int representing the color.
            The color is returned as a four-byte integer in the format 0x00RRGGBB.
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.RightBorder">
            <summary> 
            the border style for the right edge of the cell.
            a short integer representing one of the following border styles:
            BorderNone             = 0
            BorderThin             = 1
            BorderMedium           = 2
            BorderDashed           = 3
            BorderDotted           = 4
            BorderThick            = 5
            BorderDouble           = 6
            BorderHair             = 7
            BorderMediumDash       = 8
            BorderDashDot          = 9
            BorderMediumDashDot    = 10
            BorderDashDotDot       = 11
            BorderMediumDashDotDot = 12
            BorderSlantedDashDot   = 13
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.RightBorderColor">
            <summary> 
            the color used to display the right edge of the cell.
            an int representing the color.
            The color is returned as a four-byte integer in the format 0x00RRGGBB.
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.HorizontalInsideBorder">
            <summary> 
            the border style for the horizontal inside border.
            a short integer representing one of the following border styles:
            BorderNone             = 0
            BorderThin             = 1
            BorderMedium           = 2
            BorderDashed           = 3
            BorderDotted           = 4
            BorderThick            = 5
            BorderDouble           = 6
            BorderHair             = 7
            BorderMediumDash       = 8
            BorderDashDot          = 9
            BorderMediumDashDot    = 10
            BorderDashDotDot       = 11
            BorderMediumDashDotDot = 12
            BorderSlantedDashDot   = 13
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.HorizontalInsideBorderColor">
            <summary> 
            the color used to display the horizontal inside border.
            an int representing the color.
            The color is returned as a four-byte integer in the format 0x00RRGGBB.
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.VerticalInsideBorder">
            <summary> 
            the border style for the vertical inside border.
            a short integer representing one of the following border styles:
            BorderNone             = 0
            BorderThin             = 1
            BorderMedium           = 2
            BorderDashed           = 3
            BorderDotted           = 4
            BorderThick            = 5
            BorderDouble           = 6
            BorderHair             = 7
            BorderMediumDash       = 8
            BorderDashDot          = 9
            BorderMediumDashDot    = 10
            BorderDashDotDot       = 11
            BorderMediumDashDotDot = 12
            BorderSlantedDashDot   = 13
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.VerticalInsideBorderColor">
            <summary> 
            the color used to display the vertical inside border.
            an int representing the color. The color is returned as a four-byte integer in the format 0x00RRGGBB.
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.Orientation">
            <summary> 
            the orientation type.
            Orientation Constants
            OrientationNone OrientationTopToBottom OrientationCounterClockwise
            OrientationClockwise eOrientationAuto
            </summary>
        </member><member name="P:SmartXLS.RangeStyle.Indent">
            <summary>
            the indent attribute
            </summary>
        </member><member name="T:SmartXLS.WorkBook">
            <summary>
            represent the workbook object
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TypeEmpty">
            <summary>
            cell type empty
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TypeNumber">
            <summary>
            cell type number
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TypeText">
            <summary>
            cell type text
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TypeLogical">
            <summary>
            cell type logic
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TypeError">
            <summary>
            cell type error
            </summary>
        </member><member name="F:SmartXLS.WorkBook.ShiftHorizontal">
            <summary>
            shift direction horizontal
            </summary>
        </member><member name="F:SmartXLS.WorkBook.ShiftVertical">
            <summary>
            shift direction vertical
            </summary>
        </member><member name="F:SmartXLS.WorkBook.ShiftRows">
            <summary>
            shift direction row
            </summary>
        </member><member name="F:SmartXLS.WorkBook.ShiftColumns">
            <summary>
            shift direction column
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TabsOff">
            <summary>
            sheet tab off
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TabsBottom">
            <summary>
            sheet tab on bottom
            </summary>
        </member><member name="F:SmartXLS.WorkBook.TabsTop">
            <summary>
            sheet tab on top
            </summary>
        </member><member name="F:SmartXLS.WorkBook.ShowOff">
            <summary>
            scrollbar off
            </summary>
        </member><member name="F:SmartXLS.WorkBook.ShowOn">
            <summary>
            scrollbar on
            </summary>
        </member><member name="F:SmartXLS.WorkBook.ShowAutomatic">
            <summary>
            scrollbar auto show 
            </summary>
        </member><member name="F:SmartXLS.WorkBook.SheetStateShown">
            <summary>
            sheet shown
            </summary>
        </member><member name="F:SmartXLS.WorkBook.SheetStateHidden">
            <summary>
            sheet hidden
            </summary>
        </member><member name="F:SmartXLS.WorkBook.SheetStateVeryHidden">
            <summary>
            sheet hidden,can't be unhidden by UI
            </summary>
        </member><member name="F:SmartXLS.WorkBook.CopyFormulas">
            <summary>
            copy options formulas
            </summary>
        </member><member name="F:SmartXLS.WorkBook.CopyValues">
            <summary>
            copy options values
            </summary>
        </member><member name="F:SmartXLS.WorkBook.CopyFormats">
            <summary>
            copy options formats
            </summary>
        </member><member name="F:SmartXLS.WorkBook.CopyAll">
            <summary>
            copy options all(value/formula/formats)
            </summary>
        </member><member name="F:SmartXLS.WorkBook.SheetNormalView">
            <summary>
            normal sheet view 
            </summary>
        </member><member name="F:SmartXLS.WorkBook.SheetPageLayoutView">
            <summary>
            page layout sheet view
            </summary>
        </member><member name="F:SmartXLS.WorkBook.SheetPageBreakPreView">
            <summary>
            page break preview
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowNone">
            <summary>
            sheet protection option none
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowEditObjects">
            <summary>
            sheet protection option allow edit objects
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowEditScenarios">
            <summary>
            sheet protection option allow edit scenarious
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowFormatCells">
            <summary>
            sheet protection option allow format cells
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowFormatColumns">
            <summary>
            sheet protection option allow format columns
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowFormatRows">
            <summary>
            sheet protection option allow format rows
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowInsertColumns">
            <summary>
            sheet protection option allow insert columns
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowInsertRows">
            <summary>
            sheet protection option allow insert rows
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowInsertHyperlinks">
            <summary>
            sheet protection option allow insert hyperlinks
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowDeleteColumns">
            <summary>
            sheet protection option allow delete columns
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowDeleteRows">
            <summary>
            sheet protection option allow delete rows
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowSelectLocked">
            <summary>
            sheet protection option allow selecte locked
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowSort">
            <summary>
            sheet protection option allow sort
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowUseAutoFilter">
            <summary>
            sheet protection option allow use autofilter
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowUsePivotRanges">
            <summary>
            sheet protection option allow use pivot ranges
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowSelectUnlocked">
            <summary>
            sheet protection option allow select unlocked
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowDefault">
            <summary>
            sheet protection option allow default value
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowLockedInCellEdit">
            <summary>
            sheet protection option allow locked in cell edit
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowLockedEditFocus">
            <summary>
            sheet protection option allow locked edit focus
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowChangeLocked">
            <summary>
            sheet protection option allow change locked
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowChangeUnlocked">
            <summary>
            sheet protection option allow change unlocked
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowUseOutlining">
            <summary>
            sheet protection option allow use outline
            </summary>
        </member><member name="F:SmartXLS.WorkBook.sheetProtectionAllowEverything">
            <summary>
            sheet protection option allow everything
            </summary>
        </member><member name="F:SmartXLS.WorkBook.bookProtectStructure">
            <summary>
            book protect structure
            </summary>
        </member><member name="F:SmartXLS.WorkBook.bookProtectWindow">
            <summary>
            book protect window
            </summary>
        </member><member name="M:SmartXLS.WorkBook.#ctor">
            <summary>
            default constructer
            </summary>
        </member><member name="M:SmartXLS.WorkBook.getText(System.Int32,System.Int32,System.Int32)">
            <summary>  Returns the text value of the specified cell.</summary>
            <param name="sheet">number of sheet.
            </param>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <returns> the text value of the specified cell.
            </returns>
            <throws>  Exception  Exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setText(System.Int32,System.Int32,System.Int32,System.String)">
            <summary> Sets the value of the specified cell in the specified sheet.</summary>
            <param name="sheet">number of sheet.</param>
            <param name="row">number of row.</param>
            <param name="col">number of column.</param>
            <param name="text"> the text value for the cell.</param>
        </member><member name="M:SmartXLS.WorkBook.getText(System.Int32,System.Int32)">
            <summary> Returns the text value of the specified cell.</summary>
            <param name="row">number of row.</param>
            <param name="col">number of column.</param>
            <returns> the text value of the specified cell.</returns>
        </member><member name="M:SmartXLS.WorkBook.getText(System.Int32,System.Int32,System.Boolean)">
            <summary> Returns the text value of the specified cell.</summary>
            <param name="row">number of row.</param>
            <param name="col">number of column.</param>
            <param name="calc">need calculate before get the text.</param>
            <returns> the text value of the specified cell.</returns>
        </member><member name="M:SmartXLS.WorkBook.getRichText(System.Int32,System.Int32)">
            <summary>
            return the rtf string for specidied cell.
            </summary>
            <param name="row">number of row.</param>
            <param name="col">number of column.</param>
            <returns>rtf string</returns>
        </member><member name="M:SmartXLS.WorkBook.setText(System.Int32,System.Int32,System.String)">
            <summary> Sets the value of the specified cell for current selected sheet.</summary>
            <param name="row">number of row.</param>
            <param name="col">number of column.</param>
            <param name="text"> the text value for the cell.
            </param>
            <throws>  Exception if the cell reference is invalid or the text is too long. </throws>
        </member><member name="M:SmartXLS.WorkBook.getText(System.String)">
            <summary> Returns the text value of the specified cell.</summary>
            <param name="name">range name.
            </param>
            <returns> the text value of the specified cell.
            </returns>
            <throws>  Exception  exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setText(System.String,System.String)">
            <summary> Sets the value of the specified cell for current selected sheet.</summary>
            <param name="name">range name.
            </param>
            <param name="text"> the text value for the cell.
            </param>
            <throws>  Exception if the cell reference is invalid or the text is too long. </throws>
        </member><member name="M:SmartXLS.WorkBook.setNumber(System.Int32,System.Int32,System.Double)">
            <summary> Sets the numeric value of the specified cell in current selected sheet.</summary>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <param name="number">the cell value.
            </param>
            <throws>  Exception if row or column numbers are not valid </throws>
        </member><member name="M:SmartXLS.WorkBook.getNumber(System.String)">
            <summary> Returns the numeric value of the specified cell.
            Cells containing a formula return the numeric result of the formula.
            if a cell contains text, an attempt is made to convert the text to a number.
            if the text cannot be converted, 0 (No Error) is returned.
            </summary>
            <param name="name">range cell name.
            </param>
            <returns> the numeric value of the specified cell.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setNumber(System.String,System.Double)">
            <summary> Sets the numeric value of the specified cell in current selected sheet.</summary>
            <param name="name">range name.
            </param>
            <param name="number">the cell value.
            </param>
            <throws>  Exception if row or column numbers are not valid </throws>
        </member><member name="M:SmartXLS.WorkBook.getNumber(System.Int32,System.Int32)">
            <summary> Returns the numeric value of the specified cell.
            Cells containing a formula return the numeric result of the formula.
            if a cell contains text, an attempt is made to convert the text to a number.
            if the text cannot be converted, 0 (No Error) is returned.
            </summary>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <returns> the numeric value of the specified cell.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setNumber(System.Int32,System.Int32,System.Int32,System.Double)">
            <summary> Sets the numeric value of the specified cell in the specified worksheet.</summary>
            <param name="sheet">number of sheet.
            </param>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <param name="number">the cell value.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getNumber(System.Int32,System.Int32,System.Int32)">
            <summary> Returns the numeric value of the specified cell in the specified worksheet.
            Cells containing a formula return the numeric result of the formula.
            if a cell contains text, an attempt is made to convert the text to a number.
            if the text cannot be converted, 0 (No Error) is returned.
            </summary>
            <param name="sheet">number of sheet.
            </param>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <returns> the numeric value of the specified cell.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setFormula(System.Int32,System.Int32,System.String)">
            <summary> Sets the formula for the specified cell in current selected sheets.</summary>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <param name="formula">formula for the active cell. The string should not have a leading equal sign (=).
            </param>
            <throws>  Exception if the string is not a valid workbook formula. </throws>
        </member><member name="M:SmartXLS.WorkBook.getFormula(System.Int32,System.Int32)">
            <summary> Returns the text of the formula of the specified cell</summary>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <returns> the formula of the specified cell.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setFormula(System.String,System.String)">
            <summary> Sets the formula for the specified cell in current selected sheets.</summary>
            <param name="name">range name.
            </param>
            <param name="formula">formula for the active cell. The string should not have a leading equal sign (=).
            </param>
            <throws>  Exception if the string is not a valid workbook formula. </throws>
        </member><member name="M:SmartXLS.WorkBook.getFormula(System.String)">
            <summary> Returns the text of the formula of the specified cell</summary>
            <param name="name">range name.
            </param>
            <returns> the formula of the specified cell.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setFormula(System.Int32,System.Int32,System.Int32,System.String)">
            <summary> Sets the formula for the specified cell on the specified sheet.</summary>
            <param name="sheet">number of sheet.
            </param>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <param name="formula">formula for the active cell. The string should not have a leading equal sign (=).
            </param>
            <throws>  Exception if the string is not a valid workbook formula. </throws>
        </member><member name="M:SmartXLS.WorkBook.getFormula(System.Int32,System.Int32,System.Int32)">
            <summary> Returns the text of the formula of the specified cell in current selected sheets.</summary>
            <param name="sheet">number of sheet.
            </param>
            <param name="row">number of row.
            </param>
            <param name="col">number of column.
            </param>
            <returns> the formula of the specified cell.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.moveSheet(System.Int32)">
            <summary> Moves selected sheet to a specified location.</summary>
            <param name="insertAt">The location to which to move the sheets.
            </param>
            <throws>  Exception if insertAt is not a valid sheet index. </throws>
        </member><member name="M:SmartXLS.WorkBook.deleteSheets(System.Int32,System.Int32)">
            <summary> Deletes one or more worksheets from a workbook.
            The names displayed on the sheet tabs are not affected by deletions; however, the worksheet index is adjusted.
            </summary>
            <param name="sheet">specifies the index to the first worksheet to be deleted. do not confuse the index with the sheet name on the sheet tab.
            </param>
            <param name="sheets">specifies how many sheets to delete, beginning with the worksheet specified by sheet.
            </param>
            <throws>  Exception if a parameter is invalid </throws>
        </member><member name="M:SmartXLS.WorkBook.insertSheets(System.Int32,System.Int32)">
            <summary> Inserts one or more worksheets at the specified location.</summary>
            <param name="sheet">identifies the index number of the worksheet in front of which you want to insert the new sheets. Sheets are indexed from left to right beginning with 0. do not confuse the index number with the sheet name that appears on the sheet tab.
            </param>
            <param name="sheets">determines how many sheets are inserted before sheet.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setSheetName(System.Int32,System.String)">
            <summary> Assigns a name to the specified worksheet.
            this name appears on the sheet tab.
            When you change a sheet name, formulas that reference the worksheet are updated to reference the new sheet name.
            </summary>
            <param name="sheet">index number of the sheet. Worksheets are indexed from left to right beginning with 0.
            </param>
            <param name="sheetName">the name of the sheet.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getSheetName(System.Int32)">
            <summary> Returns the name of the specified worksheet.</summary>
            <param name="sheet">index number of the sheet. Worksheets are indexed from left to right beginning with 0.
            </param>
            <returns> the name of the worksheet.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.copySheet(System.Int32)">
            <summary> Creates copies of the selected sheet.</summary>
            <param name="insertAt">The location at which to insert the new sheets.
            </param>
            <throws>  Exception if insertAt is not a valid sheet index. </throws>
        </member><member name="M:SmartXLS.WorkBook.copyRange(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Copies a range of cells to a new location in the current workbook.</summary>
            <param name="dstRow1">the first row in the destination range.
            </param>
            <param name="dstCol1">the first column in the destination range.
            </param>
            <param name="dstRow2">the ending row in the destination range, inclusive.
            </param>
            <param name="dstCol2">the ending column in the destination range, inclusive.
            </param>
            <param name="srcRow1">the first row in the source range.
            </param>
            <param name="srcCol1">the first column in the source range.
            </param>
            <param name="srcRow2">the ending row in the source range, inclusive.
            </param>
            <param name="srcCol2">the ending column in the source range, inclusive.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0.
            </param>
            <throws>  Exception if a parameter is invalid </throws>
        </member><member name="M:SmartXLS.WorkBook.copyRange(System.Int32,System.Int32,System.Int32,System.Int32,SmartXLS.WorkBook,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Copies a range of cells to a new location between the workbooks.</summary>
            <param name="dstRow1">the first row in the destination range.</param>
            <param name="dstCol1">the first column in the destination range.</param>
            <param name="dstRow2">the ending row in the destination range, inclusive.</param>
            <param name="dstCol2">the ending column in the destination range, inclusive.</param>
            <param name="book">the source workbook.</param>
            <param name="srcRow1">the first row in the source range.</param>
            <param name="srcCol1">the first column in the source range.</param>
            <param name="srcRow2">the ending row in the source range, inclusive.</param>
            <param name="srcCol2">the ending column in the source range, inclusive.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0. </param>
            <throws>  Exception if a parameter is invalid </throws>
        </member><member name="M:SmartXLS.WorkBook.copyRange(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,SmartXLS.WorkBook,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Copies a range of cells to a new location between the workbooks.</summary>
            <param name="dstSheet">the destination sheet index.</param>
            <param name="dstRow1">the first row in the destination range.</param>
            <param name="dstCol1">the first column in the destination range.</param>
            <param name="dstRow2">the ending row in the destination range, inclusive.</param>
            <param name="dstCol2">the ending column in the destination range, inclusive.</param>
            <param name="book">the source workbook.</param>
            <param name="srcSheet">the source sheet index.</param>
            <param name="srcRow1">the first row in the source range.</param>
            <param name="srcCol1">the first column in the source range.</param>
            <param name="srcRow2">the ending row in the source range, inclusive.</param>
            <param name="srcCol2">the ending column in the source range, inclusive.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0. </param>
        </member><member name="M:SmartXLS.WorkBook.clearRange(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Clears the specified range.</summary>
            <param name="row1">Coordinate specifying the beginning row of the range.
            </param>
            <param name="col1">Coordinate specifying the beginning column of the range.
            </param>
            <param name="row2">Coordinate specifying the ending row of the range.
            </param>
            <param name="col2">Coordinate specifying the ending column of the range.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.deleteRange(System.Int32,System.Int32,System.Int32,System.Int32,System.Int16)">
            <summary> Deletes cells, rows, or columns from the specified selected sheets.</summary>
            <param name="row1">Coordinate specifying the beginning row of the range.
            </param>
            <param name="col1">Coordinate specifying the beginning column of the range.
            </param>
            <param name="row2">Coordinate specifying the ending row of the range.
            </param>
            <param name="col2">Coordinate specifying the ending column of the range.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0.
            </param>
            <param name="shift">shift direction.
            ShiftHorizontal Shifts cells horizontally beyond the last column, one cell to the right.
            ShiftVertical Shifts cells vertically beyond the last row, one cell up.
            ShiftRows Shifts all cells beyond the last row, up to the first deleted row.
            ShiftColumns Shifts all cells beyond the last column to the left.
            </param>
            <throws>  Exception if a parameter is invalid.</throws>
        </member><member name="M:SmartXLS.WorkBook.deleteRange(System.Int32,System.Int32,System.Int32,System.Int32,System.Int16,System.Boolean)">
            <summary> Deletes cells, rows, or columns from the specified selected sheets.</summary>
            <param name="row1">Coordinate specifying the beginning row of the range.
            </param>
            <param name="col1">Coordinate specifying the beginning column of the range.
            </param>
            <param name="row2">Coordinate specifying the ending row of the range.
            </param>
            <param name="col2">Coordinate specifying the ending column of the range.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0.
            </param>
            <param name="shift">shift direction.
            ShiftHorizontal Shifts cells horizontally beyond the last column, one cell to the right.
            ShiftVertical Shifts cells vertically beyond the last row, one cell up.
            ShiftRows Shifts all cells beyond the last row, up to the first deleted row.
            ShiftColumns Shifts all cells beyond the last column to the left.
            </param>
            <param name="bFixFormulas">fix the formulas references after changing</param>
            <throws>  Exception if a parameter is invalid.</throws>
        </member><member name="M:SmartXLS.WorkBook.insertRange(System.Int32,System.Int32,System.Int32,System.Int32,System.Int16)">
            <summary> insert new cells, rows or columns.</summary>
            <param name="row1">Coordinate specifying the beginning row of the range.
            </param>
            <param name="col1">Coordinate specifying the beginning column of the range.
            </param>
            <param name="row2">Coordinate specifying the ending row of the range.
            </param>
            <param name="col2">Coordinate specifying the ending column of the range.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0.
            </param>
            <param name="shift">shift direction.
            ShiftHorizontal Shifts cells horizontally beyond the last column, one cell to the right.
            ShiftVertical Shifts cells vertically beyond the last row, one cell up.
            ShiftRows Shifts all cells beyond the last row, up to the first deleted row.
            ShiftColumns Shifts all cells beyond the last column to the left.
            </param>
            <throws>  Exception if a parameter is invalid.</throws>
        </member><member name="M:SmartXLS.WorkBook.getRangeStyle">
            <summary> Returns the format of the selected range of cells.</summary>
            <returns> RangeStyle
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getRangeStyle(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Returns the RangeStyle of the specified range of cells</summary>
            <param name="row1">the first row in the range.
            </param>
            <param name="col1">the first column in the range.
            </param>
            <param name="row2">the ending row in the range.
            </param>
            <param name="col2">the ending column in the range.
            Rows are indexed from top to bottom beginning with 0.
            Sheets and columns are indexed from left to right beginning with 0.
            </param>
            <returns> RangeStyle
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setRangeStyle(SmartXLS.RangeStyle)">
            <summary> Sets the RangeStyle of the selected range of cells.</summary>
            <param name="rangestyle">RangeStyle
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setRangeStyle(SmartXLS.RangeStyle,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Sets the RangeStyle of the selected range of cells.</summary>
            <param name="rangestyle">RangeStyle
            </param>
            <param name="row1">the first row in the range.
            </param>
            <param name="col1">the first column in the range.
            </param>
            <param name="row2">the ending row in the range.
            </param>
            <param name="col2">the ending column in the range.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getRowPageBreaks">
            <summary>
            return all row index which has page break
            </summary>
            <returns>row index</returns>
        </member><member name="M:SmartXLS.WorkBook.addRowPageBreak(System.Int32)">
            <summary> Adds a horizontal page break adjacent to the top edge of the specified row.</summary>
            <param name="row">integer indicating the row.
            </param>
            <throws>  Exception if parameter is invalid </throws>
        </member><member name="M:SmartXLS.WorkBook.removeRowPageBreak(System.Int32)">
            <summary> Removes a horizontal page break adjacent to the current cell.</summary>
            <param name="row">the index number of the row where the page break is removed.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getColPageBreaks">
            <summary>
            return all col index which has page break
            </summary>
            <returns>col index</returns>
        </member><member name="M:SmartXLS.WorkBook.addColPageBreak(System.Int32)">
            <summary> Adds a vertical page break to the left edge of the specified column.</summary>
            <param name="col">an integer indicating the column.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.WorkBook.removeColPageBreak(System.Int32)">
            <summary> Removes a vertical page break adjacent to the left edge of the specified column.</summary>
            <param name="col">the index number of the column.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getDefinedName(System.String)">
            <summary> Returns the definition associated with the specified name.
            A defined name can refer to a cell, a group of cells, a value, or a formula.
            </summary>
            <param name="name">a defined name.
            </param>
            <returns> a string containing the name's definition.
            </returns>
            <throws>  Exception if the specified name does not exist </throws>
        </member><member name="M:SmartXLS.WorkBook.getDefinedName(System.Int32)">
            <summary> Returns the defined name for the specified index number.
            A defined name can refer to a cell, a group of cells, a value, or a formula.
            </summary>
            <param name="name">identifies a name by index number. Defined names are numbered in the order in which they are created, beginning with 1.
            </param>
            <returns> a string containing the name.
            </returns>
            <throws>  Exception if an invalid index number is specified </throws>
        </member><member name="M:SmartXLS.WorkBook.setDefinedName(System.String,System.String)">
            <summary> Sets the formula associated with a defined name</summary>
            <param name="name">A defined name.
            Enter an existing name if you are returning the formula associated with the name or changing the value associated with the name. 
            Enter a unique name if you are creating a new value.
            </param>
            <param name="formula">Describes the range represented by name.
            A name can refer to a cell, a group of cells, a value, or a formula. 
            When setting name, do not include a leading equal sign (=)in the formula.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.deleteDefinedName(System.String)">
            <summary>
            remove the defined name
            </summary>
            <param name="name">A defined name.</param>
        </member><member name="M:SmartXLS.WorkBook.setRowHeight(System.Int32,System.Int32)">
            <summary> Sets the height of a single specified row.</summary>
            <param name="row">the row number.
            </param>
            <param name="height">the row height in twips. A twip is 1/1440th of an inch.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getRowHeight(System.Int32)">
            <summary> Returns the height of a single specified row.</summary>
            <param name="row"> an integer indicating the index number of the row.
            </param>
            <returns> the row height in twips. A twip is 1/1440th of an inch.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setRowHidden(System.Int32,System.Boolean)">
            <summary> Sets the display status of the specified row.</summary>
            <param name="row">row number.
            </param>
            <param name="rowHidden">Specifies whether the row is hidden (true) or displayed (false).
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.isRowHidden(System.Int32)">
            <summary> Returns true if row is hidden. Returns false otherwise.</summary>
            <param name="row">identifies the row by number
            </param>
            <returns> true/false
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setRowOutlineLevel(System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary> Sets the outline level of a range of rows.</summary>
            <param name="row1">the first row in the range.
            </param>
            <param name="row2">the last row in the range.
            </param>
            <param name="outlineLevel">the outline level to set.
            </param>
            <param name="additive">Specifies whether the outline level is an absolute value to set, or should be added to the current outline level. if false, the outline level of the specified rows is set to the value set with the outlineLevel parameter.
            Otherwise, the value set with the outlineLevel parameter is added to the current outline level of each row.
            Note: The outline level may be set from zero to seven.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getRowOutlineLevel(System.Int32)">
            <summary> Returns the outline level of a single specified row.</summary>
            <param name="row">an integer indicating the index number of the row.
            </param>
            <returns> the outline level of the specified row.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setColWidth(System.Int32,System.Int32)">
            <summary> Sets the width of a single column.</summary>
            <param name="col">identifies a column by number.
            </param>
            <param name="width">the width of the column.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setColWidthAutoSize(System.Int32,System.Boolean)">
            <summary> autoSize the width of a single column.</summary>
            <param name="col">identifies a column by number.
            </param>
            <param name="auto">auto size.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getColWidth(System.Int32)">
            <summary> Returns the width of a single column.</summary>
            <param name="col">an integer indicating the column index number.
            </param>
            <returns> the column width value.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setColHidden(System.Int32,System.Boolean)">
            <summary> Sets a flag indicating whether or not the specified column is hidden.</summary>
            <param name="col">number identifying the column.
            </param>
            <param name="colHidden">the specified column is hidden.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.isColHidden(System.Int32)">
            <summary> Returns true if the column is hidden.</summary>
            <param name="col">identifies a column by number. Columns are indexed from left to right beginning at 0.
            </param>
            <returns> true/false
            </returns>
            <throws>  Exception if a parameter is invalid </throws>
        </member><member name="M:SmartXLS.WorkBook.setColOutlineLevel(System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary> Sets the outline level of a range of columns.</summary>
            <param name="col1">the first column in the range.
            </param>
            <param name="col2">the last column in the range.
            </param>
            <param name="outlineLevel">the outline level to set.
            </param>
            <param name="additive">Specifies whether the outline level is an absolute value to set, or should be added to the current outline level.
            if this value is false, the outline level of the specified columns will be set to outlineLevel. 
            Otherwise, outlineLevel will be added to the current outline level of each column.
            Note: The outline level may be set from zero to seven.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getColOutlineLevel(System.Int32)">
            <summary> Returns the outline level of a single specified column.</summary>
            <param name="col">identifies a column by number. Columns are indexed from left to right beginning at 0.
            </param>
            <returns>  the outline level of the specified column.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.freezePanes(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary> Sets frozen panes at the specified position.</summary>
            <param name="topRow">The top row of the top frozen pane. (Ignored if splitRow is 0.)
            </param>
            <param name="leftCol">The left column of the left frozen pane. (Ignored if splitCol is 0.)
            </param>
            <param name="splitRows">The number of rows that should be visible in the top pane. Use 0 to specify no top frozen pane.
            </param>
            <param name="splitCols">The number of cols that should be visible in the left pane. Use 0 to specify no left frozen pane.
            </param>
            <param name="splitView">Specifies whether this is a frozen split view. if true, autoFreezePanes and unfreezePanes will convert the frozen panes to a split view.
            if false, those methods will set the view to its default (unsplit) state.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.unfreezePanes">
            <summary> Removes frozen panes.</summary>
        </member><member name="M:SmartXLS.WorkBook.setSelection(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Selects the specified range and moves the active cell to the top left cell in the range.</summary>
            <param name="row1">Coordinate specifying the beginning row of the range.
            </param>
            <param name="col1">Coordinate specifying the beginning column of the range.
            </param>
            <param name="row2">Coordinate specifying the ending row of the range.
            </param>
            <param name="col2">Coordinate specifying the ending column of the range.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setSelection(System.String)">
            <summary> Selects the range represented by the formula.</summary>
            <param name="range">Identifies the starting and ending rows and columns of the selection or a defined name that represents the selection
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getSelection">
            <summary>
            get the seleted range from the current selected worksheet
            </summary>
            <returns>RangeArea</returns>
        </member><member name="M:SmartXLS.WorkBook.getMergedRanges">
            <summary>
            list all the merged range in the current selected sheet
            </summary>
            <returns>merged range list</returns>
        </member><member name="M:SmartXLS.WorkBook.editCopyRight">
            <summary> Copies cells in the left column of a selection to the other columns in the selected range and adjusts relative cell references appropriately.</summary>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.editCopyDown">
            <summary> Copies cells in the top row of a selection to the other rows in the selected range and adjusts relative cell references appropriately.</summary>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getPaletteEntry(System.Int32)">
            <summary> Returns a color in the color palette. The color is returned as a four-byte integer in the format 0x00RRGGBB</summary>
            <param name="entry">Zero-based index of the entry to change
            </param>
            <returns> color value
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.recalc">
            <summary> Recalculate the workbook</summary>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.addPicture(System.Double,System.Double,System.Double,System.Double,System.String)">
            <summary> Adds a picture object to the worksheet</summary>
            <param name="x1">Coordinate of the first anchor point of the object; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y1">Coordinate of the first anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="x2">Coordinate of the second anchor point; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y2">Coordinate of the second anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="filename"> image file name
            </param>
            <returns>pictureShape</returns>
        </member><member name="M:SmartXLS.WorkBook.addPicture(System.Double,System.Double,System.Double,System.Double,System.Byte[])">
            <summary>
            Adds a picture object to the worksheet
            </summary>
            <param name="x1">Coordinate of the first anchor point of the object; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y1">Coordinate of the first anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="x2">Coordinate of the second anchor point; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y2">Coordinate of the second anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="buffer">image data</param>
            <returns>pictureShape</returns>
        </member><member name="M:SmartXLS.WorkBook.getPictureData(System.Int32)">
            <summary> picture image data to specified stream</summary>
            <param name="index">image index
            </param>
            <throws>  Exception exception </throws>
            <returns> image data
            </returns>
        </member><member name="M:SmartXLS.WorkBook.getPictureType(System.Int32)">
            <summary> get picture image data type</summary>
            <param name="index">image index
            </param>
            <returns> image type
            -1  gif
            5   jpg 
            6   png
            7   dib
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.removePicture(System.Int32)">
            <summary> remove specified index image from current sheet</summary>
            <param name="index">image index
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getPictureShape(System.Int32)">
            <summary>
            return the picture shape with specified index
            </summary>
            <param name="index"> shape index</param>
            <returns>picture</returns>
        </member><member name="M:SmartXLS.WorkBook.removePictureShape(System.Int32)">
            <summary>
            remove the picture shape with specified index
            </summary>
            <param name="index">shape index</param>
        </member><member name="M:SmartXLS.WorkBook.addChart(System.Double,System.Double,System.Double,System.Double)">
            <summary> Creates and adds a chart to the active worksheet.</summary>
            <param name="x1">Coordinate of the first anchor point of the object; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y1">Coordinate of the first anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="x2">Coordinate of the second anchor point; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y2">Coordinate of the second anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <returns> ChartShape
            </returns>
            <throws>  Exception if a parameter is invalid. </throws>
        </member><member name="M:SmartXLS.WorkBook.addChartSheet(System.Int32)">
            <summary> Creates and adds a chart to the book and make it a chart sheet.</summary>
            <param name="sheetIndex">sheet index
            </param>
            <returns> ChartShape
            </returns>
            <throws>  Exception if a parameter is invalid. </throws>
        </member><member name="M:SmartXLS.WorkBook.getChart(System.Int32)">
            <summary> Returns the first chart in the active worksheet.</summary>
            <param name="index">chart index in the sheet
            </param>
            <returns> ChartShape
            </returns>
            <throws>  Exception if not a valid chart index </throws>
        </member><member name="M:SmartXLS.WorkBook.removeChart(System.Int32)">
            <summary> remove specified index chart from current sheet</summary>
            <param name="index">chart index
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.addHyperlink(System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.Int32,System.String)">
            <summary> Adds a hyperlink to the range in the current worksheet.</summary>
            <param name="row1">the beginning row coordinate of the Hyperlink.
            </param>
            <param name="col1">the beginning column coordinate of the Hyperlink.
            </param>
            <param name="row2">the ending row coordinate of the Hyperlink.
            </param>
            <param name="col2">the ending column coordinate of the Hyperlink.
            </param>
            <param name="url">the link string.
            </param>
            <param name="type">0 - Intra doc, 1 - Absolute URL, 2 - Relative URL,
            3 - Absolute file path, 4 - Relative File path, 5 - Network File path.
            </param>
            <param name="tooltip">the tool tip string this can be null.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getHyperlink(System.Int32)">
            <summary> get the specified index hyperlink object from current sheet</summary>
            <param name="index">index in the sheet
            </param>
            <returns> hyperlink object null if not found
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getHyperlink(System.Int32,System.Int32)">
            <summary> get the specified index hyperlink object from current sheet</summary>
            <param name="row">the row coordinate of the hyperlink.
            </param>
            <param name="col">the col coordinate of the hyperlink.
            </param>
            <returns> hyperlink object null if not found
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.removeHyperlink(SmartXLS.HyperLink)">
            <summary> remove the specified hyperlink object</summary>
            <param name="hyperlink">hyperlink object
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.addComment(System.Int32,System.Int32,System.String,System.String)">
            <summary> add a comment to the cell in the current sheet</summary>
            <param name="row1">the row coordinate of the comment.
            </param>
            <param name="col1">the col coordinate of the comment.
            </param>
            <param name="text">comment text content
            </param>
            <param name="author">comment author
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getComment(System.Int32,System.Int32)">
            <summary> get the comment from the specified cell</summary>
            <param name="row1">the row coordinate of the cell.
            </param>
            <param name="col1">the col coordinate of the cell.
            </param>
            <returns> comment shape
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getComment(System.Int32)">
            <summary> Returns the specified comment in the active worksheet.</summary>
            <param name="index">Comment index in the sheet
            </param>
            <returns> CommentShape
            </returns>
            <throws>  Exception if not a valid comment index </throws>
        </member><member name="M:SmartXLS.WorkBook.removeComment(SmartXLS.CommentShape)">
            <summary> remove the specified comment from the current sheet</summary>
            <param name="comment">comment shape
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.autoFilter">
            <summary> 
            Creates an AutoFilter using this range to identify the bounds.
            If an AutoFilter already exists, it will be removed and replaced with this one.
            </summary>
            <throws>  Exception if this range cannot be filtered. </throws>
        </member><member name="M:SmartXLS.WorkBook.CreateConditionFormat">
            <summary> Creates a new instance of a conditional format in current selected sheet.</summary>
            <returns> ConditionFormat
            </returns>
        </member><member name="M:SmartXLS.WorkBook.setPaletteEntry(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> Sets a color in the workbook color palette using decimal values. 
            RGB decimal values range from 0 (no color) to 255 (maximum color intensity) for each color.
            </summary>
            <param name="entry">Zero-based index of the entry to change.
            </param>
            <param name="iRed">the decimal value of the red component of the color.
            </param>
            <param name="iGreen">the the decimal value of the green component of the color.
            </param>
            <param name="iBlue">the the decimal value of the blue component of the color.
            </param>
        </member><member name="M:SmartXLS.WorkBook.addFormControl(System.Double,System.Double,System.Double,System.Double,System.Int16)">
            <summary> Creates and adds a FormControl to the active worksheet.</summary>
            <param name="x1">Coordinate of the first anchor point of the object; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y1">Coordinate of the first anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="x2">Coordinate of the second anchor point; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y2">Coordinate of the second anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="type">Form control type  20-ComBox 11-CheckBox 18-ListBox
            </param>
            <returns> FormControlShape Form Control Object
            </returns>
            <throws>  Exception if a parameter is invalid. </throws>
        </member><member name="M:SmartXLS.WorkBook.addAutoShape(System.Double,System.Double,System.Double,System.Double,System.Int16)">
            <summary> Creates and adds a AutoShape to the active worksheet.</summary>
            <param name="x1">Coordinate of the first anchor point of the object; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y1">Coordinate of the first anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="x2">Coordinate of the second anchor point; measured in columns from the left edge of the worksheet.
            </param>
            <param name="y2">Coordinate of the second anchor point; measured in rows from the top edge of the worksheet.
            </param>
            <param name="type">Form control type 0-Rectangle 1-Oval 2-Line 3-TextBox
            </param>
            <returns> FormControlShape Form Control Object
            </returns>
            <throws>  Exception if a parameter is invalid. </throws>
        </member><member name="M:SmartXLS.WorkBook.getAutoShape(System.Int32)">
            <summary>
            get the auto shape with index
            </summary>
            <param name="index">shape index</param>
            <returns>autoshape</returns>
        </member><member name="M:SmartXLS.WorkBook.removeAutoShape(System.Int32)">
            <summary>
            remove the autoshape with index
            </summary>
            <param name="index">shape index</param>
        </member><member name="M:SmartXLS.WorkBook.setCSVString(System.String)">
            <summary>
            import the csv string to current sheet
            </summary>
            <param name="csv">csv string</param>
        </member><member name="M:SmartXLS.WorkBook.readCSV(System.String)">
            <summary>
            load csv content from csv file
            </summary>
            <param name="fileName">csv file</param>
        </member><member name="M:SmartXLS.WorkBook.readCSV(System.IO.Stream)">
            <summary>
            load csv from stream
            </summary>
            <param name="inputstream"> stream</param>
        </member><member name="M:SmartXLS.WorkBook.read(System.IO.Stream)">
            <summary> Reads workbook from the specified input stream.</summary>
            <param name="inputstream">the input stream to read from.
            </param>
            <returns> result </returns>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.read(System.IO.Stream,System.String)">
            <summary> Reads workbook from the specified input stream.</summary>
            <param name="inputstream">the input stream to read from.
            </param>
            <param name="password">The password to use when opening a password-protected file.
            </param>
            <returns> result
            </returns>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.read(System.String)">
            <summary> Reads workbook from the specified file.</summary>
            <param name="fileName">the path and filename of the workbook.
            </param>
            <returns> result
            </returns>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.read(System.String,System.String)">
            <summary> Reads workbook from the specified file.</summary>
            <param name="fileName">the path and filename of the workbook.
            </param>
            <param name="password">The password to use when opening a password-protected file.
            </param>
            <returns> result
            </returns>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.write(System.String)">
            <summary> Saves the workbook to a file.</summary>
            <param name="fileName">path describing name and location of the file.
            </param>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.write(System.String,System.String)">
            <summary> Saves the workbook to a file.</summary>
            <param name="fileName">path describing name and location of the file.
            </param>
            <param name="password">The password to use when opening a password-protected file.
            </param>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.writeCSV(System.String)">
            <summary> Saves the workbook to a file.</summary>
            <param name="fileName">path describing name and location of the file.
            </param>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.writeCSV(System.String,System.Char,System.Boolean,System.Boolean)">
            <summary>
            Saves the workbook to a file in the tabbed text.
            </summary>
            <param name="fileName"></param>
            <param name="seperator">csv seprator char,{',', ';', '\t'}</param>
            <param name="unicode">if true encode text in unicode</param>
            <param name="bValue">if true text without formatting</param>
        </member><member name="M:SmartXLS.WorkBook.writeCSV(System.IO.Stream)">
            <summary> Saves the workbook to the specified output stream.</summary>
            <param name="outputstream">outputstream that describes where to save the file.
            </param>
        </member><member name="M:SmartXLS.WorkBook.writeCSV(System.IO.Stream,System.Char,System.Boolean,System.Boolean)">
            <summary>
            Saves the workbook to output stream in the tabbed text.
            </summary>
            <param name="outputstream">outputstream that describes where to save the file.</param>
            <param name="seperator">csv seprator char,{',', ';', '\t'}</param>
            <param name="unicode">if true encode text in unicode</param>
            <param name="bValue">if true text without formatting</param>
        </member><member name="M:SmartXLS.WorkBook.toCSV(System.Char)">
            <summary>
            get the sheet data in csv string
            </summary>
            <param name="seperator">csv seprator char,{',', ';', '\t'}</param>
            <returns>csv content</returns>
        </member><member name="M:SmartXLS.WorkBook.toCSV(System.Char,System.Boolean,System.Boolean)">
            <summary>
            get the sheet data in csv string
            </summary>
            <param name="seperator">csv seprator char,{',', ';', '\t'}</param>
            <param name="unicode">if true encode text in unicode</param>
            <param name="bValue">if true text without formatting</param>
        </member><member name="M:SmartXLS.WorkBook.write(System.IO.Stream)">
            <summary> Saves the workbook to the specified output stream.</summary>
            <param name="outputstream">outputstream that describes where to save the file.
            </param>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.write(System.IO.Stream,System.String)">
            <summary> Saves the workbook to the specified output stream.</summary>
            <param name="outputstream">outputstream that describes where to save the file.
            </param>
            <param name="password">The password to use when opening a password-protected file.
            </param>
            <throws>  Exception if an error occurs </throws>
        </member><member name="M:SmartXLS.WorkBook.setTextSelection(System.Int32,System.Int32)">
            <summary> Sets text selection for the selected cell</summary>
            <param name="iStart">starting positon of selection.
            </param>
            <param name="iEnd">ending positon of selection.
            </param>
            <throws>  Exception if the positions are invalid, selected cell is not text or more than one cell is selected. </throws>
        </member><member name="M:SmartXLS.WorkBook.getPivotModel">
            <summary> 
            Returns a reference to the Pivot model.
            </summary>
            <returns> return the Pivot model.
            </returns>
        </member><member name="M:SmartXLS.WorkBook.getPivotRange(System.Int32,System.Int32)">
            <summary> 
            Returns the Pivot range from current selected sheet.
            </summary>
            <param name="row">cell row index. </param>
            <param name="col">cell col index. </param>
            <returns> return the Pivot range. </returns>
        </member><member name="M:SmartXLS.WorkBook.setActiveCell(System.Int32,System.Int32)">
            <summary> Sets the row and column coordinates of the active cell.</summary>
            <param name="row">the active cell row coordinate.
            </param>
            <param name="col">the active cell column coordinate.
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.getLastColForRow(System.Int32)">
            <summary> Returns the number of the last occupied column in the specified row.
            this method returns the last column that is not empty, including cells that contain only formatting.
            -1 is returned for a row with no cells.
            </summary>
            <param name="row">the specified row.
            </param>
            <returns> the last occupied column in the row or -1 for an empty row.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.setPrintPaperSize(System.Int32,System.Int32)">
            <summary> Sets the size of the paper in twips (1/20th of a point)</summary>
            <param name="width">width of paper in twips.
            </param>
            <param name="height">height of paper in twips.
            </param>
            <throws>  Exception if a parameter is invalid </throws>
        </member><member name="M:SmartXLS.WorkBook.CreateDataValidation">
            <summary> Creates a new instance of a data validation object in current selected sheet.</summary>
            <returns> DVRecord
            </returns>
        </member><member name="M:SmartXLS.WorkBook.getValidation(System.Int32,System.Int32)">
            <summary> get the validation object from specified cell</summary>
            <param name="row1"> row number
            </param>
            <param name="col1">column number
            </param>
            <returns> DVRecord
            </returns>
        </member><member name="M:SmartXLS.WorkBook.ExportDataTable">
             <summary>
            export current sheet data into datatable 
             </summary>
             <returns>datatable which filled with current sheet data</returns>
        </member><member name="M:SmartXLS.WorkBook.ExportDataTable(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Exports worksheet data into a DataTable
            </summary>
            <param name="firstRow">
            Row of the first cell from where DataTable should be exported
            </param>
            <param name="firstColumn">
            Column of the first cell from where DataTable should be exported
            </param>
            <param name="maxRows">Maximum number of rows to export</param>
            <param name="maxColumns">Maximum number of columns to export</param>
            <returns>DataTable with worksheet data</returns>
        </member><member name="M:SmartXLS.WorkBook.ExportDataTable(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Exports worksheet data into a DataTable
            </summary>
            <param name="firstRow">
            Row of the first cell from where DataTable should be exported
            </param>
            <param name="firstColumn">
            Column of the first cell from where DataTable should be exported
            </param>
            <param name="maxRows">Maximum number of rows to export</param>
            <param name="maxColumns">Maximum number of columns to export</param>
            <param name="fah">First row data as column name</param>
            <returns>DataTable with worksheet data</returns>
        </member><member name="M:SmartXLS.WorkBook.ExportDataTable(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Exports worksheet data into a DataTable
            </summary>
            <param name="firstRow">
            Row of the first cell from where DataTable should be exported
            </param>
            <param name="firstColumn">
            Column of the first cell from where DataTable should be exported
            </param>
            <param name="maxRows">Maximum number of rows to export</param>
            <param name="maxColumns">Maximum number of columns to export</param>
            <param name="fah">First row data as column name</param>
            <param name="checkDate">check column is datetime type</param>
            <returns>DataTable with worksheet data</returns>
        </member><member name="M:SmartXLS.WorkBook.ImportDataTable(System.Data.DataTable,System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Imports data from a DataTable into worksheet
            </summary>
            <param name="dataTable">DataTable with desired data</param>
            <param name="isFieldNameShown">TRUE if column names must also be imported</param>
            <param name="firstRow">
            Row of the first cell where DataTable should be imported
            </param>
            <param name="firstColumn">
            Column of the first cell where DataTable should be imported
            </param>
            <param name="maxRows">Maximum number of rows to import</param>
            <param name="maxColumns">Maximum number of columns to import</param>
            <returns>Number of imported rows</returns>
        </member><member name="M:SmartXLS.WorkBook.ImportDataTable(System.Data.DataTable,System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32,System.Data.DataColumn[],System.Boolean)">
            <summary>
            Imports data from a DataTable into worksheet
            </summary>
            <param name="dataTable">DataTable with desired data</param>
            <param name="isFieldNameShown">TRUE if column names must also be imported</param>
            <param name="firstRow">
            Row of the first cell where DataTable should be imported
            </param>
            <param name="firstColumn">
            Column of the first cell where DataTable should be imported
            </param>
            <param name="maxRows">Maximum number of rows to import</param>
            <param name="maxColumns">Maximum number of columns to import</param>
            <param name="arrColumns">Array of columns to import.</param>
            <param name="bPreserveTypes">Indicates whether to preserve column types.</param>
            <returns>Number of imported rows</returns>
        </member><member name="M:SmartXLS.WorkBook.ConvertNumberToDateTime(System.Double)">
            <summary>
            convert date numeric value(in Excel) to .Net datetime object
            </summary>
            <param name="dNumber">number value to present Excel date</param>
            <returns></returns>
        </member><member name="M:SmartXLS.WorkBook.ConvertDateTimeToNumber(System.DateTime)">
            <summary>
            convert the .net datetime object to Excel date value in number
            </summary>
            <param name="dateTime">Datetime object</param>
            <returns>number in Excel date value</returns>
        </member><member name="M:SmartXLS.WorkBook.setWorkbookName(System.String)">
            <summary>
            workbook name to be used for group workbooks
            </summary>
            <param name="name">name</param>
        </member><member name="M:SmartXLS.WorkBook.setGroup(System.String)">
            <summary>
            group name
            </summary>
            <param name="name">name</param>
        </member><member name="M:SmartXLS.WorkBook.getFormulaRange(System.String)">
            <summary>
            formula string to rangearea object
            </summary>
            <param name="formula">formula text</param>
            <returns>rangearea</returns>
        </member><member name="M:SmartXLS.WorkBook.formatRCNr(System.Int32,System.Int32,System.Boolean)">
            <summary> Returns a string containing a formatted row and column reference.</summary>
            <param name="row">the row number of the reference.
            </param>
            <param name="col">the column number of the reference.
            </param>
            <param name="doAbsolute">boolean. if true, uses absolute references. if false, uses relative references.
            </param>
            <returns> the string containing the formatted reference.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.WorkBook.readXLSX(System.String)">
            <summary>
            Reads a worksheet or workbook from excel xlsx file.
            </summary>
            <param name="filename">the path and filename of the excel xlsx file.</param>
        </member><member name="M:SmartXLS.WorkBook.readXLSX(System.IO.Stream)">
            <summary>
            Reads the workbook from the stream in the 2007 format.
            </summary>
            <param name="stream">inputstream</param>
        </member><member name="M:SmartXLS.WorkBook.readXML(System.String)">
            <summary>
            Reads the workbook from the file in SpreadsheetXML format.
            </summary>
            <param name="filename">input file</param>
        </member><member name="M:SmartXLS.WorkBook.readXML(System.IO.Stream)">
            <summary>
            Reads the workbook from the stream in SpreadsheetXML format.
            </summary>
            <param name="stream">inputstream</param>
        </member><member name="M:SmartXLS.WorkBook.readXLSX(System.String,System.String)">
            <summary>
            Reads a worksheet or workbook from excel xlsx file.
            </summary>
            <param name="filename">the path and filename of the excel xlsx file.</param>
            <param name="password">The password to use when opening a password-protected file.
            </param>
        </member><member name="M:SmartXLS.WorkBook.readXLSX(System.IO.Stream,System.String)">
            <summary>
            Reads a worksheet or workbook from excel xlsx file.
            </summary>
            <param name="stream">file stream</param>
            <param name="password">open password</param>
        </member><member name="M:SmartXLS.WorkBook.writeXLSX(System.String)">
            <summary>
            write workbook to specified excel file in xlsx format.
            </summary>
            <param name="filename">the path and filename of the excel xlsx file.</param>
        </member><member name="M:SmartXLS.WorkBook.writeXLSX(System.String,System.String)">
            <summary>
            write workbook to specified excel file in xlsx format.
            </summary>
            <param name="filename">the path and filename of the excel xlsx file.</param>
            <param name="password">The password to use when opening a password-protected file.
            </param>
        </member><member name="M:SmartXLS.WorkBook.writeXLSX(System.IO.Stream)">
            <summary>
            write workbook to specified outputstream in xlsx format.
            </summary>
            <param name="stream">outputstream.</param>
        </member><member name="M:SmartXLS.WorkBook.writeXLSX(System.IO.Stream,System.String)">
            <summary>
            write workbook to specified outputstream in xlsx format.
            </summary>
            <param name="stream">outputstream.</param>
            <param name="password">The password to use when opening a password-protected file.
            </param>
        </member><member name="M:SmartXLS.WorkBook.writeXML(System.String)">
            <summary>
            write workbook to specified excel file in spreadsheet xml format.
            </summary>
            <param name="filename">the path and filename of the excel spreadsheet xml file.</param>
        </member><member name="M:SmartXLS.WorkBook.writeXML(System.IO.Stream)">
            <summary>
            write workbook to specified outputstream in spreadsheet xml format.
            </summary>
            <param name="stream">outputstream</param>
        </member><member name="M:SmartXLS.WorkBook.resetRowInfo(System.Int32,System.Int32)">
            <summary>
            reset the selected rows to default row height
            </summary>
            <param name="row1">start row</param>
            <param name="row2">end row</param>
        </member><member name="M:SmartXLS.WorkBook.getFormattedText(System.Int32,System.Int32)">
            <summary>
            Returns the formatted text value of the specified cell.
            </summary>
            <param name="row">row number.</param>
            <param name="col">column number.</param>
        </member><member name="M:SmartXLS.WorkBook.setEntry(System.Int32,System.Int32,System.String)">
            <summary>
            Sets the value to the specified cell of current selected worksheets.
            </summary>
            <param name="row">row number.</param>
            <param name="col">column number.</param>
        </member><member name="M:SmartXLS.WorkBook.sort(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Int32,System.Int32,System.Int32)">
            <summary>
            Specified a range of data to be sorted by three keys.
            If by rows,each row of data in the range is considered as a record.
            If by columns,each column in the range is considered as a record.
            keys are the number of the row/column,0-indicates no key.
            </summary>
            <param name="row1">first row.</param>
            <param name="col1">first column.</param>
            <param name="row2">last row.</param>
            <param name="col2">last column.</param>
            <param name="byRows">if ture,data is sorted by row.</param>
            <param name="key1">the primary key.</param>
            <param name="key2">the second key.</param>
            <param name="key3">the last key.</param>
        </member><member name="M:SmartXLS.WorkBook.sort(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Int32[])">
            <summary>
            Specified a range of data to be sorted by the keys.
            If by rows,each row of data in the range is considered as a record.
            If by columns,each column in the range is considered as a record.
            </summary>
            <param name="row1">first row.</param>
            <param name="col1">first column.</param>
            <param name="row2">last row.</param>
            <param name="col2">last column.</param>
            <param name="byRows">if ture,data is sorted by row.</param>
            <param name="keys">the keys to use to sort the data.
            Positive numbers (1..n) indicate an ascending sort key.
            Negative numbers (-1..-n) indicate an descending sort key.
            keys are the number of the row/column,0-indicates no key.
             </param>
        </member><member name="M:SmartXLS.WorkBook.subTotal(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32[])">
            <summary>
            Specified a range of data to be subtotaled.
            </summary>
            <param name="row1">first row.</param>
            <param name="col1">first column.</param>
            <param name="row2">last row.</param>
            <param name="col2">last column.</param>
            <param name="changeCol">detect each change in the column.</param>
            <param name="subs">the columns used to subtotal the data.</param>
        </member><member name="M:SmartXLS.WorkBook.Lock">
            <summary>
            lock the workbook to synchonize the workbook,most for calculation
            </summary>
        </member><member name="M:SmartXLS.WorkBook.unLock">
            <summary>
            unlock this workbook to let other workbook to continue working
            </summary>
        </member><member name="M:SmartXLS.WorkBook.setBookProtection(System.String,System.Boolean)">
            <summary>
            Sets whether protection is enabled for this workbook.
            </summary>
            <param name="password">The protection password. this may be null if no password is to be used.</param>
            <param name="isProtected">Indicates whether protection is to be enabled.</param>
        </member><member name="M:SmartXLS.WorkBook.setSheetProtection(System.Int32,System.String,System.Boolean)">
            <summary>
            Sets whether protection is enabled for the specified sheet.
            </summary>
            <param name="sheetIndex">The sheet index number to protect or unprotect.</param>
            <param name="password">The protection password. this may be null if no password is to be used.</param>
            <param name="isProtected">Indicates whether protection is to be enabled.</param>
        </member><member name="M:SmartXLS.WorkBook.CopySheetFromBook(SmartXLS.WorkBook,System.Int32,System.Int32)">
            <summary>
            copy the specified sheet from source workbook to destination workbook.
            </summary>
            <param name="m_SrcWorkbook">source workbook</param>
            <param name="srcSheetIndex">the index number of the sheet,the sheets indexed from left to right beginning with 0</param>
            <param name="destSheetIndex">the index number of the sheet,the sheets indexed from left to right beginning with 0</param>
        </member><member name="M:SmartXLS.WorkBook.find(System.Int32,System.Int32,System.String)">
            <summary>
            find the first cell which match the search string from the specified cell.
            the searched area is the range area from the cell (row,col) to end cell(lastrow,lastcol)
            </summary>
            <param name="row1">cell row index</param>
            <param name="col1">cell column index</param>
            <param name="text">search string</param>
            <returns>rangeArea which indicate the searched cell address,null if not found.</returns>
        </member><member name="M:SmartXLS.WorkBook.find(System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            find the first cell which match the search string from the specified cell.
            </summary>
            <param name="row1">cell row index</param>
            <param name="col1">cell column index</param>
            <param name="text">search string</param>
            <param name="matchEntire">if search string match entire or be contained</param>
            <returns></returns>
        </member><member name="M:SmartXLS.WorkBook.replace(System.String,System.String)">
            <summary>
            find all the range cells with specified search string in current selected sheet,replace with the other string.
            </summary>
            <param name="find">search string</param>
            <param name="replace">replace string</param>
        </member><member name="M:SmartXLS.WorkBook.replace(System.String,System.String,System.Boolean)">
            <summary>
            find all the range cells with specified search string in current selected sheet,replace with the other string.
            </summary>
            <param name="find">search string</param>
            <param name="replace">replace string</param>
            <param name="matchEntire">if search string match entire or be contained</param>
        </member><member name="M:SmartXLS.WorkBook.findSheetByName(System.String)">
            <summary>
            find the sheet index number by the sheet name.
            </summary>
            <param name="sheetName">sheet name</param>
            <returns>sheet index,-1 if no sheet found</returns>
        </member><member name="M:SmartXLS.WorkBook.setPrintHeaderImage(System.Int32,System.Drawing.Image)">
            <summary>
            set the print header image
            </summary>
            <param name="pos">
            0-left 1-center 2- right
            </param>
            <param name="image">System.Drawing.Image object</param>
        </member><member name="M:SmartXLS.WorkBook.setPrintFooterImage(System.Int32,System.Drawing.Image)">
            <summary>
            set the print footer image
            </summary>
            <param name="pos">
            0-left 1-center 2- right
            </param>
            <param name="image">System.Drawing.Image object</param>
        </member><member name="M:SmartXLS.WorkBook.clearCell(System.Int32,System.Int32)">
            <summary>
            set cell value to empty,do nothing if cell is null
            </summary>
            <param name="row">cell row index</param>
            <param name="col">cell column index</param>
        </member><member name="M:SmartXLS.WorkBook.ImportArray(System.Object[0:,0:],System.Int32,System.Int32)">
            <summary>
            import object array to the range begin from the row,col 
            </summary>
            <param name="arrObject">array of objects</param>
            <param name="firstRow">first row</param>
            <param name="firstColumn">first col</param>
        </member><member name="M:SmartXLS.WorkBook.getSheetProperty(SmartXLS.SheetVarType)">
            <summary>
            access the sheet internal property by the variant type
            </summary>
            <param name="varType">var type</param>
            <returns>property object</returns>
        </member><member name="M:SmartXLS.WorkBook.Dispose">
            <summary>
            default implement for dispose 
            </summary>
        </member><member name="P:SmartXLS.WorkBook.CSVSeparator">
            <summary>
            csv separator char,used in importing/exporting
            </summary>
        </member><member name="P:SmartXLS.WorkBook.Sheet">
            <summary> the current selected worksheet index.</summary>
        </member><member name="P:SmartXLS.WorkBook.NumSheets">
            <summary> the number of worksheets in the current workbook.
            Worksheets are added to or deleted from the end of the current list of worksheets.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.DefinedNameCount">
            <summary> the number counts of defined names</summary>
        </member><member name="P:SmartXLS.WorkBook.ViewScale">
            <summary>
            the current display scale for the selected sheet.
            an integer ranging from 10 to 400. 100 percent is normal display.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.CommentCount">
            <summary> the Comment counts in the active worksheet.</summary>
        </member><member name="P:SmartXLS.WorkBook.ConditionalFormats">
            <summary> Returns the conditional formats for the current selection.
            this method returns an array of conditonal formats for the current selection.
            if all of the cells in the current selection do not have the same conditional formats, an array of one conditional format is returned and this conditional format has the type and operator set to eTypeNone and eOperatorNone.
            if there are no conditional formats on any of the cells, an array of length zero is returned.
            The conditional formats returned are copies of the actual conditional formats, and may be modified for passing back to setConditionalFormats.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.LastCol">
            <summary> Returns the number of the last column.
            this method returns the last column that is not empty, including cells that contain only formatting.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.LastRow">
            <summary> Returns the number of the last row that is not empty, including cells that contain only formatting.
            The value -1 is returned if the sheet contains no cells.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.PrintHeaderMargin">
            <summary> the page header margin used during printing.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintFooterMargin">
            <summary> the page footer margin</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintTopMargin">
            <summary> the top print margin in inches.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintBottomMargin">
            <summary> the bottom print margin in inches.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintLeftMargin">
            <summary> the left print margin in inches.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintRightMargin">
            <summary> the right print margin in inches.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintPaperHeight">
            <summary> Returns the height of the paper in twips.
            Twips are 1/1440 of an inch.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.PrintPaperWidth">
            <summary> Returns the width of the paper in twips.
            Twips are 1/1440 of an inch.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.PrintScale">
            <summary> the scale factor for the current worksheet.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintHRes">
            <summary> the Horizontal DPI for the current worksheet.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintVRes">
            <summary> the Vertical DPI for the current worksheet.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintScaleFitHPages">
            <summary> the number of horizontal pages to which the print job is fit.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintScaleFitVPages">
            <summary> the number of vertical pages to which the print job is fit.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintAutoPageNumbering">
            <summary> automatic page numbering is enabled.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintStartPageNumber">
            <summary> the number displayed on the first page printed.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintTitles">
            <summary> the print titles to be printed at the top of each page</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintPaperSize">
            <summary> the print paper size</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintRowColHeading">
            <summary>whether row/col headings are printed for the current sheet.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintGridLines">
            <summary> whether grid lines are printed for the current sheet.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintVCenter">
            <summary> whether the current sheet is centered vertically when printed.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintHCenter">
            <summary> the current sheet is centered horizontally when printed.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintLandscape">
            <summary> true if the workbook is printed with a landscape orientation.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintLeftToRight">
            <summary> the current sheet is printed left to right then top to bottom, or top to bottom then left to right.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintNoColor">
            <summary> all workbook colors are converted to black and white, and all patterns are removed when printing.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintNumberOfCopies">
            <summary> the number copies to print</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintArea">
            <summary> the current print area.</summary>
        </member><member name="P:SmartXLS.WorkBook.ShowGridLines">
            <summary> true if grid lines are shown. </summary>
        </member><member name="P:SmartXLS.WorkBook.ShowRowColHeaders">
            <summary> true if row and column headers are shown. </summary>
        </member><member name="P:SmartXLS.WorkBook.DataValidation">
            <summary> Sets the specified data validations for the current selection.</summary>
        </member><member name="P:SmartXLS.WorkBook.AutoCalc">
            <summary>
            the flag indicating whether automatic recalc is on
            </summary>
        </member><member name="P:SmartXLS.WorkBook.ForceCalcFlag">
            <summary>
            force calculation in startup,only applied to ooxml
            </summary>
        </member><member name="P:SmartXLS.WorkBook.MaxIterations">
            <summary>
            the maximum iterations
            </summary>
        </member><member name="P:SmartXLS.WorkBook.MaxIterationChange">
            <summary>
            the maximun iteration change value
            </summary>
        </member><member name="P:SmartXLS.WorkBook.IterationEnabled">
            <summary>
            enable the iteration for the calculation
            </summary>
        </member><member name="P:SmartXLS.WorkBook.ShowTabs">
            <summary>
            the visibility or position of the sheet name tabs on a workbook.
            the display status value which is one of the following:
            TabsOff 	 - hide tabs<see cref="F:SmartXLS.WorkBook.TabsOff" />
            TabsBottom 	 - tabs on bottom<see cref="F:SmartXLS.WorkBook.TabsBottom" />
            TabsTop 	 - tabs on top<see cref="F:SmartXLS.WorkBook.TabsTop" />
            </summary>
        </member><member name="P:SmartXLS.WorkBook.ShowHScrollBar">
            <summary>
            the mode for the horizontal scroll bar.
            ShowOff  - never show.<see cref="F:SmartXLS.WorkBook.ShowOff" />
            ShowOn 	 - always show.<see cref="F:SmartXLS.WorkBook.ShowOn" />
            ShowAutomatic - show when appropriate.<see cref="F:SmartXLS.WorkBook.ShowAutomatic" />
            </summary>
        </member><member name="P:SmartXLS.WorkBook.ShowVScrollBar">
            <summary>
            the mode for the vertical scroll bar.
            ShowOff  - never show.<see cref="F:SmartXLS.WorkBook.ShowOff" />
            ShowOn 	 - always show.<see cref="F:SmartXLS.WorkBook.ShowOn" />
            ShowAutomatic - show when appropriate.<see cref="F:SmartXLS.WorkBook.ShowAutomatic" />
            </summary>
        </member><member name="P:SmartXLS.WorkBook.ShowZeroValues">
            <summary>
            whether zero value cells are displayed.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.SummaryRowsBeforeDetail">
            <summary>
            whether row outline summaries come before row detail information.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.SheetTabColor">
            <summary>
            sheet tab color
            </summary>
        </member><member name="P:SmartXLS.WorkBook.SummaryColsBeforeDetail">
            <summary>
            whether col outline summaries come before col detail information.
            </summary>
        </member><member name="P:SmartXLS.WorkBook.PrintHeader">
            <summary> Returns the page header printed at the top of each page.</summary>
        </member><member name="P:SmartXLS.WorkBook.PrintFooter">
            <summary> string that contains the codes and text that make up the current footer</summary>
        </member><member name="P:SmartXLS.WorkBook.SheetViewType">
            <summary> the sheet view type:
            0 - Normal view<see cref="F:SmartXLS.WorkBook.SheetNormalView" />
            1 - Page Layout View<see cref="F:SmartXLS.WorkBook.SheetPageLayoutView" />
            2 - Page break preview<see cref="F:SmartXLS.WorkBook.SheetPageBreakPreView" />
            </summary>
        </member><member name="P:SmartXLS.WorkBook.RightToLeft">
            <summary>
            whether show sheet text as right to left direction
            </summary>
        </member><member name="P:SmartXLS.WorkBook.CalcOnSave">
            <summary>
            whether the workbook is recalc before save,default it is true to recalc before save
            </summary>
        </member><member name="P:SmartXLS.WorkBook.DocumentProperty">
            <summary>
            wrapper for document properties,for both build-in properties or custom properties
            </summary>
        </member><member name="P:SmartXLS.WorkBook.useDefaultCompressor">
            <summary>
            compressor for xlsx,false to use the best level
            </summary>
        </member><member name="P:SmartXLS.WorkBook.PrecisionAsDisplayed">
            <summary>
            set this falg to enable the 'precision as displayed' feature in Excel WorkBook and calculation engine
            </summary>
        </member><member name="P:SmartXLS.WorkBook.InlineCellTextMode">
            <summary>
            this will let cell text saved in place and not build the sst(share string table),for huge data workbook only
            </summary>
        </member><member name="P:SmartXLS.WorkBook.BiffDocPropsParsed">
            <summary>
            flag to enble the xls workbook property parsing,default not parsing
            </summary>
        </member><member name="P:SmartXLS.WorkBook.SheetCustomProperties">
            <summary>
            return the worksheet custom properties
            </summary>
        </member><member name="P:SmartXLS.WorkBook.OleObjects">
            <summary>
            return the current seleted worksheet ole objects
            </summary>
        </member><member name="P:SmartXLS.WorkBook.TopLeftCell">
            <summary>
            return the top left cell in the current seleted worksheet
            </summary>
        </member><member name="T:IThreadRunnable">
            <summary>
            This interface should be implemented by any class whose instances are intended 
            to be executed by a thread.
            </summary>
        </member><member name="M:IThreadRunnable.Run">
            <summary>
            This method has to be implemented in order that starting of the thread causes the object's 
            run method to be called in that separately executing thread.
            </summary>
        </member><member name="T:SmartXLS.RtfTags">
            <summary>
            Elements order is very important.
            </summary>
        </member><member name="T:SmartXLS.ShapePos">
            <summary>
            the shape placement position
            </summary>
        </member><member name="P:SmartXLS.ShapePos.X1">
            <summary>
            left position
            left position
            The whole part of the value is the column containing the left side, and the fractional part of the value is the position of the left side within that column.
            </summary>
        </member><member name="P:SmartXLS.ShapePos.X2">
            <summary>
            right position
            The whole part of the value is the column containing the right side, and the fractional part of the value is the position of the right side within that column.
            </summary>
        </member><member name="P:SmartXLS.ShapePos.Y1">
            <summary>
            top position
            The whole part of the value is the row containing the top side, and the fractional part of the value is the position of the top side within that row.
            </summary>
        </member><member name="P:SmartXLS.ShapePos.Y2">
            <summary>
            bottom position
            The whole part of the value is the row containing the bottom side, and the fractional part of the value is the position of the bottom side within that row.
            </summary>
        </member><member name="T:SmartXLS.BookPivotField">
            <summary>
            represent the pivot field
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcSum">
            <summary>
            calculated field with type sum
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcCount">
            <summary>
            calculated field with type count
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcAverage">
            <summary>
            calculated field with type average
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcMax">
            <summary>
            calculated field with type max
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcMin">
            <summary>
            calculated field with type min
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcProduct">
            <summary>
            calculated field with type product
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcCountNums">
            <summary>
            calculated field with type count nums
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcStdDev">
            <summary>
            calculated field with type stddev
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcStdDevp">
            <summary>
            calculated field with type stddevp
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcVar">
            <summary>
            calculated field with type var
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SummarizeCalcVarp">
            <summary>
            calculated field with type varp
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SubTotalTypeNone">
            <summary>
            subtotal field with type none
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SubTotalTypeAutomatic">
            <summary>
            subtotal field with type auto
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SubTotalTypeCustom">
            <summary>
            subtotal field with type custom
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SortAuto">
            <summary>
            field sort auto
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SortAscend">
            <summary>
            field sort asc
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SortDescend">
            <summary>
            field sort dsc
            </summary>
        </member><member name="F:SmartXLS.BookPivotField.SortManual">
            <summary>
            field sort manual
            </summary>
        </member><member name="M:SmartXLS.BookPivotField.canAddToArea(SmartXLS.BookPivotArea)">
            <summary>
            Indicates if this field can be added to the specified area.
            </summary>
            <param name="area">The desired area for adding the field.</param>
            <returns>if this field can be added</returns>
        </member><member name="M:SmartXLS.BookPivotField.isSummarizeField">
            <summary>
            Indicates if this field is a summary field
            </summary>
            <returns>true if field is a summary field</returns>
        </member><member name="M:SmartXLS.BookPivotField.setShowDataAs(System.Int32)">
            <summary>
            set the ShowDataAs value for the summary field
            </summary>
            <param name="type">0-Normal 1-DifferenceFrom 2-PercentOf 3-PercentDifferenceFrom 4-RunningTotalIn 5-PercentOfRow 6-PercentOfColumn 7-PercentOfTotal 8-Index</param>
        </member><member name="M:SmartXLS.BookPivotField.setLayout(System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            set the layout of the pivot field
            </summary>
            <param name="compact">whether the application will display fields compactly in the sheet on which this PivotTable resides</param>
            <param name="outline">whether the items in this field should be shown in Outline form</param>
            <param name="subtotalTop">whether to display subtotals at the top of the group</param>
        </member><member name="M:SmartXLS.BookPivotField.setShowDetail(System.Boolean)">
            <summary>
            set whether the details are hidden for this field
            </summary>
            <param name="show">whether the details are shown for this field</param>
        </member><member name="M:SmartXLS.BookPivotField.setFieldItemHidden(System.Int32,System.Boolean)">
            <summary>
            set the field item hidden or shown
            exception when data field is applied or invalid parameter
            </summary>
            <param name="index">item index</param>
            <param name="isVisible">visibility</param>
        </member><member name="M:SmartXLS.BookPivotField.isFiedItemHidden(System.Int32)">
            <summary>
            is the field item hidden or shown
            </summary>
            <param name="index">item index</param>
            <returns>item hidden or shown status</returns>
        </member><member name="M:SmartXLS.BookPivotField.addCalcItem(System.String,System.String)">
            <summary>
            add a calculated item to the field list items
            </summary>
            <param name="name">item name</param>
            <param name="formula">item formula content</param>
        </member><member name="P:SmartXLS.BookPivotField.NumberFormatting">
            <summary>
            custom formatting for field area in the pivot table
            </summary>
        </member><member name="T:SmartXLS.BookPivotArea">
            <summary>
            represent the pivot area
            </summary>
        </member><member name="M:SmartXLS.BookPivotArea.addField(SmartXLS.BookPivotField)">
            <summary>
            Adds the field to this area and positions the field after any existing fields.
            </summary>
            <param name="field">The field that should be added to the area.</param>
        </member><member name="M:SmartXLS.BookPivotArea.getField(System.Int32)">
            <summary>
            Returns the PivotRange field at the specified index within this area.
            </summary>
            <param name="i">The index of the desired field.</param>
            <returns>Field located at specified index.</returns>
        </member><member name="M:SmartXLS.BookPivotArea.getFieldCount">
            <summary>
            Returns the number of fields in this area.
            </summary>
            <returns>The number of fields in this area.</returns>
        </member><member name="T:SmartXLS.RtfTextWriter">
            <summary>
            Rtf text writer used for converting rtf string into rtf format.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.DEF_COLOR_FORMAT">
            <summary>
            0 - red component (0-255),
            1 - green,
            2 - blue.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.DEF_FONT">
            <summary>
            0 - font index,
            1 - charset,
            2 - font name.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.DEF_FONT_ATTRIBUTE">
            <summary>
            Font attribute.
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.#ctor">
            <summary>
            
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.#ctor(System.Boolean)">
            <summary>
            
            </summary>
            <param name="enableFormatting"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.#ctor(System.IO.TextWriter)">
            <summary>
            
            </summary>
            <param name="underlyingWriter"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.#ctor(System.IO.TextWriter,System.Boolean)">
            <summary>
            
            </summary>
            <param name="underlyingWriter"></param>
            <param name="enableFormatting"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.AddColor(System.Drawing.Color)">
            <summary>
            Adds color to the colors table.
            </summary>
            <param name="color"></param>
            <returns></returns>
        </member><member name="M:SmartXLS.RtfTextWriter.AddFont(System.Drawing.Font)">
            <summary>
            Adds new font to the collection.
            </summary>
            <param name="font">Font to add.</param>
            <returns>Index of the font.</returns>
        </member><member name="M:SmartXLS.RtfTextWriter.OutputTabs">
            <summary>
            
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.ToString">
            <summary>
            Returns string that implement current object.
            </summary>
            <returns>Returns string that implement current object.</returns>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Boolean)">
            <summary>
            Writes a bool to the text stream.
            </summary>
            <param name="value">Bool param to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Char)">
            <summary>
            Writes a character to the text stream.
            </summary>
            <param name="value">Char value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Double)">
            <summary>
            Writes a double to the text stream.
            </summary>
            <param name="value">Double param to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Int32)">
            <summary>
            Writes a int value to the text stream.
            </summary>
            <param name="value">Int value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Int64)">
            <summary>
            Writes a long value to the text stream.
            </summary>
            <param name="value">Long value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Object)">
            <summary>
            Writes a object to the text stream.
            </summary>
            <param name="value">Object value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Char[])">
            <summary>
            Writes a array of characters to the text stream.
            </summary>
            <param name="buffer">Array param to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Single)">
            <summary>
            Writes a float value to the text stream.
            </summary>
            <param name="value">Float value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.String)">
            <summary>
            Writes a string to the text stream.
            </summary>
            <param name="s">Streang to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.UInt32)">
            <summary>
            Writes a uint value to the text stream.
            </summary>
            <param name="value">Uint value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.String,System.Object[])">
            <summary>
            Writes out a formatted string, using the same semantics as string Format.
            </summary>
            <param name="format">The formatting string.</param>
            <param name="arg">The object array to write into the formatted string.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.String,System.Object)">
            <summary>
            Writes out a formatted string, using the same semantics as string Format.
            </summary>
            <param name="format">The formatting string.</param>
            <param name="arg0">An object to write into the formatted string.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.String,System.Object,System.Object)">
            <summary>
            Writes out a formatted string, using the same semantics as string.Format.
            </summary>
            <param name="format">The formatting string.</param>
            <param name="arg0">An object to write into the formatted string.</param>
            <param name="arg1">An object to write into the formatted string.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.Write(System.Char[],System.Int32,System.Int32)">
            <summary>
            Writes a subarray of characters to the text stream.
            </summary>
            <param name="buffer">The character array to write data from.</param>
            <param name="index">Starting index in the buffer.</param>
            <param name="count">The number of characters to write. </param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteBackColorAttribute(System.Drawing.Color)">
            <summary>
            
            </summary>
            <param name="color"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteChar(System.Char)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteColorInTable(System.Drawing.Color)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteColorTable">
            <summary>
            Writes colors table into inner text writer.
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteFont(System.Drawing.Font)">
            <summary>
            
            </summary>
            <param name="font"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteFontAttribute(System.Drawing.Font)">
            <summary>
            
            </summary>
            <param name="font"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteFontAttribute(System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="iFontId"></param>
            <param name="iFontSize"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteFontInTable(System.Drawing.Font)">
            <summary>
            
            </summary>
            <param name="font"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteFontItalicBoldStriked(System.Drawing.Font)">
            <summary>
            
            </summary>
            <param name="font"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteFontTable">
            <summary>
            Writes fonts table into inner text writer.
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteForeColorAttribute(System.Drawing.Color)">
            <summary>
            
            </summary>
            <param name="color"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine">
            <summary>
            Writes a line terminator to the text stream.
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Boolean)">
            <summary>
            Writes the text representation of a Boolean followed by a line
            terminator to the text stream.
            </summary>
            <param name="value">The Boolean to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Char)">
            <summary>
            Writes a character followed by a line terminator to the text stream.
            </summary>
            <param name="value">The character to write to the text stream.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Double)">
            <summary>
            Writes the text representation of a 8-byte floating-point value followed
            by a line terminator to the text stream.
            </summary>
            <param name="value">The 8-byte floating-point value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Int32)">
            <summary>
            Writes the text representation of a 4-byte signed integer
            followed by a line terminator to the text stream.
            </summary>
            <param name="value">The 4-byte signed integer to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Int64)">
            <summary>
            Writes the text representation of an 8-byte signed
            integer followed by a line terminator to the text stream.
            </summary>
            <param name="value">The 8-byte signed integer to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Object)">
            <summary>
            Writes the text representation of an object by calling
            ToString on this object, followed by a line terminator to the text stream.
            </summary>
            <param name="value">The object to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Single)">
            <summary>
            Writes the text representation of a 4-byte floating-point
            value followed by a line terminator to the text stream.
            </summary>
            <param name="value">The 4-byte floating-point value to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.String)">
            <summary>
            Writes a string followed by a line terminator to the text stream.
            </summary>
            <param name="s">The string to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.UInt32)">
            <summary>
            Writes the text representation of a 4-byte unsigned integer followed
            by a line terminator to the text stream.
            </summary>
            <param name="value">The 4-byte unsigned integer to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Char[])">
            <summary>
            Writes an array of characters followed by a line terminator to the text stream.
            </summary>
            <param name="buffer">The character array from which data is read.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.String,System.Object[])">
            <summary>
            Writes out a formatted string and a new line, using the same semantics as Format.
            </summary>
            <param name="format">The formatting string.</param>
            <param name="arg">The object array to write into format string.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.String,System.Object)">
            <summary>
            Writes out a formatted string and a new line, using the same semantics as Format.
            </summary>
            <param name="format">The formatted string.</param>
            <param name="arg0">The object to write into the formatted string.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.String,System.Object,System.Object)">
            <summary>
            Writes out a formatted string and a new line, using the same semantics as Format.
            </summary>
            <param name="format">The formatting string.</param>
            <param name="arg0">The object to write into the format string.</param>
            <param name="arg1">The object to write into the format string.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLine(System.Char[],System.Int32,System.Int32)">
            <summary>
            Writes a subarray of characters followed by a line terminator to the text stream.
            </summary>
            <param name="buffer">The character array from which data is read.</param>
            <param name="index">The index into buffer at which to begin reading.</param>
            <param name="count">The maximum number of characters to write.</param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteLineNoTabs(System.String)">
            <summary>
            
            </summary>
            <param name="s"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteNewLine">
            <summary>
            
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteNewLine(System.String)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteStrikeThrough(SmartXLS.StrikeThroughStyle)">
            <summary>
            
            </summary>
            <param name="style"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteString(System.String)">
            <summary>
            
            </summary>
            <param name="value"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteTag(SmartXLS.RtfTags)">
            <summary>
            
            </summary>
            <param name="tag"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteTag(SmartXLS.RtfTags,System.Object[])">
            <summary>
            
            </summary>
            <param name="tag"></param>
            <param name="arrParams"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteText(System.Drawing.Font,System.String)">
            <summary>
            
            </summary>
            <param name="font"></param>
            <param name="strText"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteText(System.Drawing.Font,System.Drawing.Color,System.String)">
            <summary>
            
            </summary>
            <param name="font"></param>
            <param name="foreColor"></param>
            <param name="strText"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteText(System.Drawing.Font,System.Drawing.Color,System.Drawing.Color,System.String)">
            <summary>
            
            </summary>
            <param name="font"></param>
            <param name="foreColor"></param>
            <param name="backColor"></param>
            <param name="strText"></param>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteUnderlineAttribute">
            <summary>
            
            </summary>
        </member><member name="M:SmartXLS.RtfTextWriter.WriteUnderlineAttribute(SmartXLS.UnderlineStyle)">
            <summary>
            
            </summary>
            <param name="style"></param>
        </member><member name="F:SmartXLS.RtfTextWriter.DEF_TAGS">
            <summary>
            Other tags.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.m_arrColors">
            <summary>
            Array list with all used colors.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.m_bEnableFormatting">
            <summary>
            Indicates whether formatting is enabled.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.m_bEscape">
            <summary>
            
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.m_bTabsPending">
            <summary>
            
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.m_hashColorTable">
            <summary>
            Colors hashtable. Color - to - color index.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.m_hashFonts">
            <summary>
            Fonts hashtable. Font - to - font index.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.m_innerWriter">
            <summary>
            Inner text writer.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.newLine">
            <summary>
            
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.StrikeThroughTags">
            <summary>
            Strike through tags.
            </summary>
        </member><member name="F:SmartXLS.RtfTextWriter.UnderlineTags">
            <summary>
            Underline tags.
            </summary>
        </member><member name="P:SmartXLS.RtfTextWriter.Encoding">
            <summary>
            Returns current Encoding.
            </summary>
        </member><member name="P:SmartXLS.RtfTextWriter.Escape">
            <summary>
            
            </summary>
        </member><member name="T:SmartXLS.BookPivotRange">
            <summary>
            represent the pivot range
            </summary>
        </member><member name="F:SmartXLS.BookPivotRange.row">
            <summary>
            row pivot area
            </summary>
        </member><member name="F:SmartXLS.BookPivotRange.column">
            <summary>
            column pivot area
            </summary>
        </member><member name="F:SmartXLS.BookPivotRange.page">
            <summary>
            page pivot area
            </summary>
        </member><member name="F:SmartXLS.BookPivotRange.data">
            <summary>
            data pivot area
            </summary>
        </member><member name="M:SmartXLS.BookPivotRange.getArea(System.Int16)">
            <summary>
            Returns the Area object associated with the specified area value.
            </summary>
            <param name="area">Area index</param>
            <returns></returns>
        </member><member name="M:SmartXLS.BookPivotRange.getField(System.String)">
            <summary>
            Returns the Field with the specified name.
            </summary>
            <param name="name">Name of the desired Field.</param>
            <returns>Field with specified name.</returns>
        </member><member name="M:SmartXLS.BookPivotRange.getField(System.Int32)">
            <summary>
            Returns the Field located at the specified index.
            </summary>
            <param name="i">Index to the desired Field.</param>
            <returns>Field located at specified index.</returns>
        </member><member name="M:SmartXLS.BookPivotRange.getFieldCount">
            <summary>
            Returns the number of Fields in the PivotRange.
            </summary>
            <returns>Number of Fields in the PivotRange.</returns>
        </member><member name="M:SmartXLS.BookPivotRange.addCalcField(System.String,System.String)">
            <summary>
            add a calculated field to the data area
            </summary>
            <param name="name">field name</param>
            <param name="formula">calculated formula</param>
            <returns>pivot field</returns>
        </member><member name="M:SmartXLS.BookPivotRange.addField(System.String,SmartXLS.BookPivotArea)">
            <summary>
            Returns the Field with the specified name.
            </summary>
            <param name="name">Name of the desired Field.</param>
            <param name="area">pivot area</param>
            <returns>Field with specified name.</returns>
        </member><member name="M:SmartXLS.BookPivotRange.getRangeArea">
            <summary>
            returns the boundary of the PivotRange
            </summary>
            <returns>the boundary of the PivotRange</returns>
        </member><member name="M:SmartXLS.BookPivotRange.setDataOnRow(System.Boolean)">
            <summary>
             set the data field in the column area if flag is false.
            </summary>
            <param name="onRow">true to set field in row area.</param>
        </member><member name="M:SmartXLS.BookPivotRange.refresh">
            <summary>
            refresh the selected pivot range
            </summary>
        </member><member name="M:SmartXLS.BookPivotRange.setTableFormat(SmartXLS.RangeStyle,System.Int32)">
            <summary>
            format the selection part of the pivot table
            </summary>
            <param name="rs">formatting object</param>
            <param name="selection">selection part of the pivot table 0-BlankColumnHeader 2-Data 3-all 4-TopLeft</param>
        </member><member name="M:SmartXLS.BookPivotRange.setConditionalFormat(SmartXLS.ConditionFormat,System.Int32,System.Int32)">
            <summary>
            apply the conditional formatting to the pivot table
            </summary>
            <param name="conditionFormat">conditional formatting</param>
            <param name="dataFiled">data field index</param>
            <param name="selection">conditional type to be applied
            0 - Selected cells
            1 - All cells showing "#DATAFIELD" values
            2 - All cells showing "#DATAFIELD" values for "#ROW_AREA_FIELDS" and "#COL_AREA_FIELDS"
            </param>
        </member><member name="P:SmartXLS.BookPivotRange.ShowRowGrandTotal">
            <summary>
            Indicates whether the row grand total is shown or not
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.ShowColGrandTotal">
            <summary>
            Indicates whether the col grand total is shown or not
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.InGridZone">
            <summary>
            Indicates whether the pivot field can be droped in pivot area or not
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.ShowDrill">
            <summary>
            Indicates whether the drill button is shown or not
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.ShowHeader">
            <summary>
            Indicates whether the field caption is shown or not
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.DataCaption">
            <summary>
            data area caption
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.ErrorCaption">
            <summary>
            error filed caption
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.MissingCaption">
            <summary>
            null filed caption
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.TableStyle">
            <summary>
            table style
            </summary>
        </member><member name="P:SmartXLS.BookPivotRange.Compact">
            <summary>
            the compact flag
            </summary>
        </member><member name="T:SmartXLS.AutoShape">
            <summary> This class represents a drawing shape object.
            0-Rectangle 1-Oval 2-Line 3-TextBox
            </summary>
        </member><member name="F:SmartXLS.AutoShape.Rectangle">
            <summary>
            rectangle shape
            </summary>
        </member><member name="F:SmartXLS.AutoShape.Oval">
            <summary>
            oval shape
            </summary>
        </member><member name="F:SmartXLS.AutoShape.Line">
            <summary>
            line shape
            </summary>
        </member><member name="F:SmartXLS.AutoShape.TextBox">
            <summary>
            text box shape
            </summary>
        </member><member name="P:SmartXLS.AutoShape.Text">
            <summary> text content.</summary>
        </member><member name="F:SmartXLS.io.ByteBuffer.mark_Renamed_Field">
            
        </member><member name="F:SmartXLS.io.ByteBuffer.hb">
            
        </member><member name="T:SmartXLS.SheetVarType">
            <summary>
            ignoredError_numberStoredAsText --- the attribute "numberStoredAsText" of the ignoredError for the current worksheet
            </summary>
        </member><member name="T:SmartXLS.ShapeFormat">
            <summary>
            This class represents the shape format object.
            </summary>
        </member><member name="F:SmartXLS.ShapeFormat.PlacementFreeFloating">
            <summary>
            placement style freefloating
            </summary>
        </member><member name="F:SmartXLS.ShapeFormat.PlacementMove">
            <summary>
            placement style move
            </summary>
        </member><member name="T:SmartXLS.FormControlShape">
            <summary> 
            This class represents a Form control object.
            Types: 20-ComBox 11-CheckBox 18-ListBox
            </summary>
        </member><member name="F:SmartXLS.FormControlShape.CheckBox">
            <summary>
            shape check box
            </summary>
        </member><member name="F:SmartXLS.FormControlShape.CombBox">
            <summary>
            shape comb box
            </summary>
        </member><member name="F:SmartXLS.FormControlShape.ListBox">
            <summary>
            shape list box
            </summary>
        </member><member name="P:SmartXLS.FormControlShape.CellRange">
            <summary> the source range link to the control</summary>
        </member><member name="P:SmartXLS.FormControlShape.CellLink">
            <summary> the link cell of the control</summary>
        </member><member name="P:SmartXLS.FormControlShape.Text">
            <summary> lable text.</summary>
        </member><member name="T:SmartXLS.Argument">
            <summary>
            function parameter values
            </summary>
        </member><member name="M:SmartXLS.Argument.isCell">
            <summary>
            the parameter reference cell
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.Argument.isRange">
            <summary>
            the parameter reference range area
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.Argument.isNumber">
            <summary>
            is value in number
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.Argument.isText">
            <summary>
            is value in text
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.Argument.isError">
            <summary>
            contain an error value
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.Argument.isLogical">
            <summary>
            is logic value
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.Argument.getNumber">
            <summary>
            return the value in number
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.Argument.getText">
            <summary>
            return the value in text
            </summary>
            <returns></returns>
        </member><member name="P:SmartXLS.Argument.Sheet">
            <summary>
            the sheet
            </summary>
        </member><member name="P:SmartXLS.Argument.Row1">
            <summary>
            start row
            </summary>
        </member><member name="P:SmartXLS.Argument.Col1">
            <summary>
            start column
            </summary>
        </member><member name="P:SmartXLS.Argument.Row2">
            <summary>
            end row
            </summary>
        </member><member name="P:SmartXLS.Argument.Col2">
            <summary>
            end column
            </summary>
        </member><member name="F:SmartXLS.image.ChecksumCalculator.crc_table">
            <summary>
            for MsoCrc32Compute
            </summary>
        </member><member name="T:SmartXLS.DocumentProperty">
            <summary>
            represent the document property of this workbook
            </summary>
        </member><member name="M:SmartXLS.DocumentProperty.getCustomPropertiesKeys">
            <summary>
            keys of the custom properties
            </summary>
            <returns></returns>
        </member><member name="M:SmartXLS.DocumentProperty.isCustomPropertiesContainKey(System.String)">
            <summary>
            true if the key contained by the custom properties
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member><member name="M:SmartXLS.DocumentProperty.getCustomProperty(System.String)">
            <summary>
            return the value of the custom property
            </summary>
            <param name="key">key</param>
            <returns>value</returns>
        </member><member name="M:SmartXLS.DocumentProperty.setCustomProperty(System.String,System.String)">
            <summary>
            set the custom property
            </summary>
            <param name="key">key</param>
            <param name="value">value</param>
        </member><member name="P:SmartXLS.DocumentProperty.ApplicationName">
            <summary>
            ApplicationName
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.LastAuthor">
            <summary>
            LastAuthor
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Author">
            <summary>
            Author
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Title">
            <summary>
            Title
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Subject">
            <summary>
            Subject
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Keywords">
            <summary>
            Keywords
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Category">
            <summary>
            Category
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Comments">
            <summary>
            Comments
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Manager">
            <summary>
            Manager
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.Company">
            <summary>
            Company
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.CreationDate">
            <summary>
            CreationDate
            </summary>
        </member><member name="P:SmartXLS.DocumentProperty.LastSaveDate">
            <summary>
            LastSaveDate
            </summary>
        </member><member name="T:SmartXLS.ChartFormat">
            <summary> The chart format class, providing information about chart formatting.
            This interface can be used to get and set formatting values on all elements within the chart. Certain formatting options only apply to certain parts of the chart.
            This object can be reused, so there is no need to create a new one for each object to be formatted.
            </summary>
        </member><member name="M:SmartXLS.ChartFormat.setLineCustom">
            <summary> set line custom style and weight</summary>
        </member><member name="M:SmartXLS.ChartFormat.isLineNone">
            <summary> if line is none</summary>
            <returns> true or false
            </returns>
        </member><member name="M:SmartXLS.ChartFormat.setLineNone">
            <summary> set line none</summary>
        </member><member name="M:SmartXLS.ChartFormat.isSolid">
            <summary> Returns whether fill is solid.</summary>
            <returns> whether fill is solid
            </returns>
        </member><member name="M:SmartXLS.ChartFormat.setSolid">
            <summary> Sets the fill to solid.</summary>
        </member><member name="M:SmartXLS.ChartFormat.isLineAuto">
            <summary> return if the line auto</summary>
            <returns> true = auto
            </returns>
        </member><member name="M:SmartXLS.ChartFormat.setLineAuto">
            <summary> set line auto</summary>
        </member><member name="P:SmartXLS.ChartFormat.DataLabelSeparator">
            <summary> the data label separator value.</summary>
        </member><member name="P:SmartXLS.ChartFormat.MarkerBackground">
            <summary> the marker background color as an RGB value.
            The color is returned as a four-byte integer in the format 0x00RRGGBB. 
            </summary>
        </member><member name="P:SmartXLS.ChartFormat.MarkerForeground">
            <summary> the marker foreground color as an RGB value.
            The color is returned as a four-byte integer in the format 0x00RRGGBB.
            </summary>
        </member><member name="P:SmartXLS.ChartFormat.MarkerSize">
            <summary> 
            the marker size in twips.
            the value is between 0 and 1440
            </summary>
        </member><member name="P:SmartXLS.ChartFormat.FillShadow">
            <summary> whether the object has a shadow behind it.</summary>
        </member><member name="P:SmartXLS.ChartFormat.MarkerAuto">
            <summary> whether to use automatic formatting for a marker.</summary>
        </member><member name="P:SmartXLS.ChartFormat.ValueFormatLinkedToDataSource">
            <summary> whether the object gets the value format from the data source. true = value format from data source.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FillAuto">
            <summary> whether fill is auto.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FillSolid">
            <summary> whether fill is solid.</summary>
        </member><member name="P:SmartXLS.ChartFormat.BackColor">
            <summary> the color used to display the pattern background.in the format 0x00RRGGBB</summary>
        </member><member name="P:SmartXLS.ChartFormat.ForeColor">
            <summary> the color used to display the pattern foreground.in the format 0x00RRGGBB.</summary>
        </member><member name="P:SmartXLS.ChartFormat.LineAuto">
            <summary> the line auto flag.</summary>
        </member><member name="P:SmartXLS.ChartFormat.LineNone">
            <summary> the line none flag.</summary>
        </member><member name="P:SmartXLS.ChartFormat.LineShadow">
            <summary> whether the font is shadow.</summary>
        </member><member name="P:SmartXLS.ChartFormat.LineColor">
            <summary> the line color.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FontName">
            <summary> the name of the font.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FontSize">
            <summary> the size of the font in twips.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FontItalic">
            <summary> whether the font is italic.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FontUnderline">
            <summary> whether the font is underline.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FontBold">
            <summary> whether the font is bold.</summary>
        </member><member name="P:SmartXLS.ChartFormat.FontColor">
            <summary> 
            the color used to display the font.
            an integer representing the color as an RGB value.
            </summary>
        </member><member name="P:SmartXLS.ChartFormat.FontAutoScale">
            <summary> whether fonts are automatically scaled based on chart size.</summary>
        </member><member name="P:SmartXLS.ChartFormat.LinkedToDataSource">
            <summary> whether the object gets the value format from the data source. </summary>
        </member><member name="P:SmartXLS.ChartFormat.CustomFormat">
            <summary> the number format.</summary>
        </member><member name="P:SmartXLS.ChartFormat.TextRotation">
            <summary> the text rotation.</summary>
        </member><member name="T:SmartXLS.PictureShape">
            <summary>
            This class represents a picture shape object.
            </summary>
        </member><member name="P:SmartXLS.PictureShape.PictureImageData">
            <summary>
            picture image data
            </summary>
        </member><member name="P:SmartXLS.PictureShape.PictureImageFormat">
            <summary>
            picture image data type
            * -1  gif
            * 5   jpg
            * 6   png
            * 7   dib
            </summary>
        </member><member name="T:SmartXLS.ChartShape">
            <summary> This class contains the chart info.</summary>
        </member><member name="F:SmartXLS.ChartShape.Column">
            <summary>
            chart type column
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Line">
            <summary>
            chart type line
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Area">
            <summary>
            chart type area
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Step">
            <summary>
            chart type step
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Bar">
            <summary>
            chart type bar
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Combination">
            <summary>
            chart type mixed,the chart type defined with series
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Pie">
            <summary>
            chart type pie
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Doughnut">
            <summary>
            chart type doughnut
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Scatter">
            <summary>
            chart type scatter
            </summary>
        </member><member name="F:SmartXLS.ChartShape.Bubble">
            <summary>
            chart type bubble
            </summary>
        </member><member name="F:SmartXLS.ChartShape.XAxis">
            <summary>
            Axis type in direction X
            </summary>
        </member><member name="F:SmartXLS.ChartShape.YAxis">
            <summary>
            Axis type in direction Y
            </summary>
        </member><member name="F:SmartXLS.ChartShape.ZAxis">
            <summary>
            Axis type in direction Z
            </summary>
        </member><member name="F:SmartXLS.ChartShape.CategoryScale">
            <summary>
            scale type in category
            </summary>
        </member><member name="F:SmartXLS.ChartShape.ValueScale">
            <summary>
            scale type in value
            </summary>
        </member><member name="F:SmartXLS.ChartShape.TimeScale">
            <summary>
            scale type in time
            </summary>
        </member><member name="F:SmartXLS.ChartShape.SeriesScale">
            <summary>
            scale type in series value
            </summary>
        </member><member name="M:SmartXLS.ChartShape.getLinkRange">
            <summary> Returns a formula indicating the range that the chart is linked to.
            This formula can be null if the range is too complex.
            </summary>
            <returns> the formula of the range.
            </returns>
        </member><member name="M:SmartXLS.ChartShape.setLinkRange(System.String,System.Boolean)">
            <summary> Links the chart to the range in the workbook represented by the specified formula.</summary>
            <param name="range">string indicating the range to link to.
            </param>
            <param name="bSeriesInRows">Indicates whether to link each series to rows or to columns. true = link to rows.
            </param>
            <throws>  Exception if the formula is invalid </throws>
        </member><member name="M:SmartXLS.ChartShape.getSeriesName(System.Int32)">
            <summary> Returns the name of the specified series.</summary>
            <param name="iSeries">an integer indicating which series
            </param>
            <returns> the series name as a formula or a text string.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setSeriesName(System.Int32,System.String)">
            <summary> Sets the name of the specified series.</summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <param name="name">the series name as a formula or a text string.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.getAxisTitle(System.Int16,System.Int32)">
            <summary> Returns the title of the specified axis.</summary>
            <param name="sAxisType">a short indicating the axis type. Either eXAxis or eYAxis.
            </param>
            <param name="iAxisIndex">an integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y-Axes can be any number 0 to (Y axis count -1).
            </param>
            <returns> the axis title as a formula or a text string.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setAxisTitle(System.Int16,System.Int32,System.String)">
            <summary> Sets the title of the specified axis.</summary>
            <param name="sAxisType">a short indicating the axis type. Either eXAxis.
            </param>
            <param name="iAxisIndex">an integer indicating which axis.
            The X axis is always 0 since there's currently only one. 
            Y-Axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="title">the axis title as a formula or a text string.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.getSeriesXValueFormula(System.Int32)">
            <summary> Returns the X axis value formula of the specified series.
            This method applies to bubble and XY (scatter) charts only.
            </summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <returns> a formula indicating the values.
            </returns>
            <throws>  Exception if argument is invalid or series does not use X values. </throws>
        </member><member name="M:SmartXLS.ChartShape.setSeriesXValueFormula(System.Int32,System.String)">
            <summary> Sets the X axis value formula of the specified series.
            This method applies to bubble and XY (scatter) charts only.
            </summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <param name="formula">a formula indicating the values.
            </param>
            <throws>  Exception if argument is invalid or series does not use X values. </throws>
        </member><member name="M:SmartXLS.ChartShape.getSeriesYValueFormula(System.Int32)">
            <summary> Returns the Y axis value formula of the specified series.</summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <returns> a formula indicating the values.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setSeriesYValueFormula(System.Int32,System.String)">
            <summary> Returns the Y axis value formula of the specified series.</summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <param name="formula">a formula indicating the values.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.getSeriesSizeValueFormula(System.Int32)">
            <summary> Returns the size value formula of the specified series. This method applies to bubble charts only.</summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <returns> a formula indicating the values.
            </returns>
            <throws>  Exception if argument is invalid or series does not use size values. </throws>
        </member><member name="M:SmartXLS.ChartShape.setSeriesSizeValueFormula(System.Int32,System.String)">
            <summary> Sets the size value formula of the specified series. This method applies to bubble charts only.</summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <param name="formula">a formula indicating the values.
            </param>
            <throws>  Exception if argument is invalid or series does not use size values. </throws>
        </member><member name="M:SmartXLS.ChartShape.addSeries">
            <summary> Adds a series to the chart.
            </summary>
            <throws>  Exception if selection is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.removeSeries(System.Int32)">
            <summary> Removes the specified series.</summary>
            <param name="iSeries">an integer indicating the series.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.set3Dimensional(System.Boolean)">
            <summary> Sets whether the chart is 3 dimensional or not.
            This attribute is ignored unless the chart type is a valid 3D chart type.
            </summary>
            <param name="b3Dimensional">true or false
            </param>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.ChartShape.is3Dimensional">
            <summary> Indicates whether the chart is 3 dimensional or not.
            This attribute is ignored unless the chart type is a valid 3D chart type.
            </summary>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.ChartShape.getSeriesFormat(System.Int32)">
            <summary> Returns formatting information for the specified series.</summary>
            <param name="iSeriesIndex">An integer indicating the series.
            Zero-based index where 0 = the first series, etc. 
            The series index number does not change after initially assigned.
            </param>
            <returns> a chart Format object.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setSeriesFormat(System.Int32,SmartXLS.ChartFormat)">
            <summary> Sets the format of the specified series.</summary>
            <param name="iSeriesIndex">An integer indicating the series.
            Zero-based index where 0 = the first series, etc. 
            The series index number does not change after initially assigned.
            </param>
            <param name="format">A chart Format object used to set and retrieve formatting information for objects in a chart.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.getAxisFormat(System.Int16,System.Int32)">
            <summary> Returns the format of the specified axis.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.
            </param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns> a chart Format object.
            After setting formats in a Format object, calling the corresponding set method applies new formatting.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setAxisFormat(System.Int16,System.Int32,SmartXLS.ChartFormat)">
            <summary> Sets formatting for the specified axis.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.
            </param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="format">A chart Format object used to set and retrieve formatting information for objects in a chart.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.getAxisTitleFormat(System.Int16,System.Int32)">
            <summary> Returns the specified axis title format object.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.
            </param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns> a chart Format object.
            After setting formats in a Format object, calling the corresponding set method applies new formatting.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setAxisTitleFormat(System.Int16,System.Int32,SmartXLS.ChartFormat)">
            <summary> Sets the formatting for the title of the specified axis.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.
            </param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="format">A chart Format object used to set and retrieve formatting information for objects in a chart.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.getMajorGridFormat(System.Int16,System.Int32)">
            <summary> Returns grid line formatting information for the major grid of the specified axis.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.
            </param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns> a chart Format object.
            After setting formats in a Format object, calling the corresponding set method applies new formatting.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setMajorGridFormat(System.Int16,System.Int32,SmartXLS.ChartFormat)">
            <summary> Sets grid line formatting information for the major grid of the specified axis.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.
            </param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="format">A chart Format object used to set and retrieve formatting information for objects in a chart.
            </param>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.getMinorGridFormat(System.Int16,System.Int32)">
            <summary> Returns grid line formatting information for the minor grid of the specified axis.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.
            </param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns> a chart Format object.
            After setting formats in a Format object, calling the corresponding set method applies new formatting.
            </returns>
            <throws>  Exception if argument is invalid. </throws>
        </member><member name="M:SmartXLS.ChartShape.setMinorGridFormat(System.Int16,System.Int32,SmartXLS.ChartFormat)">
            <summary> Sets grid line formatting information for the minor grid of the specified axis.</summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="format">A chart Format object used to set and retrieve formatting information for objects in a chart.</param>
        </member><member name="M:SmartXLS.ChartShape.getDataLabelFormat(System.Int32)">
            <summary>
            Returns data label formatting information for the specified series.
            </summary>
            <param name="seriesIndex">An integer indicating the series.Zero-based index.</param>
            <returns>chart Format object,After setting formats in a Format object, calling the corresponding set method applies new formatting.</returns>
        </member><member name="M:SmartXLS.ChartShape.setDataLabelFormat(System.Int32,SmartXLS.ChartFormat)">
            <summary>
            Sets data label formatting for the specified series.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index.</param>
            <param name="format">A chart Format object used to set and retrieve formatting information for objects in a chart.</param>
        </member><member name="M:SmartXLS.ChartShape.getDataLabelFormat(System.Int32,System.Int32)">
            <summary>
            Returns data label formatting information for the specified point of the specified series.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index.</param>
            <param name="pointIndex">An integer indicating the ordinal value of the selected or specified data point. Zero-based index.</param>
            <returns>chart Format object,After setting formats in a Format object, calling the corresponding set method applies new formatting.</returns>
        </member><member name="M:SmartXLS.ChartShape.setDataLabelFormat(System.Int32,System.Int32,SmartXLS.ChartFormat)">
            <summary>
            Sets data label formatting for the specified data point.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index.</param>
            <param name="pointIndex">An integer indicating the ordinal value of the selected or specified data point. Zero-based index.</param>
            <param name="format">A chart Format object used to set and retrieve formatting information for objects in a chart.</param>
        </member><member name="M:SmartXLS.ChartShape.isMajorGridVisible(System.Int16,System.Int32)">
            <summary>
            Indicates whether the major grid lines of the specified axis are visible.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">The X axis is always 0 since there's currently only one.Y axes can be any number 0 to (Y axis count -1).</param>
            <returns></returns>
        </member><member name="M:SmartXLS.ChartShape.setMajorGridVisible(System.Int16,System.Int32,System.Boolean)">
            <summary>
            Sets whether the major grid lines of the specified axis are visible.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">The X axis is always 0 since there's currently only one.Y axes can be any number 0 to (Y axis count -1).</param>
            <param name="b">true if visible</param>
        </member><member name="M:SmartXLS.ChartShape.isMinorGridVisible(System.Int16,System.Int32)">
            <summary>
            Indicates whether the minor grid lines of the specified axis are visible.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.The X axis is always 0 since there's currently only one.Y axes can be any number 0 to (Y axis count -1).</param>
            <returns></returns>
        </member><member name="M:SmartXLS.ChartShape.setMinorGridVisible(System.Int16,System.Int32,System.Boolean)">
            <summary>
            Sets whether the minor grid lines of the specified axis are visible.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.The X axis is always 0 since there's currently only one.Y axes can be any number 0 to (Y axis count -1).</param>
            <param name="b"></param>
        </member><member name="M:SmartXLS.ChartShape.getAxisLengthRatio(System.Int16,System.Int32)">
            <summary>
            Returns the ratio of the area taken up on the specified axis.
            This method is only valid on a Y axis.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns>an integer indicating the ratio.</returns>
        </member><member name="M:SmartXLS.ChartShape.setAxisLengthRatio(System.Int16,System.Int32,System.Int32)">
            <summary>
            Sets the ratio of the area taken up on the specified axis.
            This method is only valid on a Y axis.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="ratio">An integer indicating the ratio of the area taken up on the plot of each Y-axis.</param>
        </member><member name="M:SmartXLS.ChartShape.getAxisScaleType(System.Int16,System.Int32)">
            <summary>
            Returns the axis scale type for the specified axis.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns>short value indicating scale type:
            Axis Scale Type Constants
            CategoryScale<see cref="F:SmartXLS.ChartShape.CategoryScale" />   	scale based on category type.
            TimeScale<see cref="F:SmartXLS.ChartShape.TimeScale" /> 	scale that groups data into time categories.
            ValueScale<see cref="F:SmartXLS.ChartShape.ValueScale" /> 	scale based on range of values in the data.
            </returns>
        </member><member name="M:SmartXLS.ChartShape.setAxisScaleType(System.Int16,System.Int32,System.Int16)">
            <summary>
            Sets the axis scale type for the specified axis.
            This method can only be used to change the X axis from a category scale to a time scale or back to a category scale.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="scaleType">A constant indicating the scale type.
            Axis Scale Type Constants
            CategoryScale<see cref="F:SmartXLS.ChartShape.CategoryScale" />   	scale based on category type.
            TimeScale<see cref="F:SmartXLS.ChartShape.TimeScale" /> 	scale that groups data into time categories.
            ValueScale<see cref="F:SmartXLS.ChartShape.ValueScale" /> 	scale based on range of values in the data.
            </param>
        </member><member name="M:SmartXLS.ChartShape.isAxisVisible(System.Int16,System.Int32)">
            <summary>
            Indicates whether the specified axis is visible.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns>true or false.</returns>
        </member><member name="M:SmartXLS.ChartShape.setAxisVisible(System.Int16,System.Int32,System.Boolean)">
            <summary>
            Sets whether the specified axis is visible.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="b">true or false.</param>
        </member><member name="M:SmartXLS.ChartShape.getSeriesType(System.Int32)">
            <summary>
            Returns the series type value for the specified series.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index.</param>
            <returns>series type.
            Area<see cref="F:SmartXLS.ChartShape.Area" /> 	Bar<see cref="F:SmartXLS.ChartShape.Bar" /> 	Bubble<see cref="F:SmartXLS.ChartShape.Bubble" />
            Column<see cref="F:SmartXLS.ChartShape.Column" /> 	Combination<see cref="F:SmartXLS.ChartShape.Combination" /> 	Doughnut<see cref="F:SmartXLS.ChartShape.Doughnut" />
            Line<see cref="F:SmartXLS.ChartShape.Line" /> 	Pie<see cref="F:SmartXLS.ChartShape.Pie" /> 	Scatter<see cref="F:SmartXLS.ChartShape.Scatter" /> 	Step<see cref="F:SmartXLS.ChartShape.Step" />
            </returns>
        </member><member name="M:SmartXLS.ChartShape.setSeriesType(System.Int32,System.Int16)">
            <summary>
            Sets the series type of the specified series.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index.</param>
            <param name="type">series type constants
            Area<see cref="F:SmartXLS.ChartShape.Area" /> 	Bar<see cref="F:SmartXLS.ChartShape.Bar" /> 	Bubble<see cref="F:SmartXLS.ChartShape.Bubble" />
            Column<see cref="F:SmartXLS.ChartShape.Column" /> 	Combination<see cref="F:SmartXLS.ChartShape.Combination" /> 	Doughnut<see cref="F:SmartXLS.ChartShape.Doughnut" />
            Line<see cref="F:SmartXLS.ChartShape.Line" /> 	Pie<see cref="F:SmartXLS.ChartShape.Pie" /> 	Scatter<see cref="F:SmartXLS.ChartShape.Scatter" /> 	Step<see cref="F:SmartXLS.ChartShape.Step" />
            </param>
        </member><member name="M:SmartXLS.ChartShape.getDataLabelText(System.Int32,System.Int32)">
            <summary>
            Returns the data label text of the specified data point of the specified series.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index.</param>
            <param name="pointIndex">An integer indicating the ordinal value of the selected or specified data point. Zero-based index.</param>
            <returns></returns>
        </member><member name="M:SmartXLS.ChartShape.setDataLabelText(System.Int32,System.Int32,System.String,System.Boolean)">
            <summary>
            Sets data label text for the specified data point.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index.</param>
            <param name="pointIndex">An integer indicating the ordinal value of the selected or specified data point. Zero-based index.</param>
            <param name="text">The text string to use.</param>
            <param name="bAutomatic">true = use the default data label text.</param>
        </member><member name="M:SmartXLS.ChartShape.setSeriesYAxisIndex(System.Int32,System.Int32)">
            <summary>
            Sets the index of the Y axis that the specified series is plotted on.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index</param>
            <param name="axisIndex">An integer indicating which axis. Y axes can be any number 0 to (Y axis count -1).</param>
        </member><member name="M:SmartXLS.ChartShape.getDataPointCount(System.Int32)">
            <summary>
            Returns the number of data points in the specified series.
            </summary>
            <param name="seriesIndex">zero-based series index.</param>
            <returns>the number of data points.</returns>
        </member><member name="M:SmartXLS.ChartShape.getDataPointFormat(System.Int32,System.Int32)">
            <summary>
            Returns data point formatting information for the specified data point.
            </summary>
            <param name="seriesIndex">zero-based series index.</param>
            <param name="pointIndex">zero-based data point index.</param>
            <returns>chartFormat object.</returns>
        </member><member name="M:SmartXLS.ChartShape.setDataPointFormat(System.Int32,System.Int32,SmartXLS.ChartFormat)">
            <summary>
            Sets formatting for the specified data point.
            </summary>
            <param name="seriesIndex">zero-based series index.</param>
            <param name="pointIndex">zero-based data point index.</param>
            <param name="format">chartFormat object.</param>
        </member><member name="M:SmartXLS.ChartShape.setPlotGroupStack(System.Int32,System.Boolean)">
            <summary>
            Sets the selected PlotGroup to display stacked series.
            </summary>
            <param name="seriesIndex">An integer indicating the series. Zero-based index</param>
            <param name="stacked">true or false.</param>
        </member><member name="M:SmartXLS.ChartShape.getPieExplosion(System.Int32)">
            <summary>
            Returns explosion percentage for the specified data point.
            </summary>
            <param name="iPointIndex">An integer indicating the data point</param>
            <returns>an integer indicating explosion percentage of the specified data point</returns>
        </member><member name="M:SmartXLS.ChartShape.setPieExplosion(System.Int32,System.Int32)">
            <summary>
            Sets the explosion percentage of the specified data point.
            </summary>
            <param name="iPointIndex">An integer indicating the data point</param>
            <param name="percent">An integer indicating the explosion percentage</param>
        </member><member name="M:SmartXLS.ChartShape.setErrorBars(System.Int32,System.Boolean,System.Int32,System.Boolean,System.String)">
            <summary>
            set the errorbar informations
            </summary>
            <param name="seriesIndex">An integer indicating the series</param>
            <param name="xy">false-X true-Y</param>
            <param name="type">one or more of the values:1-Plus 2-Minus 3-Both</param>
            <param name="visible">true to display the errorbar,false not shown.</param>
            <param name="range">Minus or Plus link range</param>
        </member><member name="M:SmartXLS.ChartShape.setErrorBars(System.Int32,System.Boolean,System.Int32,System.Boolean,System.Int32,System.Int32)">
            <summary>
            set the errorbar informations
            </summary>
            <param name="seriesIndex">An integer indicating the series</param>
            <param name="xy">false-X true-Y</param>
            <param name="type">one or more of the values:1-Plus 2-Minus 3-Both</param>
            <param name="visible">true to display the errorbar,false not shown.</param>
            <param name="source">1-percentage 2-fixed value</param>
            <param name="value">the value number</param>
        </member><member name="M:SmartXLS.ChartShape.isSeriesSmoothedLine(System.Int32)">
            <summary>
            indicate whether the series is smooth line
            </summary>
            <param name="seriesIndex">An integer indicating the series</param>
            <returns>true if smooth line</returns>
        </member><member name="M:SmartXLS.ChartShape.setSeriesSmoothedLine(System.Int32)">
            <summary>
            set the series smooth line
            </summary>
            <param name="seriesIndex">An integer indicating the series</param>
        </member><member name="M:SmartXLS.ChartShape.setAutoMaximumScale(System.Int16,System.Int32,System.Boolean)">
            <summary>
            Sets whether the upper limit of the scale of the specified axis is automatically calculated.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="auto">upper limit is automatically calculated.</param>
        </member><member name="M:SmartXLS.ChartShape.setAutoMinimumScale(System.Int16,System.Int32,System.Boolean)">
            <summary>
            Sets whether the lower limit of the scale of the selected axis is automatically calculated.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="auto">lower limit is automatically calculated.</param>
        </member><member name="M:SmartXLS.ChartShape.setScaleValueRange(System.Int16,System.Int32,System.Double,System.Double)">
            <summary>
            Sets the minimum and maximum range of the selected axis. 
            This method is only valid on an axis that uses a value or time scale.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="dMin">The minimum value of the range of the selected axis</param>
            <param name="dMax">The maximum value of the range of the selected axis</param>
        </member><member name="M:SmartXLS.ChartShape.setAxisScaleCrosses(System.Int16,System.Int32,System.Boolean,System.Double)">
            <summary>
            Sets the crosses value for the specified axis.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="auto"> automatically configured</param>
            <param name="intersect">intersection value</param>
        </member><member name="M:SmartXLS.ChartShape.getAxisScaleCrosses(System.Int16,System.Int32)">
            <summary>
            gets the crosses value for the specified axis.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns>intersection value</returns>
        </member><member name="M:SmartXLS.ChartShape.isAxisScaleCrossAuto(System.Int16,System.Int32)">
            <summary>
            return whether the specified axis is 'auto' cross
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns>true - auto</returns>
        </member><member name="M:SmartXLS.ChartShape.isAxisScaleCrossMax(System.Int16,System.Int32)">
            <summary>
            return whether the specified axis is 'max' cross
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns>true - max</returns>
        </member><member name="M:SmartXLS.ChartShape.setAxisScaleCrossMax(System.Int16,System.Int32,System.Boolean)">
            <summary>
            Sets the crosses max value for the specified axis.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="t">true - max</param>
        </member><member name="M:SmartXLS.ChartShape.isAxisScaleCrossMin(System.Int16,System.Int32)">
            <summary>
            return whether the specified axis is 'min' cross
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <returns>true - min</returns>
        </member><member name="M:SmartXLS.ChartShape.setAxisScaleCrossMin(System.Int16,System.Int32,System.Boolean)">
            <summary>
            Sets the crosses min value for the specified axis.
            </summary>
            <param name="axisType">The axis type. Either XAxis or YAxis.</param>
            <param name="axisIndex">An integer indicating which axis.
            The X axis is always 0 since there's currently only one.
            Y axes can be any number 0 to (Y axis count -1).
            </param>
            <param name="t">true - min</param>
        </member><member name="M:SmartXLS.ChartShape.getSeriesCount">
            <summary>
            returns the series count in the chart
            </summary>
            <returns>the series count</returns>
        </member><member name="M:SmartXLS.ChartShape.setAxisAuto(System.Int16,System.Int32,System.Boolean)">
            <summary>
            set the axis type whether based on the value or not,the axis type is one of auto,text,date
            </summary>
            <param name="axisType">aixs type</param>
            <param name="axisIndex">index</param>
            <param name="b">flag</param>
        </member><member name="M:SmartXLS.ChartShape.setTrendLineType(System.Int32,System.Int32,System.Int16)">
            <summary>
            set the trendline type
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <param name="type">trendline type
             -1-linear(default) 0-ploy 1-exp 2-log 3-power 4-movingAvg
            </param>
        </member><member name="M:SmartXLS.ChartShape.getTrendLinePeriod(System.Int32,System.Int32)">
            <summary>
            get the trendline period value
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <returns>period value</returns>
        </member><member name="M:SmartXLS.ChartShape.setTrendLinePeriod(System.Int32,System.Int32,System.Int16)">
            <summary>
            set the trendline period value
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <param name="period">period value</param>
        </member><member name="M:SmartXLS.ChartShape.getTrendLineFormat(System.Int32,System.Int32)">
            <summary>
            return the trendline format object
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <returns>ChartFormat object</returns>
        </member><member name="M:SmartXLS.ChartShape.setTrendLineFormat(System.Int32,System.Int32,SmartXLS.ChartFormat)">
            <summary>
            set the trendline format object
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <param name="format">chartformat object</param>
        </member><member name="M:SmartXLS.ChartShape.isTrendLineDisplayRSquared(System.Int32,System.Int32)">
            <summary>
            return whether the trendline is display RSquared.
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <returns>true if displayRSquared</returns>
        </member><member name="M:SmartXLS.ChartShape.setTrendLineDisplayRSquared(System.Int32,System.Int32,System.Boolean)">
            <summary>
            set the flag of the trendline's DisplayRSquared attr.
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <param name="vflag">true to set this attr</param>
        </member><member name="M:SmartXLS.ChartShape.isTrendLineDisplayEquation(System.Int32,System.Int32)">
            <summary>
            return whether the trendline is display Equation.
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <returns>true if displayEquation</returns>
        </member><member name="M:SmartXLS.ChartShape.setTrendLineDisplayEquation(System.Int32,System.Int32,System.Boolean)">
            <summary>
            set the flag of the trendline's DisplayEquation attr.
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <param name="vflag">true to set this attr</param>
        </member><member name="M:SmartXLS.ChartShape.getTrendLineIntercept(System.Int32,System.Int32)">
            <summary>
            return the intercept value of the trendline
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <returns>intercept value</returns>
        </member><member name="M:SmartXLS.ChartShape.setTrendLineIntercept(System.Int32,System.Int32,System.Double)">
            <summary>
            set the trendline intercept value
            </summary>
            <param name="series">series index</param>
            <param name="trendline">trendline index</param>
            <param name="intercept">intercept value</param>
        </member><member name="P:SmartXLS.ChartShape.ChartType">
            <summary> 
            Returns the current chart type.
            Chart Type Constants
            Area<see cref="F:SmartXLS.ChartShape.Area" />  	Bar  	Bubble
            Column 	Combination 	Doughnut
            Line 	Pie 	Scatter 	Step
            </summary>
        </member><member name="P:SmartXLS.ChartShape.CategoryFormula">
            <summary> formula that the categories are linked to</summary>
        </member><member name="P:SmartXLS.ChartShape.Title">
            <summary>  the chart title as a formula or a text string..</summary>
        </member><member name="P:SmartXLS.ChartShape.SeriesInRows">
            <summary> Indicates whether series data is in rows.</summary>
        </member><member name="P:SmartXLS.ChartShape.TitleFormat">
            <summary> Returns chart title formatting information.</summary>
        </member><member name="P:SmartXLS.ChartShape.PlotFormat">
            <summary> plot formatting information.</summary>
        </member><member name="P:SmartXLS.ChartShape.LegendFormat">
            <summary> legend formatting information.</summary>
        </member><member name="P:SmartXLS.ChartShape.ChartFormat">
            <summary> chart formatting information.</summary>
        </member><member name="P:SmartXLS.ChartShape.DropLinesFormat">
            <summary> drop line formatting.</summary>
        </member><member name="P:SmartXLS.ChartShape.YAxisCount">
            <summary> the number of Y axes on the chart.</summary>
        </member><member name="P:SmartXLS.ChartShape.PlotStacked">
            <summary> Indicates whether the series in the chart are stacked.</summary>
        </member><member name="P:SmartXLS.ChartShape.BarGapRatio">
            <summary> 
            the bar gap ratio on a bar or column chart.
            100  = spaces are the same size as bars.
            0    = no space.
            -100 = complete overlap.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.Explosion">
            <summary> 
            the explosion percentage.
            This method applies to doughnut and pie charts only.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.LegendPosition">
            <summary>
            current legend position
            0 - LegendPlacementBottom 1 - LegendPlacementCorner 2 - LegendPlacementTop 3 - LegendPlacementRight
            4 - LegendPlacementLeft 5 - LegendPlacementTopRightCorner 6 - LegendPlacementBottomRightCorner 7 - LegendPlacementNotDocked
            8 - LegendPlacementTopLeftCorner 9 - LegendPlacementBottomLeftCorner
            </summary>
        </member><member name="P:SmartXLS.ChartShape.LegendVisible">
            <summary>
            Indicates whether the legend is visible.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.DropLinesVisible">
            <summary>
            Indicates whether drop lines are visible.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.LogScale">
            <summary>
            whether the axis is logarithmic.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.Percent">
            <summary>
            whether the values in the chart are plotted as percentages of the category.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.VaryColors">
            <summary>
            whether the series in the chart use varied colors.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.StartAngle">
            <summary>
            the starting angle of chart shape object,only applies to pie and doughnut charts.
            values is in 0-360.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.IsLegendKey">
            <summary>
            whether the datalabels in the chart use the legend key.
            </summary>
        </member><member name="P:SmartXLS.ChartShape.CategoryScaleCrossBetween">
            <summary>
            return true if the scale of the category is cross between 
            </summary>
        </member><member name="P:SmartXLS.ChartShape.PivotSource">
            <summary>
            set the pivot range for this chart to be linked to the pivot table
            </summary>
        </member><member name="M:SmartXLS.RtfString.GenerateRtfText">
            <summary>
            Generates text in rtf format.
            </summary>
            <returns>Generated text.</returns>
        </member><member name="M:SmartXLS.RtfString.WriteFormattingRun(SmartXLS.RtfTextWriter,System.Int32,System.Int32)">
            <summary>
            Writes formatting run with corresponding text into writer.
            </summary>
            <param name="writer">Writer to write text and formatting into.</param>
            <param name="iRunIndex">Index of the formatting run.</param>
            <param name="iStartPos">First character in the text range.</param>
            <returns>End position of the text range.</returns>
        </member><member name="M:SmartXLS.RtfString.AddFonts(SmartXLS.RtfTextWriter,SmartXLS.paint.MFTextRuns)">
            <summary>
            Adds all used fonts to the rtf text writer.
            </summary>
            <param name="writer">RtfTextWriter to write into.</param>
        </member><member name="T:SmartXLS.LOGFONT">
            <summary>
            Represents LogFont structure that defines the attributes of a font.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfHeight">
            <summary>
            Specifies the height, in logical units, of the font's character cell or character.
            The character height value (also known as the em height) is the character cell
            height value minus the internal-leading value.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfWidth">
            <summary>
            Specifies the average width, in logical units, of characters in the font.
            If lfWidth is zero, the aspect ratio of the device is matched against the
            digitization aspect ratio of the available fonts to find the closest match,
            determined by the absolute value of the difference.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfEscapement">
            <summary>
            Specifies the angle, in tenths of degrees, between the escapement vector
            and the x-axis of the device. The escapement vector is parallel
            to the base line of a row of text.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfOrientation">
            <summary>
            Specifies the angle, in tenths of degrees, between each character's base
            line and the x-axis of the device.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfWeight">
            <summary>
            Specifies the weight of the font in the range 0 through 1000.
            For example, 400 is normal and 700 is bold. If this value is zero,
            a default weight is used.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfItalic">
            <summary>
            Specifies an italic font if set to TRUE.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfUnderline">
            <summary>
            Specifies an underlined font if set to TRUE.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfStrikeOut">
            <summary>
            Specifies a strikeout font if set to TRUE.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfCharSet">
            <summary>
            Specifies the character set. The following values are predefined.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfOutPrecision">
            <summary>
            Specifies the output precision. The output precision defines how closely
            the output must match the requested font's height, width, character
            orientation, escapement, pitch, and font type.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfClipPrecision">
            <summary>
            Specifies the clipping precision. The clipping precision defines how
            to clip characters that are partially outside the clipping region.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfQuality">
            <summary>
            Specifies the output quality. The output quality defines how carefully
            the graphics device interface (GDI) must attempt to match the logical-font
            attributes to those of an actual physical font.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfPitchAndFamily">
            <summary>
            Specifies the pitch and family of the font. The two low-order bits
            specify the pitch of the font. Font families describe the look of
            a font in a general way. They are intended for specifying fonts when
            the exact typeface desired is not available.
            </summary>
        </member><member name="F:SmartXLS.LOGFONT.lfFaceName">
            <summary>
            A null-terminated string that specifies the typeface name of the font.
            The length of this string must not exceed 32 characters, including
            the terminating null character. The EnumFontFamiliesEx function can
            be used to enumerate the typeface names of all currently available fonts.
            If lfFaceName is an empty string, GDI uses the first font that matches
            the other specified attributes.
            </summary>
        </member><member name="T:SmartXLS.CommentShape">
            <summary> This class represents a comment object.</summary>
        </member><member name="M:SmartXLS.CommentShape.seText(System.String)">
            <summary> set the text content of the comment</summary>
            <param name="text">comment content.
            </param>
        </member><member name="M:SmartXLS.CommentShape.setTextSelection(SmartXLS.RangeStyle,System.Int32,System.Int32)">
            <summary> Sets text selection formatting</summary>
            <param name="rs">rangestyle object contain the font information.
            </param>
            <param name="start">starting positon of selection.
            </param>
            <param name="end">ending positon of selection.
            </param>
        </member><member name="P:SmartXLS.CommentShape.Row">
            <summary> Row of the commented cell.</summary>
        </member><member name="P:SmartXLS.CommentShape.Col">
            <summary> Column of the commented cell.</summary>
        </member><member name="P:SmartXLS.CommentShape.Author">
            <summary> Comment's author.</summary>
        </member><member name="P:SmartXLS.CommentShape.Text">
            <summary> Comment text.</summary>
        </member><member name="P:SmartXLS.CommentShape.RichText">
            <summary> Comment text.</summary>
        </member><member name="T:SmartXLS.TextboxShape">
            <summary>
            represent the textBox shape
            </summary>
        </member><member name="P:SmartXLS.TextboxShape.Row">
            <summary> Row of the commented cell.</summary>
            <returns> row num
            </returns>
            <summary> set row of the commented cell</summary>
            <param name="row">row index
            </param>
        </member><member name="P:SmartXLS.TextboxShape.Col">
            <summary> Column of the commented cell.</summary>
            <returns> column
            </returns>
            <summary> set column of the commented cell.</summary>
            <param name="col">col index
            </param>
        </member><member name="P:SmartXLS.TextboxShape.Author">
            <summary> Comment's author.</summary>
            <returns> Comment's author.
            </returns>
            <summary> set author of the comment</summary>
            <param name="author">comment author
            </param>
        </member><member name="P:SmartXLS.TextboxShape.Text">
            <summary> Comment text.</summary>
        </member><member name="T:SmartXLS.HyperLink">
            <summary> This class represents a Hyperlink.</summary>
        </member><member name="F:SmartXLS.HyperLink.kRange">
            <summary>
            type range
            </summary>
        </member><member name="F:SmartXLS.HyperLink.kURLAbs">
            <summary>
            type url abs
            </summary>
        </member><member name="F:SmartXLS.HyperLink.kURLRel">
            <summary>
            type url rel
            </summary>
        </member><member name="F:SmartXLS.HyperLink.kFileAbs">
            <summary>
            type file abs
            </summary>
        </member><member name="F:SmartXLS.HyperLink.kFileRel">
            <summary>
            type file rel
            </summary>
        </member><member name="F:SmartXLS.HyperLink.kNet">
            <summary>
            type net
            </summary>
        </member><member name="M:SmartXLS.HyperLink.getRange">
            <summary> Returns the range to which this hyperlink applies.</summary>
            <returns> a string containing the range reference.
            </returns>
        </member><member name="M:SmartXLS.HyperLink.setRange(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> set the hyperlink range</summary>
            <param name="row1">top row
            </param>
            <param name="col1">left col
            </param>
            <param name="row2">bottom row
            </param>
            <param name="col2">right col
            </param>
        </member><member name="P:SmartXLS.HyperLink.LinkString">
            <summary> the hyperlink link string.</summary>
        </member><member name="P:SmartXLS.HyperLink.ToolTipString">
            <summary> the tool tip string.</summary>
        </member><member name="P:SmartXLS.HyperLink.ToolTip">
            <summary> the tool tip string</summary>
        </member><member name="T:SmartXLS.RangeArea">
            <summary>
            represent the range address
            </summary>
        </member><member name="M:SmartXLS.RangeArea.#ctor">
            <summary>
            empty constructer
            </summary>
        </member><member name="M:SmartXLS.RangeArea.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            construct with range 
            </summary>
            <param name="row1">start row</param>
            <param name="col1">start col</param>
            <param name="row2">end row</param>
            <param name="col2">end col</param>
        </member><member name="M:SmartXLS.RangeArea.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            construct with range 
            </summary>
            <param name="sheet">sheet index</param>
            <param name="row1">start row</param>
            <param name="col1">start col</param>
            <param name="row2">end row</param>
            <param name="col2">end col</param>
        </member><member name="M:SmartXLS.RangeArea.ToString">
            <summary>
            range address string in relative
            </summary>
            <returns></returns>
        </member><member name="P:SmartXLS.RangeArea.Col1">
            <summary>
            start col
            </summary>
        </member><member name="P:SmartXLS.RangeArea.Col2">
            <summary>
            end col
            </summary>
        </member><member name="P:SmartXLS.RangeArea.Row1">
            <summary>
            start row
            </summary>
        </member><member name="P:SmartXLS.RangeArea.Row2">
            <summary>
            end row
            </summary>
        </member><member name="P:SmartXLS.RangeArea.Sheet">
            <summary>
            sheet index
            </summary>
        </member><member name="T:SmartXLS.ConditionFormat">
            <summary> This class represents a conditional format condition and the associated formatting.
            Conditional cell formats are formats that are applied to a cell only when the value of a cell or formula meets a predetermined set of conditions.
            A range of cells can be formatted with up to three conditional formats.
            Conditional formats may inclued font colors and styles and cell border colors, styles and patterns.
            </summary>
        </member><member name="F:SmartXLS.ConditionFormat.eTypeNone">
            <summary> No condition type.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eTypeCell">
            <summary> Condition has a type of "Cell". This causes the cell to be compared to the appropriate formula using the specified operator.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eTypeFormula">
            <summary> Condition has a type of "Formula". The first formula is evaluated to determine whether to apply the format. The operator is ignored.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorNone">
            <summary> No operator.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorBetween">
            <summary> The format is applied if the cell is greater than or equal to formula1 and less than or equal to formula2.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorNotBetween">
            <summary> The format is applied if the cell is less than formula1 or greater than formula2.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorEqual">
            <summary> The format is applied if the cell is equal to formula1.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorNotEqual">
            <summary> The format is applied if the cell is not equal to formula1.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorGreaterThan">
            <summary> The format is applied if the cell is greater than formula1.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorLessThan">
            <summary> The format is applied if the cell is less than formula1.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorGreaterThanOrEqual">
            <summary> The format is applied if the cell is greater than or equal to formula1.</summary>
        </member><member name="F:SmartXLS.ConditionFormat.eOperatorLessThanOrEqual">
            <summary> The format is applied if the cell is less than or equal to formula1.</summary>
        </member><member name="M:SmartXLS.ConditionFormat.getFormula1(System.Int32,System.Int32)">
            <summary> Returns the first formula associated with this conditional format</summary>
            <param name="row">the row this formula is relative to.
            </param>
            <param name="col">the column this formula is relative to.
            </param>
            <returns> the formula. Returns null if there is no formula.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.ConditionFormat.setFormula1(System.String,System.Int32,System.Int32)">
            <summary> Sets the first formula associated with this conditional format</summary>
            <param name="formula">the first formula for this conditional format.
            </param>
            <param name="row">the row the formula will be relative to.
            </param>
            <param name="col">the column the formula will be relative to.
            </param>
            <throws>  Exception if there is an error in the formula. </throws>
        </member><member name="M:SmartXLS.ConditionFormat.getFormula2(System.Int32,System.Int32)">
            <summary> Returns the second formula associated with this conditional format</summary>
            <param name="row">the row this formula is relative to.
            </param>
            <param name="col">the column this formula is relative to.
            </param>
            <returns> the formula. Returns null if there is no formula.
            </returns>
            <throws>  Exception exception </throws>
        </member><member name="M:SmartXLS.ConditionFormat.setFormula2(System.String,System.Int32,System.Int32)">
            <summary> Sets the second formula associated with this conditional format</summary>
            <param name="formula">the first formula for this conditional format.
            </param>
            <param name="row">the row the formula will be relative to.
            </param>
            <param name="col">the column the formula will be relative to.
            </param>
            <throws>  Exception if there is an error in the formula. </throws>
        </member><member name="M:SmartXLS.ConditionFormat.setColorScale(System.Int32,System.Drawing.Color[])">
            <summary>
            Sets the colorscale's type of this conditional format.
            </summary>
            <param name="conType">2 or 3.</param>
            <param name="colors">colors to be set</param>
        </member><member name="P:SmartXLS.ConditionFormat.RangeStyle">
            <summary> formatting options for this conditional format from the specified RangeStyle object.
            Only the following attributes of cell formatting are supported by conditional formats: bold, italic, strikeout, underline, font color, border, fill pattern, fill foreground and fill background.
            </summary>
        </member><member name="T:SmartXLS.Gradient">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member><member name="P:SmartXLS.Gradient.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member><member name="P:SmartXLS.Gradient.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Brass">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Calm_Water">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Chrome">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_ChromeII">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Daybreak">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Desert">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Early_Sunset">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Fire">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Fog">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Gold">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_GoldII">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Horizon">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Late_Sunset">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Mahogany">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Moss">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Nightfall">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Ocean">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Parchment">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Peacock">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Rainbow">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_RainbowII">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Sapphire">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Silver">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad_Wheat">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad1">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad10">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad11">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad12">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad13">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad14">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad15">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad16">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad17">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad18">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad19">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad2">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad20">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad21">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad22">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad23">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad24">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad3">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad4">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad5">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad6">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad7">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad8">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Grad9">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt1">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt10">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt11">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt12">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt13">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt14">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt15">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt16">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt17">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt18">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt19">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt2">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt20">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt21">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt22">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt23">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt24">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt25">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt26">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt27">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt28">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt29">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt3">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt30">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt31">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt32">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt33">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt34">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt35">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt36">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt37">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt38">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt39">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt4">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt40">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt41">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt42">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt43">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt44">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt45">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt46">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt47">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt48">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt5">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt6">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt7">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt8">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Patt9">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text1">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text10">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text11">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text12">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text13">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text14">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text15">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text16">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text17">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text18">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text19">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text2">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text20">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text21">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text22">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text23">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text24">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text3">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text4">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text5">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text6">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text7">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text8">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="P:SmartXLS.Gradient.Text9">
            <summary>
              Looks up a localized resource of type System.Byte[].
            </summary>
        </member><member name="T:SmartXLS.DataValidation">
            <summary> This class contains the DataValidation AutoFilter.</summary>
        </member><member name="F:SmartXLS.DataValidation.eAny">
            <summary>
            Type any
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eInteger">
            <summary>
            Type integer
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eDecimal">
            <summary>
            Type decimal
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eUser">
            <summary>
            Type user
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eDate">
            <summary>
            Type date
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eTime">
            <summary>
            Type time
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eTextLength">
            <summary>
            Type text length
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eFormula">
            <summary>
            Type formula
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eStop">
            <summary>
            ErrorStyle stop
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eWarning">
            <summary>
            ErrorStyle warning
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eInfo">
            <summary>
            ErrorStyle info
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eBetween">
            <summary>
            Operator between
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eNotBetween">
            <summary>
            Operator not between
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eEqual">
            <summary>
            Operator equal
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eNotEqual">
            <summary>
            Operator not equal
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eGreater">
            <summary>
            Operator greater
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eLess">
            <summary>
            Operator less
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eGreaterOrEqual">
            <summary>
            Operator greater or equal
            </summary>
        </member><member name="F:SmartXLS.DataValidation.eLessOrEqual">
            <summary>
            Operator less or equal
            </summary>
        </member><member name="P:SmartXLS.DataValidation.Formula1">
            <summary>
            formula content
            </summary>
        </member><member name="P:SmartXLS.DataValidation.Formula2">
            <summary>
            formula content
            </summary>
        </member><member name="P:SmartXLS.DataValidation.ErrorBoxText">
            <summary>
            error box text
            </summary>
        </member><member name="P:SmartXLS.DataValidation.ErrorBoxTitle">
            <summary>
            error box title
            </summary>
        </member><member name="P:SmartXLS.DataValidation.PromtBoxText">
            <summary>
            promt box text
            </summary>
        </member><member name="P:SmartXLS.DataValidation.PromtBoxTitle">
            <summary>
            promt box title
            </summary>
        </member><member name="T:SmartXLS.BookPivotRangeModel">
            <summary>
             represent the pivot model
            </summary>
        </member><member name="M:SmartXLS.BookPivotRangeModel.setList(System.String)">
            <summary>
            Sets the Excel list that should be used for the PivotRange being defined.
            </summary>
            <param name="range">The range identifying the Excel list.</param>
        </member><member name="M:SmartXLS.BookPivotRangeModel.setDataQuery(SmartXLS.data.SXQuery)">
            <summary>
            Sets the Excel external data source that should be used for the PivotRange being defined.
            </summary>
            <param name="query">The range identifying the Excel list.</param>
        </member><member name="M:SmartXLS.BookPivotRangeModel.setLocation(System.Int32,System.Int32,System.Int32)">
            <summary>
            Defines the location of the PivotRange.
            </summary>
            <param name="sheet">The worksheet that contains the PivotRange.</param>
            <param name="row">The row where the PivotRange starts.</param>
            <param name="col">The column where the PivotRange starts.</param>
        </member><member name="M:SmartXLS.BookPivotRangeModel.getActivePivotRange">
            <summary>
            Returns the currently selected PivotRange.
            </summary>
            <returns>the currently selected PivotRange.</returns>
        </member><member name="M:SmartXLS.BookPivotRangeModel.refreshRange(SmartXLS.BookPivotRange)">
            <summary>
            refresh the pivot table from the data source
            </summary>
            <param name="pivotRange">active pivot range</param>
        </member></members></doc>