namespace inRiver.iPMC.Persistance
{
    using System;

    public class Field
    {
        public int EntityId { get; set; }

        public FieldType FieldType { get; set; }

        private string _fieldTypeId;

        public string FieldTypeId
        {
            get
            {
                var retVal = (string.IsNullOrEmpty(_fieldTypeId) && FieldType != null) ? FieldType.Id : _fieldTypeId;
                return retVal;
            }

            set => _fieldTypeId = value;
        }

        // could be string (to be backward compatible with existing DTO models) or could be stronger data type
        public object Data { get; set; }

        private string _dataType;

        public string DataType
        {
            get
            {
                var retVal = (string.IsNullOrEmpty(_dataType) && FieldType != null) ? FieldType.DataType : _dataType;
                return retVal;
            }

            set => _dataType = value;
        }

        public int Revision { get; set; }

        public DateTime LastModified { get; set; }

        public bool IsEmpty()
        {
            if (this.Data == null)
            {
                return true;
            }

            if (FieldType.DataType == "String")
            {
                return string.IsNullOrWhiteSpace(this.Data.ToString());
            }

            if (FieldType.DataType == "LocaleString")
            {
                return LocaleString.IsNullOrEmpty((LocaleString)this.Data);
            }

            if (FieldType.DataType == "Xml" || FieldType.DataType == "CVL")
            {
                return string.IsNullOrEmpty(this.Data.ToString());
            }

            return false;
        }
    }
}
