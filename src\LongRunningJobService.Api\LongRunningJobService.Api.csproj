<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Server\inRiver.Server.csproj" />
    <ProjectReference Include="..\LongRunningJobActor\LongRunningJobActor.csproj" />
    <ProjectReference Include="..\LongRunningJobService\LongRunningJobService.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development-LocalEuw.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.Development-LocalUse.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>


</Project>
