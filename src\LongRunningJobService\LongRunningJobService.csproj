<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IsServiceFabricServiceProject>True</IsServiceFabricServiceProject>
    <ServerGarbageCollection>True</ServerGarbageCollection>
    <RuntimeIdentifier>win7-x64</RuntimeIdentifier>
    <TargetLatestRuntimePatch>False</TargetLatestRuntimePatch>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="inRiver.Log" Version="2.1.1" />
    <PackageReference Include="inRiver.Remoting.iPMC" Version="8.19.2" />
    <PackageReference Include="inRiver.StackEssentials" Version="6.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.19.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.ServiceFabric.Native" Version="2.3.1" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.ServiceFabric.Actors" Version="6.1.1653" />
    <PackageReference Include="Microsoft.ServiceFabric.AspNetCore.Configuration" Version="6.1.1653" />
    <PackageReference Include="Microsoft.ServiceFabric.AspNetCore.Kestrel" Version="6.1.1653" />
    <PackageReference Include="Scrutor" Version="3.3.0" />
    <PackageReference Include="Serilog" Version="2.8.0" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="3.1.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Core\inRiver.Core.csproj" />
    <ProjectReference Include="..\inRiver.Server\inRiver.Server.csproj" />
    <ProjectReference Include="..\LongRunningJob.Core\LongRunningJob.Core.csproj" />
    <ProjectReference Include="..\LongRunningJobActor.Interfaces\LongRunningJobActor.Interfaces.csproj" />
    <ProjectReference Include="..\LongRunningJobActor\LongRunningJobActor.csproj" />
    <ProjectReference Include="..\Telemetry\Telemetry.csproj" />
  </ItemGroup>

</Project>
