namespace inRiver.Server.UnitTests.Completeness
{
    using System.Collections.Generic;
    using FakeItEasy;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Completeness.Criteria;
    using inRiver.Server.DataAccess;
    using Xunit;

    public class LinkTypeExistsCriteriaTests
    {
        [Fact]
        public void PercentageShouldBeZeroIfNoSettingsAreProvided()
        {
            // Arrange
            var linkTypeExistsCriteria = new LinkTypeExistsCriteria(null);

            // Act
            var result = linkTypeExistsCriteria.GetCriteriaCompletenessPercentage(int.MaxValue, new List<CompletenessRuleSetting>());

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void PercentageShouldBeZeroIfSettingsValueIsNotSet()
        {
            // Arrange
            var linkTypeExistsCriteria = new LinkTypeExistsCriteria(null);

            // Act
            var result = linkTypeExistsCriteria.GetCriteriaCompletenessPercentage(
                int.MaxValue,
                new List<CompletenessRuleSetting>
                {
                    new CompletenessRuleSetting { Key = "LinkTypeId", Value = null }
                });

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void PercentageShouldBeZeroIfNoOutboundLinksAreFound()
        {
            // Arrange
            var dataPersistanceFake = new Fake<IDataPersistance>();
            dataPersistanceFake.CallsTo(x => x.GetLinkCountForOutboundLinkType("keke", int.MaxValue)).Returns(0);
            var linkTypeExistsCriteria = new LinkTypeExistsCriteria(dataPersistanceFake.FakedObject);

            // Act
            var result = linkTypeExistsCriteria.GetCriteriaCompletenessPercentage(
                int.MaxValue,
                new List<CompletenessRuleSetting>
                {
                    new CompletenessRuleSetting { Key = "LinkTypeId", Value = "keke" }
                });

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public void PercentageShouldBeOneHundredIfAnyOutboundLinksAreFound()
        {
            // Arrange
            var dataPersistanceFake = new Fake<IDataPersistance>();
            dataPersistanceFake.CallsTo(x => x.GetLinkCountForOutboundLinkType("keke", int.MaxValue)).Returns(1);
            var linkTypeExistsCriteria = new LinkTypeExistsCriteria(dataPersistanceFake.FakedObject);

            // Act
            var result = linkTypeExistsCriteria.GetCriteriaCompletenessPercentage(
                int.MaxValue,
                new List<CompletenessRuleSetting>
                {
                    new CompletenessRuleSetting { Key = "LinkTypeId", Value = "keke" }
                });

            // Assert
            Assert.Equal(100, result);
        }
    }
}
