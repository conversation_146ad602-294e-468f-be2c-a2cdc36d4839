namespace Telemetry.Initializers
{
    using Microsoft.ApplicationInsights.Channel;
    using Microsoft.ApplicationInsights.Extensibility;

    public class CloudRoleNameTelemetryInitializer : ITelemetryInitializer
    {
        private readonly string cloudRoleName;

        public CloudRoleNameTelemetryInitializer(string cloudRoleName)
        {
            if (string.IsNullOrWhiteSpace(cloudRoleName))
            {
                throw new System.ArgumentNullException(nameof(cloudRoleName));
            }

            this.cloudRoleName = cloudRoleName;
        }

        public void Initialize(ITelemetry telemetry)
        {
            if (telemetry is null)
            {
                throw new System.ArgumentNullException(nameof(telemetry));
            }

            telemetry.Context.Cloud.RoleName = this.cloudRoleName;
        }
    }
}
