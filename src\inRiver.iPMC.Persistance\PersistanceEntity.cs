namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Globalization;
    using System.Linq;
    using System.Text;
    using System.Text.RegularExpressions;
    using System.Threading;
    using System.Threading.Tasks;
    using Dapper;
    using inRiver.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Query;
    using inRiver.Remoting.Util;
    using Newtonsoft.Json;
    using Exception = System.Exception;

    public class PersistanceEntity : BasePersistance, IPersistanceEntity
    {
        public const string InternalDateTimeFormat = "yyyy-MM-dd HH:mm:ss";
        public const int MAX_ENTITY_TO_QUERY = 4000;
        private const int ERROR_SQL_INVALID_COLUMN = 207;

        private readonly IPersistanceEntityType _persistanceEntityType;
        private readonly IPersistanceFieldType _persistanceFieldType;
        private readonly IPersistanceContentSegmentation _persistanceContentSegmentation;

        public PersistanceEntity(
            string connectionString,
            ICommonLogging logInstance,
            IPersistanceEntityType persistanceEntityType,
            IPersistanceFieldType persistanceFieldType,
            IPersistanceContentSegmentation persistanceContentSegmentation,
            IContentSegmentPermissionProvider contentSegmentProvider
        )

            : base(connectionString, logInstance, contentSegmentProvider)
        {
            _persistanceEntityType = persistanceEntityType;
            _persistanceFieldType = persistanceFieldType;
            _persistanceContentSegmentation = persistanceContentSegmentation;
        }

        public Entity AddEntity(Entity entity)
        {

            var now = DateTime.UtcNow;

            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();

                var transac = connection.BeginTransaction("AddEntity");

                try
                {
                    var command = connection.CreateCommand();

                    command.Transaction = transac;

                    command.CommandText =
                        "INSERT INTO Entity" +
                        " (Version, EntityTypeId, DateCreated, LastModified, ChangeSet, FieldSetId, CreatedBy, ModifiedBy, DisplayNameFieldTypeId, DisplayName, DisplayDescriptionFieldTypeId, DisplayDescription, MainPicture, MainPictureUrl, ContentSegmentationId)" +
                        " VALUES (@Version, @EntityTypeId, @DateCreated, @LastModified, @ChangeSet, @FieldSetId, @CreatedBy, @ModifiedBy, @DisplayNameFieldTypeId, @DisplayName, @DisplayDescriptionFieldTypeId, @DisplayDescription, @MainPicture, @MainPictureUrl, @ContentSegmentationId)" +
                        " SET @Id = SCOPE_IDENTITY()";

                    command.Parameters.AddWithValue("@Version", 1);
                    command.Parameters.AddWithValue("@EntityTypeId", entity.EntityType.Id);
                    command.Parameters.AddWithValue("@DateCreated", now);
                    command.Parameters.AddWithValue("@LastModified", now);
                    command.Parameters.AddWithValue("@ChangeSet", 1);
                    command.Parameters.AddWithValue("@CreatedBy", entity.CreatedBy ?? string.Empty); // inRiverContext.Default.CurrentUser);
                    command.Parameters.AddWithValue("@ModifiedBy", entity.ModifiedBy ?? string.Empty); // inRiverContext.Default.CurrentUser);
                    int segmentId = entity.Segment == null ? 0 : entity.Segment.Id;
                    command.Parameters.AddWithValue("@ContentSegmentationId", segmentId);

                    if (string.IsNullOrWhiteSpace(entity.FieldSetId))
                    {
                        command.Parameters.AddWithValue("@FieldSetId", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@FieldSetId", entity.FieldSetId);
                    }

                    if (entity.MainPictureId.HasValue)
                    {
                        command.Parameters.AddWithValue("@MainPicture", entity.MainPictureId.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@MainPicture", DBNull.Value);
                    }

                    if (string.IsNullOrWhiteSpace(entity.MainPictureUrl))
                    {
                        command.Parameters.AddWithValue("@MainPictureUrl", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@MainPictureUrl", entity.MainPictureUrl);
                    }


                    if (entity.DisplayName != null)
                    {
                        command.Parameters.AddWithValue("@DisplayNameFieldTypeId", entity.DisplayName.FieldType.Id);

                        if (entity.DisplayName.Data == null)
                        {
                            command.Parameters.AddWithValue("@DisplayName", DBNull.Value);
                        }
                        else switch (entity.DisplayName.DataType)
                            {
                                case "DateTime":
                                    command.Parameters.AddWithValue(
                                        "@DisplayName",
                                        DateTime.Parse(entity.DisplayName.Data.ToString()).ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture));
                                    break;
                                case "LocaleString":
                                    command.Parameters.AddWithValue(
                                        "@DisplayName",
                                        JsonConvert.SerializeObject(entity.DisplayName.Data));
                                    break;
                                default:
                                    command.Parameters.AddWithValue("@DisplayName", entity.DisplayName.Data);
                                    break;
                            }
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@DisplayNameFieldTypeId", DBNull.Value);
                        command.Parameters.AddWithValue("@DisplayName", DBNull.Value);
                    }

                    if (entity.DisplayDescription != null)
                    {
                        command.Parameters.AddWithValue(
                            "@DisplayDescriptionFieldTypeId",
                            entity.DisplayDescription.FieldType.Id);

                        if (entity.DisplayDescription.Data == null)
                        {
                            command.Parameters.AddWithValue("@DisplayDescription", DBNull.Value);
                        }
                        else switch (entity.DisplayDescription.DataType)
                            {
                                case "DateTime":
                                    command.Parameters.AddWithValue(
                                        "@DisplayDescription",
                                        DateTime.Parse(entity.DisplayDescription.Data.ToString()).ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture));
                                    break;
                                case "LocaleString":
                                    command.Parameters.AddWithValue(
                                        "@DisplayDescription",
                                        JsonConvert.SerializeObject(entity.DisplayDescription.Data));
                                    break;
                                default:
                                    command.Parameters.AddWithValue(
                                        "@DisplayDescription",
                                        entity.DisplayDescription.Data);
                                    break;
                            }
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@DisplayDescriptionFieldTypeId", DBNull.Value);
                        command.Parameters.AddWithValue("@DisplayDescription", DBNull.Value);
                    }

                    var idParameter = new SqlParameter("@Id", SqlDbType.Int)
                    {
                        Direction = ParameterDirection.Output
                    };
                    command.Parameters.Add(idParameter);

                    command.ExecuteNonQuery();

                    var id = (int)idParameter.Value;

                    entity.DateCreated = now;
                    entity.LastModified = now;
                    entity.Id = id;
                    entity.Version = 1;
                    entity.ChangeSet = 1;

                    entity.CreatedBy = entity.CreatedBy ?? string.Empty; // inRiverContext.Default.CurrentUser;
                    entity.ModifiedBy = entity.ModifiedBy ?? string.Empty; // inRiverContext.Default.CurrentUser;
                    entity.Segment = _persistanceContentSegmentation.GetSegment(segmentId);
                    // to insert to the ENTITY_* table
                    AddEntityFields(entity, command);

                    transac.Commit();
                }
                catch (Exception ex)
                {
                    try
                    {
                        transac.Rollback();
                    }
                    catch
                    {
                        // do nothing
                    }

                    throw ex;
                }
            }

            return entity;
        }

        public List<Entity> AddEntities(List<Entity> entities)
        {
            var now = DateTime.UtcNow;
            Entity entity;
            int entitiesProcessed = entities.Count;
            int currentEntity = 0;

            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();

                var transac = connection.BeginTransaction("AddEntities");

                try
                {
                    var command = connection.CreateCommand();

                    command.Transaction = transac;

                    command.CommandText = "create table #EntityIds (id int)";
                    command.ExecuteNonQuery();

                    // First Add Entity to Entity Table. We can do 2100 parameters in a bulk insert so we need to chunk the inserts in batches 100 to be safe
                    while (currentEntity < entitiesProcessed)
                    {
                        command.Parameters.Clear();

                        string valuesLines = string.Empty;

                        int startIndex = currentEntity;
                        int entitiesAdded = 0;

                        int BATCH_SIZE = 100;

                        // the following loop is to batch the insertions into a chunk of 100 (BATCH_SIZE) items
                        for (int index = 0; index < BATCH_SIZE && currentEntity < entitiesProcessed; index++)
                        {
                            entity = entities[currentEntity];

                            valuesLines = string.Format(
                                "{1},(@Version_{0}, @EntityTypeId_{0}, @DateCreated_{0}, @LastModified_{0}, @ChangeSet_{0}, @FieldSetId_{0}, @CreatedBy_{0}, @ModifiedBy_{0}, @DisplayNameFieldTypeId_{0}, @DisplayName_{0}, @DisplayDescriptionFieldTypeId_{0}, @DisplayDescription_{0}, @MainPicture_{0}, @MainPictureUrl_{0}, @ContentSegmentationId_{0})",
                            currentEntity, valuesLines);

                            command.Parameters.AddWithValue(string.Format("@Version_{0}", currentEntity), 1);
                            command.Parameters.AddWithValue(string.Format("@EntityTypeId_{0}", currentEntity), entity.EntityType.Id);
                            command.Parameters.AddWithValue(string.Format("@DateCreated_{0}", currentEntity), now);
                            command.Parameters.AddWithValue(string.Format("@LastModified_{0}", currentEntity), now);
                            command.Parameters.AddWithValue(string.Format("@ChangeSet_{0}", currentEntity), 1);
                            command.Parameters.AddWithValue(string.Format("@CreatedBy_{0}", currentEntity),
                                entity.CreatedBy ?? string.Empty); // inRiverContext.Default.CurrentUser);
                            command.Parameters.AddWithValue(string.Format("@ModifiedBy_{0}", currentEntity)
                                , entity.ModifiedBy ?? string.Empty); // inRiverContext.Default.CurrentUser);

                            int segmentId = entity.Segment == null ? 0 : entity.Segment.Id;
                            command.Parameters.AddWithValue(string.Format("@ContentSegmentationId_{0}", currentEntity), segmentId);

                            if (string.IsNullOrWhiteSpace(entity.FieldSetId))
                            {
                                command.Parameters.AddWithValue(string.Format("@FieldSetId_{0}", currentEntity), DBNull.Value);
                            }
                            else
                            {
                                command.Parameters.AddWithValue(string.Format("@FieldSetId_{0}", currentEntity), entity.FieldSetId);
                            }

                            if (entity.MainPictureId.HasValue)
                            {
                                command.Parameters.AddWithValue(string.Format("@MainPicture_{0}", currentEntity), entity.MainPictureId.Value);
                            }
                            else
                            {
                                command.Parameters.AddWithValue(string.Format("@MainPicture_{0}", currentEntity), DBNull.Value);
                            }

                            if (string.IsNullOrWhiteSpace(entity.MainPictureUrl))
                            {
                                command.Parameters.AddWithValue(string.Format("@MainPictureUrl_{0}", currentEntity), DBNull.Value);
                            }
                            else
                            {
                                command.Parameters.AddWithValue(string.Format("@MainPictureUrl_{0}", currentEntity), entity.MainPictureUrl);
                            }

                            if (entity.DisplayName != null)
                            {
                                command.Parameters.AddWithValue(string.Format("@DisplayNameFieldTypeId_{0}", currentEntity), entity.DisplayName.FieldType.Id);

                                if (entity.DisplayName.Data == null)
                                {
                                    command.Parameters.AddWithValue(string.Format("@DisplayName_{0}", currentEntity), DBNull.Value);
                                }
                                else
                                {
                                    switch (entity.DisplayName.DataType)
                                    {
                                        case "DateTime":
                                            command.Parameters.AddWithValue(
                                                string.Format("@DisplayName_{0}", currentEntity),
                                                DateTime.Parse(entity.DisplayName.Data.ToString())
                                                    .ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture));
                                            break;
                                        case "LocaleString":
                                            command.Parameters.AddWithValue(
                                                string.Format("@DisplayName_{0}", currentEntity),
                                                JsonConvert.SerializeObject(entity.DisplayName.Data));
                                            break;
                                        default:
                                            command.Parameters.AddWithValue(
                                                string.Format("@DisplayName_{0}", currentEntity),
                                                entity.DisplayName.Data);
                                            break;
                                    }
                                }
                            }
                            else
                            {
                                command.Parameters.AddWithValue(string.Format("@DisplayNameFieldTypeId_{0}", currentEntity), DBNull.Value);
                                command.Parameters.AddWithValue(string.Format("@DisplayName_{0}", currentEntity), DBNull.Value);
                            }


                            if (entity.DisplayDescription != null)
                            {
                                command.Parameters.AddWithValue(
                                    string.Format("@DisplayDescriptionFieldTypeId_{0}", currentEntity),
                                    entity.DisplayDescription.FieldType.Id);

                                if (entity.DisplayDescription.Data == null)
                                {
                                    command.Parameters.AddWithValue(string.Format("@DisplayDescription_{0}", currentEntity), DBNull.Value);
                                }
                                else
                                {
                                    switch (entity.DisplayDescription.DataType)
                                    {
                                        case "DateTime":
                                            command.Parameters.AddWithValue(
                                                string.Format("@DisplayDescription_{0}", currentEntity),
                                                DateTime.Parse(entity.DisplayDescription.Data.ToString())
                                                    .ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture));
                                            break;
                                        case "LocaleString":
                                            command.Parameters.AddWithValue(
                                                string.Format("@DisplayDescription_{0}", currentEntity),
                                                JsonConvert.SerializeObject(entity.DisplayDescription.Data));
                                            break;
                                        default:
                                            command.Parameters.AddWithValue(
                                                string.Format("@DisplayDescription_{0}", currentEntity),
                                                entity.DisplayDescription.Data);
                                            break;
                                    }
                                }
                            }
                            else
                            {
                                command.Parameters.AddWithValue(string.Format("@DisplayDescriptionFieldTypeId_{0}", currentEntity), DBNull.Value);
                                command.Parameters.AddWithValue(string.Format("@DisplayDescription_{0}", currentEntity), DBNull.Value);
                            }

                            currentEntity += 1;
                            entitiesAdded += 1;
                        }

                        valuesLines = valuesLines.Substring(1, valuesLines.Length - 1);

                        command.CommandText =
                            string.Format("{0} {1} VALUES {2}",
                            "INSERT INTO Entity (Version, EntityTypeId, DateCreated, LastModified, ChangeSet, FieldSetId, CreatedBy, ModifiedBy, DisplayNameFieldTypeId, DisplayName, DisplayDescriptionFieldTypeId, DisplayDescription, MainPicture, MainPictureUrl,ContentSegmentationId) ",
                            " OUTPUT inserted.ID INTO #EntityIds",
                            valuesLines);

                        command.ExecuteNonQuery();

                        command.CommandText = "Select id from #EntityIds";

                        int entityTableStart = startIndex;

                        using (SqlDataReader reader = command.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                var anEntity = entities[startIndex];

                                anEntity.Id = reader.GetInt32(0);
                                anEntity.DateCreated = now;
                                anEntity.LastModified = now;
                                anEntity.Version = 1;
                                anEntity.ChangeSet = 1;

                                anEntity.CreatedBy = anEntity.CreatedBy ?? string.Empty; // inRiverContext.Default.CurrentUser;
                                anEntity.ModifiedBy = anEntity.ModifiedBy ?? string.Empty; // inRiverContext.Default.CurrentUser;

                                startIndex += 1;
                            }

                            reader.Close();
                        }


                        command.CommandText = "Delete From #EntityIds";
                        command.ExecuteNonQuery();

                        AddEntityFields(entities, command, entityTableStart, BATCH_SIZE);
                    }

                    transac.Commit();
                }
                catch (Exception ex)
                {
                    try
                    {
                        transac.Rollback();
                    }
                    catch
                    {
                        // do nothing
                    }

                    LogInstance.Error($"PersistanceEntity.AddEntities() - possibly from import tool: {ex.Message}", ex, string.Empty, string.Empty);
                    throw ex;
                }
            }

            return entities;
        }

        private void AddEntityFields(Entity entity, SqlCommand command)
        {
            if (entity.Fields == null)
            {
                entity.Fields = new List<Field>();
            }

            command.Parameters.Clear();

            string columns = "EntityId, Revision, LastModified";
            string parameters = "@EntityID, @Revision, @LastModified";

            for (int i = 0; i < entity.Fields.Count; i++)
            {
                Field field = entity.Fields[i];
                field.Revision = 1; // this value will not be stored in DB in NDL,
                                    // however it is needed for the FieldRevisionHistory generation

                if (!field.IsEmpty())
                {
                    columns += ", " + field.FieldType.Id;
                    parameters += ", @" + field.FieldType.Id;
                }
            }

            command.CommandText = "INSERT INTO [" + CreateTableName(entity.EntityType.Id) + "] (" + columns + ") VALUES (" + parameters + ");";
            command.Parameters.AddWithValue("@EntityID", entity.Id);

            // ********** added revision (int) and LastModified (small date time)
            DateTime now = DateTime.UtcNow;
            command.Parameters.AddWithValue("@LastModified", now);
            command.Parameters.AddWithValue("@Revision", 1);

            for (int i = 0; i < entity.Fields.Count; i++)
            {
                Field field = entity.Fields[i];

                if (!field.IsEmpty())
                {
                    var value = field.Data;
                    value = PersistanceEntity.ToSqlFriendlyValue(value, field.FieldType.DataType);

                    command.Parameters.AddWithValue("@" + field.FieldType.Id, value);
                }
            }

            command.ExecuteNonQuery();
        }

        private void AddEntityFields(List<Entity> entities, SqlCommand command, int start, int rowNbrToAdd)
        {
            if (entities.Count == 0 || start > entities.Count)
                return;

            command.CommandText = string.Empty;
            command.Parameters.Clear();

            // Get the max number of rows to insert at one time based upon field count
            int maxParamenters = 2000;
            int maxCommandLength = 60000;
            int parametersAdded = 0;

            int entityIndex = start;

            string columns = string.Empty;

            string baseColumns = string.Format("EntityId, Revision, LastModified");

            string parameters = string.Empty;

            int rowsAdded = 0;

            while (rowsAdded < rowNbrToAdd && entityIndex < entities.Count)
            {
                columns = baseColumns;

                parameters = string.Format("@EntityID_{0}, @Revision_{0}, @LastModified_{0}", entityIndex);

                command.Parameters.AddWithValue(string.Format("@EntityID_{0}", entityIndex), entities[entityIndex].Id);
                parametersAdded += 1;
                command.Parameters.AddWithValue(string.Format("@LastModified_{0}", entityIndex), DateTime.Now);
                parametersAdded += 1;
                command.Parameters.AddWithValue(string.Format("@Revision_{0}", entityIndex), 1);
                parametersAdded += 1;

                for (int i = 0; i < entities[entityIndex].Fields.Count; i++)
                {
                    parameters += string.Format(", @{0}_{1}", entities[entityIndex].Fields[i].FieldType.Id, entityIndex);
                    columns += ", " + entities[entityIndex].Fields[i].FieldType.Id;

                    var value = entities[entityIndex].Fields[i].Data;
                    value = PersistanceEntity.ToSqlFriendlyValue(value, entities[entityIndex].Fields[i].FieldType.DataType);

                    command.Parameters.AddWithValue(string.Format("@{0}_{1}", entities[entityIndex].Fields[i].FieldType.Id, entityIndex), value);
                    parametersAdded += 1;
                }


                command.CommandText += string.Format("INSERT INTO {0} ({1}) VALUES ({2});", EntityTablePrefix + entities[start].EntityTypeId, columns, parameters);

                rowsAdded += 1;
                entityIndex += 1;

                if (parametersAdded >= maxParamenters || command.CommandText.Length >= maxCommandLength)
                {

                    command.ExecuteNonQuery();

                    // Clear values for next insert statement
                    command.CommandText = string.Empty;
                    parametersAdded = 0;
                    command.Parameters.Clear();
                }

            }
            if (!string.IsNullOrEmpty(command.CommandText))
                command.ExecuteNonQuery();
        }

        private static object ToSqlFriendlyValue(object value, string fieldDataType)
        {
            if (value == null)
            {
                value = DBNull.Value;
            }
            else if (value is DateTime || fieldDataType == DataTypeHelper.DataTypeEnum.DateTime.ToString())
            {
                value = (DateTime.Parse(value.ToString())).ToString(InternalDateTimeFormat);
            }
            else if (value is LocaleString || fieldDataType == DataTypeHelper.DataTypeEnum.LocaleString.ToString())
            {
                value = JsonConvert.SerializeObject(value);
            }

            return value;
        }

        public Entity GetFullEntity(
            int id,
            EntityType entityType,
            bool includePendingDelete = false,
            bool ignoreSegmentCheck = false // if 'true' then it will ignore the the permission check,
                                            // matching entity of any segments will be returned regardless of which segment user has permission to
            )
        {
            Entity entity = null;

            using (var connection = new SqlConnection(ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();

                    string segmentIdsInClause = ignoreSegmentCheck ? "" : permittedSegmentIdsClause();

                    command.Parameters.AddWithValue("@Id", id);
                    command.Parameters.AddWithValue("@IncludePendingDelete", includePendingDelete);

                    command.CommandText =
                        "SELECT EntityTypeId, DateCreated, LastModified, [Version], Locked, ChangeSet, FieldSetId, CreatedBy, ModifiedBy, Completeness, " +
                        " DisplayName, DisplayNameFieldTypeId, DisplayDescription, DisplayDescriptionFieldTypeId, ContentSegmentationId " +
                        $" FROM Entity WHERE {segmentIdsInClause} Id = @Id AND (PendingDelete IS NULL OR @IncludePendingDelete = 1)";

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            entity = new Entity();
                            entity.Id = id;
                            entity.EntityType = new EntityType { Id = reader.GetString(0) };
                            entity.DateCreated = reader.GetDateTime(1);
                            entity.LastModified = reader.GetDateTime(2);
                            entity.Version = reader.GetInt32(3);

                            if (!reader.IsDBNull(4))
                            {
                                entity.Locked = reader.GetString(4);
                            }

                            entity.ChangeSet = reader.GetInt32(5);

                            if (!reader.IsDBNull(6))
                            {
                                entity.FieldSetId = reader.GetString(6);
                            }

                            entity.CreatedBy = reader.GetString(7);
                            entity.ModifiedBy = reader.GetString(8);

                            if (!reader.IsDBNull(9))
                            {
                                entity.Completeness = reader.GetInt32(9);
                            }

                            if (!reader.IsDBNull(10) && !reader.IsDBNull(11))
                            {
                                entity.DisplayName = new Field()
                                {
                                    Data = reader.GetString(10),
                                    FieldTypeId = reader.GetString(11),
                                    EntityId = id,
                                    LastModified = entity.LastModified,
                                    Revision = 0
                                };
                            }

                            if (!reader.IsDBNull(12) && !reader.IsDBNull(13))
                            {
                                entity.DisplayDescription = new Field()
                                {
                                    Data = reader.GetString(12),
                                    FieldTypeId = reader.GetString(13),
                                    EntityId = id,
                                    LastModified = entity.LastModified,
                                    Revision = 0
                                };
                            }

                            if (!reader.IsDBNull(14))
                            {
                                var contentSegmentation = _persistanceContentSegmentation.GetSegment(reader.GetInt32(14));
                                entity.Segment = contentSegmentation;
                            }

                            entity.LoadLevel = LoadLevel.Shallow;
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    LogInstance.Error($"An unexpected error occured when getting entity {id}", ex, string.Empty, string.Empty);
                }
            }

            if (entity != null)
            {
                entity.EntityType = entityType ?? _persistanceEntityType.GetEntityType(entity.EntityType.Id, true);

                AssignFieldTypeBasedOnFieldTypeId(entity.DisplayName);
                AssignFieldTypeBasedOnFieldTypeId(entity.DisplayDescription);
            }

            return entity;
        }

        /**
         * assumption:
         *  - field.FieldTypeId is not blank
         * action:
         *  - field.FieldType will be assigned with a FieldType object corresponds to the field.FieldTypeId
         *  - field.DataType will be assigned with FieldType.DataType
         */
        private void AssignFieldTypeBasedOnFieldTypeId(Field field)
        {
            if (field != null && !string.IsNullOrEmpty(field.FieldTypeId))
            {
                field.FieldType = _persistanceFieldType.GetFieldType(field.FieldTypeId);
                field.DataType = (field.FieldType != null) ? field.FieldType.DataType : field.DataType;
            }
        }

        public List<Entity> GetEntities(List<int> entityIds)
        {
            List<Entity> entities = new List<Entity>();

            int startIndex = 0;
            int maxSingleCall = MAX_ENTITY_TO_QUERY;

            Dictionary<int, Segment> contentSegments = new Dictionary<int, Segment>();

            while (startIndex < entityIds.Count)
            {
                if (startIndex + maxSingleCall > entityIds.Count)
                    maxSingleCall = entityIds.Count - startIndex;

                var batchOfEntities = GetEntitiesInternal(
                    $"WHERE Id IN ({string.Join(",", entityIds.GetRange(startIndex, maxSingleCall))})",
                    contentSegments);

                entities.AddRange(batchOfEntities);

                startIndex += maxSingleCall;

            }

            return entities;
        }

        public async Task<IEnumerable<Entity>> GetEntitiesAsync(IEnumerable<int> entityIds)
        {
            var entities = new List<Entity>();
            var contentSegments = new Dictionary<int, Segment>();

            if (!entityIds.Any())
            {
                return new List<Entity>();
            }

            var totalIdsCount = entityIds.Count();
            var batchCounter = 0;

            do
            {
                var batchIds = entityIds.Skip(batchCounter).Take(MAX_ENTITY_TO_QUERY).ToList();
                batchCounter += batchIds.Count;

                var commaSeparatedIds = string.Join(",", batchIds.Select(n => n.ToString()));

                using (var connection = new SqlConnection(this.ConnectionString))
                {
                    await using var command = connection.CreateCommand();
                    var segmentIdsInClause = permittedSegmentIdsClause();

                    command.CommandText =
                        $@"SELECT Id, EntityTypeId, DateCreated, LastModified, [Version], Locked, ChangeSet, FieldSetId, " +
                          " CreatedBy, ModifiedBy, [MainPicture], [DisplayNameFieldTypeId], [DisplayName], " +
                          " [DisplayDescriptionFieldTypeId], [DisplayDescription], Completeness, [MainPictureUrl], ContentSegmentationId " +
                          $" FROM Entity WHERE {segmentIdsInClause} Id IN ({commaSeparatedIds}) AND PendingDelete IS NULL";

                    command.CommandTimeout = 3000;

                    await connection.OpenAsync();

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var entity = new Entity
                            {
                                Id = reader.GetInt32(0),
                                EntityTypeId = reader.GetString(1),
                                DateCreated = reader.GetDateTime(2),
                                LastModified = reader.GetDateTime(3),
                                Version = reader.GetInt32(4)
                            };

                            if (!reader.IsDBNull(5))
                            {
                                entity.Locked = reader.GetString(5);
                            }

                            entity.ChangeSet = reader.GetInt32(6);

                            if (!reader.IsDBNull(7))
                            {
                                entity.FieldSetId = reader.GetString(7);
                            }

                            entity.CreatedBy = reader.GetString(8);
                            entity.ModifiedBy = reader.GetString(9);

                            if (!reader.IsDBNull(10))
                            {
                                entity.MainPictureId = reader.GetInt32(10);
                            }

                            entity.DisplayName = GetFieldFromEntityDataRecord(reader, entity, 11, 12);

                            entity.DisplayDescription = GetFieldFromEntityDataRecord(reader, entity, 13, 14);

                            if (!reader.IsDBNull(15))
                            {
                                entity.Completeness = reader.GetInt32(15);
                            }

                            if (!reader.IsDBNull(16))
                            {
                                entity.MainPictureUrl = reader.GetString(16);
                            }

                            if (!reader.IsDBNull(17))
                            {
                                var segmentId = reader.GetInt32(17);

                                if (!contentSegments.TryGetValue(segmentId, out var contentSegment))
                                {
                                    contentSegment = await this._persistanceContentSegmentation.GetSegmentAsync(segmentId);
                                    contentSegments.Add(contentSegment.Id, contentSegment);
                                }

                                entity.Segment = contentSegment;
                            }

                            entity.LoadLevel = LoadLevel.Shallow;

                            entities.Add(entity);
                        }
                    }
                }
            }
            while (batchCounter < totalIdsCount);

            return entities;
        }

        public async Task<List<Entity>> GetEntitiesAsync(List<int> entityIds)
        {
            List<Entity> entities = new List<Entity>();

            int startIndex = 0;
            int maxSingleCall = MAX_ENTITY_TO_QUERY;

            Dictionary<int, Segment> contentSegments = new Dictionary<int, Segment>();

            while (startIndex < entityIds.Count)
            {
                if (startIndex + maxSingleCall > entityIds.Count)
                    maxSingleCall = entityIds.Count - startIndex;

                var batchOfEntities = await GetEntitiesInternalAsync(
                    $"WHERE Id IN ({string.Join(",", entityIds.GetRange(startIndex, maxSingleCall))})",
                    contentSegments);

                entities.AddRange(batchOfEntities);

                startIndex += maxSingleCall;

            }

            return entities;
        }

        private void GetEntitiesFields(List<Entity> entities)
        {
            Dictionary<int, Entity> entityDictionary = entities.ToDictionary(x => x.Id, x => x);
            string entityTypeId = entities[0].EntityTypeId;
            Entity entity;

            int MaxEntityToQuery = MAX_ENTITY_TO_QUERY;
            int currentStart = 0;
            List<int> entityIds = entities.Select(r => r.Id).Distinct().ToList();

            List<FieldType> fieldTypes = _persistanceFieldType.GetFieldTypesForEntityType(entityTypeId);

            using (SqlConnection connection = new SqlConnection(ConnectionString))
            {

                SqlCommand command = connection.CreateCommand();
                connection.Open();
                int amountToQuery = 0;

                while (currentStart < entities.Count)
                {

                    command.Parameters.Clear();
                    if (currentStart + MaxEntityToQuery > entities.Count)
                        amountToQuery = entities.Count - currentStart;
                    else
                        amountToQuery = MaxEntityToQuery;

                    command.CommandText =
                        $"SELECT * FROM {CreateTableName(entityTypeId)} WHERE EntityId IN ({string.Join(",", entityIds.GetRange(currentStart, amountToQuery))})";


                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        int fieldIdx;


                        while (reader.Read())
                        {
                            int entityId = int.Parse(reader["EntityId"].ToString());
                            entity = entityDictionary[entityId];
                            if (entity.Fields == null)
                                entity.Fields = new List<Field>();

                            for (int i = 0; i < fieldTypes.Count; i++)
                            {
                                FieldType fieldType = fieldTypes[i];

                                Field field = new Field();
                                field.FieldType = fieldType;
                                field.FieldTypeId = fieldType.Id;
                                field.DataType = fieldType.DataType;
                                field.EntityId = entityId;

                                // If the field does not return, just ignore geting the information
                                try
                                {
                                    fieldIdx = reader.GetOrdinal(fieldType.Id);
                                    if (!reader.IsDBNull(fieldIdx))
                                    {
                                        field.Data = PersistanceEntity.ReadToObject(reader, fieldIdx, fieldType.DataType);
                                    }
                                }
                                catch
                                {
                                }

                                int lastModifiedIdx = reader.GetOrdinal("LastModified");
                                if (reader.IsDBNull(lastModifiedIdx))
                                {
                                    field.LastModified = entity.LastModified;
                                }
                                else
                                {
                                    field.LastModified = reader.GetDateTime(lastModifiedIdx);

                                }

                                field.Revision = entity.ChangeSet;

                                field.DataType = fieldType.DataType;

                                if (field.DataType == DataTypeHelper.DataTypeEnum.DateTime.ToString() && !string.IsNullOrEmpty(field.Data as string))
                                {
                                    field.Data = Convert.ToDateTime(field.Data).ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                }
                                entity.Fields.Add(field);
                            }
                        }

                        reader.Close();
                        currentStart += MaxEntityToQuery;
                    }
                }
                connection.Close();
            }

        }

        public List<Entity> GetEntitiesWithData(List<int> entityIds, CancellationToken cancellationToken)
        {
            List<Entity> entities = new List<Entity>();

            int startIndex = 0;
            int maxSingleCall = MAX_ENTITY_TO_QUERY;

            Dictionary<int, Segment> contentSegments = new Dictionary<int, Segment>();

            while (startIndex < entityIds.Count)
            {
                cancellationToken.ThrowIfCancellationRequested();
                if (startIndex + maxSingleCall > entityIds.Count)
                    maxSingleCall = entityIds.Count - startIndex;

                var batchOfEntities = GetEntitiesInternal(
                        $"WHERE Id IN ({string.Join(",", entityIds.GetRange(startIndex, maxSingleCall))})",
                        contentSegments);

                entities.AddRange(batchOfEntities);

                startIndex += maxSingleCall;
            }
            // If entities are not found return
            if (entities.Count == 0)
                return entities;

            List<string> entityTypes = new List<string>();

            entityTypes = entities.Select(x => x.EntityTypeId).Distinct().ToList();

            foreach (var item in entityTypes)
            {
                cancellationToken.ThrowIfCancellationRequested();
                var entitiesForType = entities.Where(x => x.EntityTypeId.Equals(item)).ToList();
                GetEntitiesFields(entitiesForType);
            }

            return entities;
        }

        public async Task<List<Entity>> GetEntitiesWithDataAsync(List<int> entityIds, CancellationToken cancellationToken)
        {
            List<Entity> entities = new List<Entity>();

            int startIndex = 0;
            int maxSingleCall = MAX_ENTITY_TO_QUERY;

            Dictionary<int, Segment> contentSegments = new Dictionary<int, Segment>();

            var segmentIdsInClause = this.permittedSegmentIdsClause();

            while (startIndex < entityIds.Count)
            {
                cancellationToken.ThrowIfCancellationRequested();
                if (startIndex + maxSingleCall > entityIds.Count)
                    maxSingleCall = entityIds.Count - startIndex;

                var batchOfEntities = await GetEntitiesInternalAsync(
                        $"WHERE {segmentIdsInClause} Id IN ({string.Join(",", entityIds.GetRange(startIndex, maxSingleCall))})",
                        contentSegments);

                entities.AddRange(batchOfEntities);

                startIndex += maxSingleCall;
            }
            // If entities are not found return
            if (entities.Count == 0)
                return entities;

            List<string> entityTypes = new List<string>();

            entityTypes = entities.Select(x => x.EntityTypeId).Distinct().ToList();

            foreach (var item in entityTypes)
            {
                cancellationToken.ThrowIfCancellationRequested();
                var entitiesForType = entities.Where(x => x.EntityTypeId.Equals(item)).ToList();
                GetEntitiesFields(entitiesForType);
            }

            return entities;
        }

        public DataTable GetEntitiesAsDataTable(List<int> entityIds, string entityType, List<FieldType> fields, ContentSegmentationEnum segmentationOption, CancellationToken cancellationToken)
        {
            DataTable entityTable = new DataTable();

            int startIndex = 0;
            int maxSingleCall = MAX_ENTITY_TO_QUERY;

            if (entityIds is null)
            {
                return entityTable;
            }

            var segmentIdsInClause = this.permittedSegmentIdsClause();

            while (startIndex < entityIds.Count)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (startIndex + maxSingleCall > entityIds.Count)
                    maxSingleCall = entityIds.Count - startIndex;

                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();

                    var command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    var fieldTypes = new List<FieldType>();

                    string columns = "EntityId as sys_Id, E.fieldSetId as sys_fieldset, ";

                    if (segmentationOption != ContentSegmentationEnum.None)
                    {
                        columns += ((segmentationOption == ContentSegmentationEnum.SegmentationIdAndSegmentationName)
                            ? $"C.{ContentSegmentationEnum.ID} as {ContentSegmentationEnum.ID.GetDescription().Replace(" ", "")}, C.{ContentSegmentationEnum.SegmentName} as {ContentSegmentationEnum.SegmentName.GetDescription().Replace(" ", "")}"
                            : $"C.{segmentationOption} as {segmentationOption.GetDescription().Replace(" ", "")}") + ",";
                    }
                    columns += PersistanceEntity.ToCommaDelimitedColumnNames(fields);
                    columns = columns.Substring(0, columns.Length - 2);

                    command.CommandText = "SELECT " + columns +
                                          " FROM [" + CreateTableName(entityType) + $"] T" +
                                          $" INNER JOIN Entity E on T.EntityId = E.Id" +
                                          $" INNER JOIN ContentSegmentation C on E.ContentSegmentationId = C.ID" +
                                          $" WHERE {segmentIdsInClause} EntityId IN ({string.Join(",", entityIds.GetRange(startIndex, maxSingleCall))})";

                    using (var sqlAdapter = new SqlDataAdapter(command))
                    {
                        sqlAdapter.Fill(entityTable);
                    }

                    connection.Close();
                }
                startIndex += maxSingleCall;
            }

            return entityTable;
        }

        public List<Field> GetFieldsForEntity(Entity entity, string fieldTypeId = null)
        {
            var fieldTypes = new List<FieldType>();

            if (entity.EntityType != null && entity.EntityType.FieldTypes != null && entity.EntityType.FieldTypes.Count > 0)
            {
                foreach (FieldType fieldType in entity.EntityType.FieldTypes)
                {
                    fieldTypes.Add(fieldType);
                }
            }
            else
            {
                if (fieldTypeId == null)
                    fieldTypes = _persistanceFieldType.GetFieldTypesForEntityType(entity.EntityTypeId);
                else
                {
                    var ft = _persistanceFieldType.GetFieldType(fieldTypeId);
                    if (ft != null)
                    {
                        fieldTypes.Add(ft);
                    }
                }
            }

            var ret = GetFieldsInternal(entity, fieldTypes);
            return ret;
        }

        public List<Field> GetFields(int entityId, List<string> fieldTypeIds)
        {
            var entity = GetEntity(entityId);

            if (entity != null)
            {
                var entityType = _persistanceEntityType.GetEntityType(entity.EntityTypeId, true);
                var fieldTypes = entityType.FieldTypes;
                var fieldTypesDictionary = fieldTypes.ToDictionary(x => x.Id, x => x);

                List<FieldType> validFieldTypes = new List<FieldType>();

                foreach (var fieldTypeId in fieldTypeIds)
                {
                    FieldType fieldType;

                    if (!string.IsNullOrWhiteSpace(fieldTypeId) && fieldTypesDictionary.TryGetValue(fieldTypeId, out fieldType))
                    {
                        validFieldTypes.Add(fieldType);
                    }
                }

                if (validFieldTypes.Count > 0)
                {
                    return GetFieldsInternal(entity, validFieldTypes);
                }
            }

            return new List<Field>();
        }

        public bool DeleteEntity(int id, string entityTypeId = null)
        {
            // if optional parameter 'entityTypeId'is not supplied
            if (entityTypeId == null)
            {
                var entityIds = new List<int>() { id };
                var entities = ( this.GetEntities(entityIds));

                if (entities.Count > 0)
                {
                    entityTypeId = entities[0].EntityTypeId;
                }
            }

            // if entityTypeId is blank, throw exception
            if (string.IsNullOrEmpty(entityTypeId))
            {
                throw new PersistanceException($"DeleteEntity(id: {id}) - unable to find the entityTypeId");
            }

            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();
                command.CommandTimeout = 3000;
                command.CommandText = "DELETE FROM WorkAreaFolderEntities WITH (ROWLOCK)  WHERE EntityId = @Id;" +
                                      "DELETE FROM CompletenessEntityState WITH (ROWLOCK)  WHERE EntityId = @Id;" +
                                      "DELETE FROM EntityComment WITH (ROWLOCK)  WHERE EntityId = @Id;" +
                                      "DELETE FROM Link WITH (ROWLOCK)  WHERE SourceEntityId = @Id OR TargetEntityId = @Id;" +
                                      "UPDATE Link WITH (ROWLOCK) SET LinkEntityId = NULL WHERE LinkEntityId = @Id;" +
                                      "DELETE FROM EntityHistoryRevisions WITH (ROWLOCK)  WHERE EntityId = @Id;" +
                                      "DELETE FROM EntityHistory WITH (ROWLOCK)  WHERE EntityId = @Id;" +
                                      "DELETE FROM FieldRevisionHistory WITH (ROWLOCK)  WHERE EntityId = @Id;" +
                                      $"DELETE FROM {CreateTableName(entityTypeId)} WITH (ROWLOCK)  WHERE EntityId = @Id;" +
                                      "DELETE FROM Entity  WITH (ROWLOCK) WHERE Id = @Id;";

                command.Parameters.AddWithValue("@Id", id);

                connection.Open();
                command.ExecuteNonQuery();
                connection.Close();
            }
            catch (Exception ex)
            {
                if (connection.State != ConnectionState.Closed)
                {
                    connection.Close();
                }

                this.LogInstance.Error($"An unexpected error occured when deleting entity {id}", ex, string.Empty, string.Empty);
            }

            return true;
        }

        // copied code from Portal. This will use the field.Revision from field revision history
        private List<Field> GetFieldsInternal(Entity entity, List<FieldType> fieldTypes)
        {
            var fields = new List<Field>();

            //"ChannelPublished" column does not exists in the Publication entity Table. This specific condition must not log the error details. Hence this condition.
            if (string.Equals(entity.EntityTypeId, "Publication", StringComparison.CurrentCultureIgnoreCase) && fieldTypes != null && fieldTypes.Any(x => x.Id.ToLower().Contains("channelpublished")))
            {
                return fields;
            }
            try
            {
                using (var connection = new SqlConnection(this.ConnectionString))
                {
                    connection.Open();
                    var fieldsRevisionFromHistory = this.GetFieldRevisionNumbers(connection, entity.Id);

                    // To avoid searching for columns that does not exists in the entity type table, which leads to exceptions.
                    var existingFieldTypes = this.GetExistingFieldTypesForEntityType(fieldTypes, entity.EntityTypeId, connection);
                    var command = connection.CreateCommand();
                    var columns = $"{ToCommaDelimitedColumnNames(existingFieldTypes)} Revision, LastModified";
                    command.CommandText = $"SELECT {columns} FROM [{CreateTableName(entity.EntityTypeId)}] WHERE EntityId = @EntityId";
                    command.Parameters.AddWithValue("@EntityId", entity.Id);
                    var lastModifiedIdx = existingFieldTypes.Count + 1;
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            for (int i = 0; i < existingFieldTypes.Count; i++)
                            {
                                FieldType fieldType = existingFieldTypes[i];
                                if (fieldType != null)
                                {
                                    Field field = new Field();
                                    field.FieldType = fieldType;
                                    field.FieldTypeId = fieldType.Id;
                                    field.DataType = fieldType.DataType;

                                    if (!reader.IsDBNull(i))
                                    {
                                        field.Data = PersistanceEntity.ReadToObject(reader, i, fieldType.DataType);
                                    }

                                    field.EntityId = entity.Id;

                                    if (reader.IsDBNull(lastModifiedIdx))
                                    {
                                        field.LastModified = entity.LastModified;
                                    }
                                    else
                                    {
                                        field.LastModified = reader.GetDateTime(lastModifiedIdx);
                                    }

                                    field.Revision = fieldsRevisionFromHistory.GetValueOrDefault(field.FieldTypeId, 0);
                                    if (field.Revision == 0)
                                    {
                                        field.Revision = entity.ChangeSet;
                                    }

                                    field.DataType = fieldType.DataType;
                                    if (field.DataType == DataTypeHelper.DataTypeEnum.DateTime.ToString()
                                        && !string.IsNullOrEmpty(field.Data as string))
                                    {
                                        field.Data = Convert.ToDateTime(field.Data).ToString(
                                            InternalDateTimeFormat,
                                            CultureInfo.InvariantCulture);
                                    }

                                    fields.Add(field);
                                }
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                LogInstance.Error(
                    $"Error occurred in GetFieldsInternal {ex.Message}",
                    ex,
                    "PersistanceEntity",
                    string.Empty);

                if (!(ex is SqlException) || ((SqlException)ex).Number != ERROR_SQL_INVALID_COLUMN)
                {
                    throw;
                }
            }

            return fields;
        }

        /// <summary>
        /// Gets the latest revision numbers of fields from the field revision history.
        /// </summary>
        /// <returns>The dictionary of FieldTypeIds to Revision Number</returns>
        private Dictionary<string,int> GetFieldRevisionNumbers(SqlConnection customerDataBaseConnection, int entityId)
        {
            var fieldRevisionNumbers = new Dictionary<string, int>();
            var result = customerDataBaseConnection.Query(
                @"SELECT [FieldTypeId], MAX(Revision) as [Revision]
                                FROM FieldRevisionHistory
                                WHERE EntityId = @EntityId
                                GROUP BY [FieldTypeId]
                                ORDER BY 2 DESC",
                new { EntityId = entityId });

            foreach (var row in result)
            {
                fieldRevisionNumbers.Add(row.FieldTypeId, row.Revision);
            }

            return fieldRevisionNumbers;
        }

        private List<FieldType> GetExistingFieldTypesForEntityType(List<FieldType> fieldTypes, string entityTypeId, SqlConnection connection)
        {
            var result = new List<FieldType>();
            var columns = PersistanceEntity.ToCommaDelimitedTextColumnNames(fieldTypes);
            if (string.IsNullOrEmpty(columns))
            {
                return result;
            }

            using (var command = connection.CreateCommand())
            {
                command.CommandText = $"SELECT [name] FROM sys.columns WHERE Object_ID = Object_ID(N'[dbo].[{CreateTableName(entityTypeId)}]') AND [name] in ({columns})";
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        if (reader.IsDBNull(0))
                        {
                            continue;
                        }

                        var columnName = reader.GetString(0);
                        var fieldType = fieldTypes.FirstOrDefault(p => p.Id == columnName);
                        if (fieldType != null)
                        {
                            result.Add(fieldType);
                        }
                    }
                }
            }

            return result;
        }

        private static Field GetFieldFromEntityDataRecord(IDataRecord dataRecord, Entity entity, int fieldTypeIdIndex, int dataIndex)
        {
            return dataRecord.IsDBNull(fieldTypeIdIndex)
                       ? null
                       : new Field
                       {
                           EntityId = entity.Id,
                           LastModified = entity.LastModified,
                           FieldTypeId = dataRecord.GetString(fieldTypeIdIndex),
                           Data =
                                     dataRecord.IsDBNull(dataIndex)
                                         ? string.Empty
                                         : dataRecord.GetString(dataIndex),
                           Revision = entity.ChangeSet
                       };
        }

        private List<Entity> GetEntitiesInternal(
            string whereClause,
            Dictionary<int, Segment> contentSegments = null, // dictionary containing Id to ContentSegmentation object
            SqlParameter[] parameters = null,
            int? maxCount = null)
        {
            var entities = new List<Entity>();

            if (contentSegments == null)
                contentSegments = new Dictionary<int, Segment>();

            var top = maxCount == null ? string.Empty : $"TOP({maxCount})";

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();

                string segmentIdsInClause = permittedSegmentIdsClause();

                segmentIdsInClause = segmentIdsInClause.Replace("AND", "");
                if (!string.IsNullOrWhiteSpace(segmentIdsInClause))
                {
                    if (string.IsNullOrWhiteSpace(whereClause))
                    {
                        segmentIdsInClause = $"WHERE {segmentIdsInClause}";
                    }
                    else
                    {
                        segmentIdsInClause = $"AND {segmentIdsInClause}";
                    }
                }

                command.CommandText =
                    $@"SELECT {top} Id, EntityTypeId, DateCreated, LastModified, [Version], Locked, ChangeSet, FieldSetId, " +
                      " CreatedBy, ModifiedBy, [MainPicture], [DisplayNameFieldTypeId], [DisplayName], " +
                      " [DisplayDescriptionFieldTypeId], [DisplayDescription], Completeness, [MainPictureUrl], ContentSegmentationId " +
                      $" FROM Entity {whereClause} {segmentIdsInClause}";

                command.CommandTimeout = 3000;

                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var entity = new Entity
                        {
                            Id = reader.GetInt32(0),
                            EntityTypeId = reader.GetString(1),
                            DateCreated = reader.GetDateTime(2),
                            LastModified = reader.GetDateTime(3),
                            Version = reader.GetInt32(4)
                        };

                        if (!reader.IsDBNull(5))
                        {
                            entity.Locked = reader.GetString(5);
                        }

                        entity.ChangeSet = reader.GetInt32(6);

                        if (!reader.IsDBNull(7))
                        {
                            entity.FieldSetId = reader.GetString(7);
                        }

                        entity.CreatedBy = reader.GetString(8);
                        entity.ModifiedBy = reader.GetString(9);

                        if (!reader.IsDBNull(10))
                        {
                            entity.MainPictureId = reader.GetInt32(10);
                        }

                        entity.DisplayName = GetFieldFromEntityDataRecord(reader, entity, 11, 12);

                        entity.DisplayDescription = GetFieldFromEntityDataRecord(reader, entity, 13, 14);

                        if (!reader.IsDBNull(15))
                        {
                            entity.Completeness = reader.GetInt32(15);
                        }

                        if (!reader.IsDBNull(16))
                        {
                            entity.MainPictureUrl = reader.GetString(16);
                        }

                        if (!reader.IsDBNull(17))
                        {
                            int segmentId = reader.GetInt32(17);

                            Segment contentSegment = null;

                            if (!contentSegments.TryGetValue(segmentId, out contentSegment))
                            {
                                contentSegment = _persistanceContentSegmentation.GetSegment(segmentId);
                                contentSegments.Add(contentSegment.Id, contentSegment);
                            }
                            entity.Segment = contentSegment;
                        }

                        entity.LoadLevel = LoadLevel.Shallow;

                        entities.Add(entity);
                    }
                }
            }

            return entities;
        }

        private async Task<IList<Entity>> GetEntitiesInternalAsync(
            string whereClause,
            Dictionary<int, Segment> contentSegments = null, // dictionary containing Id to ContentSegmentation object
            SqlParameter[] parameters = null,
            int? maxCount = null)
        {
            var entities = new List<Entity>();

            if (contentSegments == null)
                contentSegments = new Dictionary<int, Segment>();

            var top = maxCount == null ? string.Empty : $"TOP({maxCount})";

            using var connection = new SqlConnection(this.ConnectionString);

            var command = connection.CreateCommand();

            var segmentIdsInClause = permittedSegmentIdsClause();

            segmentIdsInClause = segmentIdsInClause.Replace("AND", "");
            if (!string.IsNullOrWhiteSpace(segmentIdsInClause))
            {
                if (string.IsNullOrWhiteSpace(whereClause))
                {
                    segmentIdsInClause = $"WHERE {segmentIdsInClause}";
                }
                else
                {
                    segmentIdsInClause = $"AND {segmentIdsInClause}";
                }
            }

            command.CommandText =
                @$"SELECT {top} Id, EntityTypeId, DateCreated, LastModified, [Version], Locked, ChangeSet, FieldSetId, 
CreatedBy, ModifiedBy, [MainPicture], [DisplayNameFieldTypeId], [DisplayName], 
[DisplayDescriptionFieldTypeId], [DisplayDescription], Completeness, [MainPictureUrl], ContentSegmentationId 
FROM Entity {whereClause} {segmentIdsInClause}";

            command.CommandTimeout = 3000;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            await connection.OpenAsync();

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var entity = new Entity
                {
                    Id = reader.GetInt32(0),
                    EntityTypeId = reader.GetString(1),
                    DateCreated = reader.GetDateTime(2),
                    LastModified = reader.GetDateTime(3),
                    Version = reader.GetInt32(4)
                };

                if (!reader.IsDBNull(5))
                {
                    entity.Locked = reader.GetString(5);
                }

                entity.ChangeSet = reader.GetInt32(6);

                if (!reader.IsDBNull(7))
                {
                    entity.FieldSetId = reader.GetString(7);
                }

                entity.CreatedBy = reader.GetString(8);
                entity.ModifiedBy = reader.GetString(9);

                if (!reader.IsDBNull(10))
                {
                    entity.MainPictureId = reader.GetInt32(10);
                }

                entity.DisplayName = GetFieldFromEntityDataRecord(reader, entity, 11, 12);

                entity.DisplayDescription = GetFieldFromEntityDataRecord(reader, entity, 13, 14);

                if (!reader.IsDBNull(15))
                {
                    entity.Completeness = reader.GetInt32(15);
                }

                if (!reader.IsDBNull(16))
                {
                    entity.MainPictureUrl = reader.GetString(16);
                }

                if (!reader.IsDBNull(17))
                {
                    int segmentId = reader.GetInt32(17);

                    Segment contentSegment = null;

                    if (!contentSegments.TryGetValue(segmentId, out contentSegment))
                    {
                        contentSegment = _persistanceContentSegmentation.GetSegment(segmentId);
                        contentSegments.Add(contentSegment.Id, contentSegment);
                    }
                    entity.Segment = contentSegment;
                }

                entity.LoadLevel = LoadLevel.Shallow;

                entities.Add(entity);
            }

            connection.Close();

            return entities;
        }


        public int? GetEntityIdByUniqueValue(string fieldTypeId, string value)
        {
            if (string.IsNullOrWhiteSpace(fieldTypeId) || string.IsNullOrWhiteSpace(value))
            {
                return null;
            }

            int? entityId = null;

            FieldType fieldType = _persistanceFieldType.GetFieldType(fieldTypeId, true);


            using (SqlConnection connection = new SqlConnection(ConnectionString))
            {
                SqlCommand command = connection.CreateCommand();


                command.CommandText = "SELECT TOP 1 EntityId FROM " + CreateTableName(fieldType.EntityTypeId) + " FieldType WHERE " + fieldTypeId + " = @FieldValue";

                command.Parameters.Add(CreateParameterFromField(fieldType, "@FieldValue", value));

                connection.Open();

                using (SqlDataReader reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        if (!reader.IsDBNull(0))
                        {
                            entityId = reader.GetInt32(0);
                        }
                    }

                    reader.Close();
                }

                connection.Close();
            }

            return entityId;
        }

        public bool SetSegmentForEntities(List<int> entityIds, int segmentId)
        {
            bool success = false;

            // Make sure there are ids in the list
            if (entityIds.Count < 1)
                return true;

            List<List<int>> splitList = SplitListIntoBatches(entityIds);

            using (var conn = new SqlConnection(ConnectionString))
            {
                conn.Open();
                var tran = conn.BeginTransaction("SetEntityContSeg");

                foreach (var batch in splitList)
                {
                    try
                    {
                        var cmd = conn.CreateCommand();
                        cmd.Transaction = tran;
                        cmd.CommandText = $"Update [dbo].[Entity] SET [ContentSegmentationId] = @ContentSegmentationId WHERE [Id] IN ({string.Join(",", batch)}) ";
                        cmd.Parameters.AddWithValue("@ContentSegmentationId", segmentId);
                        success = cmd.ExecuteNonQuery() > 0 ? true : false;
                    }
                    catch (Exception e)
                    {
                        tran.Rollback();
                        throw new Exception($"Error occured while updating Segment Id {segmentId}, to the Entities {string.Join(",", batch)}");
                    }
                }
                tran.Commit();
            }
            return success;
        }

        private static string ToCommaDelimitedColumnNames(List<FieldType> fieldTypes)
        {
            if (fieldTypes == null)
            {
                return string.Empty;
            }
            string columns = string.Empty;
            for (int i = 0; i < fieldTypes.Count; i++)
            {
                FieldType fieldType = fieldTypes[i];
                if (fieldType != null)
                {
                    columns += $"[{fieldType.Id}], ";
                }
            }
            return columns;
        }

        private static string ToCommaDelimitedTextColumnNames(List<FieldType> fieldTypes)
        {
            if (fieldTypes == null)
            {
                return string.Empty;
            }

            var list = fieldTypes.Where(ft => ft != null).Select(ft => $"'{ft.Id}'").ToList();
            return string.Join(",", list);
        }

        private static object ReadToObject(SqlDataReader reader, int i, string fieldTypeDataType)
        {
            if (reader.IsDBNull(i))
            {
                return null;
            }

            switch (DataTypeHelper.GetDataType(fieldTypeDataType))
            {
                case DataTypeHelper.DataTypeEnum.String:
                    return reader.GetString(i);
                case DataTypeHelper.DataTypeEnum.LocaleString:
                    return reader.GetString(i);
                case DataTypeHelper.DataTypeEnum.Double:
                    return reader.GetDouble(i);
                case DataTypeHelper.DataTypeEnum.Integer:
                    return reader.GetInt32(i);
                case DataTypeHelper.DataTypeEnum.Boolean:
                    return reader.GetBoolean(i);
                case DataTypeHelper.DataTypeEnum.DateTime:
                    return reader.GetDateTime(i);
                case DataTypeHelper.DataTypeEnum.Xml:
                    return reader.GetString(i);
                case DataTypeHelper.DataTypeEnum.File:
                    return int.Parse(reader.GetString(i));
                case DataTypeHelper.DataTypeEnum.CVL:
                    return reader.GetString(i);
                case null:
                default:
                    return reader.GetValue(i).ToString();
            }
        }

        public void UpdateEntityFields(Entity entity)
        {
            var fields = entity.Fields;
            var parameters = new DynamicParameters();
            var setString = "LastModified = @LastModified";

            try
            {
                // Build the dynamic SET clause for the SQL query where datatype is not localestring
                var nonLocaleStringFields = fields.Where(f => f.DataType != DataType.LocaleString).ToList();
                if (nonLocaleStringFields.Count > 0)
                {
                    setString = setString + ", " + string.Join(", ", nonLocaleStringFields
                        .Select(f => $"{f.FieldType.Id} = @{f.FieldType.Id}"));

                    foreach (var field in fields.Where(f => f.DataType != DataType.LocaleString))
                    {
                        field.Revision = entity.ChangeSet;
                        parameters.Add("@" + field.FieldType.Id, toSqlFriendlyValue(field.Data, convertNullToDbNull: false));
                    }
                }

                // Prepare the SQL query
                var query = $"UPDATE [{CreateTableName(entity.EntityTypeId)}] WITH (ROWLOCK) SET {setString} WHERE EntityId = @EntityID;";

                // Prepare the parameters dynamically
                parameters.Add("@EntityID", entity.Id);
                parameters.Add("@LastModified", DateTime.UtcNow);


                // Create updates for localestring fields
                foreach (var field in fields.Where(f => f.DataType == DataType.LocaleString))
                {
                    var ls = (LocaleString)field.Data;

                    if (ls.StringMap.Count > 0)
                    {
                        // Normalizing stringMap to StringMap
                        // JSON_MODIFY does not support case-insensitive keys and stringMap cannot be located using JSON_VALUE
                        query = query + $@"
                                        UPDATE [{CreateTableName(entity.EntityTypeId)}]
                                        WITH (ROWLOCK)
                                        SET {field.FieldType.Id} = CASE
                                            -- Check if it is 'stringMap' update it to 'StringMap'
                                            WHEN EXISTS (SELECT 1 FROM OPENJSON({field.FieldType.Id}) WHERE [key] = 'stringMap')
                                            THEN JSON_MODIFY(
                                                JSON_MODIFY({field.FieldType.Id}, '$.StringMap', JSON_QUERY({field.FieldType.Id}, '$.stringMap')), 
                                                '$.stringMap', NULL
                                            )
                                            -- If it is already 'StringMap' leave the value unchanged
                                            ELSE {field.FieldType.Id}
                                        END
                                        WHERE EntityId = @EntityID;";
                    }

                    // Each language will have its own UPDATE Query
                    foreach (var sm in ls.StringMap)
                    {
                        var paramName = SanitizeParameterName($"{field.FieldType.Id}_{sm.Key}");

                        query = query + $@"
                                        UPDATE [{CreateTableName(entity.EntityTypeId)}]
                                        WITH (ROWLOCK)
                                        SET {field.FieldType.Id} = CASE
                                            WHEN {field.FieldType.Id} IS NULL THEN 
                                                JSON_MODIFY(JSON_QUERY('{{ ""StringMap"": {{}} }}'), '$.StringMap.""{sm.Key}""', @{paramName})
                                            ELSE 
                                                JSON_MODIFY({field.FieldType.Id}, '$.StringMap.""{sm.Key}""', @{paramName})
                                        END
                                        WHERE EntityId = @EntityID;
                                        ";

                        parameters.Add($"@{paramName}", toSqlFriendlyValue(sm.Value, convertNullToDbNull: false));
                    }
                }

                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Execute the query using Dapper within the transaction
                            connection.Execute(query, parameters, transaction);

                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();

                            this.LogInstance.Error(
                                "An unexpected error occured when updating entity", ex,
                                null, null);
                            throw ex;
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                this.LogInstance.Error(
                    "An unexpected error occured when updating entity", ex,
                    null, null);
                throw ex;
            }
        }

        /**
         * Returns true if entity exists for the given criteria
         * Pre-requisites: fieldType not null, value not null
         */
        public bool DoesEntityExist(FieldType fieldType, object value)
        {
            bool retVal = false;

            if (fieldType != null && value != null)
            {
                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText =
                        $"SELECT TOP 1 EntityId FROM {CreateTableName(fieldType.EntityTypeId)} WHERE {fieldType.Id} = @Value";

                    command.Parameters.AddWithValue("@Value", value.ToString());

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            retVal = true;
                        }
                    }
                }
            }

            return retVal;
        }

        public async Task<object> GetFieldValueAsync(int entityId, string fieldTypeId)
        {
            object fieldValue = null;
            string entityTypeId = null;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                await using var command = connection.CreateCommand();
                await connection.OpenAsync();

                command.CommandText = @$"DECLARE @@entityTypeIdValue VARCHAR(64) = (SELECT EntityTypeId FROM Entity WHERE Id = @EntityId);
                                        IF EXISTS (SELECT [name] FROM sys.columns WHERE Object_ID = Object_ID(CONCAT('[dbo].[{EntityTablePrefix}', @@entityTypeIdValue, ']')) AND [name] = @FieldTypeId)
                                        BEGIN
	                                        SELECT @@entityTypeIdValue as EntityTypeId
                                        END";
                _ = command.Parameters.AddWithValue("@EntityId", entityId);
                _ = command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                using (var reader = await command.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        entityTypeId = reader.GetString(reader.GetOrdinal("EntityTypeId"));
                    }
                }

                if (entityTypeId is null)
                {
                    return null;
                }

                // "ChannelPublished" column does not exists in the Publication entity Table. This specific condition must not log the error details. Hence this condition.
                if (string.Equals(entityTypeId, "Publication", StringComparison.OrdinalIgnoreCase) && fieldTypeId.ToLower().Contains("channelpublished"))
                {
                    return null;
                }

                command.CommandText = $"SELECT {fieldTypeId}, ft.DataType FROM {CreateTableName(entityTypeId)} JOIN FieldType ft ON ft.Id = '{fieldTypeId}' WHERE EntityId = @EntityId";

                using (var reader = await command.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        var dataType = reader.GetString(reader.GetOrdinal("DataType"));
                        fieldValue = ReadToObject(reader, reader.GetOrdinal(fieldTypeId), dataType);

                        if (dataType == DataTypeHelper.DataTypeEnum.DateTime.ToString() && !string.IsNullOrEmpty(fieldValue as string))
                        {
                            fieldValue = Convert.ToDateTime(fieldValue).ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture);
                        }
                    }
                }
            }

            return fieldValue;
        }

        public Entity GetEntity(int entityId)
        {
            List<Entity> items = GetEntities(new List<int>() { entityId });
            return (items != null && items.Count > 0) ? items[0] : null;
        }

        public async Task<Entity> GetEntityAsync(int entityId)
        {
            List<Entity> items = await GetEntitiesAsync(new List<int>() { entityId });
            return (items != null && items.Count > 0) ? items[0] : null;
        }

        public void ReCalculateDisplayValuesForEntity(int entityId, List<Field> updatedFields)
        {
            Field displayNameField = updatedFields.FirstOrDefault(f => f.FieldType.IsDisplayName);
            Field displayDescriptionField = updatedFields.FirstOrDefault(f => f.FieldType.IsDisplayDescription);

            if ((displayNameField != null) || (displayDescriptionField != null))
            {

                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction sqlTransaction = connection.BeginTransaction("SetDisplayValueForEntity");
                    command.Transaction = sqlTransaction;

                    try
                    {
                        string t1 = displayNameField != null ? $"DisplayNameFieldTypeId = @DisplayNameFieldTypeId, DisplayName = @DisplayName" : "";
                        string t2 = displayDescriptionField != null ? $"DisplayDescriptionFieldTypeId = @DisplayDescriptionFieldTypeId, DisplayDescription = @DisplayDescription" : "";

                        string columnsUpdate = t1 + ((displayNameField != null && displayDescriptionField != null) ? ", " : "") + t2;

                        command.CommandText = $"UPDATE Entity SET {columnsUpdate} WHERE Id = @EntityId";

                        if (displayNameField != null)
                        {
                            command.Parameters.AddWithValue("@DisplayNameFieldTypeId", displayNameField.FieldTypeId);
                            command.Parameters.AddWithValue("@DisplayName", ToSqlFriendlyValue(displayNameField.Data, displayNameField.DataType));
                        }

                        if (displayDescriptionField != null)
                        {
                            command.Parameters.AddWithValue("@DisplayDescriptionFieldTypeId", displayDescriptionField.FieldTypeId);
                            command.Parameters.AddWithValue("@DisplayDescription", ToSqlFriendlyValue(displayDescriptionField.Data, displayDescriptionField.DataType));
                        }

                        command.Parameters.AddWithValue("@EntityId", entityId);

                        command.CommandTimeout = 6000;

                        command.ExecuteNonQuery();

                        sqlTransaction.Commit();

                    }
                    catch
                    {
                        sqlTransaction.Rollback();
                        throw;
                    }
                }
            }
        }

        private void ReCalculateEntityMainPictureForResource(int entityId)
        {
            string entityResourceTable = CreateTableName("Resource");

            using (SqlConnection connection = new SqlConnection(ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText =
                        $"SELECT r.Id, r.Url " +
                        $"FROM [ResourceFile] r, {entityResourceTable} e WHERE e.ResourceFileId = r.Id AND e.entityId = @entityId";

                    command.Parameters.AddWithValue("entityId", entityId);

                    connection.Open();

                    object mainPictureId = null;
                    object mainPictureUrl = null;

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.HasRows && reader.Read())
                        {
                            mainPictureId = reader.GetInt32(0);
                            mainPictureUrl = reader.GetString(1);
                        }
                    }

                    if (mainPictureId != null || mainPictureUrl != null)
                    {
                        command.CommandText = "UPDATE Entity SET MainPicture = @mainPictureId, MainPictureUrl = @mainPictureUrl " +
                                              " WHERE Id = @entityId";

                        command.Parameters.Clear();
                        command.Parameters.AddWithValue("@mainPictureId", toSqlFriendlyValue(mainPictureId));
                        command.Parameters.AddWithValue("@mainPictureUrl", toSqlFriendlyValue(mainPictureUrl));
                        command.Parameters.AddWithValue("@entityId", entityId);

                        command.ExecuteNonQuery();
                    }
                }
                catch (Exception ex)
                {
                    this.LogInstance.Error(
                        "An unexpected error occurred when re-calculating main picture id for Resource " + entityId, ex,
                        null, null);
                    throw ex;
                }
            }
        }

        public void ReCalculateEntityMainPicture(int entityId, string entityTypeId)
        {
            if (entityTypeId == "Resource")
            {
                ReCalculateEntityMainPictureForResource(entityId);
                return;
            }

            this._persistanceEntityType.GetEntityType(entityTypeId);

            using (SqlConnection connection = new SqlConnection(ConnectionString))
            {
                connection.Open();
                SqlCommand command = connection.CreateCommand();
                SqlTransaction sqlTransaction = connection.BeginTransaction("SetDisplayValueForEntity");
                command.Transaction = sqlTransaction;

                try
                {
                    command.CommandText =
                        "SELECT TOP 1 @LinkTypeId = Id FROM [LinkType] WITH (NOLOCK) WHERE SourceEntityTypeId = @EntityTypeId AND TargetEntityTypeId = 'Resource' ORDER BY[Index] ASC";

                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                    var idParameter = new SqlParameter("@LinkTypeId", SqlDbType.NVarChar, 64)
                    {
                        Direction = ParameterDirection.Output
                    };
                    command.Parameters.Add(idParameter);

                    command.ExecuteNonQuery();

                    var linkTypeId = idParameter.Value == System.DBNull.Value ? string.Empty : (string)idParameter.Value;


                    if (!string.IsNullOrEmpty(linkTypeId))
                    {
                        var resourceTableName = EntityTablePrefix + "Resource";
                        command.CommandText =
                            $@"WITH t AS (
                              SELECT Entity.Id AS 'Id',
                                {resourceTableName}.ResourceFileId AS 'MainPicture',
                                ROW_NUMBER() OVER(
                                  PARTITION BY Entity.Id
                                  ORDER BY Entity.Id
                                ) AS RowNumber,
                                ResourceFile.[Url]
                              FROM Entity
                                INNER JOIN Link ON Entity.Id = Link.SourceEntityId
                                INNER JOIN {resourceTableName} ON Link.TargetEntityId = {resourceTableName}.EntityId
                                INNER JOIN ResourceFile ON {resourceTableName}.ResourceFileId = ResourceFile.Id
                              WHERE Link.LinkTypeId = @LinkTypeId AND Link.[Index] = 0 AND Entity.Id = @EntityId
                            )
                            UPDATE Entity
                            SET Entity.MainPicture = a.MainPicture, Entity.MainPictureUrl = a.[Url]
                            FROM(
                                SELECT *
                                FROM t
                                UNION ALL
                                (
                                  SELECT @EntityId, null, 1, null
                                  FROM t
                                  HAVING COUNT(*) = 0
                                )
                              ) AS a
                            WHERE a.RowNumber = 1 AND Entity.Id = a.Id";

                        command.Parameters.Clear();
                        command.Parameters.AddWithValue("@EntityId", entityId);
                        command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                        command.CommandTimeout = 9000;

                        command.ExecuteNonQuery();

                        sqlTransaction.Commit();
                    }
                }
                catch
                {
                    sqlTransaction.Rollback();
                    throw;
                }
            }
        }

        public List<int> SearchEntitiesByCriterions(Join joinOperator, List<Criteria> criteria)
        {
            List<int> ids = new List<int>();

            var fieldType = _persistanceFieldType.GetFieldType(criteria.FirstOrDefault().FieldTypeId);

            if (fieldType != null)
            {
                string tableName = CreateTableName(fieldType.EntityTypeId);

                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    SqlCommand command = connection.CreateCommand();

                    // this SQL Statement is possibly to be appended with " AND " SQL criteria 
                    command.CommandText = $"select DISTINCT B.EntityId FROM {tableName} B WHERE 1 = 1";
                    AddFieldLevelCriteria(command, criteria, joinOperator);
                    command.CommandTimeout = 9000;

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
            }

            return ids;
        }

        public List<int> Search(Criteria criteria, Join? joinOperator = null)
        {
            List<int> ids = new List<int>();

            var fieldType = _persistanceFieldType.GetFieldType(criteria.FieldTypeId);

            if (fieldType != null)
            {
                string tableName = CreateTableName(fieldType.EntityTypeId);

                using (SqlConnection connection = new SqlConnection(ConnectionString))
                {
                    SqlCommand command = connection.CreateCommand();

                    // this SQL Statement is possibly to be appended with " AND " SQL criteria 
                    command.CommandText = $"select DISTINCT B.EntityId FROM {tableName} B WHERE 1 = 1";

                    var sqlwhereClause = AddFieldLevelCriterion(command, criteria, 0, fieldType);
                    if (!string.IsNullOrEmpty(sqlwhereClause))
                    {
                        command.CommandText += $" AND {sqlwhereClause}";
                    }

                    command.CommandTimeout = 9000;

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
            }

            return ids;
        }

        private static object toSqlFriendlyValue(object value, bool convertNullToDbNull = true)
        {
            if (value == null)
            {
                return convertNullToDbNull ? DBNull.Value : null;
            }

            if (value is LocaleString)
            {
                return JsonConvert.SerializeObject(value);
            }

            return value;
        }

        /**
         * This search provides search on Entity with general entity criteria (CreatedBy, ModifiedBy, etc) and
         *  also field level criteria.
         *
         * Pre-requisites: 
         * - fieldLevelCriteria must have at least one item
         */
        public List<int> SearchEntity(SystemQuery generalEntityCriteria, List<Criteria> fieldLevelCriteria, Join? joinOperator = null)
        {
            List<int> entityIds = new List<int>();

            // pre-requisites rule
            if (fieldLevelCriteria == null || !fieldLevelCriteria.Any())
            {
                return entityIds;
            }

            string entityTypeId = _persistanceFieldType.GetFieldType(fieldLevelCriteria[0].FieldTypeId).EntityTypeId;
            string tableName = CreateTableName(entityTypeId);

            // select A.Id FROM Entity A, Entity_iPMC_Item B WHERE A.Id = B.EntityId
            //   AND A.PendingDelete is null
            //   AND A.CreateBy .... AND A.ModifiedBy .....        ---- generalEntityCriteria goes here
            //   AND B.ItemNumber .... AND B.ItemCategory ....     ---- fieldLevelCriteria goes here

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                SqlCommand command = connection.CreateCommand();

                string segmentIdsInClause = permittedSegmentIdsClause();

                if (!string.IsNullOrWhiteSpace(segmentIdsInClause))
                {
                    segmentIdsInClause = $"A.{segmentIdsInClause}";
                }

                command.CommandText = $"select A.Id FROM Entity A, {tableName} B " +
                                      $" WHERE {segmentIdsInClause} A.Id = B.EntityId AND A.PendingDelete is null ";

                if (generalEntityCriteria != null)
                {
                    AddGeneralCriteria(command, generalEntityCriteria);
                }

                AddFieldLevelCriteria(command, fieldLevelCriteria, joinOperator);

                connection.Open();
                command.CommandTimeout = 60;

                using (SqlDataReader reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        entityIds.Add(reader.GetInt32(0));
                    }

                    reader.Close();
                }
            }

            return entityIds;
        }

        private void AddGeneralCriteria(SqlCommand command, SystemQuery systemQuery)
        {
            StringBuilder query = new StringBuilder();

            DateTime? dateCreated = TimeIntervalQueryUtil.GetSystemQueryValueFromDatetimeInterval(systemQuery.IntervalValueCreated);
            DateTime? dateModified = TimeIntervalQueryUtil.GetSystemQueryValueFromDatetimeInterval(systemQuery.IntervalValueLastModified);
            systemQuery.Created = dateCreated.HasValue ? dateCreated.Value : systemQuery.Created;
            systemQuery.LastModified = dateModified.HasValue ? dateModified.Value : systemQuery.LastModified;


            if (systemQuery.EntityIdsList.Count == 0 && systemQuery.EntityId.HasValue)
            {
                systemQuery.EntityIdsList.Add(systemQuery.EntityId);
            }

            // EntityIdsList 
            if (systemQuery.EntityIdsList.Count > 0)
            {
                if (systemQuery.EntityIdOperator == Operator.Equal)
                {
                    StringBuilder sb = new StringBuilder();
                    int i = 0;

                    foreach (var id in systemQuery.EntityIdsList)
                    {
                        if (i >= 1000)
                        {
                            break;
                        }

                        if (id != null)
                        {
                            if (sb.Length > 0)
                            {
                                sb.Append(",");
                            }

                            sb.Append(id);
                            i++;
                        }
                    }

                    if (sb.Length > 0)
                    {
                        query.Append($" AND A.id in ({sb.ToString()}) ");
                    }

                }
            }

            if (!string.IsNullOrEmpty(systemQuery.EntityTypeId))
            {
                var operand = (systemQuery.EntityTypeIdOperator == Operator.Equal) ? "=" : "<>";
                query.Append($" AND A.EntityTypeId {operand} @EntityTypeId ");
                command.Parameters.AddWithValue("@EntityTypeId", systemQuery.EntityTypeId);
            }


            if (!string.IsNullOrEmpty(systemQuery.FieldSetId) || systemQuery.FieldSetIdOperator == Operator.Empty || systemQuery.FieldSetIdOperator == Operator.NotEmpty)
            {
                if (systemQuery.FieldSetIdOperator == Operator.Equal)
                {
                    query.Append(" AND A.FieldSetId = @FieldSetId");
                    command.Parameters.AddWithValue("@FieldSetId", systemQuery.FieldSetId);
                }
                else if (systemQuery.FieldSetIdOperator == Operator.NotEqual)
                {
                    query.Append(" AND (A.FieldSetId <> @FieldSetId OR LEN(ISNULL(A.FieldSetId,'')) = 0)");
                    command.Parameters.AddWithValue("@FieldSetId", systemQuery.FieldSetId);
                }
                else if (systemQuery.FieldSetIdOperator == Operator.Empty)
                {
                    query.Append(" AND LEN(ISNULL(A.FieldSetId,'')) = 0");
                }
                else if (systemQuery.FieldSetIdOperator == Operator.NotEmpty)
                {
                    query.Append(" AND LEN(ISNULL(A.FieldSetId,'')) > 0");
                }
            }


            if (systemQuery.LastModified.HasValue)
            {
                var operatorSymbol = systemQuery.LastModifiedOperator.GetSqlSymbol();

                if (!string.IsNullOrEmpty(operatorSymbol))
                {
                    query.Append($" AND A.LastModified {operatorSymbol} @LastModified");

                    DateTime validUtcDate = CheckSearchDate(systemQuery.LastModified.Value.ToUniversalTime());
                    command.Parameters.AddWithValue("@LastModified", validUtcDate);
                }
            }


            if (systemQuery.Created.HasValue)
            {
                var operatorSymbol = systemQuery.CreatedOperator.GetSqlSymbol();

                if (!string.IsNullOrEmpty(operatorSymbol))
                {
                    query.Append($" AND A.DateCreated {operatorSymbol} @DateCreated");
                }

                DateTime validUtcDate = CheckSearchDate(systemQuery.Created.Value.ToUniversalTime());
                command.Parameters.AddWithValue("@DateCreated", validUtcDate);
            }


            if (!string.IsNullOrEmpty(systemQuery.CreatedBy))
            {
                var operand = (systemQuery.CreatedByOperator == Operator.Equal) ? "=" : "<>";
                query.Append($" AND A.CreatedBy {operand} @CreatedBy");
                command.Parameters.AddWithValue("@CreatedBy", systemQuery.CreatedBy);
            }


            if (!string.IsNullOrEmpty(systemQuery.ModifiedBy))
            {
                var operand = (systemQuery.ModifiedByOperator == Operator.Equal) ? "=" : "<>";
                query.Append($" AND A.ModifiedBy {operand} @ModifiedBy");
                command.Parameters.AddWithValue("@ModifiedBy", systemQuery.ModifiedBy);
            }


            if (!string.IsNullOrEmpty(systemQuery.LockedBy))
            {
                var operand = (systemQuery.LockedByOperator == Operator.Equal) ? "=" : "<>";
                query.Append($" AND A.Locked {operand} @LockedBy");
                command.Parameters.AddWithValue("@LockedBy", systemQuery.LockedBy);
            }


            if (systemQuery.Completeness.HasValue)
            {
                var operatorSymbol = systemQuery.CompletenessOperator.GetSqlSymbol();
                query.Append($" AND A.Completeness {operatorSymbol} @Completeness");
                command.Parameters.AddWithValue("@Completeness", systemQuery.Completeness.Value);
            }

            if (systemQuery.SegmentIds != null && systemQuery.SegmentIds.Any())
            {
                var operatorSymbol = string.Empty;

                switch (systemQuery.SegmentIdsOperator)
                {
                    case Operator.In:
                    case Operator.Equal:
                    case Operator.Contains:
                    case Operator.ContainsAny:
                        operatorSymbol = Operator.In.GetSqlSymbol();
                        break;

                    case Operator.NotIn:
                    case Operator.NotEqual:
                    case Operator.NotContains:
                    case Operator.NotContainsAny:
                        operatorSymbol = Operator.NotIn.GetSqlSymbol();
                        break;

                    default:
                        throw new NotSupportedException($"The operator {systemQuery.CompletenessOperator.GetSqlSymbol()} is not supported.");
                }

                var theIds = string.Join(",", systemQuery.SegmentIds);
                query.Append($" AND A.ContentSegmentationId {operatorSymbol} ({theIds})");
            }

            command.CommandText += query.ToString();
        }

        /**
         * To add extra criteria on the Entity_iPMC_* table
         *
         * Base query in the command.CommandText is as follow:
         *   select A.Id FROM Entity A, {iPMCEntityTableName} B WHERE A.Id = B.EntityId AND A.PendingDelete is null
         *
         */
        private void AddFieldLevelCriteria(SqlCommand command, List<Criteria> criteriaList, Join? joinOperator = null)
        {
            int i = 0;
            string whereClause = string.Empty;
            foreach (var criterion in criteriaList)
            {
                var s = AddFieldLevelCriterion(command, criterion, i);
                whereClause = (string.IsNullOrEmpty(whereClause))
                    ? s
                    : $"{whereClause} {joinOperator ?? Join.And} {s}";
                i++;
            }
            if (!string.IsNullOrEmpty(whereClause))
            {
                command.CommandText += $" AND ({whereClause}) ";
            }
        }

        /**
         * To an extra criterion on the Entity_iPMC_* table
         *
         * Base query in the command.CommandText is as follow:
         *   select A.Id FROM Entity A, {iPMCEntityTableName} B WHERE A.Id = B.EntityId AND A.PendingDelete is null
         *
         */
        private string AddFieldLevelCriterion(SqlCommand command, Criteria criteria, int iterationNumber, FieldType fieldType = null)
        {
            // if fieldType is not provided in parameter, then retrieve it from DB
            fieldType = fieldType ?? _persistanceFieldType.GetFieldType(criteria.FieldTypeId);

            var value = criteria.Value != null ? criteria.Value.ToString() : "";

            if (fieldType.DataType == DataTypeHelper.DataTypeEnum.DateTime.ToString() && criteria.Operator != Operator.Empty && criteria.Operator != Operator.NotEmpty)
            {
                value = Convert.ToDateTime(criteria.Value.ToString())
                    .ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture);
            }

            if (fieldType.DataType == DataTypeHelper.DataTypeEnum.LocaleString.ToString())
            {
                value = JsonConvert.SerializeObject(value);

                // Remove first and last " char
                value = value.Substring(1, value.Length - 1);
                value = value.Substring(0, value.Length - 1);
            }

            string tableColumn = $"B.{fieldType.Id}";

            string dataParam = $"@Data{iterationNumber}";

            string wildCardValue = string.IsNullOrEmpty(criteria.Language) ? null : $"%\"{criteria.Language}\":%";
            string wildCard3Value = string.IsNullOrEmpty(criteria.Language) ? null : $"%\"{criteria.Language}\":null%";

            string sql = null;

            switch (criteria.Operator)
            {
                case Operator.Empty:
                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        sql = $" ({tableColumn} IS NULL)";
                    }
                    else
                    {
                        // empty:  LIKE %"sv-SE":""%   or   NOT LIKE %"sv-SE":%   or   NULL
                        sql = $" ({tableColumn} LIKE {dataParam} OR {tableColumn} NOT LIKE {dataParam}2 OR {tableColumn} IS NULL OR {tableColumn} LIKE {dataParam}3)";

                        // %"sv-SE":""%
                        string wildCard = $"%\"{criteria.Language}\":\"\"%";

                        command.Parameters.AddWithValue(dataParam, wildCard);
                        command.Parameters.AddWithValue($"{dataParam}2", wildCardValue);
                        command.Parameters.AddWithValue($"{dataParam}3", wildCard3Value);
                    }
                    break;

                case Operator.NotEmpty:
                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        sql = $" {tableColumn} IS NOT NULL AND {tableColumn} != ''";
                    }
                    else
                    {
                        // not empty:  NOT LIKE %"sv-SE":""%  AND  LIKE %"sv-SE":% 
                        sql = $" {tableColumn} NOT LIKE {dataParam} AND {tableColumn} LIKE {dataParam}2 AND {tableColumn} NOT LIKE {dataParam}3";

                        // %"sv-SE":""%
                        string wildCard = $"%\"{criteria.Language}\":\"\"%";

                        command.Parameters.AddWithValue(dataParam, wildCard);
                        command.Parameters.AddWithValue($"{dataParam}2", wildCardValue);
                        command.Parameters.AddWithValue($"{dataParam}3", wildCard3Value);
                    }
                    break;
                case Operator.IsNull:
                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        sql = $"  {tableColumn} IS NULL";
                    }
                    else
                    {
                        sql = $"  ({tableColumn} IS NULL OR {tableColumn} LIKE {dataParam})";
                        command.Parameters.AddWithValue(dataParam, wildCardValue);
                    }
                    break;
                case Operator.IsNotNull:
                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        sql = $" {tableColumn} IS NOT NULL";
                    }
                    else
                    {
                        sql = $" ({tableColumn} NOT LIKE {dataParam})";
                        command.Parameters.AddWithValue(dataParam, wildCardValue);
                    }
                    break;
                case Operator.Equal:
                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        sql = $" {tableColumn} = {dataParam}";
                        command.Parameters.AddWithValue(dataParam, value);
                    }
                    else
                    {
                        sql = $" {tableColumn} like {dataParam}";

                        var doubleQuoteEscapedValue = value.Replace("\"", "\\\"");
                        string wildcard = $"%\"{criteria.Language}\":\"{doubleQuoteEscapedValue}\"%";

                        command.Parameters.AddWithValue(dataParam, wildcard);
                    }
                    break;
                case Operator.NotEqual:
                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        if (fieldType.DataType == DataTypeHelper.DataTypeEnum.CVL.ToString())
                        {
                            sql = $"  ({tableColumn} IS NULL OR (({tableColumn} NOT LIKE {dataParam}0)" +
                                    $"  AND ({tableColumn} NOT LIKE {dataParam}1)" +
                                    $"  AND ({tableColumn} NOT LIKE {dataParam}2)" +
                                    $"  AND ({tableColumn} <> {dataParam}3 )))";

                            string wildCardValue0 = string.Format("%;{0};%", value);
                            string wildCardValue1 = string.Format("{0};%", value);
                            string wildCardValue2 = string.Format("%;{0}", value);

                            command.Parameters.AddWithValue($"{dataParam}0", wildCardValue0);
                            command.Parameters.AddWithValue($"{dataParam}1", wildCardValue1);
                            command.Parameters.AddWithValue($"{dataParam}2", wildCardValue2);
                            command.Parameters.AddWithValue($"{dataParam}3", value);
                        }
                        else
                        {
                            sql = $" ({tableColumn} IS NULL OR {tableColumn} <> {dataParam})";
                            command.Parameters.AddWithValue(dataParam, value);
                        }
                    }
                    else
                    {
                        sql = $" ({tableColumn} IS NULL OR {tableColumn} NOT LIKE {dataParam})";

                        var doubleQuoteEscapedValue = value.Replace("\"", "\\\"");
                        string wildcard = $"%\"{criteria.Language}\":\"{doubleQuoteEscapedValue}\"%";

                        command.Parameters.AddWithValue(dataParam, wildcard);
                    }
                    break;
                case Operator.Contains:
                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        if (fieldType.DataType == DataTypeHelper.DataTypeEnum.CVL.ToString())
                        {
                            sql = $" ({tableColumn} LIKE {dataParam} OR {tableColumn} LIKE {dataParam}1 OR {tableColumn} LIKE {dataParam}2 OR {tableColumn} = {dataParam}3 )";

                            string wildCard = $"%;{value};%";
                            string wildCard1 = $"{value};%";
                            string wildCard2 = $"%;{value}";

                            command.Parameters.AddWithValue(dataParam, wildCard);
                            command.Parameters.AddWithValue($"{dataParam}1", wildCard1);
                            command.Parameters.AddWithValue($"{dataParam}2", wildCard2);
                            command.Parameters.AddWithValue($"{dataParam}3", value);
                        }
                        else
                        {
                            sql = $" {tableColumn} like {dataParam}";
                            string wildCard = $"%{value}%";
                            command.Parameters.AddWithValue(dataParam, wildCard);
                        }
                    }
                    else
                    {
                        // sample data:  .....  "en-UK":"Hello this is me" .....
                        // language: en-UK, keyword: this
                        // intended wild card: %"en-UK":"%this%
                        sql = $" {tableColumn} like {dataParam}";

                        var doubleQuoteEscapedValue = value.Replace("\"", "\\\"");
                        string wildCard = $"%\"{criteria.Language}\":\"%{doubleQuoteEscapedValue}%";

                        command.Parameters.AddWithValue(dataParam, wildCard);
                    }
                    break;
                case Operator.BeginsWith:

                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        sql = $" {tableColumn} LIKE {dataParam}";
                        command.Parameters.AddWithValue(dataParam, $"{value}%");
                    }
                    else
                    {
                        sql = $" {tableColumn} LIKE {dataParam}";

                        var doubleQuoteEscapedValue = value.Replace("\"", "\\\"");
                        string wildcard = $"%\"{criteria.Language}\":\"{doubleQuoteEscapedValue}%";

                        command.Parameters.AddWithValue(dataParam, wildcard);
                    }
                    break;
                case Operator.IsTrue:

                    sql = $" ({tableColumn} = 'True' OR {tableColumn} = '1')";
                    break;

                case Operator.IsFalse:

                    sql = $" ({tableColumn} = 'False' OR {tableColumn} = '0')";
                    break;

                case Operator.GreaterThan:
                case Operator.GreaterThanOrEqual:
                case Operator.LessThan:
                case Operator.LessThanOrEqual:

                    string operatorSymbol = criteria.Operator.GetSqlSymbol();

                    if (fieldType.DataType == DataTypeHelper.DataTypeEnum.DateTime.ToString() ||
                        fieldType.DataType == DataTypeHelper.DataTypeEnum.Double.ToString() ||
                        fieldType.DataType == DataTypeHelper.DataTypeEnum.Integer.ToString() ||
                        fieldType.DataType == DataTypeHelper.DataTypeEnum.File.ToString())
                    {
                        sql = $" {tableColumn} {operatorSymbol} {dataParam}";
                        command.Parameters.AddWithValue(dataParam, value);
                    }
                    break;

                // ----- CVL only operators
                case Operator.ContainsAll:
                case Operator.NotContainsAny:
                case Operator.NotContains:
                    {
                        string not = (criteria.Operator == Operator.ContainsAll) ? "" : "not";
                        string notequal = (criteria.Operator == Operator.ContainsAll) ? "=" : "<>";
                        string andOr = (criteria.Operator == Operator.ContainsAll) ? " OR " : " AND ";
                        string orColumnIsNull = (criteria.Operator == Operator.ContainsAll)
                            ? ""
                            : $" OR {tableColumn} IS NULL";

                        string[] stringTokens = value.Split(';');

                        StringBuilder sb = new StringBuilder();

                        for (int i = 0; i < stringTokens.Length; i++)
                        {
                            if (i > 0)
                            {
                                sb.Append(" AND ");
                            }

                            string token = stringTokens[i].Replace("'", "''");

                            sb.Append($"({tableColumn} {not} like '%;{token};%'");
                            sb.Append($"{andOr}");
                            sb.Append($"{tableColumn} {not} like '{token};%'");
                            sb.Append($"{andOr}");
                            sb.Append($"{tableColumn} {not} like '%;{token}'");
                            sb.Append($"{andOr}");
                            sb.Append($"{tableColumn} {notequal} '{token}')");
                        }

                        sql = sb.Length > 0 ? $" ({sb.ToString()} {orColumnIsNull})" : sql;
                    }

                    break;

                case Operator.NotContainsAll:
                    {
                        string token = value.Replace("'", "''");
                        sql = $" ({tableColumn} <> '{token}' OR {tableColumn} IS NULL)";
                    }
                    break;

                case Operator.ContainsAny:
                    {
                        string[] stringTokens = value.Split(';');

                        StringBuilder sb = new StringBuilder();

                        for (int i = 0; i < stringTokens.Length; i++)
                        {
                            if (i > 0)
                            {
                                sb.Append(" OR ");
                            }

                            string token = stringTokens[i].Replace("'", "''");
                            sb.Append($"({tableColumn} like '%;{token};%'");
                            sb.Append(" OR ");
                            sb.Append($"{tableColumn} like '{token};%'");
                            sb.Append(" OR ");
                            sb.Append($"{tableColumn} like '%;{token}'");
                            sb.Append(" OR ");
                            sb.Append($"{tableColumn} = '{token}')");
                        }

                        sql = sb.Length > 0 ? $" ({sb.ToString()})" : sql;
                    }
                    break;

                // ----- end of CVL only operators

                default:
                    break;
            }

            return $"({sql})";
        }

        private static DateTime CheckSearchDate(DateTime dateTime)
        {
            if (dateTime.Year < 1753)
                return new DateTime(1753, 1, 1);
            else if (dateTime.Year > 9999)
                return DateTime.MaxValue;
            else
                return dateTime;
        }

        public IDictionary<int, IList<SyndicationRelatedEntityFieldValue>> GetRelatedEntityFields(
            string entityName,
            string field,
            string[] linkTypeIds,
            int[] entityLinkPosition,
            string language)
        {
            var relatedEntityFields = new Dictionary<int, IList<SyndicationRelatedEntityFieldValue>>();

            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                string[] relationSelector = { "SourceEntityId", "TargetEntityId" };
                var command = connection.CreateCommand();

                for (var i = 0; i < linkTypeIds.Length; i++)
                {
                    _ = command.Parameters.Add("@LinkType" + i, SqlDbType.VarChar, 64).Value = linkTypeIds[i];
                }

                command.CommandText = $@"SELECT TargetLink0.{relationSelector[entityLinkPosition[0] == 0 ? 1 : 0]} as FinalEntityId,
                        Entity.{field} as Field, Field.DataType as FieldDataType, Entity.EntityId as EntityId, STRING_AGG(cvalue.[Value], ';') AS CvlValue
                        FROM Entity_iPMC_{entityName} Entity
                        INNER JOIN FieldType Field ON Field.id = @field
                        INNER JOIN Link TargetLink{linkTypeIds.Length - 1}
                        ON TargetLink{linkTypeIds.Length - 1}.LinkTypeId = @LinkType{linkTypeIds.Length - 1}
                        AND TargetLink{linkTypeIds.Length - 1}.{relationSelector[entityLinkPosition[linkTypeIds.Length - 1]]} = Entity.EntityId
                        LEFT JOIN CVLKey AS ckey
                            ON EXISTS (
                                SELECT 1 FROM STRING_SPLIT(CONVERT(nvarchar(max), Entity.{field}), ';') AS SplitValues
                                WHERE SplitValues.value = ckey.[Key]
                            )
                            AND ckey.CVLId = Field.CVLId
                            AND Field.DataType = '{DataType.CVL}'
                        LEFT JOIN CVLValue AS cvalue ON cvalue.CVLKeyId = ckey.Id ";

                if (!string.IsNullOrEmpty(language))
                {
                    command.CommandText += " AND cvalue.Language = @language ";
                    _ = command.Parameters.Add("@language", SqlDbType.VarChar, 64).Value = language;
                }

                for (var i = linkTypeIds.Length - 2; i >= 0; i--)
                {
                    command.CommandText += $" INNER JOIN Link TargetLink{i} ON TargetLink{i}.LinkTypeId = @LinkType{i} "
                                           + $"AND TargetLink{i}.{relationSelector[entityLinkPosition[i]]} = TargetLink{i + 1}.{relationSelector[entityLinkPosition[i + 1] == 0 ? 1 : 0]} ";
                }

                _ = command.Parameters.Add("@field", SqlDbType.VarChar, 64).Value = field;
                
                command.CommandText += $" GROUP BY TargetLink0.{relationSelector[entityLinkPosition[0] == 0 ? 1 : 0]}, Entity.{field},  Field.DataType, Entity.EntityId;";
                command.CommandTimeout = (int)TimeSpan.FromMinutes(5).TotalSeconds;

                connection.Open();
                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    if (reader.IsDBNull(reader.GetOrdinal("FinalEntityId")))
                    {
                        break;
                    }

                    if (reader.IsDBNull(reader.GetOrdinal("Field")) ||
                        reader.IsDBNull(reader.GetOrdinal("FieldDataType")) ||
                        reader.IsDBNull(reader.GetOrdinal("EntityId")))
                    {
                        continue;
                    }

                    var dataType = reader.GetString(reader.GetOrdinal("FieldDataType"));
                    var finalEntityId = reader.GetInt32(reader.GetOrdinal("FinalEntityId"));
                    var entityId = reader.GetInt32(reader.GetOrdinal("EntityId"));

                    _ = relatedEntityFields
                        .TryGetValue(finalEntityId, out var entityFields);
                    if (entityFields is null)
                    {
                        entityFields = new List<SyndicationRelatedEntityFieldValue>();

                        relatedEntityFields.Add(finalEntityId, entityFields);
                    }

                    var value = GetRelatedEntityFieldValue(dataType, language, entityId, reader);
                    entityFields.Add(value);
                }
            }
            catch (Exception ex)
            {
                var message = $"An unexpected error occurred when populating dictionary in {nameof(this.GetRelatedEntityFields)} ";
                this.LogInstance.Error(message, ex, string.Empty, string.Empty);
            }

            return relatedEntityFields;
        }

        private static SyndicationRelatedEntityFieldValue GetRelatedEntityFieldValue(string dataType, string language, int entityId, SqlDataReader reader)
        {
            string value;

            if (dataType == DataType.LocaleString)
            {
                var fieldData = reader.GetString(reader.GetOrdinal("Field"));
                value = JsonConvert.DeserializeObject<LocaleString>(fieldData)[new CultureInfo(language)];
            } 
            else if (dataType == DataType.CVL && reader["CvlValue"] != DBNull.Value)
            {
                value = (string)Convert.ChangeType(reader["CvlValue"], typeof(string));
            }
            else
            {
                value = (string)Convert.ChangeType(reader["Field"], typeof(string));
            }

            return new SyndicationRelatedEntityFieldValue
            {
                EntityId = entityId,
                FieldValue = value
            };
        }

        private static string SanitizeParameterName(string parameterName)
        {
            // Replace invalid characters with underscores
            // Invalid characters: hyphens, spaces, special symbols
            string sanitized = Regex.Replace(parameterName, @"[^A-Za-z0-9_]", "_");

            // Ensure the parameter does not start with a digit
            if (char.IsDigit(sanitized[0]))
            {
                sanitized = "_" + sanitized;
            }

            return sanitized;
        }
    }
}
