namespace inRiver.Server.UnitTests.Helpers
{
    using System;
    using FluentAssertions;
    using inRiver.Server.Helpers;
    using Xunit;

    public class FileHelperTests
    {
        public class DoNotThrowIfPathTraversalIsNotDetectedTests
        {
            [Theory]
            [InlineData("abc.jpg")]
            [InlineData("folder./abc.jpg")]
            [InlineData("folder.\\abc.jpg")]
            [InlineData("folder././abc.jpg")]
            [InlineData("folder./.\\abc.jpg")]
            [InlineData("folder.\\./abc.jpg")]
            public void WhenFileNameDoesNotContainsFolderTraversal_ThenArgumentExceptionWithInvalidFilenameShallNotBeThrown(string filename)
            {
                // Arrange
                var fileHelper = new FileHelper();

                // Act
                Action act = () => fileHelper.ThrowIfPathTraversalIsDetected(filename);

                // Assert
                act.Should()
                    .NotThrow<ArgumentException>();
            }
        }

        public class ThrowIfPathTraversalIsDetectedTests
        {
            [Theory]
            [InlineData("../abc.jpg")]
            [InlineData("..\\abc.jpg")]
            [InlineData("./../abc.jpg")]
            [InlineData("folder/../abc.jpg")]
            [InlineData("folder/../../abc.jpg")]
            [InlineData("folder\\..\\../abc.jpg")]
            public void WhenFileNameContainsFolderTraversal_ThenArgumentExceptionWithInvalidFilenameShallBeThrown(string filename)
            {
                // Arrange
                var fileHelper = new FileHelper();

                // Act
                Action act = () => fileHelper.ThrowIfPathTraversalIsDetected(filename);

                // Assert
                act.Should()
                    .Throw<ArgumentException>()
                    .WithMessage("Invalid filename");
            }
        }
    }
}
