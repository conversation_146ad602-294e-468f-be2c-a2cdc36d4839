namespace inRiver.Server.Syndication.Service
{
    using System.Collections.Generic;
    using inRiver.Server.Syndication.Mapping;
    using Newtonsoft.Json.Linq;
    using Xunit;

    public class EntityFieldServiceTests
    {
        private readonly EntityFieldService service;

        public EntityFieldServiceTests()
        {
            this.service = new EntityFieldService();
        }

        [Fact]
        public void GetMappedData_ShouldReturnMappedValues_WhenEnumerationsArePresent()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = new List<MapEnumeration> {
                    new MapEnumeration { EnumValue = "EnumValue1", FieldValue = "Value1" },
                    new MapEnumeration { EnumValue = "EnumValue2", FieldValue = "Value2" }
                }
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField { Data = "Value1" },
                new InRiverField { Data = "Value2" }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { "EnumValue1", "EnumValue2" }, result);
        }

        [Fact]
        public void GetMappedData_ShouldReturnOriginalValues_WhenNoEnumerations()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = null
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField { Data = "Original1" },
                new InRiverField { Data = "Original2" }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { "Original1", "Original2" }, result);
        }

        [Fact]
        public void GetMappedData_ShouldReturnNullValues_WhenDataIsNull()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = null
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField { Data = null },
                new InRiverField { Data = "Original1" }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { null, "Original1" }, result);
        }

        [Fact]
        public void GetMappedData_ShouldReturnOriginalEntityImagesValue_WhenNoEnumerations()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = null
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField {
                    Data = GetAllImages()
                }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { foundCollection[0].Data }, result);
        }

        [Fact]
        public void GetMappedData_ShouldReturnOriginalEntityImageValue_WhenNoEnumerations()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = null
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField {
                    Data = GetSingleImage()
                }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { foundCollection[0].Data }, result);
        }

        [Fact]
        public void GetMappedData_ShouldReturnOriginalEntityImageUrlValue_WhenNoEnumerations()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = null
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField {
                    Data = "https://media.inriver.com/inriver-test/l/images/v1737465649/54139d43-d6f5-4f13-a952-74e3921721b7"
                }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { foundCollection[0].Data }, result);
        }

        [Fact]
        public void GetMappedData_ShouldReturnOriginaLocaleStringValue_WhenNoEnumerations()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = null
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField {
                    Data = new Dictionary<string, string?>
                    {
                        { "ar-SA", "" },
                        { "bg-BG", "" },
                        { "zh-TW", "" },
                        { "en", "Test en" },
                        { "de", "" },
                        { "de-DE", "" },
                        { "it", "" },
                        { "ko-KR", null },
                        { "pl-PL", "" },
                        { "es-MX", "" },
                        { "es-US", "" },
                        { "sv-SE", "" },
                        { "tr-TR", null }
                    }
                }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { foundCollection[0].Data }, result);
        }

        [Theory]
        [InlineData(1)] // Integer
        [InlineData(true)] // Boolean
        [InlineData("Davao;UK")] // CVL
        [InlineData(@"
            <?xml version=""1.0"" encoding=""UTF-8""?>
            <note>
                <from>Jani</from>
                <to>Tove</to>
                <message>Remember me this weekend</message>
            </note>")] // XML
        public void GetMappedData_ShouldReturnOriginaDefaulTypeValue_WhenNoEnumerations(object data)
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = null
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField {
                    Data = data
                }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { foundCollection[0].Data }, result);
        }

        [Fact]
        public void GetMappedData_ShouldReturnNullValue_WhenEnumerationsArePresent()
        {
            // Arrange
            var mapField = new MapField
            {
                Enumerations = new List<MapEnumeration> {
                    new MapEnumeration { EnumValue = "EnumValue1", FieldValue = "Value1" },
                    new MapEnumeration { EnumValue = "EnumValue2", FieldValue = "Value2" }
                }
            };
            var foundCollection = new List<InRiverField>
            {
                new InRiverField {
                    Data = new Dictionary<string, string?>
                    {
                        { "ar-SA", "" },
                        { "bg-BG", "" },
                        { "zh-TW", "" },
                        { "en", "Test en" },
                        { "de", "" },
                        { "de-DE", "" },
                        { "it", "" },
                        { "ko-KR", null },
                        { "pl-PL", "" },
                        { "es-MX", "" },
                        { "es-US", "" },
                        { "sv-SE", "" },
                        { "tr-TR", null }
                    }
                }
            };

            // Act
            var result = this.service.GetMappedData(mapField, foundCollection);

            // Assert
            Assert.Equal(new List<object> { null }, result);
        }

        private static JArray GetAllImages()
        {
            var jArray = new JArray();
            var imageJson1 = new JObject
            {
                { "Id", 1609 },
                { "Filename", "396100_101.webp" },
                { "Index", 0 },
                { "LinkTypeId", "ProductResource" },
                { "Url", "https://www.test.test/test" },
                { "EntityId", 101615 },
                { "Extension", ".webp" },
                { "ActiveResource", null },
                { "ResourceFileId", 1609 },
                { "ResourceFilename", "396100_101.webp" },
                { "ResourceImageMap", null },
                { "ResourceMimeType", "application/octet-stream" },
                { "ResourceName", "396100_101.webp" }
            };
            jArray.Add(imageJson1);

            var imageJson2 = new JObject
            {
                { "Id", 1610 },
                { "Filename", "396101_102.webp" },
                { "Index", 1 },
                { "LinkTypeId", "ProductResource" },
                { "Url", "https://www.test.test/test2" },
                { "EntityId", 101616 },
                { "Extension", ".webp" },
                { "ActiveResource", null },
                { "ResourceFileId", 1610 },
                { "ResourceFilename", "396101_102.webp" },
                { "ResourceImageMap", null },
                { "ResourceMimeType", "application/octet-stream" },
                { "ResourceName", "396101_102.webp" }
            };
            jArray.Add(imageJson2);

            return jArray;
        }

        private static JObject GetSingleImage()
        {
            var imageJson1 = new JObject
            {
                { "Id", 1609 },
                { "Filename", "396100_101.webp" },
                { "Index", 0 },
                { "LinkTypeId", "ProductResource" },
                { "Url", "https://www.test.test/test" },
                { "EntityId", 101615 },
                { "Extension", ".webp" },
                { "ActiveResource", null },
                { "ResourceFileId", 1609 },
                { "ResourceFilename", "396100_101.webp" },
                { "ResourceImageMap", null },
                { "ResourceMimeType", "application/octet-stream" },
                { "ResourceName", "396100_101.webp" }
            };

            return imageJson1;
        }
    }
}
