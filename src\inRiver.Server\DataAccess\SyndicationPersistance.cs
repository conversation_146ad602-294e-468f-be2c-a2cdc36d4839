namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Linq;
    using Dapper;
    using inRiver.Remoting.Log;
    using inRiver.Server.Syndication;
    using Serilog;
    using Syndication.Models;

    public partial class inRiverPersistance
    {
        private T CheckNull<T>(object value)
        {
            if (value == DBNull.Value)
                return default(T);

            return (T)value;
        }

        public List<MapEnumeration> GetEnumerations(List<int> formatFieldIds, int mappingId)
        {
            List<MapEnumeration> mapEnumerations = new List<MapEnumeration>();

            try
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    SqlCommand command = connection.CreateCommand();
                    connection.Open();
                    var ids = string.Join(",", formatFieldIds);
                    command.CommandText =
                        "SELECT Id, FormatFieldId, MappingId, EnumKey, EnumValue, FieldValue FROM SyndicationFormatEnumeration" +
                        " LEFT JOIN SyndicationFormatEnumerationMap ON SyndicationFormatEnumeration.Id = SyndicationFormatEnumerationMap.FormatEnumerationId" +
                        "   AND MappingId = @MappingId" +
                        $" WHERE FormatFieldId IN({ids})";

                    command.Parameters.Add("@MappingId", SqlDbType.Int);
                    command.Parameters["@MappingId"].Value = mappingId;

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        using (var dataTable = new DataTable())
                        {
                            dataTable.Load(reader);

                            if (dataTable.Rows.Count > 0)
                            {
                                var result = (from row in dataTable.AsEnumerable()
                                    select new MapEnumeration()
                                    {
                                        MappingEnumerationId = CheckNull<int>(row["Id"]),
                                        MappingFormatFieldId = (int)row["FormatFieldId"],
                                        MappingFormatMappingId = CheckNull<int>(row["MappingId"]),
                                        EnumKey = (int)row["EnumKey"],
                                        EnumValue = (string)row["EnumValue"],
                                        FieldValue = CheckNull<string>(row["FieldValue"])
                                    }).ToList();

                                mapEnumerations.AddRange(result);
                            }
                        }

                        return mapEnumerations;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetEnumerations caught an unexpected exception");
                throw;
            }
        }

        public MapFormat GetMapFormat(int id)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    SqlCommand command = connection.CreateCommand();
                    connection.Open();

                    command.CommandText = "SELECT Id, Namespace, Prefix, Encoding, XmlVersion, RssVersion, LastModified FROM SyndicationFormat WHERE Id = @Id";
                    command.Parameters.Add("@Id", SqlDbType.Int);
                    command.Parameters["@Id"].Value = id;

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        reader.Read();

                        MapFormat mapType = new MapFormat()
                        {
                            Id = reader.GetInt32(0),
                            Namespace = reader.IsDBNull(1) ? null : reader.GetString(1),
                            Prefix = reader.IsDBNull(2) ? null : reader.GetString(2),
                            Encoding = reader.IsDBNull(3) ? null : reader.GetString(3),
                            XmlVersion = reader.IsDBNull(4) ? null : reader.GetString(4),
                            RssVersion = reader.IsDBNull(5) ? null : reader.GetString(5),
                            LastModified = reader.GetDateTime(6)
                        };

                        return mapType;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetMapFormat caught an unexpected exception");
                throw;
            }
        }

        public SyndicationMapping GetMappingDetails(int mappingId)
        {
            SyndicationMapping syndicationMapping = null;

            try
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    SqlCommand command = connection.CreateCommand();
                    connection.Open();

                    command.CommandText = "SELECT * FROM ViewMappings WHERE MappingId = @MappingId";

                    command.Parameters.Add("@MappingId", SqlDbType.VarChar);
                    command.Parameters["@MappingId"].Value = mappingId;

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        using (var dataTable = new DataTable())
                        {
                            dataTable.Load(reader);

                            if (dataTable.Rows.Count > 0)
                            {
                                syndicationMapping = new SyndicationMapping()
                                {
                                    FormatId = (int)dataTable.Rows[0]["SyndicationFormatId"],
                                    WorkareaEntityTypeId = CheckNull<string>(dataTable.Rows[0]["WorkareaEntityTypeId"]),
                                    FirstRelatedEntityTypeId = CheckNull<string>(dataTable.Rows[0]["FirstRelatedEntityTypeId"]),
                                    MappingId = (int)dataTable.Rows[0]["MappingId"],
                                    MappingName = (string)dataTable.Rows[0]["MappingName"],
                                    FirstLinkEntityTypeId = CheckNull<string>(dataTable.Rows[0]["FirstLinkEntityTypeId"]),
                                    SecondRelatedEntityTypeId = CheckNull<string>(dataTable.Rows[0]["SecondRelatedEntityTypeId"]),
                                    SecondLinkEntityTypeId = CheckNull<string>(dataTable.Rows[0]["SecondLinkEntityTypeId"]),
                                    OutputEntityTypeId = CheckNull<string>(dataTable.Rows[0]["OutputEntityTypeId"]),
                                    EnableSKU = CheckNull<bool>(dataTable.Rows[0]["EnableSKU"]),
                                    DefaultLanguage = CheckNull<string>(dataTable.Rows[0]["DefaultLanguage"]),
                                    IsResourceExportConfigured = (bool)dataTable.Rows[0]["IsResourceExportConfigured"]
                                };

                                syndicationMapping.SyndicationMappingFields = (from row in dataTable.AsEnumerable()
                                                                               select new SyndicationMappingField()
                                                                               {
                                                                                   MappingFieldId = CheckNull<int?>(row["MappingFieldId"]),
                                                                                   MappingFormatId = (int)row["SyndicationFormatId"],
                                                                                   MappingId = (int)row["MappingId"],
                                                                                   MappingName = (string)row["MappingName"],
                                                                                   MappingFormatFieldId = (int)row["FormatFieldId"],
                                                                                   WorkareaEntityTypeId = CheckNull<string>(row["WorkareaEntityTypeId"]),
                                                                                   FirstRelatedEntityTypeId = CheckNull<string>(row["FirstRelatedEntityTypeId"]),
                                                                                   SecondRelatedEntityTypeId = CheckNull<string>(row["SecondRelatedEntityTypeId"]),
                                                                                   EntityTypeId = CheckNull<string>(row["EntityTypeId"]),
                                                                                   FieldTypeId = CheckNull<string>(row["FieldTypeId"]),
                                                                                   FieldDataType = CheckNull<string>(row["FieldDataType"]),
                                                                                   MapFieldTypeId = (string)row["FormatFieldTypeId"],
                                                                                   Enumerations = new List<MapEnumeration>(),
                                                                                   UnitType = CheckNull<string>(row["UnitType"]),
                                                                                   UnitCvl = CheckNull<string>(row["UnitCvl"]),
                                                                                   UnitDefaultValue = CheckNull<string>(row["UnitDefaultValue"]),
                                                                                   UnitValue = CheckNull<string>(row["UnitValue"]),
                                                                                   Converter = CheckNull<string>(row["FunctionName"]),
                                                                                   ConverterId = CheckNull<int?>(row["FunctionId"]),
                                                                                   IsCustom = CheckNull<bool>(row["IsCustom"]),
                                                                                   Script = CheckNull<string>(row["Script"]),
                                                                                   Args = CheckNull<string>(row["Args"]),
                                                                                   MapPath = CheckNull<string>(row["FormatPath"]),
                                                                                   DataType = (string)row["DataType"],
                                                                                   Mandatory = (bool)row["Mandatory"],
                                                                                   Unique = (bool)row["Unique"],
                                                                                   Recommended = (bool)row["Recommended"],
                                                                                   MaxLength = CheckNull<int?>(row["MaxLength"]),
                                                                                   DefaultValue = CheckNull<string>(row["DefaultValue"]),
                                                                                   MetaData = CheckNull<string>(row["MetaData"]),
                                                                                   Description = CheckNull<string>(row["Description"]),
                                                                                   CvlCompleteness = CheckNull<bool>(row["CvlCompleteness"]),
                                                                                   SortOrder = this.CheckNull<int>(row["SortOrder"])
                                                                               }).ToList();
                            }
                        }

                        var formatFieldIds = syndicationMapping.SyndicationMappingFields.Select(x => x.MappingFormatFieldId).ToList();
                        var enumerations = GetEnumerations(formatFieldIds, mappingId);
                        if (enumerations.Count > 0)
                        {
                            foreach (var mapField in syndicationMapping.SyndicationMappingFields)
                                mapField.Enumerations = enumerations.Where(x => x.MappingFormatFieldId == mapField.MappingFormatFieldId).ToList();
                        }

                        if (syndicationMapping.IsResourceExportConfigured)
                        {
                            syndicationMapping.SyndicationMappingResourceFields = this.GetResourceFields(command, mappingId);
                        }

                        return syndicationMapping;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetMappingDetails caught an unexpected exception");
                throw;
            }
        }

        public SyndicationMappingFunction GetMappingFunction(int id)
        {
            try
            {
                using var connection = new SqlConnection(this.ConnectionString);
                var command = connection.CreateCommand();
                connection.Open();

                command.CommandText = "SELECT Id, Name, Script, IsCustom FROM SyndicationMappingFunction WHERE Id = @Id";
                _ = command.Parameters.Add("@Id", SqlDbType.Int);
                _ = command.Parameters["@Id"].Value = id;

                using (var reader = command.ExecuteReader())
                {
                    reader.Read();

                    var syndicationMappingFunction = new SyndicationMappingFunction
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("Id")),
                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name")),
                        Script = reader.IsDBNull(reader.GetOrdinal("Script")) ? null : reader.GetString(reader.GetOrdinal("Script")),
                        IsCustom = !reader.IsDBNull(reader.GetOrdinal("IsCustom")) && reader.GetBoolean(reader.GetOrdinal("IsCustom")),
                    };

                    return syndicationMappingFunction;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetMappingFunction caught an unexpected exception");
                throw;
            }
        }

        private IList<SyndicationMappingResourceField> GetResourceFields(SqlCommand command, int mappingId)
        {
            command.CommandText =
                @"SELECT smrf.Id AS Id,
                   smrf.FieldTypeId AS FieldTypeId,
                   smrf.FieldDataType AS FieldDataType,
                   smfm.Args AS Args,
                   smf.Id AS FunctionId,
                   smf.Script AS Script
                FROM SyndicationMapping
                INNER JOIN SyndicationMappingFieldMapping smfm ON SyndicationMapping.Id = smfm.MappingId
                INNER JOIN SyndicationMappingResourceField smrf ON smfm.Id = smrf.MappingFieldMappingId
                LEFT JOIN SyndicationMappingFunction smf ON smfm.MappingFunctionId = smf.Id
                WHERE MappingId = @MappingId";

            command.Parameters.Clear();
            command.Parameters.Add("@MappingId", SqlDbType.VarChar).Value = mappingId;

            var resourceFields = new List<SyndicationMappingResourceField>();
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var resourceField = new SyndicationMappingResourceField
                {
                    FieldTypeId = reader.GetString(reader.GetOrdinal("FieldTypeId")),
                    FieldDataType = reader.GetString(reader.GetOrdinal("FieldDataType"))
                };
                if (!reader.IsDBNull(reader.GetOrdinal("Id")))
                {
                    resourceField.Id = reader.GetInt32(reader.GetOrdinal("Id"));
                }

                if (!reader.IsDBNull(reader.GetOrdinal("Args")))
                {
                    resourceField.Args = reader.GetString(reader.GetOrdinal("Args"));
                }

                if (!reader.IsDBNull(reader.GetOrdinal("FunctionId")))
                {
                    resourceField.ConverterId = reader.GetInt32(reader.GetOrdinal("FunctionId"));
                }

                if (!reader.IsDBNull(reader.GetOrdinal("Script")))
                {
                    resourceField.Script = reader.GetString(reader.GetOrdinal("Script"));
                }

                resourceFields.Add(resourceField);
            }

            return resourceFields;
        }
    }
}
