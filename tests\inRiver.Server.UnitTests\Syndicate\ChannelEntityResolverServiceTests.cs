namespace inRiver.Server.UnitTests.Syndicate
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using FakeItEasy;
    using inRiver.Remoting.Dto;
    using inRiver.Server.Models;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Xunit;

    public class ChannelEntityResolverServiceTests
    {
        private readonly ISyndicationChannelService fakeSyndicationChannelService;
        private readonly ISyndicationEntityService fakeSyndicationEntityService;
        private readonly ChannelEntityResolverService resolverService;

        public ChannelEntityResolverServiceTests()
        {
            this.fakeSyndicationEntityService = A.Fake<ISyndicationEntityService>();
            this.fakeSyndicationChannelService = A.Fake<ISyndicationChannelService>();
            this.resolverService = new ChannelEntityResolverService(this.fakeSyndicationChannelService, this.fakeSyndicationEntityService);
        }

        [Fact]
        public async Task GetEntityIdsFromChannelAsync_EntityIdsAreEmpty_ShouldReturnAllEntities()
        {
            // Arrange
            const int channelId = 123;
            const string mappingEntityType = "Product";
            var expectedStructure = new List<ChannelStructure>
            {
                new ChannelStructure { EntityId = 1 },
                new ChannelStructure { EntityId = 2 }
            };
            A.CallTo(() => this.fakeSyndicationChannelService.GetChannelStructureByEntityTypeIds(channelId, A<List<string>>.That.Contains(mappingEntityType)))
                .Returns(Task.FromResult<IList<ChannelStructure>>(expectedStructure));

            // Act
            var result = await this.resolverService.GetEntityIdsFromChannelAsync(channelId, new List<int>(), mappingEntityType);

            // Assert
            Assert.Equal(new List<int> { 1, 2 }, result);
        }

        [Fact]
        public async Task GetEntityIdsFromChannelAsync_EntityTypeIdIsEmpty_ShouldThrowException()
        {
            // Arrange
            const int channelId = 123;
            var entityIds = new List<int> { 1 };
            const string mappingEntityType = "Product";
            A.CallTo(() => this.fakeSyndicationEntityService.GetEntitiesAsync(A<IEnumerable<int>>._))
                .Returns(Task.FromResult<IEnumerable<DtoEntity>>(new List<DtoEntity>()));

            // Act + Assert
            await Assert.ThrowsAsync<SyndicateException>(() =>
                this.resolverService.GetEntityIdsFromChannelAsync(channelId, entityIds, mappingEntityType));
        }

        [Fact]
        public async Task GetEntityIdsFromChannelAsync_InputEntityTypeIsEqualToMappingEntityType_ShouldReturnInputEntityIds()
        {
            // Arrange
            const int channelId = 123;
            const string mappingEntityType = "Product";
            var entityIds = new List<int> { 10, 11, 12 };
            var expectedEntities = new List<DtoEntity>
            {
                new DtoEntity { EntityTypeId = "Product" }
            };
            A.CallTo(() => this.fakeSyndicationEntityService.GetEntitiesAsync(A<IEnumerable<int>>._))
                .Returns(Task.FromResult<IEnumerable<DtoEntity>>(expectedEntities));
            var call = A.CallTo(() => this.fakeSyndicationChannelService.GetChannelStructureByEntityTypeIds(channelId, A<List<string>>._));

            // Act
            var result = await this.resolverService.GetEntityIdsFromChannelAsync(channelId, entityIds, mappingEntityType);

            // Assert
            call.MustNotHaveHappened();
            Assert.Equal(result.Count, 3);
            Assert.Contains(10, result);
            Assert.Contains(11, result);
            Assert.Contains(12, result);
        }

        [Fact]
        public async Task GetEntityIdsFromChannelAsync_HasInboundRelation_ShouldReturnInboundEntityIds()
        {
            // Arrange
            const int channelId = 123;
            const string mappingEntityType = "Product";
            var entityIds = new List<int> { 10, 6115 };

            var channelStructure = new List<ChannelStructure>
            {
                new ChannelStructure { EntityId = 2298, EntityTypeId = "Product", FullPath = "../Product_2298" },
                new ChannelStructure { EntityId = 10, EntityTypeId = "Item", FullPath = "../Product_2298/Link_ProductActivity/Activity_123/Link_ActivityItem/Item_10" },
                new ChannelStructure { EntityId = 100, EntityTypeId = "Item", FullPath = "../Product_2298/Link_ProductActivity/Activity_123/Link_ActivityItem/Item_100" },
                new ChannelStructure { EntityId = 2299, EntityTypeId = "Product", FullPath = "../Product_2299" },
                new ChannelStructure { EntityId = 6115, EntityTypeId = "Item", FullPath = "../Product_2299/Link_ProductActivity/Activity_123/Link_ActivityItem/Item_6115" }
            };
            var expectedEntities = new List<DtoEntity>
            {
                new DtoEntity { EntityTypeId = "Item" }
            };
            A.CallTo(() => this.fakeSyndicationEntityService.GetEntitiesAsync(A<IEnumerable<int>>._))
                .Returns(Task.FromResult<IEnumerable<DtoEntity>>(expectedEntities));
            A.CallTo(() => this.fakeSyndicationChannelService.GetChannelStructureByEntityTypeIds(channelId, A<List<string>>._))
                .Returns(Task.FromResult<IList<ChannelStructure>>(channelStructure));

            // Act
            var result = await this.resolverService.GetEntityIdsFromChannelAsync(channelId, entityIds, mappingEntityType);

            // Assert
            Assert.Equal(result.Count, 2);
            Assert.Contains(2298, result);
            Assert.Contains(2299, result);
        }

        [Fact]
        public async Task GetEntityIdsFromChannelAsync_HasOutboundRelation_ShouldReturnOutboundEntityIds()
        {
            // Arrange
            const int channelId = 123;
            const string mappingEntityType = "Item";
            var entityIds = new List<int> { 2298 };

            var channelStructure = new List<ChannelStructure>
            {
                new ChannelStructure { EntityId = 2298, EntityTypeId = "Product", FullPath = ".../Product_2298" },
                new ChannelStructure { EntityId = 6114, EntityTypeId = "Item", FullPath = "../Product_2298/Link_ProductActivity/Activity_123/Link_ActivityItem/Item_6114" },
                new ChannelStructure { EntityId = 6115, EntityTypeId = "Item", FullPath = "../Product_2298/Link_ProductActivity/Activity_123/Link_ActivityItem/Item_6115" }
            };
            var expectedEntities = new List<DtoEntity>
            {
                new DtoEntity { EntityTypeId = "Product" }
            };
            A.CallTo(() => this.fakeSyndicationEntityService.GetEntitiesAsync(A<IEnumerable<int>>._))
                .Returns(Task.FromResult<IEnumerable<DtoEntity>>(expectedEntities));
            A.CallTo(() => this.fakeSyndicationChannelService.GetChannelStructureByEntityTypeIds(channelId, A<List<string>>._))
                .Returns(Task.FromResult<IList<ChannelStructure>>(channelStructure));

            // Act
            var result = await this.resolverService.GetEntityIdsFromChannelAsync(channelId, entityIds, mappingEntityType);

            // Assert
            Assert.Equal(result.Count, 2);
            Assert.Contains(6114, result);
            Assert.Contains(6115, result);
        }

        [Fact]
        public async Task GetEntityIdsFromChannelAsync_HasOutboundRelationWithMultipleLevels_ShouldReturnOutboundEntityIdsFromAllLevels()
        {
            // Arrange
            const int channelId = 123;
            const string mappingEntityType = "Item";
            var entityIds = new List<int> { 2298 };

            var channelStructure = new List<ChannelStructure>
            {
                new ChannelStructure { EntityId = 2298, EntityTypeId = "Product", FullPath = ".../Product_2298" },
                new ChannelStructure { EntityId = 6114, EntityTypeId = "Item", FullPath = "../Product_2298/Link_ProductActivity/Activity_123/Link_ActivityItem/Item_6114" },
                new ChannelStructure { EntityId = 6115, EntityTypeId = "Item", FullPath = "../Product_2298/Link_ProductActivity/Activity_123/Link_ActivityItem/Item_6114/Link_ItemItem/Item_6115" }
            };
            var expectedEntities = new List<DtoEntity>
            {
                new DtoEntity { EntityTypeId = "Product" }
            };
            A.CallTo(() => this.fakeSyndicationEntityService.GetEntitiesAsync(A<IEnumerable<int>>._))
                .Returns(Task.FromResult<IEnumerable<DtoEntity>>(expectedEntities));
            A.CallTo(() => this.fakeSyndicationChannelService.GetChannelStructureByEntityTypeIds(channelId, A<List<string>>._))
                .Returns(Task.FromResult<IList<ChannelStructure>>(channelStructure));

            // Act
            var result = await this.resolverService.GetEntityIdsFromChannelAsync(channelId, entityIds, mappingEntityType);

            // Assert
            Assert.Equal(result.Count, 2);
            Assert.Contains(6114, result);
            Assert.Contains(6115, result);
        }
    }
}
