namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using System;
    using inRiver.Api.Data.Client;

    /// <summary>
    /// Documentation.
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        public override void UpdateUniqueHashOnFields(Models.inRiver.FieldType fieldType)
        {
            InRiverDataApiClient.ToggleUniqueFlagOnFieldType(this._origInRiverPersistance.EnvironmentId, fieldType.Id, fieldType.Unique);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
        }

        public override bool DeleteFieldType(string id)
        {
            InRiverDataApiClient.DeleteFieldTypeByJob(this._origInRiverPersistance.EnvironmentId, id);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return true;
        }

        public override bool DeleteAllFieldTypes()
            => throw new NotImplementedException();

        public override Models.inRiver.FieldType AddFieldType(Models.inRiver.FieldType fieldType, bool generateIndex)
        {
            var result = _origInRiverPersistance.AddFieldType(fieldType, generateIndex);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override Models.inRiver.FieldType UpdateFieldType(Models.inRiver.FieldType fieldType)
        {
            var result = _origInRiverPersistance.UpdateFieldType(fieldType);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }
    }
}
