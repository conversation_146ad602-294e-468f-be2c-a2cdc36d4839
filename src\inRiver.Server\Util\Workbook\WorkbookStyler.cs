namespace inRiver.Server.Util.Workbook
{
    using DocumentFormat.OpenXml.Packaging;
    using DocumentFormat.OpenXml.Spreadsheet;

    public static class WorkbookStyler
    {
        public static void AddStyleSheet(SpreadsheetDocument spreadsheet)
        {
            var stylesheet = spreadsheet.WorkbookPart.AddNewPart<WorkbookStylesPart>();

            var workbookStyleSheet = new Stylesheet();

            var fonts = new Fonts();
            var font0 = new Font();         // Default font

            var font1 = new Font();         // White font
            var color = new Color() { Rgb = "0xFFFFFF" };

            font1.Append(color);
            fonts.Append(font0);
            fonts.Append(font1);

            var fills = new Fills();

            var fill0 = new Fill(); // FillId = 0
            var patternFill0 = new PatternFill() { PatternType = PatternValues.None };

            fill0.Append(patternFill0);

            var fill1 = new Fill(); // FillId = 1
            var patternFill1 = new PatternFill() { PatternType = PatternValues.Gray125 };

            fill1.Append(patternFill1);

            var fill2 = new Fill(); // FillId = 2,RED
            var patternFill2 = new PatternFill() { PatternType = PatternValues.Solid };

            // Cell fill patterns operate with two colors: a background color and a foreground color. These combine together to make a patterned cell fill.
            var foregroundColor2 = new ForegroundColor() { Rgb = "0xFE0000" }; // red color
            var backgroundColor2 = new BackgroundColor() { Indexed = 64U };

            patternFill2.Append(foregroundColor2);
            patternFill2.Append(backgroundColor2);
            fill2.Append(patternFill2);

            fills.Append(fill0);
            fills.Append(fill1);
            fills.Append(fill2);

            var borders = new Borders() { Count = 1U };
            var border = new Border();

            var leftBorder = new LeftBorder();
            var rightBorder = new RightBorder();
            var topBorder = new TopBorder();
            var bottomBorder = new BottomBorder();
            var diagonalBorder = new DiagonalBorder();

            border.Append(leftBorder);
            border.Append(rightBorder);
            border.Append(topBorder);
            border.Append(bottomBorder);
            border.Append(diagonalBorder);

            borders.Append(border);

            var cellFormats = new CellFormats();

            var cellFormat0 = new CellFormat() { FontId = 0, FillId = 0, BorderId = 0 }; // Default style : Mandatory | Style ID =0

            var cellFormat1 = new CellFormat() { FontId = 1, FillId = 2, BorderId = 0 };  // white text, fill red

            cellFormats.Append(cellFormat0);
            cellFormats.Append(cellFormat1);


            // Append FONTS, FILLS , BORDERS & CellFormats to stylesheet <Preserve the ORDER>
            workbookStyleSheet.Append(fonts);
            workbookStyleSheet.Append(fills);
            workbookStyleSheet.Append(borders);
            workbookStyleSheet.Append(cellFormats);

            // Finalize
            stylesheet.Stylesheet = workbookStyleSheet;
            stylesheet.Stylesheet.Save();
        }
    }
}
