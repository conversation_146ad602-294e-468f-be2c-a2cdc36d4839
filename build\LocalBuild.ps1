#Requires -PSEdition Desktop -RunAsAdministrator

[CmdletBinding()]
param()

Write-Output "  iPMC.LongRunningJob"
$ErrorActionPreference = "Stop"
if (-not (Get-Command msbuild -ErrorAction SilentlyContinue)) { throw "msbuild needs to be available in the path environment variable"; exit 1 }
if (-not (Get-Command nuget -ErrorAction SilentlyContinue)) { throw "nuget needs to be available in the path environment variable"; exit 1 }

# Define log levels for the different tools to make output reasonable if the user would like verbose logging
$verboseLevel = 'quiet'
$consoleLogger = 'ErrorsOnly'
if ($VerbosePreference -eq 'Continue') {
    $verboseLevel = 'minimal'
    $consoleLogger = 'Summary'
}

$Configuration = "Development-LocalEuw"
if ($env:iPMC_REGION -eq 'use') {
    $Configuration = "Development-LocalUse"
}

. "$PSScriptRoot/CreateLocalConfigFiles.ps1" -Configuration $Configuration

#Variables
$basePath = Convert-Path "$PSScriptRoot/.."
$publishProfile = Join-Path $basePath "src/LongRunningJob/PublishProfiles/Local.1Node.xml"
$project = Join-Path $basePath "src/LongRunningJob/LongRunningJob.sfproj"
$solution = Join-Path $basePath "inRiver.ServiceFabric.LongRunningJob.sln"
$deployScript = Join-Path $basePath "src/LongRunningJob/Scripts/Deploy-FabricApplication.ps1"
$packageLocation = Join-Path $basePath "src/LongRunningJob/pkg/Debug"

Write-Output "    -> Restoring nuget packages"
nuget restore $solution | Write-Verbose
if ($LASTEXITCODE -ne 0) { throw "iPMC.LongRunningJob failed to restore packages"; exit 1 }

Write-Output "    -> Building solution"
msbuild $solution -p:platform='x64' -p:configuration=$Configuration -verbosity:$verboseLevel -consoleLoggerParameters:$consoleLogger -nologo /restore -m
if ($LASTEXITCODE -ne 0) { throw "iPMC.LongRunningJob failed to build"; exit 1 }

Write-Output "    -> Packaging Service Fabric project"
msbuild $project -p:platform='x64' -p:configuration=$Configuration -t:Package -p:PackageLocation=$packageLocation -verbosity:$verboseLevel -consoleLoggerParameters:$consoleLogger -nologo -m
if ($LASTEXITCODE -ne 0) { throw "iPMC.LongRunningJob failed to deploy to package"; exit 1 }

Write-Output "    -> Deploying to local Service Fabric cluster"
$err = ''
try { Connect-ServiceFabricCluster localhost:19000 -TimeoutSec 7 -WarningAction SilentlyContinue | Write-Verbose } catch { $err = $_ }
if ($err) { throw "iPMC.Token failed to connect to local cluster, is the cluster running?, ${err}"; exit 1 }
$Global:ClusterConnection = $ClusterConnection

. $deployScript `
    -ApplicationPackagePath $packageLocation `
    -PublishProfileFile $publishProfile `
    -OverwriteBehavior 'Always' `
    -UseExistingClusterConnection *>&1 | Write-Verbose

if ($LASTEXITCODE -ne 0) { throw "iPMC.LongRunningJob failed to deploy deploy to service fabric"; exit 1 }
