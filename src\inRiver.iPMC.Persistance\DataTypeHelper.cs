namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Data;

    public class DataTypeHelper
    {
        public enum DataTypeEnum
        {
            String,
            LocaleString,
            Double,
            Integer,
            Boolean,
            DateTime,
            Xml,
            File,
            CVL
        }

        public static DataTypeEnum? GetDataType(string fieldDataType)
        {
            if (string.IsNullOrWhiteSpace(fieldDataType))
                return null;

            if (Enum.TryParse<DataTypeEnum>(fieldDataType, true, out var dataType))
                return dataType;

            throw new ArgumentException($"fieldDataType is an invalid value: {fieldDataType}.");
        }

        public static DbType GetDbType(string fieldDataType)
        {
            switch (GetDataType(fieldDataType))
            {
                case DataTypeEnum.Double:
                    return DbType.Double;
                case DataTypeEnum.Integer:
                    return DbType.Int64;
                case DataTypeEnum.Boolean:
                    return DbType.Boolean;
                case DataTypeEnum.DateTime:
                    return DbType.DateTime;
                case DataTypeEnum.String:
                case DataTypeEnum.LocaleString:
                case DataTypeEnum.Xml:
                case DataTypeEnum.File:
                case DataTypeEnum.CVL:
                case null:
                default:
                    return DbType.String;
            }
        }
    }
}
