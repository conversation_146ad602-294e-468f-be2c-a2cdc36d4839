namespace inRiver.Core.Objects
{
    /// <summary>
    /// Supported data types in the inRiver system
    /// </summary>
    public sealed class DataType
    {
        /// <summary>Data stored as a string</summary>
        public static string String => "String";

        /// <summary>Data stored as a localized string</summary>
        public static string LocaleString => "LocaleString";

        /// <summary>Data stored as a double</summary>
        public static string Double => "Double";

        /// <summary>Data stored as an integer</summary>
        public static string Integer => "Integer";

        /// <summary>Data stored as a boolean</summary>
        public static string Boolean => "Boolean";

        /// <summary>Data stored as a date time object</summary>
        public static string DateTime => "DateTime";

        /// <summary>Data stored as an xml string</summary>
        public static string Xml => "Xml";

        /// <summary>Data referring to a file id. Stored as an integer. </summary>
        public static string File => "File";

        /// <summary>Data referring to a CVL key. </summary>
        public static string CVL => "CVL";
    }
}
