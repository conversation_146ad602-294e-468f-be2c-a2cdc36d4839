namespace inRiver.Core.Models.inRiver.ApplyExpressions
{
    using global::inRiver.Core.Enum;

    public class ApplyExpressionsModel
    {
        public string EntityTypeId { get; set; }

        public string FieldTypeId { get; set; }

        public string FieldSetId { get; set; }

        public ApplyExpressionOverwriteMode OverwriteMode { get; set; }

        public string Expression { get; set; }

        public string Target { get; set; }
    }
}
