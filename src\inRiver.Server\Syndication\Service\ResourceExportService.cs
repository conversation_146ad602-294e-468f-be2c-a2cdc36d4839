namespace inRiver.Server.Syndication.Service
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Script;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Newtonsoft.Json;
    using Serilog;

    public class ResourceExportService : IResourceExportService
    {
        /// <summary>
        ///     Maximum compressed file size.
        ///     Since we do not have statistics on the size of exported resource files from syndication,
        ///     a 4 gb limit has been introduced that will prevent exporting for very large syndications.
        ///     The value may change in the future after collecting statistics on the use of resource export.
        /// </summary>
        private const double MaxCompressedFileSizeInGigabytes = 4;

        /// <summary>
        ///     We assume that the size of the compressed file will be 20% less than the sum of the sizes of the original files.
        /// </summary>
        private const double AssumedCompressionRatio = 0.8;

        private readonly IResourceExportParsingService resourceExportParsingService;

        private readonly ICompressionManager compressionManager;

        private readonly IJobMetadataManager jobMetadataManager;

        private readonly RequestContext requestContext;

        private readonly bool isResourceExportEnabled;

        private readonly bool disableLimitPreCheck;

        private bool isResourceExportConfigured;

        public ResourceExportService(
            RequestContext requestContext,
            IJobMetadataManager jobMetadataManager,
            IResourceExportParsingService resourceExportParsingService,
            ICompressionManager compressionManager,
            SyndicationModel syndicationModel)
        {
            if (syndicationModel == null)
            {
                throw new ArgumentNullException(nameof(syndicationModel));
            }

            this.requestContext = requestContext ?? throw new ArgumentNullException(nameof(requestContext));
            this.jobMetadataManager = jobMetadataManager;
            this.resourceExportParsingService = resourceExportParsingService;
            this.compressionManager = compressionManager;
            this.isResourceExportEnabled = syndicationModel.IsResourceExportEnabled;
            this.disableLimitPreCheck = syndicationModel.DisableResourceExportLimitPreCheck;
        }

        public bool IsResourceExportAllowed => this.isResourceExportConfigured && this.isResourceExportEnabled && !this.IsOriginalFileSizeLimitExceeded;

        private bool IsOriginalFileSizeLimitExceeded { get; set; }

        public void SetResourceExportAsConfigured() => this.isResourceExportConfigured = true;

        public object[] GetScriptValues(TransformationManager transformationManager, InRiverEntity entity, object mainValue)
        {
            var scriptValues = mainValue != null
                ? new List<object> { mainValue }
                : new List<object>();
            scriptValues.AddRange(transformationManager.GetValues(entity, enableSKU: false, skuInjector: null, skuId: null));

            return scriptValues.ToArray();
        }

        public ResourceExportModel GetResourceExportModel(string syndicationName, IList<ExportContainer> exportContainers)
        {
            var resourcesData = exportContainers
                .SelectMany(container => container.ResourceExportFields.Select(field => field.Data)).ToList();

            return new ResourceExportModel
            {
                ZipFileNamePrefix = $"{syndicationName}_",
                Resources = this.resourceExportParsingService.ParseResourcesData(resourcesData)
            };
        }

        public void ValidateResourceFiles(IList<Entity> allResourceEntities)
        {
            if (!this.IsResourceExportAllowed || this.disableLimitPreCheck)
            {
                return;
            }

            var resourceFileIds = allResourceEntities
                .Select(x => x.Fields.FirstOrDefault(field => field.FieldType.Id == "ResourceFileId")?.Data)
                .Where(x => x != null)
                .Select(x => int.Parse(x.ToString())).ToList();
            var allResourceFiles = this.requestContext.DataPersistance.GetResourceFiles(resourceFileIds);
            var totalSize = allResourceFiles.Select(file => file.FileSize).Sum();
            if (totalSize > GetMaxOriginalFileSizeInBytes())
            {
                this.IsOriginalFileSizeLimitExceeded = true;
            }
        }

        public async Task HandleResourceExportAsync(ResourceExportModel resourceData, CancellationToken cancellationToken)
        {
            if (this.isResourceExportConfigured && this.isResourceExportEnabled)
            {
                try
                {
                    if (this.IsOriginalFileSizeLimitExceeded)
                    {
                        _ = this.jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.FinishedWithErrors, true);
                        this.jobMetadataManager.Update(LongRunningJobMetaDataKeys.Message, GetCompressedFileLimitExceededErrorMessage());
                    }
                    else
                    {
                        var compressionServiceOutputModelJson = await this.compressionManager.CallResourceExportAsync(resourceData, cancellationToken);
                        if (string.IsNullOrEmpty(compressionServiceOutputModelJson))
                        {
                            throw new ArgumentNullException(nameof(compressionServiceOutputModelJson), "Error getting compression service result.");
                        }

                        var compressionServiceOutputModel = JsonConvert.DeserializeObject<CompressionServiceOutputModel>(compressionServiceOutputModelJson);
                        if (IsValidFileSize(compressionServiceOutputModel.ZipFileSize, out var exceptionMessage))
                        {
                            _ = this.jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.ResourceZipMetadata, compressionServiceOutputModelJson);
                            Log.Information("Compression service result: {compressionServiceOutputModelJson}", compressionServiceOutputModelJson);
                        }
                        else
                        {
                            _ = this.jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.FinishedWithErrors, true);
                            this.jobMetadataManager.Update(LongRunningJobMetaDataKeys.Message, exceptionMessage);
                            Log.Error(
                                "{exceptionMessage} Compression service result: {compressionServiceOutputModelJson}",
                                exceptionMessage,
                                compressionServiceOutputModelJson);

                            await this.compressionManager.DeleteFileAsync(compressionServiceOutputModel.ZipFileName);
                        }
                    }
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    _ = this.jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.FinishedWithErrors, true);
                    this.jobMetadataManager.Update(LongRunningJobMetaDataKeys.Message, $"Unable to export resource files: {ex.Message}");
                    Log.Error(ex, $"Unable to export resource files: {ex.Message}");
                }
            }
        }

        private static bool IsValidFileSize(long fileSize, out string exceptionMessage)
        {
            exceptionMessage = string.Empty;
            if (fileSize <= GetMaxCompressedFileSizeInBytes())
            {
                return true;
            }

            exceptionMessage = GetCompressedFileLimitExceededErrorMessage();

            return false;
        }

        private static double GetMaxOriginalFileSizeInBytes() => GetMaxCompressedFileSizeInBytes() / AssumedCompressionRatio;

        private static double GetMaxCompressedFileSizeInBytes() => MaxCompressedFileSizeInGigabytes * 1024 * 1024 * 1024;

        private static string GetCompressedFileLimitExceededErrorMessage() =>
            $"Resource export file is larger than {MaxCompressedFileSizeInGigabytes} GB and could not be exported. The product data files can still be downloaded.";
    }
}
