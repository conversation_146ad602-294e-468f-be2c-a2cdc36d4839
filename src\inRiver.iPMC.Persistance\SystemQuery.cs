namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Collections.Generic;

    public class SystemQuery
    {
        public Operator EntityIdOperator { get; set; }
        public Operator EntityTypeIdOperator { get; set; }
        public Operator FieldSetIdOperator { get; set; }
        public Operator LastModifiedOperator { get; set; }
        public Operator CreatedOperator { get; set; }
        public Operator CreatedByOperator { get; set; }
        public Operator ModifiedByOperator { get; set; }
        public Operator LockedByOperator { get; set; }
        public Operator CompletenessOperator { get; set; }
        public Operator SegmentIdsOperator { get; set; }

        public int? EntityId { get; set; }

        public List<int?> EntityIdsList { get; set; }

        public string EntityTypeId { get; set; }

        public string FieldSetId { get; set; }

        public DateTime? LastModified { get; set; }

        public string IntervalValueLastModified { get; set; }

        public string IntervalValueCreated { get; set; }

        public DateTime? Created { get; set; }

        public string CreatedBy { get; set; }

        public string ModifiedBy { get; set; }

        public string LockedBy { get; set; }

        public int? Completeness { get; set; }

        public List<int> SegmentIds { get; set; }

        public SystemQuery()
        {
            this.EntityId = new int?();
            this.EntityIdsList = new List<int?>();
            this.EntityTypeId = (string)null;
            this.FieldSetId = (string)null;
            this.LastModified = new DateTime?();
            this.Created = new DateTime?();
            this.CreatedBy = (string)null;
            this.ModifiedBy = (string)null;
            this.LockedBy = (string)null;
            this.SegmentIds = new List<int>();
            this.IntervalValueCreated = null;
            this.IntervalValueLastModified = null;
        }
    }
}
