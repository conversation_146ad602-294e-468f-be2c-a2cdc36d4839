namespace inRiver.Server.ExcelExport
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Text.RegularExpressions;
    using DocumentFormat.OpenXml;
    using DocumentFormat.OpenXml.Packaging;
    using DocumentFormat.OpenXml.Spreadsheet;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;
    using inRiver.Server.Util.IllegalCharacters;
    using inRiver.Server.Util.Workbook;

    using RemotingDataType = inRiver.Remoting.Objects.DataType;

    public class ExcelWriter : IDisposable
    {
        private readonly RequestContext context;
        private readonly WorkbookPart workBookPart;
        private readonly WorksheetPart dataWorksheetPart;
        private readonly DataValidations cvlKeysDataValidations;
        private readonly List<Sheet> sheets = new List<Sheet>();

        private SpreadsheetDocument workBook;
        private bool disposedValue;
        private OpenXmlWriter xmlWriter;
        private int writtenRows;
        private string[] columnTypes;

        public ExcelWriter(RequestContext context, DataTable dataTable, EntityType entityType)
        {
            this.context = context;

            this.Stream = new MemoryStream();
            this.workBook = SpreadsheetDocument.Create(this.Stream, SpreadsheetDocumentType.Workbook);

            this.workBookPart = this.workBook.AddWorkbookPart();
            this.dataWorksheetPart = this.workBookPart.AddNewPart<WorksheetPart>();

            this.cvlKeysDataValidations = new DataValidations
            {
                Count = 0
            };

            this.xmlWriter = OpenXmlWriter.Create(this.dataWorksheetPart);
            this.xmlWriter.WriteStartElement(new Worksheet());
            this.xmlWriter.WriteStartElement(new SheetData());

            this.ImportTableHeader(dataTable);
            this.PopulateColumnTypes(entityType, dataTable);
            WorkbookStyler.AddStyleSheet(this.workBook);
        }

        public MemoryStream Stream { get; }

        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        public void WriteToExcelFile(DataTable dataTable)
        {
            var hasErrors = dataTable.HasErrors;
            foreach (DataRow item in dataTable.Rows)
            {
                this.WriteSingleErrorRow(item, hasErrors);
            }
        }

        public void PopulateCvlKeySheetsAndDataValidation(DataTable dataTable, Dictionary<string, List<Core.Models.inRiver.CVLKey>> cvlKeys)
        {
            if (cvlKeys == null || !cvlKeys.Any())
            {
                return;
            }

            foreach (var keyValue in cvlKeys)
            {
                this.AddCvlKeysReferenceSheet(keyValue);

                var column = dataTable.Columns[keyValue.Key];
                if (column != null)
                {
                    var dataValidation = this.CreateDataValidation(dataTable, keyValue, column);
                    this.AppendDataValidation(dataValidation);
                }
            }
        }

        public void EndData(EntityType entityType)
        {
            // this is for SheetData
            this.xmlWriter.WriteEndElement();

            if (this.cvlKeysDataValidations.Count > 0)
            {
                this.xmlWriter.WriteElement(this.cvlKeysDataValidations);
            }

            // this is for Worksheet
            this.xmlWriter.WriteEndElement();
            this.xmlWriter.Close();

            this.xmlWriter = OpenXmlWriter.Create(this.workBook.WorkbookPart);
            this.xmlWriter.WriteStartElement(new Workbook());
            this.xmlWriter.WriteStartElement(new Sheets());

            this.xmlWriter.WriteElement(new Sheet()
            {
                Name = entityType is null ? "Import history" : this.GetWorksheetName(entityType.Id, entityType.Name),
                SheetId = 1,
                Id = this.workBook.WorkbookPart.GetIdOfPart(this.dataWorksheetPart)
            });

            this.sheets.ForEach(sheet => this.xmlWriter.WriteElement(sheet));

            // this is for Sheets
            this.xmlWriter.WriteEndElement();

            // this is for Workbook
            this.xmlWriter.WriteEndElement();
            this.xmlWriter.Close();

            this.workBook.Close();
        }

        // Protected implementation of Dispose pattern.
        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposedValue)
            {
                if (disposing)
                {
                    this.workBook?.Dispose();
                    this.workBook = null;
                }

                this.disposedValue = true;
            }
        }

        private static string GetColumnName(int columnIndex)
        {
            var dividend = columnIndex;
            var columnName = string.Empty;

            while (dividend > 0)
            {
                var modifier = (dividend - 1) % 26;
                columnName = Convert.ToChar(65 + modifier) + columnName;
                dividend = (dividend - modifier) / 26;
            }

            return columnName;
        }

        private string GetWorksheetName(string id, LocaleString name, CultureInfo cultureInfo = null)
        {
            cultureInfo ??= this.context.ModelLanguage;
            var worksheetName = name?[cultureInfo];

            if (string.IsNullOrEmpty(worksheetName))
            {
                worksheetName = string.IsNullOrEmpty(id) ? string.Empty : $"[{id}]";
            }

            // Worksheet names can't have  the following character: ],\,*,[,?,/,:
            worksheetName = Regex.Replace(worksheetName, @"[]|\\|*|[|?|/|:]", string.Empty);
            return worksheetName;
        }

        private void ImportTableHeader(DataTable dataTable)
        {
            var headerRowNumber = 1;

            var attributes = new List<OpenXmlAttribute>
            {
                new OpenXmlAttribute("r", null, headerRowNumber.ToString(CultureInfo.InvariantCulture))
            };

            this.xmlWriter.WriteStartElement(new Row(), attributes);

            var columnNum = 1;
            foreach (DataColumn column in dataTable.Columns)
            {
                this.WriteStringCell(column.ColumnName, columnNum, headerRowNumber, false);

                columnNum++;
            }

            // write the end row element
            this.xmlWriter.WriteEndElement();

            this.writtenRows++;
        }

        private void PopulateColumnTypes(EntityType entityType, DataTable dataTable)
        {
            this.columnTypes = new string[dataTable.Columns.Count];

            var fieldTypesDictionary = new Dictionary<string, FieldType>();

            if (entityType != null)
            {
                fieldTypesDictionary = entityType.FieldTypes.ToDictionary(ft => ft.Id);
            }

            for (var i = 0; i < dataTable.Columns.Count; i++)
            {
                _ = fieldTypesDictionary.TryGetValue(dataTable.Columns[i].ColumnName, out var fieldType);
                columnTypes[i] = fieldType?.DataType ?? RemotingDataType.String;
            }
        }

        private void WriteSingleErrorRow(DataRow item, bool hasErrors)
        {
            var rowNumber = this.writtenRows + 1;

            var attributes = new List<OpenXmlAttribute>
            {
                new OpenXmlAttribute("r", null, rowNumber.ToString(CultureInfo.InvariantCulture))
            };

            this.xmlWriter.WriteStartElement(new Row(), attributes);

            var columnNum = 1;
            for (var i = 0; i < item.ItemArray.Length; i++)
            {
                var fieldValue = IllegalCharacters.GetSanatizedFieldValue(item[i].ToString());
                var isDataError = hasErrors && fieldValue.ToLower().Contains("data error");

                if (this.columnTypes[i] == RemotingDataType.Integer)
                {
                    this.WriteInt32Cell(fieldValue, columnNum, rowNumber, isDataError);
                }
                else if (this.columnTypes[i] == RemotingDataType.Double)
                {
                    this.WriteDoubleCell(fieldValue, columnNum, rowNumber, isDataError);
                }
                else
                {
                    this.WriteStringCell(fieldValue, columnNum, rowNumber, isDataError);
                }

                columnNum++;
            }

            // write the end row element
            this.xmlWriter.WriteEndElement();

            this.writtenRows++;
        }

        private void WriteCell(CellValue cellValue, string cellType , int column, int row, bool dataError)
        {
            var attributes = new List<OpenXmlAttribute>
                {
                    new OpenXmlAttribute("t", null, cellType),
                    new OpenXmlAttribute("r", string.Empty, $"{GetColumnName(column)}{row}")
                };

            if (dataError)
            {
                attributes.Add(new OpenXmlAttribute("s", null, "1"));
            }

            this.xmlWriter.WriteStartElement(new Cell(), attributes);
            this.xmlWriter.WriteElement(cellValue);

            // write the end cell element
            this.xmlWriter.WriteEndElement();
        }

        private void WriteInt32Cell(string fieldValue, int column, int row, bool dataError)
        {
            if (string.IsNullOrEmpty(fieldValue))
            {
                return;
            }

            if (fieldValue.StartsWith('0') && fieldValue.Length > 1)
            {
                this.WriteCell(new CellValue(fieldValue), CellType.String, column, row, dataError);
                return;
            }

            var cellValue = int.TryParse(fieldValue, out var intValue) ?
                new CellValue(intValue) : new CellValue(fieldValue);

            this.WriteCell(cellValue, CellType.Number, column, row, dataError);
        }

        private void WriteDoubleCell(string fieldValue, int column, int row, bool dataError)
        {
            if (string.IsNullOrEmpty(fieldValue))
            {
                return;
            }

            var cellValue = double.TryParse(fieldValue, out var doubleValue) ?
                                    new CellValue(doubleValue) : new CellValue(fieldValue);

            this.WriteCell(cellValue, CellType.Number, column, row, dataError);
        }

        private void WriteStringCell(string fieldValue, int column, int row, bool dataError)
        {
            if (string.IsNullOrEmpty(fieldValue))
            {
                return;
            }

            var cellValue = new CellValue(fieldValue);

            this.WriteCell(cellValue, CellType.String, column, row, dataError);
        }

        private DataValidation CreateDataValidation(DataTable dataTable, KeyValuePair<string, List<Core.Models.inRiver.CVLKey>> keyValue, DataColumn column)
        {
            var columnName = GetColumnName(dataTable.Columns.IndexOf(column) + 1);
            var finalRowNumber = this.writtenRows + 1;

            var dataValidation = new DataValidation
            {
                Type = DataValidationValues.List,
                AllowBlank = true,
                SequenceOfReferences = new ListValue<StringValue>() { InnerText = $"{columnName}2:{columnName}{finalRowNumber}" },
                Formula1 = new Formula1($"'{keyValue.Key}'!$A$1:$A${keyValue.Value.Count + 1}")
            };
            return dataValidation;
        }

        private void AppendDataValidation(DataValidation dataValidation)
        {
            this.cvlKeysDataValidations.Count++;
            this.cvlKeysDataValidations.Append(dataValidation);
        }

        private void AddCvlKeysReferenceSheet(KeyValuePair<string, List<Core.Models.inRiver.CVLKey>> cvlKeys)
        {
            var worksheetPart = this.workBookPart.AddNewPart<WorksheetPart>();
            var sheetData = new SheetData();
            var worksheet = new Worksheet(sheetData);
            worksheetPart.Worksheet = worksheet;

            var sheet = new Sheet()
            {
                Id = this.workBookPart.GetIdOfPart(worksheetPart),
                SheetId = Convert.ToUInt32(this.sheets.Count + 2),
                Name = cvlKeys.Key
            };

            foreach (var cvlKey in cvlKeys.Value)
            {
                var row = new Row();
                var cell = new Cell()
                {
                    CellValue = string.IsNullOrEmpty(cvlKey.Key) ? null : new CellValue(cvlKey.Key),
                    DataType = CellValues.String,
                };

                row.Append(cell);
                sheetData.Append(row);
            }

            this.sheets.Add(sheet);
            worksheet.Save();
        }

        private static class CellType
        {
            public const string Boolean = "b";

            public const string Number = "n";

            public const string Error = "e";

            public const string SharedString = "s";

            public const string String = "str";

            public const string InlineString = "inlineStr";

            public const string Date = "d";
        }
    }
}
