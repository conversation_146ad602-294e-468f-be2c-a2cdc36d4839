namespace inriver.Utility.Query.Tests
{
    using System;

    public abstract class RelativeTimeCriteriaTestBase
    {
        protected class TestExpectation
        {
            public TestExpectation(string queryValue, TimeSpan duration, TimeSpan utcOffset)
            {
                this.QueryValue = queryValue;
                this.Duration = duration;
                this.UtcOffset = utcOffset;
                this.useTimeSpan = true;
            }

            public TestExpectation(string queryValue, int yearDuration, int monthDuration, TimeSpan utcOffset)
            {
                this.QueryValue = queryValue;
                this.YearDuration = yearDuration;
                this.MonthDuration = monthDuration;
                this.UtcOffset = utcOffset;
                this.useTimeSpan = false;
            }

            public TestExpectation(string queryValue, TimeSpan duration)
                : this(queryValue, duration, TimeSpan.Zero)
            {
            }

            public TestExpectation(string queryValue, int yearDuration, int monthDuration)
                : this(queryValue, yearDuration, monthDuration, TimeSpan.Zero)
            {
            }

            public string QueryValue { get; }

            public TimeSpan Duration { get; }

            public int YearDuration { get; }

            public int MonthDuration { get; }

            public TimeSpan UtcOffset { get; }

            private readonly bool useTimeSpan;

            public DateTime ExpectedUtcDateTime() => this.useTimeSpan ?
                DateTime.UtcNow.Add(this.Duration).Add(this.UtcOffset) :
                DateTime.UtcNow.AddYears(this.YearDuration).AddMonths(this.MonthDuration).Add(this.UtcOffset);

            public DateTime ExpectedLocalDateTime() => this.useTimeSpan ?
                DateTime.Now.Add(this.Duration).Add(this.UtcOffset) :
                DateTime.Now.AddYears(this.YearDuration).AddMonths(this.MonthDuration).Add(this.UtcOffset);
        }
    }
}
