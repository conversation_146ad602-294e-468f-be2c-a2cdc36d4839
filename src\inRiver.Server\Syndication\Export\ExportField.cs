namespace inRiver.Server.Syndication.Export
{
    using System;
    using inRiver.Server.Syndication.Mapping;
    using Newtonsoft.Json;

    public class ExportField : IMapField
    {
        public string Id { get; set; }

        public object Data { get; set; }

        public string Path { get; set; }

        public string UnitType { get; set; }

        public object UnitValue { get; set; }

        public string FieldTypeId { get; set; }

        [JsonIgnore]
        public int SortOrder { get; set; }

        [JsonIgnore]
        public string Source { get; set; }

        [JsonIgnore]
        public Guid MapFieldUniqueId { get; set; }

        [JsonIgnore]
        public int MappingFormatFieldId { get; set; }

        public MapFieldType MapFieldType { get; set; }

        // used by Newtonsoft (https://www.newtonsoft.com/json/help/html/conditionalproperties.htm)
        public bool ShouldSerializeMapFieldType() => ExportSerializationSettings.SerializeMapFieldType;
    }
}
