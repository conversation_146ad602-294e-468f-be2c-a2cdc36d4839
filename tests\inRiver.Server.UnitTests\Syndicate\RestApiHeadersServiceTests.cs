namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Script.Api;
    using Xunit;

    public class RestApiHeadersServiceTests
    {
        [Fact]
        public void SetHeaders_HeaderValuesAreSpecified_ShouldSetHeaders()
        {
            // Arrange
            var requestMessage = A.Fake<HttpRequestMessage>();
            var apiHeadersService = new RestApiHeadersService();
            var headers = new Dictionary<string, string>
            {
                { "Header1", "HeaderValue1" },
                { "Header2", "HeaderValue2" },
            };

            // Act
            apiHeadersService.SetHeaders(requestMessage, headers);

            // Assert
            var header1Value = requestMessage.Headers.GetValues("Header1");
            var header2Value = requestMessage.Headers.GetValues("Header2");
            header1Value.Should().BeEquivalentTo("HeaderValue1");
            header2Value.Should().BeEquivalentTo("HeaderValue2");
        }

        [Fact]
        public void SetHeaders_HeaderValuesAreNotSpecified_ShouldSetDefaultHeaders()
        {
            // Arrange
            var requestMessage = A.Fake<HttpRequestMessage>();
            var apiHeadersService = new RestApiHeadersService();

            // Act
            apiHeadersService.SetHeaders(requestMessage, null);

            // Assert
            var header1Value = requestMessage.Headers.GetValues("Accept");
            header1Value.Should().BeEquivalentTo("application/json");
        }

        [Fact]
        public void SetHeaders_RequestMessageIsNull_ShouldThrowException()
        {
            // Arrange
            var apiHeadersService = new RestApiHeadersService();
            var headers = new Dictionary<string, string>
            {
                { "Header1", "HeaderValue1" },
                { "Header2", "HeaderValue2" },
            };

            // Act
            Action act = () => apiHeadersService.SetHeaders(null, headers);

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage("Unable to set request message headers. Request message is null.");
        }
    }
}
