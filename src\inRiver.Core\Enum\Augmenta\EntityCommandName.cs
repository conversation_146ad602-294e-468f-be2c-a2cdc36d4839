namespace inRiver.Core.Enum.Augmenta
{
    /// <summary>
    ///     The name of the command.
    /// </summary>
    public enum EntityCommandName
    {
        /// <summary>
        ///     Create an entity.
        /// </summary>
        CreateEntity = 0,

        /// <summary>
        ///     Update an entity.
        /// </summary>
        UpdateEntity = 1,

        /// <summary>
        ///     Delete an entity.
        /// </summary>
        DeleteEntity = 2,

        /// <summary>
        ///     Lock an entity.
        /// </summary>
        LockEntity = 3,

        /// <summary>
        ///     Unlock an entity.
        /// </summary>
        UnlockEntity = 4,

        /// <summary>
        ///     Create a new version for an entity.
        /// </summary>
        CreateNewVersionForEntity = 5,
    }
}
