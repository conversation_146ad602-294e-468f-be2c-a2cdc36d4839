namespace inRiver.iPMC.Persistance
{
    using System;

    /// <summary>Defines the data load level for entities </summary>
    [Flags]
    public enum LoadLevel
    {
        /// <summary>Load the entity with system data only</summary>
        Shallow = 1,

        /// <summary>Load the entity with system data and fields</summary>
        DataOnly = 2,

        /// <summary>Load the entity with system data, fields and links</summary>
        DataAndLinks = 4
    }
}
