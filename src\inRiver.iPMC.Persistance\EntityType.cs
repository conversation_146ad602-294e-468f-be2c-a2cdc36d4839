namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;

    public class EntityType
    {
        public EntityType()
        {
            this.LinkTypes = new List<LinkType>();
        }

        public EntityType(string id)
        {
            this.Id = id;
            this.LinkTypes = new List<LinkType>();
            this.FieldTypes = new List<FieldType>();
        }

        public string Id { get; set; }

        public LocaleString Name { get; set; }

        public bool IsLinkEntityType { get; set; }

        public List<FieldType> FieldTypes { get; set; }

        public List<LinkType> LinkTypes { get; set; }

        public List<FieldSet> FieldSets { get; set; }

        public override string ToString() => string.IsNullOrWhiteSpace(this.Id) ? base.ToString() : this.Id;
    }
}
