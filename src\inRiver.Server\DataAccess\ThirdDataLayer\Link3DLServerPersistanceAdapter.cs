namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using System;
    using System.Collections.Generic;

    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {

        public override DtoLink AddLink(Link link)
        {
            var r = this._origInRiverPersistance.AddLink(link);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override (DtoLink, bool) AddLinkIfNotExists(Link link)
        {
            var r = this._origInRiverPersistance.AddLinkIfNotExists(link);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override DtoLink AddLinkAt(Link link, int index)
        {
            var r = this._origInRiverPersistance.AddLinkAt(link, index);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override List<DtoLink> AddLinksToNewTask(List<Link> links)
        {
            var r = this._origInRiverPersistance.AddLinksToNewTask(links);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override bool DeleteLink(int linkId)
        {
            var r =  this._origInRiverPersistance.DeleteLink(linkId);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override bool DeleteLinksAndUpdateLinksSortOrder(int entityId, string[] linkTypeIdsToIgnore)
        {
            var r = this._origInRiverPersistance.DeleteLinksAndUpdateLinksSortOrder(entityId, linkTypeIdsToIgnore);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override DtoLink UpdateLinkSortOrder(int linkId, int index)
        {
            var r = this._origInRiverPersistance.UpdateLinkSortOrder(linkId, index);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override void UpdateLinkSortOrderOnSourceEntity(int sourceEntityId, int index, string linkTypeId)
        {
            this._origInRiverPersistance.UpdateLinkSortOrderOnSourceEntity(sourceEntityId, index, linkTypeId);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }

        public override bool Inactivate(int id)
        {
            var r = this._origInRiverPersistance.Inactivate(id);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override bool Activate(int id)
        {
            var r =  this._origInRiverPersistance.Activate(id);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }
    }
}
