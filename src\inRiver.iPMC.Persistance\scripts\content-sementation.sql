/*Creation of ContentSegmentation and relationships*/
CREATE TABLE [dbo].[ContentSegmentation](
  ID    INT NOT NULL PRIMARY KEY IDENTITY(0,1),
  SegmentName    VARCHAR(100),
  SegmentDescription VARCHAR(255)
);
GO

INSERT INTO [dbo].[ContentSegmentation]
           ([SegmentName]
           ,[SegmentDescription])
      VALUES
            ('Default'
            ,'Default Content Segmentation');
GO

ALTER TABLE [dbo].[UserRoles]
ADD ContentSegmentationId int not null DEFAULT 0 FOREIGN KEY REFERENCES ContentSegmentation(ID),
CONSTRAINT ContentSegmentation_UserId_Roleid UNIQUE NONCLUSTERED (UserId,RoleId,ContentSegmentationId);
GO

ALTER TABLE [dbo].[Entity]
ADD ContentSegmentationId int not null DEFAULT 0 FOREIGN KEY REFERENCES ContentSegmentation(ID);
GO

ALTER VIEW [dbo].[ViewUserRoles]
AS
SELECT     dbo.UserRoles.UserId, dbo.[User].Username, dbo.[Role].Id, dbo.[Role].Name, dbo.[Role].[Description], dbo.UserRoles.ContentSegmentationId
FROM         dbo.Role INNER JOIN
                      dbo.UserRoles ON dbo.[Role].Id = dbo.UserRoles.RoleId INNER JOIN
                      dbo.[User] ON dbo.UserRoles.UserId = dbo.[User].Id

GO

ALTER VIEW [dbo].[ViewUserPermissions]
AS
SELECT     dbo.[User].Id, dbo.[User].Username, dbo.[User].FirstName, dbo.[User].LastName, dbo.[User].Email, dbo.[Role].Id AS RoleId, dbo.[Role].Name AS RoleName, 
                      dbo.Permission.Name AS PermissionName, dbo.Permission.Id AS PermissionId, dbo.[Role].[Description] AS RoleDescription, 
                      dbo.Permission.[Description] AS PermissionDescription, dbo.UserRoles.ContentSegmentationId
FROM         dbo.Permission INNER JOIN
                      dbo.RolePermissions ON dbo.Permission.Id = dbo.RolePermissions.PermissionId RIGHT OUTER JOIN
                      dbo.[Role] ON dbo.RolePermissions.RoleId = dbo.Role.Id INNER JOIN
                      dbo.UserRoles ON dbo.[Role].Id = dbo.UserRoles.RoleId INNER JOIN
                      dbo.[User] ON dbo.UserRoles.UserId = dbo.[User].Id

GO

/*Story 1025:- Add ChangeEntitySegment permission*/ 

INSERT INTO [dbo].[Permission]
           ([Name]
           ,[Description])
     VALUES
           ('ChangeEntitySegment'
           ,'User can change the segment of an entity');
GO