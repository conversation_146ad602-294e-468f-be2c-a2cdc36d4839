namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;
    using System.Data.SqlClient;
    using inRiver.Log;

    public class PersistanceFieldSet : BasePersistance, IPersistanceFieldSet
    {
        public PersistanceFieldSet(
            string connectionString, 
            ICommonLogging logInstance,
            IContentSegmentPermissionProvider contentSegmentProvider)
            
            : base(connectionString, logInstance, contentSegmentProvider)
        {

        }

        public FieldSet GetFieldSet(string id, bool includeFieldTypes = false)
        {
            FieldSet fieldSet = null;

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, Description, EntityTypeId FROM FieldSet WHERE Id = @Id";
                command.Parameters.AddWithValue("@Id", id);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        fieldSet = new FieldSet { Id = reader.GetString(0) };

                        var xmlName = reader.GetSqlXml(1);

                        fieldSet.Name = new LocaleString(xmlName);

                        if (!reader.IsDBNull(2))
                        {
                            var xmlDescription = reader.GetSqlXml(2);
                            fieldSet.Description = new LocaleString(xmlDescription);
                        }

                        fieldSet.EntityTypeId = reader.GetString(3);
                    }
                }
            }

            if (fieldSet == null)
            {
                return null;
            }

            if (!includeFieldTypes)
            {
                return fieldSet;
            }

            fieldSet.FieldTypes = GetFieldTypesForFieldSet(fieldSet.Id);

            return fieldSet;
        }

        public List<FieldSet> GetAllFieldSets(bool includeFieldTypes = false)
        {
            var fieldSets = new List<FieldSet>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, Description, EntityTypeId FROM FieldSet";

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var fieldSet = new FieldSet { Id = reader.GetString(0) };

                        var xmlName = reader.GetSqlXml(1);

                        fieldSet.Name = new LocaleString(xmlName);

                        if (!reader.IsDBNull(2))
                        {
                            var xmlDescription = reader.GetSqlXml(2);
                            fieldSet.Description = new LocaleString(xmlDescription);
                        }

                        fieldSet.EntityTypeId = reader.GetString(3);

                        fieldSets.Add(fieldSet);
                    }
                }
            }

            if (includeFieldTypes)
            {
                var fieldsetFieldTypes = GetAllFieldSetFieldTypes();

                foreach (var fieldset in fieldSets)
                {
                    if (fieldsetFieldTypes.ContainsKey(fieldset.Id))
                    {
                        fieldset.FieldTypes = fieldsetFieldTypes[fieldset.Id];
                    }
                    else
                    {
                        fieldset.FieldTypes = new List<string>();
                    }
                }
            }

            return fieldSets;
        }

        public List<FieldSet> GetFieldSetsForEntityType(string entityTypeId, bool includeFieldTypes = false)
        {
            var fieldSets = new List<FieldSet>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, Description, EntityTypeId FROM FieldSet WHERE EntityTypeId = @EntityTypeId";
                command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var fieldSet = new FieldSet { Id = reader.GetString(0) };

                        var xmlName = reader.GetSqlXml(1);

                        fieldSet.Name = new LocaleString(xmlName);

                        if (!reader.IsDBNull(2))
                        {
                            var xmlDescription = reader.GetSqlXml(2);
                            fieldSet.Description = new LocaleString(xmlDescription);
                        }

                        fieldSet.EntityTypeId = reader.GetString(3);

                        fieldSets.Add(fieldSet);
                    }
                }
            }

            if (includeFieldTypes)
            {
                var fieldsetFieldTypes = GetAllFieldSetFieldTypes();

                foreach (var fieldset in fieldSets)
                {
                    if (fieldsetFieldTypes.ContainsKey(fieldset.Id))
                    {
                        fieldset.FieldTypes = fieldsetFieldTypes[fieldset.Id];
                    }
                    else
                    {
                        fieldset.FieldTypes = new List<string>();
                    }
                }
            }

            return fieldSets;
        }

        public List<string> GetFieldTypesForFieldSet(string fieldSetId)
        {
            var fieldTypes = new List<string>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT FieldTypeId FROM FieldSet_FieldType WHERE FieldSetId = @FieldSetId";
                command.Parameters.AddWithValue("@FieldSetId", fieldSetId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        fieldTypes.Add(reader.GetString(0));
                    }
                }
            }

            return fieldTypes;
        }

        private Dictionary<string, List<string>> GetAllFieldSetFieldTypes()
        {
            var fieldSetFieldTypes = new Dictionary<string, List<string>>();

            using (var connection = new SqlConnection(ConnectionString))
            {

                var command = connection.CreateCommand();
                command.CommandText = "SELECT FieldSetId, FieldTypeId FROM FieldSet_FieldType";

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var fieldSetId = reader.GetString(0);
                        var fieldTypeId = reader.GetString(1);

                        if (!fieldSetFieldTypes.ContainsKey(fieldSetId))
                        {
                            fieldSetFieldTypes.Add(fieldSetId, new List<string>());
                        }

                        if (!fieldSetFieldTypes[fieldSetId].Contains(fieldTypeId))
                        {
                            fieldSetFieldTypes[fieldSetId].Add(fieldTypeId);
                        }
                    }

                    reader.Close();
                }

                connection.Close();
            }

            return fieldSetFieldTypes;
        }
    }
}
