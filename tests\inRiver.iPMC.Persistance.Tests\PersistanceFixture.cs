namespace inRiver.iPMC.Persistance.Tests
{
    using System;
    using inRiver.Log;

    public class PersistanceFixture
    {
        public string LogConnectionString;
        public string DataConnectionString;

        public ICommonLogging LogInstance;
        public IPersistanceEntity MockPersistentEntity;
        public IPersistanceEntityType MockPersistentEntityType;
        public IPersistanceFieldType MockPersistentFieldType;
        public IPersistanceFieldSet MockPersistentFieldSet;
        public IPersistanceContentSegmentation MockPersistanceContentSegmentation;

        public PersistanceFixture()
        {
            LogConnectionString = Config.Database.LogConnectionString;
            DataConnectionString = Config.Database.DataConnectionString;

            LogInstance = new CommonLogging("dev-usa.ErrorLog", LogConnectionString);

            IContentSegmentPermissionProvider contentSegmentProvider = null;

            MockPersistentFieldType = new PersistanceFieldType(DataConnectionString, LogInstance, contentSegmentProvider);
            
            MockPersistentFieldSet = new PersistanceFieldSet(DataConnectionString, LogInstance, contentSegmentProvider);

            MockPersistentEntityType = new PersistanceEntityType(DataConnectionString, LogInstance, MockPersistentFieldType, MockPersistentFieldSet, contentSegmentProvider);

            MockPersistanceContentSegmentation = new PersistanceContentSegmentation(DataConnectionString, LogInstance, contentSegmentProvider);

            MockPersistentEntity = new PersistanceEntity(DataConnectionString, LogInstance, MockPersistentEntityType, MockPersistentFieldType, MockPersistanceContentSegmentation, contentSegmentProvider);
        }
    }
}
