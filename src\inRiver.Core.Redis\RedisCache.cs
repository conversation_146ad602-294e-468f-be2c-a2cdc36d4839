namespace inRiver.Core.Redis
{
    using System;
    using StackExchange.Redis;

    public class RedisCache
    {
        private readonly TimeSpan twentyFourHourCacheExpiration = new TimeSpan(0, 24, 0, 0);
        private readonly ConnectionMultiplexer connection;

        public RedisCache(ConnectionMultiplexer connection)
        {
            this.connection = connection;
        }

        public void Add(RedisKey key, RedisValue value, TimeSpan? expiry = null)
        {
            var cacheExpiry = expiry ?? this.twentyFourHourCacheExpiration;
            var cache = this.connection.GetDatabase();
            _ = cache.StringSet(key, value, cacheExpiry);
        }

        public RedisValue Get(RedisKey key)
        {
            var cache = this.connection.GetDatabase();
            return cache.StringGet(key);
        }


        public void Remove(RedisKey key)
        {
            var cache = this.connection.GetDatabase();
            _ = cache.KeyDelete(key);
        }
    }
}
