namespace inRiver.Core.Persistance
{
    using inRiver.Core.Models;
    using inRiver.Log;

    public class BaseCorePersistance
        : BasePersistance
    {
        public readonly string ConnectionString;

        public BaseCorePersistance(string dbConnectionString, ICommonLogging logInstance, ApiCaller apiCaller)
            : base(logInstance, apiCaller)
        {
            this.ConnectionString = dbConnectionString;
        }
    }
}
