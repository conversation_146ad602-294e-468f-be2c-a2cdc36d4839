namespace LongRunningJobService
{
    using System;
    using System.Collections.Generic;
    using System.Fabric;
    using Code;
    using global::LongRunningJobService.Abstractions;
    using global::LongRunningJobService.Config;
    using global::LongRunningJobService.Constants;
    using global::LongRunningJobService.Middleware;
    using inRiver.Api.Data.Client;
    using inRiver.Configuration.Core.Service;
    using inRiver.Core.Http;
    using inRiver.Core.Persistance.ThirdDataLayer;
    using inRiver.Core.Services;
    using inRiver.Server.EventPublishing;
    using inRiver.Server.Service;
    using Inriver.StackEssentials;
    using Inriver.StackEssentials.DependencyInjection;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Repositories;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.AspNetCore.Extensions;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;
    using Microsoft.Extensions.Hosting;
    using Microsoft.ServiceFabric.AspNetCore.Configuration;
    using Middlewares;
    using Serilog;
    using Serilog.Core;
    using Serilog.Events;
    using Telemetry.Initializers;
    using inRiver.Api.Data.Client;
    using inRiver.Core.Persistance.ThirdDataLayer;
    using inRiver.Server.EventPublishing;
    using inriver.Expressions.Client;
    using Microsoft.Extensions.Caching.Memory;
    using System.Net.Http;
    using inRiver.Core.Models;

    public class Startup
    {
        private readonly IWebHostEnvironment environment;

        public Startup(IWebHostEnvironment environment)
        {
            this.environment = environment;
            StackEssentialsInitializer.Init(Util.KeyVaultBaseUrl, Util.StackConfigSecretName);
            DataApiInitializer.Init(Util.DataApiUrl, Util.DataJobServiceUrl, Util.KeyVaultBaseUrl, Util.StackConfigSecretName, LogConstants.CloudRoleName);
            ExpressionClient.Init(Util.ExpressionWorkerServiceUrl, LogConstants.CloudRoleName);
            MessagingServiceClient.Init(Util.MessagingServiceUrl);
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        // For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=398940
        public void ConfigureServices(IServiceCollection services)
        {
            var configurationBuilder = new ConfigurationBuilder()
                .AddServiceFabricConfiguration(FabricRuntime.GetActivationContext(), (options) => options.IncludePackageName = false);
            var configuration = configurationBuilder.Build();
            var defaultConfigSection = configuration.GetSection("Configuration");

            services.AddRouting();
            services.AddControllers();
            services.AddHttpContextAccessor();
            services.AddMemoryCache();

            var options = new ApplicationInsightsServiceOptions
            {
                InstrumentationKey = Util.GetInstrumentationKey(),
                DeveloperMode = this.environment.IsDevelopment(),
                EnableDependencyTrackingTelemetryModule = false
            };
            services.AddApplicationInsightsTelemetry(options);
            services.AddSingleton<ITelemetryInitializer>(_ =>
                new CloudRoleNameTelemetryInitializer(LogConstants.CloudRoleName));

            services.AddStackConfig(defaultConfigSection);

            services.AddOptions();
            services.Configure<ServiceFabricSettings>(myOptions => {
                myOptions.LongRunningJobWorkerServicePlacementConstraints =
                    defaultConfigSection.GetValue("LongRunningJobWorkerService_ServicePlacementConstraints",
                        string.Empty);
            });

            services.AddSingleton<ICustomerEnvironmentRepository, CustomerEnvironmentRepository>();
            services.Decorate<ICustomerEnvironmentRepository, CustomerEnvironmentRepositoryCachingDecorator>();
            services.AddSingleton<IEnvironmentContextAccessor, HttpContextEnvironmentContextAccessor>();
            services.AddSingleton<IJobModelRepository, JobModelBlobRepository>();
            services.AddSingleton<ILongRunningJobInitializer, LongRunningJobInitializer>();
            services.AddSingleton<ILongRunningJobRepository, LongRunningJobRepository>();
            services.AddSingleton<IJobWorkerServiceSpawner, JobWorkerServiceSpawner>();

            var keyVaultBaseUrl = defaultConfigSection["KeyVaultBaseUrl"];

            var namedAuth0Options = new Dictionary<string, Auth0Options>();
            var auth0Options = new Auth0Options
            {
                BaseAddress = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-domain").GetAwaiter().GetResult(),
                ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-id").GetAwaiter().GetResult(),
                ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-secret").GetAwaiter().GetResult(),
                Audience = defaultConfigSection["OAuth:Audience"],
            };
            namedAuth0Options.Add("Auth0Client", auth0Options);

            try
            {
                var outputAdapterAuth0Options = new Auth0Options
                {
                    BaseAddress = defaultConfigSection["Auth0Domain"],
                    ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-id").GetAwaiter().GetResult(),
                    ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-secret").GetAwaiter().GetResult(),
                    Audience = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-audience").GetAwaiter().GetResult(),
                };
                namedAuth0Options.Add("OutputAdapterAuth0Client", outputAdapterAuth0Options);
            }
            catch
            {
            }

            services.AddSingleton<IDictionary<string, Auth0Options>>(namedAuth0Options);

            services.TryAddTransient<BearerTokenHandler>();

            services.AddTransient<IAccessTokenRetriever, AccessTokenRetriever>();

            _ = services.AddHttpClient<IAugmentaHttpClient, AugmentaHttpClient>(c =>
                    c.WithBaseAddress(defaultConfigSection["Augmenta:ApiBaseAddress"])
                        .WithAcceptJsonHeader()
                        .WithTimeout(TimeSpan.FromMinutes(5)))
                .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

            _ = services.AddHttpClient<IOutputAdapterHttpClient, OutputAdapterHttpClient>(c =>
                    c.WithBaseAddress(defaultConfigSection["OutputAdapter:ApiBaseAddress"])
                        .WithAcceptJsonHeader()
                        .WithTimeout(TimeSpan.FromMinutes(5)))
                .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

            _ = services.AddMemoryCache();

            _ = services.AddHttpClient("Auth0Client", (provider, client) => {
                var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["Auth0Client"].BaseAddress;
                client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
            });

            _ = services.AddHttpClient("OutputAdapterAuth0Client", (provider, client) => {
                var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["OutputAdapterAuth0Client"].BaseAddress;
                client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
            });

            StaticServiceProvider.Configure(services.BuildServiceProvider());

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, TelemetryClient telemetryClient)
        {
            ConfigureLogging(telemetryClient);

            _ = app.UseMiddleware<ServiceFabricResourceNotFoundMiddleware>();

            if (this.environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseRouting();

            app.UseMiddleware<EnvironmentContextMiddleware>();

            app.UseEndpoints(endpoints => endpoints.MapControllers());
        }

        private static void ConfigureLogging(TelemetryClient telemetryClient)
        {
            var levelSwitch = new LoggingLevelSwitch();

            if (Enum.TryParse(Util.GetLogLevel(), out LogEventLevel logEventLevel))
            {
                levelSwitch.MinimumLevel = logEventLevel;
            }

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.ControlledBy(levelSwitch)
                .WriteTo.ApplicationInsights(telemetryClient, TelemetryConverter.Traces)
                .CreateLogger();
        }
    }
}
