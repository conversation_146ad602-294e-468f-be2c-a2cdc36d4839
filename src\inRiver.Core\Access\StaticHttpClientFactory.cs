namespace inRiver.Core.Access
{
    using System;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using Microsoft.Extensions.DependencyInjection;

    public static class StaticHttpClientFactory
    {
        private static readonly IHttpClientFactory HttpClientFactory;

        static StaticHttpClientFactory()
        {
            var services = new ServiceCollection();
            _ = services.AddHttpClient();

            HttpClientFactory = services.BuildServiceProvider().GetService<IHttpClientFactory>();
        }

        public static HttpClient CreateHttpClient(string remoteEndpointService)
            => CreateHttpClient(remoteEndpointService, TimeSpan.FromMinutes(5));

        public static HttpClient CreateHttpClient(string remoteEndpointService, TimeSpan timeout)
        {
            var httpClient = HttpClientFactory.CreateClient();

            httpClient.Timeout = timeout;
            httpClient.BaseAddress = new Uri(remoteEndpointService);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            return httpClient;
        }
    }
}
