
namespace LongRunningJobService.Api.Controllers
{
    using System;
    using System.Threading.Tasks;
    using Code;
    using inRiver.Core.Enum;
    using inRiver.Server.Extension;
    using inRiver.Server.Managers;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Enums;
    using Inriver.StackEssentials.Abstractions;
    using LongRunningJob.Core.Constants;
    using LongRunningJobActor.Abstraction;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Configuration;

    [ApiController]
    [Route("api/[controller]")]
    public class LongRunningJobDevController : ControllerBase
    {
        private readonly ISyndicationService syndicationService;
        private readonly IStackConfig stackConfig;
        private readonly IConfiguration config;

        public LongRunningJobDevController(ISyndicationService syndicationService, IStackConfig stackConfig, IConfiguration config)
        {
            this.syndicationService = syndicationService;
            this.stackConfig = stackConfig;
            this.config = config;
        }

        [HttpPost]
        [Route("RunSyndicate/{customerSafeName}/{environmentSafeName}/{username}")]
        public async Task<IActionResult> RunSyndicateAsync(string customerSafeName, string environmentSafeName, string username, [FromBody] SyndicationModel syndicationModel)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);

            if (syndicationModel.IdentifierType != SyndicationIdentifierType.None)
            {
                syndicationModel.Id = syndicationModel.IdentifierType.GetSyndicationIdentifierByIdentifierType(syndicationModel);
            }

            var environmentContextData = CustomerEnvironmentManager.Instance.GetContextForEnvironment(
                customerSafeName,
                environmentSafeName,
                this.stackConfig.ConfigurationDatabaseConnectionString.UnScramble());

            if (syndicationModel.RunPreview)
            {
                var reviewId = Guid.NewGuid();
                await this.syndicationService.RunReviewInternalAsync(environmentContextData, syndicationModel, this.config["StackGroup"], reviewId);

                return this.Ok(reviewId);
            }

            var isLongRunningJobCreated = longRunningJobManager.TryCreateLongRunningJob(
                LongRunningJobsJobType.RunSyndicate,
                syndicationModel.Id.ToString(),
                LongRunningJobScope.Personal,
                username,
                out var longRunningJobId,
                syndicationModel.IdentifierType.GetSyndicationIdentifierTypeString());

            if (!isLongRunningJobCreated)
            {
                return this.BadRequest("Could not create long running job");
            }

            await this.syndicationService.RunSyndicateInternalAsync(environmentContextData, longRunningJobId.Value, syndicationModel, "dev-actor-id", this.config["StackGroup"], "local-dev-user");

            return this.Ok(longRunningJobId);
        }
    }
}
