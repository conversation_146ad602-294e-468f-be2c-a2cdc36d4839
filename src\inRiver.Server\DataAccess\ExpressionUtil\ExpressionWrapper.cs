namespace inRiver.Server.DataAccess.ExpressionUtil
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using inriver.Expressions.Client;
    using inriver.Expressions.Client.Constants;
    using inriver.Expressions.Client.Model;
    using Inriver.Expressions.Dto;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Security;
    using inRiver.Server.Error;
    using inRiver.Server.Request;
    using Microsoft.Extensions.Caching.Memory;
    using Inriver.Expressions.Client.Model;

    public class ExpressionWrapper
    {
        private readonly IDataPersistance dataContext;
        private readonly RequestContext requestContext;

        public List<Field> Revisions { get; private set; }

        private static MemoryCache Cache { get; } = new MemoryCache(new MemoryCacheOptions());

        public ExpressionWrapper(IDataPersistance dataContext, RequestContext requestContext)
        {
            this.dataContext = dataContext;
            this.requestContext = requestContext;
            this.Revisions = new List<Field>();
        }

        public ExpressionWrapper(RequestContext requestContext)
        {
            this.requestContext = requestContext;
        }

        public void EvaluateExpressionsForEntity(DtoEntity dtoEntity, DtoEntity persistedEntity, IEnumerable<FieldType> fieldTypes)
        {
            if (!this.IsExpressionEnabled())
            {
                return;
            }

            var dtoFieldTypes = fieldTypes.ToDictionary(x => x.Id, x => DtoFactory.DtoFromFieldType(x));
            var persistedDtoFieldsDictionary = persistedEntity.Fields.ToDictionary(x => x.FieldTypeId, x => x);

            var expressionChangedDict = new Dictionary<string, bool>();
            var expressionsToRemove = new List<int>();
            var entityExpressions = this.dataContext.GetExpressionsForEntity(dtoEntity.Id);
            var diff = entityExpressions[ExpressionTargetType.FIELDTYPEID].Keys.Except(dtoFieldTypes.Keys).ToList();
            diff.AddRange(entityExpressions[ExpressionTargetType.FIELDTYPEID].Keys.Where(x => dtoFieldTypes.TryGetValue(x, out var ft) && !ft.ExpressionSupport));
            if (diff.Count > 0)
            {
                var idsToRemove = new List<int>();
                foreach (var key in diff)
                {
                    idsToRemove.Add(entityExpressions[ExpressionTargetType.FIELDTYPEID][key].Id);
                    entityExpressions[ExpressionTargetType.FIELDTYPEID].Remove(key);
                }

                this.dataContext.DeleteExpressions(idsToRemove);
            }

            foreach (var incomingField in dtoEntity.Fields)
            {
                if (!dtoFieldTypes[incomingField.FieldTypeId].ExpressionSupport)
                {
                    continue;
                }

                var isExpressionChanged = false;
                if (!entityExpressions[ExpressionTargetType.FIELDTYPEID].ContainsKey(incomingField.FieldTypeId)
                    && Remoting.Util.Utility.StringIsInriverExpression(true, incomingField.Data))
                {
                    entityExpressions[ExpressionTargetType.FIELDTYPEID].Add(incomingField.FieldTypeId, new DtoExpression
                    {
                        EntityId = dtoEntity.Id,
                        Data = incomingField.Data,
                        Status = null,
                        Target = incomingField.FieldTypeId,
                        TargetType = ExpressionTargetType.FIELDTYPEID,
                        LanguageVersion = 1
                    });
                    isExpressionChanged = true;
                }
                else
                {
                    var persistedExpressionExists = entityExpressions[ExpressionTargetType.FIELDTYPEID]
                        .TryGetValue(incomingField.FieldTypeId, out var persistedExpression);
                    if (!persistedExpressionExists)
                    {
                        continue;
                    }

                    var incomingFieldValue = incomingField.Data?.ToString();
                    isExpressionChanged = incomingFieldValue != persistedExpression.Data && Remoting.Util.Utility.StringIsInriverExpression(true, incomingFieldValue);
                    if (isExpressionChanged)
                    {
                        entityExpressions[ExpressionTargetType.FIELDTYPEID][incomingField.FieldTypeId] = new DtoExpression
                        {
                            EntityId = dtoEntity.Id,
                            Data = incomingFieldValue,
                            Status = null,
                            Target = incomingField.FieldTypeId,
                            TargetType = ExpressionTargetType.FIELDTYPEID,
                            LanguageVersion = 1
                        };
                        incomingField.Data = null;
                    }
                    else if (!Remoting.Util.Utility.StringIsInriverExpression(true, incomingFieldValue)
                        && persistedDtoFieldsDictionary[incomingField.FieldTypeId].Data != incomingFieldValue)
                    {
                        expressionsToRemove.Add(persistedExpression.Id);
                        entityExpressions[ExpressionTargetType.FIELDTYPEID].Remove(incomingField.FieldTypeId);
                    }
                }

                expressionChangedDict.Add(incomingField.FieldTypeId, isExpressionChanged);
            }

            var r = ExpressionClient.EvaluateEntity(
                new EntityExpressionEvaluationModel
                {
                    DtoEntity = dtoEntity,
                    DtoFieldTypesDictionary = dtoFieldTypes,
                    DtoExpressionsDictionary = entityExpressions
                },
                this.requestContext.CustomerSafeName,
                this.requestContext.EnvironmentSafeName);

            if (r.Success)
            {
                var dtoEntityResult = r.DtoEntity.ToObject<DtoEntity>();
                dtoEntity.Fields = dtoEntityResult.Fields;

                var fieldTypeExpressionsToUpdate = r.DtoExpressionsDictionary[ExpressionTargetType.FIELDTYPEID]
                    .Where(x => expressionChangedDict[x.Key])
                    .Select(x => x.Value)
                    .ToList();

                var effectiveExpressionsToUpdate = new List<DtoExpression>();
                effectiveExpressionsToUpdate.AddRange(fieldTypeExpressionsToUpdate);
                effectiveExpressionsToUpdate.AddRange(ResolveModifiedStatus(entityExpressions[ExpressionTargetType.FIELDTYPEID], r.DtoExpressionsDictionary[ExpressionTargetType.FIELDTYPEID]));
                effectiveExpressionsToUpdate = effectiveExpressionsToUpdate.Distinct().ToList();

                if (effectiveExpressionsToUpdate.Any())
                {
                    this.dataContext.UpsertExpressions(effectiveExpressionsToUpdate);
                }

                if (fieldTypeExpressionsToUpdate.Any())
                {
                    this.Revisions = fieldTypeExpressionsToUpdate
                        .Where(x => x.TargetType == ExpressionTargetType.FIELDTYPEID && dtoFieldTypes[x.Target].TrackChanges)
                        .Select(x => new Field
                        {
                            Data = x.Data,
                            EntityId = dtoEntity.Id,
                            FieldType = fieldTypes.First(y => y.Id == x.Target),
                            LastModified = DateTime.UtcNow,
                            Revision = dtoEntity.Fields.First(y => y.FieldTypeId == x.Target).Revision + 1
                        })
                        .ToList();
                }

                if (expressionsToRemove.Any())
                {
                    this.dataContext.DeleteExpressions(expressionsToRemove);
                }
            }
            else
            {
                throw ErrorUtility.GetArgumentFault(
                        "EvaluateExpressionsForEntity",
                        nameof(dtoEntity),
                        $"Failed to evaluate expressions for entity {dtoEntity.Id}");
            }
        }

        public void EvaluateExpressionsForEntityAndMetadata(DtoEntity dtoEntity, DtoEntity persistedEntity, IEnumerable<FieldType> fieldTypes)
        {
            if (!this.IsExpressionEnabled())
            {
                return;
            }

            var dtoFieldTypes = fieldTypes.ToDictionary(x => x.Id, x => DtoFactory.DtoFromFieldType(x));
            var persistedDtoFieldsDictionary = persistedEntity.Fields.ToDictionary(x => x.FieldTypeId, x => x);

            var expressionChangedDict = new Dictionary<string, bool>();
            var expressionsToRemove = new List<int>();
            var entityExpressions = this.dataContext.GetExpressionsForEntity(dtoEntity.Id);
            var diff = entityExpressions[ExpressionTargetType.FIELDTYPEID].Keys.Except(dtoFieldTypes.Keys).ToList();
            diff.AddRange(entityExpressions[ExpressionTargetType.FIELDTYPEID].Keys.Where(x => dtoFieldTypes.TryGetValue(x, out var ft) && !ft.ExpressionSupport));
            if (diff.Count > 0)
            {
                var idsToRemove = new List<int>();
                foreach (var key in diff)
                {
                    idsToRemove.Add(entityExpressions[ExpressionTargetType.FIELDTYPEID][key].Id);
                    entityExpressions[ExpressionTargetType.FIELDTYPEID].Remove(key);
                }

                this.dataContext.DeleteExpressions(idsToRemove);
            }

            foreach (var incomingField in dtoEntity.Fields)
            {
                if (!dtoFieldTypes[incomingField.FieldTypeId].ExpressionSupport)
                {
                    continue;
                }

                var isExpressionChanged = false;
                if (!entityExpressions[ExpressionTargetType.FIELDTYPEID].ContainsKey(incomingField.FieldTypeId)
                    && Remoting.Util.Utility.StringIsInriverExpression(true, incomingField.Data))
                {
                    entityExpressions[ExpressionTargetType.FIELDTYPEID].Add(incomingField.FieldTypeId, new DtoExpression
                    {
                        EntityId = dtoEntity.Id,
                        Data = incomingField.Data,
                        Status = null,
                        Target = incomingField.FieldTypeId,
                        TargetType = ExpressionTargetType.FIELDTYPEID,
                        LanguageVersion = 1
                    });
                    isExpressionChanged = true;
                }
                else
                {
                    var persistedExpressionExists = entityExpressions[ExpressionTargetType.FIELDTYPEID]
                        .TryGetValue(incomingField.FieldTypeId, out var persistedExpression);
                    if (!persistedExpressionExists)
                    {
                        continue;
                    }

                    var incomingFieldValue = incomingField.Data?.ToString();
                    isExpressionChanged = incomingFieldValue != persistedExpression.Data && Remoting.Util.Utility.StringIsInriverExpression(true, incomingFieldValue);
                    if (isExpressionChanged)
                    {
                        entityExpressions[ExpressionTargetType.FIELDTYPEID][incomingField.FieldTypeId] = new DtoExpression
                        {
                            EntityId = dtoEntity.Id,
                            Data = incomingFieldValue,
                            Status = null,
                            Target = incomingField.FieldTypeId,
                            TargetType = ExpressionTargetType.FIELDTYPEID,
                            LanguageVersion = 1
                        };
                        incomingField.Data = null;
                    }
                    else if (!Remoting.Util.Utility.StringIsInriverExpression(true, incomingFieldValue)
                        && persistedDtoFieldsDictionary[incomingField.FieldTypeId].Data != incomingFieldValue)
                    {
                        expressionsToRemove.Add(persistedExpression.Id);
                        entityExpressions[ExpressionTargetType.FIELDTYPEID].Remove(incomingField.FieldTypeId);
                    }
                }

                expressionChangedDict.Add(incomingField.FieldTypeId, isExpressionChanged);
            }

            if (dtoEntity.Segment.Id != -1)
            {
                _ = entityExpressions[ExpressionTargetType.ENTITYMETADATA].Remove(ExpressionTarget.SEGMENT);
            }

            var r = ExpressionClient.EvaluateEntity(
                new EntityExpressionEvaluationModel
                {
                    DtoEntity = dtoEntity,
                    DtoFieldTypesDictionary = dtoFieldTypes,
                    DtoExpressionsDictionary = entityExpressions
                },
                this.requestContext.CustomerSafeName,
                this.requestContext.EnvironmentSafeName);

            if (r.Success)
            {
                var dtoEntityResult = r.DtoEntity.ToObject<DtoEntity>();
                dtoEntity.Fields = dtoEntityResult.Fields;
                if (dtoEntity.Segment.Id == -1)
                {
                    if (r.DtoExpressionsDictionary[ExpressionTargetType.ENTITYMETADATA].TryGetValue(ExpressionTarget.SEGMENT, out var evaluatedSegExpr))
                    {
                        this.SaveExpressionsForEntity(dtoEntity.Id, new List<DtoExpression> { evaluatedSegExpr });
                    }

                    if (dtoEntityResult.Segment != null)
                    {
                        if (!(this.requestContext.UserHasPermission(UserPermission.AddEntity, dtoEntityResult.Segment.Id)
                            || this.requestContext.UserHasPermission(UserPermission.CopyEntity, dtoEntityResult.Segment.Id)))
                        {
                            throw ErrorUtility.GetSecurityException("User does not have access to the evaluated segment id");
                        }

                        dtoEntity.Segment = new DtoSegment
                        {
                            Id = dtoEntityResult.Segment.Id,
                            Description = dtoEntityResult.Segment.Description,
                            Name = dtoEntityResult.Segment.Name
                        };
                    }
                    else
                    {
                        throw ErrorUtility.GetOperationCanceledException("The provided segment expression failed to evaluate to a valid segment");
                    }
                }

                var fieldTypeExpressionsToUpdate = r.DtoExpressionsDictionary[ExpressionTargetType.FIELDTYPEID]
                    .Where(x => expressionChangedDict[x.Key])
                    .Select(x => x.Value)
                    .ToList();

                if (fieldTypeExpressionsToUpdate.Any())
                {
                    this.dataContext.UpsertExpressions(fieldTypeExpressionsToUpdate);
                    this.Revisions = fieldTypeExpressionsToUpdate
                        .Where(x => x.TargetType == ExpressionTargetType.FIELDTYPEID && dtoFieldTypes[x.Target].TrackChanges)
                        .Select(x => new Field
                        {
                            Data = x.Data,
                            EntityId = dtoEntity.Id,
                            FieldType = fieldTypes.First(y => y.Id == x.Target),
                            LastModified = DateTime.UtcNow,
                            Revision = dtoEntity.Fields.First(y => y.FieldTypeId == x.Target).Revision + 1
                        })
                        .ToList();
                }

                if (expressionsToRemove.Any())
                {
                    this.dataContext.DeleteExpressions(expressionsToRemove);
                }
            }
            else
            {
                throw ErrorUtility.GetArgumentFault(
                        "EvaluateExpressionsForEntity",
                        nameof(dtoEntity),
                        $"Failed to evaluate expressions for entity {dtoEntity.Id}");
            }
        }

        public List<DtoExpression> CreateDefaultExpressionsAndEvaluate(Entity entity, IEnumerable<FieldType> fieldTypes)
        {
            if (!this.IsExpressionEnabled())
            {
                return new List<DtoExpression>();
            }

            var dtoFieldTypes = fieldTypes.ToDictionary(x => x.Id, x => DtoFactory.DtoFromFieldType(x));
            var fieldsDictionary = entity.Fields.ToDictionary(x => x.FieldType.Id, x => x);
            var dtoEntity = DtoFactory.DtoFromEntity(entity);
            var entityExpressions = this.dataContext.GetExpressionsForEntityType(dtoEntity.EntityTypeId);

            foreach (var field in dtoEntity.Fields)
            {
                if (!dtoFieldTypes[field.FieldTypeId].ExpressionSupport)
                {
                    continue;
                }

                if (!fieldsDictionary[field.FieldTypeId].IsEmpty())
                {
                    if (Remoting.Util.Utility.StringIsInriverExpression(true, field.Data))
                    {
                        entityExpressions[ExpressionTargetType.FIELDTYPEID].Add(
                            field.FieldTypeId,
                            new DtoExpression
                            {
                                Target = field.FieldTypeId,
                                TargetType = ExpressionTargetType.FIELDTYPEID,
                                Data = field.Data,
                                Status = null,
                                LanguageVersion = 1
                            });
                        field.Data = null;
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(dtoEntity.FieldSetId) && dtoEntity.FieldSetId.StartsWith("="))
            {
                if (entityExpressions[ExpressionTargetType.ENTITYMETADATA].ContainsKey(ExpressionTarget.FIELDSETID))
                {
                    entityExpressions[ExpressionTargetType.ENTITYMETADATA][ExpressionTarget.FIELDSETID].Data = dtoEntity.FieldSetId;
                }
                else
                {
                    entityExpressions[ExpressionTargetType.ENTITYMETADATA].Add(
                        ExpressionTarget.FIELDSETID,
                        new DtoExpression
                        {
                            Target = ExpressionTarget.FIELDSETID,
                            TargetType = ExpressionTargetType.ENTITYMETADATA,
                            Data = dtoEntity.FieldSetId,
                            Status = null,
                            LanguageVersion = 1
                        });
                }

                dtoEntity.FieldSetId = null;
            }
            else if (!string.IsNullOrWhiteSpace(dtoEntity.FieldSetId))
            {
                // if fieldset is set to some user input we need to use that and remove the expression
                _ = entityExpressions[ExpressionTargetType.ENTITYMETADATA].Remove(ExpressionTarget.FIELDSETID);
            }

            if (dtoEntity.Segment.Id != -1)
            {
                _ = entityExpressions[ExpressionTargetType.ENTITYMETADATA].Remove(ExpressionTarget.SEGMENT);
            }

            var r = ExpressionClient.EvaluateEntity(
                new EntityExpressionEvaluationModel
                {
                    DtoEntity = dtoEntity,
                    DtoFieldTypesDictionary = dtoFieldTypes,
                    DtoExpressionsDictionary = entityExpressions
                },
                this.requestContext.CustomerSafeName,
                this.requestContext.EnvironmentSafeName);

            if (r.Success)
            {
                var dtoEntityResult = r.DtoEntity.ToObject<DtoEntity>();

                if (dtoEntityResult.FieldSetId != entity.FieldSetId)
                {
                    // FieldSetId has been changed by ExpressionEngine
                    var fieldSet = this.dataContext.GetFieldSet(dtoEntityResult.FieldSetId);

                    if (fieldSet == null)
                    {
                        this.requestContext.Log(LogLevel.Warning, "No fieldset exists with id " + dtoEntityResult.FieldSetId);
                        throw ErrorUtility.GetArgumentFault("AddEntity", "fieldSetId", $"No fieldset exists with id '{dtoEntityResult.FieldSetId}'");
                    }

                    if (!entity.EntityType.Id.Equals(fieldSet.EntityTypeId, StringComparison.Ordinal))
                    {
                        this.requestContext.Log(LogLevel.Warning, "The supplied fieldset id belongs to other entity type than entity");
                        throw ErrorUtility.GetArgumentFault("AddEntity", "fieldSetId", "The supplied fieldset id belongs to other entity type than entity");
                    }
                }

                entity.Fields = DtoFactory.FieldsFromDtos(dtoEntityResult.Fields, entity.EntityType);
                entity.FieldSetId = dtoEntityResult.FieldSetId;
                if (entity.Segment.Id == -1)
                {
                    if (dtoEntityResult.Segment != null)
                    {
                        if (!(this.requestContext.UserHasPermission(UserPermission.AddEntity, dtoEntityResult.Segment.Id)
                            || this.requestContext.UserHasPermission(UserPermission.CopyEntity, dtoEntityResult.Segment.Id)))
                        {
                            throw ErrorUtility.GetSecurityFault("User does not have access to the evaluated segment id");
                        }

                        entity.Segment = new Segment
                        {
                            Id = dtoEntityResult.Segment.Id,
                            Description = dtoEntityResult.Segment.Description,
                            Name = dtoEntityResult.Segment.Name
                        };
                    }
                    else
                    {
                        throw ErrorUtility.GetOperationCancelledFault("The provided segment expression failed to evaluate to a valid segment");
                    }
                }

                var expressions = r.DtoExpressionsDictionary.Values.SelectMany(x => x.Values).ToList();
                this.Revisions = expressions
                    .Where(x => x.TargetType == ExpressionTargetType.FIELDTYPEID && dtoFieldTypes[x.Target].TrackChanges)
                    .Select(x => new Field
                    {
                        Data = x.Data,
                        EntityId = dtoEntity.Id,
                        FieldType = fieldTypes.First(y => y.Id == x.Target),
                        LastModified = DateTime.UtcNow,
                        Revision = 1
                    })
                    .ToList();
                return expressions;
            }
            else
            {
                throw ErrorUtility.GetArgumentFault(
                        "CreateDefaultExpressionsAndEvaluate",
                        nameof(entity),
                        $"Failed to evaluate expressions for entity {entity.Id}");
            }
        }

        public void SaveExpressionsForEntity(int entityId, List<DtoExpression> expressions)
        {
            if (entityId <= 0)
            {
                throw new ArgumentException("Invalid entityId");
            }

            expressions.ForEach(x => {
                x.EntityId = entityId;
                x.RefType = null;
                x.RefValue = null;
            });

            this.dataContext.UpsertExpressions(expressions);
        }

        public string EvaluateFieldSetIdExpression(DtoEntity entity, string fieldSetId)
        {
            if (!this.IsExpressionEnabled())
            {
                return fieldSetId;
            }

            var expression = this.dataContext.GetExpressionForEntityByTarget(entity.Id, ExpressionTarget.FIELDSETID);

            var expressionChanged = false;
            if (Remoting.Util.Utility.StringIsInriverExpression(true, fieldSetId) && expression == null)
            {
                expression = new DtoExpression
                {
                    Data = fieldSetId,
                    EntityId = entity.Id,
                    Target = ExpressionTarget.FIELDSETID,
                    TargetType = ExpressionTargetType.ENTITYMETADATA,
                    LanguageVersion = 1
                };
                expressionChanged = true;
            }
            else if (Remoting.Util.Utility.StringIsInriverExpression(true, fieldSetId) && expression != null && expression.Data != fieldSetId)
            {
                expression.Data = fieldSetId;
                expressionChanged = true;
            }
            else if (!Remoting.Util.Utility.StringIsInriverExpression(true, fieldSetId) && expression != null)
            {
                this.dataContext.DeleteExpressions(new List<int> { expression.Id });
                expression = null;
            }

            if (expression != null)
            {
                var r = ExpressionClient.EvaluateEntitySingle(
                    new EntitySingleExpressionEvaluationModel
                    {
                        DtoEntity = entity,
                        DtoExpression = expression
                    },
                    this.requestContext.CustomerSafeName,
                    this.requestContext.EnvironmentSafeName);
                if (r.Success)
                {
                    fieldSetId = r.ExpressionResult.ToString();
                    expression = r.Expression;
                }
                else
                {
                    throw ErrorUtility.GetArgumentFault(
                        "EvaluateFieldSetIdExpression",
                        nameof(fieldSetId),
                        $"Failed to evaluate expression for fieldsetid for entity {entity.Id}");
                }
            }

            if (expressionChanged)
            {
                this.dataContext.UpsertExpressions(new List<DtoExpression> { expression });
            }

            return fieldSetId;
        }

        public void RemoveFieldSetExpression(int entityId)
        {
            if (!this.IsExpressionEnabled())
            {
                return;
            }

            var expression = this.dataContext.GetExpressionForEntityByTarget(entityId, ExpressionTarget.FIELDSETID);
            if (expression != null)
            {
                this.dataContext.DeleteExpressions(new List<int> { expression.Id });
            }
        }

        public EntitySingleExpressionEvaluationResultModel EvaluateExpression(DtoEntity entity, DtoExpression expression)
        {
            if (!this.IsExpressionEnabled())
            {
                return null;
            }

            return ExpressionClient.EvaluateEntitySingle(
                new EntitySingleExpressionEvaluationModel
                {
                    DtoEntity = entity,
                    DtoExpression = expression
                },
                this.requestContext.CustomerSafeName,
                this.requestContext.EnvironmentSafeName);
        }

        public EntitySingleExpressionEvaluationResultModel EvaluateExpression(int entityId, string expression)
        {
            if (!this.IsExpressionEnabled())
            {
                return null;
            }

            return ExpressionClient.Evaluate(
                new EvaluateExpressionModel
                {
                    EntityId = entityId,
                    Expression = expression
                },
                this.requestContext.CustomerSafeName,
                this.requestContext.EnvironmentSafeName);
        }

        public bool ParseExpressions(List<Field> fields, out KeyValuePair<string, string> failedExpression)
        {
            if (!this.IsExpressionEnabled())
            {
                failedExpression = default;
                return true;
            }

            failedExpression = default;
            var expressions = fields.Where(x => Remoting.Util.Utility.StringIsInriverExpression(x.FieldType.ExpressionSupport, x.Data as string)).ToDictionary(x => x.FieldType.Id, x => x.Data as string);
            var r = ExpressionClient.Parse(
                new ParseModel
                {
                    NameToExpression = expressions
                },
                this.requestContext.CustomerSafeName,
                this.requestContext.EnvironmentSafeName);

            if (r.Success)
            {
                return true;
            }
            else
            {
                failedExpression = r.FailedExpressions.First();
                return false;
            }
        }

        public bool ParseExpression(string expression, out string failedExpression)
        {
            failedExpression = default;
            var r = ExpressionClient.Parse(
                new ParseModel
                {
                    NameToExpression = new Dictionary<string, string> { { "expression", expression } }
                },
                this.requestContext.CustomerSafeName,
                this.requestContext.EnvironmentSafeName);

            if (r.Success)
            {
                return true;
            }
            else
            {
                failedExpression = r.FailedExpressions.First().Value;
                return false;
            }
        }

        public static List<string> GetLiterals() => ExpressionClient.GetLiterals();

        private bool IsExpressionEnabled()
            => IsExpressionEnabled(this.requestContext);

        public static bool IsExpressionEnabled(RequestContext requestContext)
        {
            var r = Cache.GetOrCreate("ExpressionEnabled", entry => {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
                return ExpressionUtilPersistance.GetExpressionEnabledEnvironments(requestContext);
            });
            return r.Contains(requestContext.EnvironmentId);
        }

        private static List<DtoExpression> ResolveModifiedStatus(Dictionary<string, DtoExpression> persistedFieldExpressions, Dictionary<string, DtoExpression> evaluatedFieldExpressions)
        {
            var expressionsWithModifiedStatus = new List<DtoExpression>();

            foreach (var evaluatedFieldExpression in evaluatedFieldExpressions)
            {
                if (persistedFieldExpressions.TryGetValue(evaluatedFieldExpression.Key, out var persistedExpression))
                {
                    if (evaluatedFieldExpression.Value.Status != persistedExpression.Status || evaluatedFieldExpression.Value.StatusMessage != persistedExpression.StatusMessage)
                    {
                        expressionsWithModifiedStatus.Add(evaluatedFieldExpression.Value);
                    }
                }
                else
                {
                    expressionsWithModifiedStatus.Add(evaluatedFieldExpression.Value);
                }
            }

            return expressionsWithModifiedStatus;
        }

    }
}
