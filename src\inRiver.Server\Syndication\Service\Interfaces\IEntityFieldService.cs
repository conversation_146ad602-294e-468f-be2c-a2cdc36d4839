namespace inRiver.Server.Syndication.Service.Interfaces
{
    using System.Collections.Generic;
    using inRiver.Server.Syndication.Mapping;

    public interface IEntityFieldService
    {
        IEnumerable<InRiverField> GetFieldsByFieldTypeId(IList<InRiverField> fields, string fieldTypeId);

        IEnumerable<object> GetMappedData(MapField mapField, IEnumerable<InRiverField> foundCollection);

        FoundField CreateFoundField(IList<object> foundFieldsData, string fieldTypeId);
    }
}
