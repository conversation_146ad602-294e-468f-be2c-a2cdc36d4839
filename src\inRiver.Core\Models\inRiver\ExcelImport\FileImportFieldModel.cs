﻿namespace inRiver.Core.Models.inRiver.ExcelImport
{

    public class FileImportFieldModel 
    {
        public Field ImportField { get; set; }

        public string FieldType { get; set; }

        public static readonly string SysFieldSetFieldType = "sys_fieldset";

        public object PersistedValue { get; set; }

        public string Language { get; set; }

        public bool Valid { get; set; }

        public string ErrorMessage { get; set; }

        public object Value { get; set; }

        public object DisplayValue { get; set; }

    }
}