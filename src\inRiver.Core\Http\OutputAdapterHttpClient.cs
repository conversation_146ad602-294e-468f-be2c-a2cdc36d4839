namespace inRiver.Core.Http
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Text;
    using System.Text.Json;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using JsonSerializer = System.Text.Json.JsonSerializer;

    public class OutputAdapterHttpClient : IOutputAdapterHttpClient
    {
        private readonly HttpClient httpClient;

        private static readonly JsonSerializerOptions camelCaseAndCaseInsensitiveJsonSerializerOptions =
            new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            };

        public OutputAdapterHttpClient(HttpClient httpClient)
        {
            this.httpClient = httpClient;
        }

        public async Task<MappingDto> GetMapping(string environmentGid, int formatMappingId,
            CancellationToken cancellationToken)
        {
            var endpointUri =
                new Uri($"api/environments/{environmentGid}/mappings/{formatMappingId}",
                    UriKind.Relative);

            using (var request = new HttpRequestMessage(HttpMethod.Get, endpointUri))
            {
                request.Properties["ClientName"] = "OutputAdapterAuth0Client"; // Set the client name before sending the request

                var response = await this.httpClient.SendAsync(request, cancellationToken).ConfigureAwait(false);

                var mappingResponseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                _ = response.EnsureSuccessStatusCode();

                var mappingDto =
                    JsonSerializer.Deserialize<MappingDto>(mappingResponseContent,
                        camelCaseAndCaseInsensitiveJsonSerializerOptions);

                return mappingDto;
            }
        }

        public async Task<DsaMappingDto> GetDsaMapping(string environmentGid, int dsaMappingId,
            CancellationToken cancellationToken)
        {
            var endpointUri = new Uri($"api/environments/{{environmentGid}}/dsa-mappings/{dsaMappingId}", UriKind.Relative);

            using var request = new HttpRequestMessage(HttpMethod.Get, endpointUri);
            request.Properties["ClientName"] = "OutputAdapterAuth0Client"; // Set the client name before sending the request

            var response = await this.httpClient.SendAsync(request, cancellationToken).ConfigureAwait(false);

            var mappingResponseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            _ = response.EnsureSuccessStatusCode();

            var mappingDto =
                JsonSerializer.Deserialize<DsaMappingDto>(mappingResponseContent,
                    camelCaseAndCaseInsensitiveJsonSerializerOptions);

            return mappingDto;
        }

        public async Task PutProducts(int longRunningJobId, string environmentSafeName, string customerSafeName,
            string environmentGid, int formatMappingId, string exportResultJson, CancellationToken cancellationToken)
        {
            var endpointUri = new Uri($"api/environments/{environmentGid}/products", UriKind.Relative);

            using (var request = new HttpRequestMessage(HttpMethod.Put, endpointUri))
            {
                request.Properties["ClientName"] = "OutputAdapterAuth0Client"; // Set the client name before sending the request
                var parameters = new List<KeyValuePair<string, object>>
                {
                    new KeyValuePair<string, object>("JobId", longRunningJobId),
                    new KeyValuePair<string, object>("FormatMappingId", formatMappingId),
                    new KeyValuePair<string, object>("EnvironmentSafeName", environmentSafeName),
                    new KeyValuePair<string, object>("CustomerSafeName", customerSafeName)
                };
                request.Content = GetProductPutRequestContent(exportResultJson, parameters);

                var response = await this.httpClient.SendAsync(request, cancellationToken).ConfigureAwait(false);

                response.EnsureSuccessStatusCode();
            }
        }

        public async Task PutDsaProductsAsync(int longRunningJobId, string environmentSafeName, string customerSafeName, string username,
            int formatMappingId, int dsaMappingid, string exportResultJson, CancellationToken cancellationToken)
        {
            var endpointUri = new Uri($"api/dsa/product-submissions", UriKind.Relative);

            using var request = new HttpRequestMessage(HttpMethod.Post, endpointUri);
            request.Properties["ClientName"] = "OutputAdapterAuth0Client"; // Set the client name before sending the request
            var parameters = new List<KeyValuePair<string, object>>
                {
                    new KeyValuePair<string, object>("JobId", longRunningJobId),
                    new KeyValuePair<string, object>("FormatMappingId", formatMappingId),
                    new KeyValuePair<string, object>("DsaMappingId", dsaMappingid),
                    new KeyValuePair<string, object>("UserUuid", username),
                    new KeyValuePair<string, object>("EnvironmentSafeName", environmentSafeName),
                    new KeyValuePair<string, object>("CustomerSafeName", customerSafeName)
                };
            request.Content = GetProductPutRequestContent(exportResultJson, parameters);

            var response = await this.httpClient.SendAsync(request, cancellationToken).ConfigureAwait(false);

            _ = response.EnsureSuccessStatusCode();
        }

        private static StringContent GetProductPutRequestContent(string exportResultJson, IList<KeyValuePair<string, object>> parameters)
        {
            var jsonRequestData = new JObject();
            foreach (var parameter in parameters)
            {
                jsonRequestData.Add(parameter.Key, JToken.FromObject(parameter.Value));
            }

            var productJsonArray = JArray.Parse(exportResultJson);
            jsonRequestData.Add(new JProperty("SyndicateCoreProductOutput", productJsonArray));

            return new StringContent(jsonRequestData.ToString(Formatting.None), Encoding.UTF8, "application/json");
        }
    }
}
