namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Concurrent;
    using System.Net.Http;
    using System.Threading.Tasks;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Script.Api;
    using inRiver.Server.Syndication.Script.Api.Models;
    using Xunit;

    public class RestApiCacheServiceTests
    {
        private const int CacheExpiryTimeMinutesDisabled = 0;

        private const int DefaultCacheExpiryTimeMinutes = 30;

        private const string CacheKeyExample = "key1";

        [Fact]
        public async Task GetOrAddAsync_CacheDisabled_GetterMethodMustBeExecutedEachTime()
        {
            // Arrange
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService();
            var getValueFunc = A.Fake<Func<Task<string>>>();
            var getValueFuncCall = A.CallTo(() => getValueFunc());

            // Act
            await restApiCacheService.GetOrAddAsync(CacheKeyExample, getValueFunc, CacheExpiryTimeMinutesDisabled);
            await restApiCacheService.GetOrAddAsync(CacheKeyExample, getValueFunc, CacheExpiryTimeMinutesDisabled);

            // Assert
            getValueFuncCall.MustHaveHappenedTwiceExactly();
        }

        [Fact]
        public async Task GetOrAddAsync_CacheEnabled_GetterMethodMustBeExecutedOnce()
        {
            // Arrange
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService();
            var getValueFunc = A.Fake<Func<Task<string>>>();
            var getValueFuncCall = A.CallTo(() => getValueFunc());

            // Act
            await restApiCacheService.GetOrAddAsync(CacheKeyExample, getValueFunc, 1);
            await restApiCacheService.GetOrAddAsync(CacheKeyExample, getValueFunc, 1);
            await restApiCacheService.GetOrAddAsync(CacheKeyExample, getValueFunc, 1);
            await restApiCacheService.GetOrAddAsync(CacheKeyExample, getValueFunc, 1);

            // Assert
            getValueFuncCall.MustHaveHappenedOnceExactly();
        }

        [Fact]
        public async Task GetOrAddAsync_CacheIsExpired_GetterMethodMustBeExecutedOnce()
        {
            // Arrange
            var expiredCacheModel =
                new CacheModel(Guid.NewGuid().ToString(), DateTime.UtcNow - TimeSpan.FromMinutes(2));
            var cache = new ConcurrentDictionary<string, CacheModel>();
            cache.TryAdd(CacheKeyExample, expiredCacheModel);
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService(cache);
            var getValueFunc = A.Fake<Func<Task<string>>>();
            var getValueFuncCall = A.CallTo(() => getValueFunc());

            // Act
            await restApiCacheService.GetOrAddAsync(CacheKeyExample, getValueFunc, 1);

            // Assert
            getValueFuncCall.MustHaveHappenedOnceExactly();
        }

        [Theory]

        // We use the Entity ID mask, the cache should be disabled (ExpiryTime = 0)
        [InlineData("1", true, CacheExpiryTimeMinutesDisabled)]
        [InlineData(10, true, CacheExpiryTimeMinutesDisabled)]

        // We do not use the Entity ID mask, ExpiryTime must be parsed to int value
        [InlineData(10, false, 10)]
        [InlineData(0, false, CacheExpiryTimeMinutesDisabled)]
        [InlineData("10", false, 10)]

        // ExpiryTime is not specified, use the default ExpiryTime value
        [InlineData(null, false, DefaultCacheExpiryTimeMinutes)]
        public void ParseExpiryTime_ShouldReturnCorrectValue(object cacheExpiryTimeMinutes, bool maskIsUsed, int result)
        {
            // Arrange
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService();

            // Act
            var parsedExpiryTime = restApiCacheService.ParseExpiryTime(cacheExpiryTimeMinutes, maskIsUsed);

            // Assert
            parsedExpiryTime.Should().Be(result);
        }

        [Fact]
        public void ParseExpiryTime_ConNotConvertCacheExpiryTimeMinutesToInt_ShouldThrowExceptionWithCorrectMessage()
        {
            // Arrange
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService();
            const string wrongCacheExpiryTimeMinutesValue = "Not int";

            // Act
            Action act = () => restApiCacheService.ParseExpiryTime(wrongCacheExpiryTimeMinutesValue, false);

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage("Can not convert cacheExpiryTimeMinutes value to int.");
        }
    }
}
