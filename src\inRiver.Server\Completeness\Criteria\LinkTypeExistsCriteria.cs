namespace inRiver.Server.Completeness.Criteria
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Remoting.Objects;
    using inRiver.Server.DataAccess;

    public class LinkTypeExistsCriteria
    {
        private readonly IDataPersistance dataPersistance;

        public LinkTypeExistsCriteria(IDataPersistance dataPersistance)
        {
            this.dataPersistance = dataPersistance;
        }

        private const string SettingsKey = "LinkTypeId";

        public string Name => "Link Exists";

        public List<string> SettingsKeys => new List<string> { SettingsKey };

        public int GetCriteriaCompletenessPercentage(int entityId, List<CompletenessRuleSetting> settings)
        {
            CompletenessRuleSetting setting = settings.FirstOrDefault(s => s.Key == SettingsKey);

            string linkTypeId = setting?.Value;

            if (string.IsNullOrEmpty(linkTypeId))
            {
                return 0;
            }

            int count = this.dataPersistance.GetLinkCountForOutboundLinkType(linkTypeId, entityId);

            if (count > 0)
            {
                return 100;
            }

            return 0;
        }

        public async Task<int> GetCriteriaCompletenessPercentageAsync(int entityId, IEnumerable<CompletenessRuleSetting> settings)
        {
            var setting = settings.FirstOrDefault(s => s.Key == SettingsKey);

            var linkTypeId = setting?.Value;

            if (string.IsNullOrEmpty(linkTypeId))
            {
                return 0;
            }

            var count = await this.dataPersistance.GetLinkCountForOutboundLinkTypeAsync(linkTypeId, entityId);

            if (count > 0)
            {
                return 100;
            }

            return 0;
        }
    }
}
