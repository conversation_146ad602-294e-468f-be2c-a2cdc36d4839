namespace inRiver.Server.Syndication.Script.Api
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Core.Constants;
    using inRiver.Core.Util;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Script.Api.Models;
    using Newtonsoft.Json;

    public class RestApiSettingsService : IRestApiSettingsService
    {
        private readonly ICrypto cryptoService;

        private readonly RequestContext requestContext;

        private IList<EndpointAliasSetting> endpointAliases;

        public RestApiSettingsService(RequestContext requestContext, ICrypto cryptoService)
        {
            this.requestContext = requestContext;
            this.cryptoService = cryptoService;
            var encodedEndpointAliases = this.GetEndpointAliasesSetting();
            this.SetEndpointAliasesAsync(encodedEndpointAliases).GetAwaiter().GetResult();
        }

        public EndpointAliasSetting GetAliasSettings(string endpointAlias)
        {
            var aliasSettings = this.endpointAliases.FirstOrDefault(setting => setting.EndpointAlias == endpointAlias);

            return aliasSettings ?? throw new SyndicateApiException($"Settings for alias '{endpointAlias}' were not found.");
        }

        public bool IsActivatedAlias(string endpointAlias)
        {
            var aliasSettings = this.endpointAliases.FirstOrDefault(setting => setting.EndpointAlias == endpointAlias);
            if (aliasSettings == null)
            {
                throw new SyndicateApiException($"Settings for alias '{endpointAlias}' were not found.");
            }

            return !aliasSettings.IsInactivated;
        }

        private string GetEndpointAliasesSetting()
            => this.requestContext.DataPersistance.GetServerSetting(ServerConstants.ENCRYPTED_SYNDICATION_ENDPOINT_ALIASES);

        private async Task SetEndpointAliasesAsync(string encryptedEndpointAliases)
        {
            string decryptedEndpointAliases;
            try
            {
                decryptedEndpointAliases = await this.cryptoService.DecryptStringAsync(encryptedEndpointAliases);
            }
            catch (Exception ex)
            {
                throw new SyndicateApiException(ex.Message);
            }

            IList<EndpointAliasSetting> endpointAliasSettings = null;
            try
            {
                if (!string.IsNullOrEmpty(decryptedEndpointAliases))
                {
                    endpointAliasSettings = JsonConvert.DeserializeObject<IList<EndpointAliasSetting>>(decryptedEndpointAliases);
                }
            }
            catch (JsonReaderException)
            {
                throw new SyndicateApiException("Invalid JSON alias configuration format.");
            }

            this.endpointAliases = endpointAliasSettings ?? throw new SyndicateApiException("API endpoint aliases setting was not found.");
        }
    }
}
