namespace inRiver.iPMC.Persistance
{
    using System.Threading;

    public interface IPersistanceChannel
    {
        void CreateChannelStructure(int channelId, string entityTypeId, string customerSafeName, string environmentSafeName, CancellationToken cancellationToken);

        bool IsExcludedByChannelFilter(int channelId, int entityId, string linkTypeId, string customerSafeName, string environmentSafeName);
    }
}
