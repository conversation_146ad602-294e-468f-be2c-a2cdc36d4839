namespace LongRunningJob.Core.UnitTests
{
    using System;
    using FluentAssertions;
    using inRiver.Core.Models.inRiver;
    using LongRunningJob.Core.Commands;
    using LongRunningJob.Core.Constants;
    using LongRunningJob.Core.Factories;
    using Xunit;

    public class CommandFactoryTests
    {
        [Theory]
        [InlineData(LongRunningJobsJobType.ExcelExport, typeof(ExcelExportCommand))]
        public void CreateCommand_KnownJobType_ShouldCreateCommandCorrectCommand(string jobType, Type expectedCommand)
        {
            var commandFactory = new CommandFactory();

            var command = commandFactory.CreateCommand(jobType, new LongRunningJob());

            command.Should().BeOfType(expectedCommand);
        }

        [Fact]
        public void CreateCommand_UnknownJobType_ShouldThrowArgumentException()
        {
            var commandFactory = new CommandFactory();
            const string jobType = "foobar";

            Action act = () => commandFactory.CreateCommand(jobType, new LongRunningJob());

            act.Should().Throw<ArgumentException>().WithMessage($"No Command for jobtype {jobType} exists");
        }
    }
}
