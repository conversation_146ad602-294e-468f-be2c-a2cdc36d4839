namespace inRiver.Server.Syndication.Helpers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Threading.Tasks.Dataflow;

    public static class ParallelTaskExecutor
    {
        /// <summary>
        /// Processes list of records one by one in maximum 10 concurrent tasks.
        /// </summary>
        /// <typeparam name="T">Input type of transform operation.</typeparam>
        /// <typeparam name="T1">Output type of transform operation.</typeparam>
        /// <param name="list">List of records to be processed. Type of these records should be of Input type.</param>
        /// <param name="transform">Block that transforms input records to an Output type.</param>
        /// <param name="action">Block that performs action on transformed values. Used to save initial sequence of records.</param>
        public static void Execute<T, T1>(IList<T> list, Func<T, T1> transform, Action<T1> action, CancellationToken cancellationToken)
        {
            if (list == null || !list.Any())
            {
                return;
            }

            var transformBlockOptions = new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken };
            var transformBlock = new TransformBlock<T, T1>(
                transform,
                transformBlockOptions);

            var outputBlock = new ActionBlock<T1>(action);

            _ = transformBlock.LinkTo(outputBlock, new DataflowLinkOptions { PropagateCompletion = true });

            foreach (var record in list)
            {
                _ = transformBlock.Post(record);
            }

            transformBlock.Complete();
            Task.Run(async () => await outputBlock.Completion.ConfigureAwait(false), cancellationToken).Wait(cancellationToken);
        }

        /// <summary>
        /// Processes list of records in batches in maximum 10 concurrent tasks.
        /// </summary>
        /// <typeparam name="T">Input list type of transform operation.</typeparam>
        /// <typeparam name="T1">Output type of transform operation.</typeparam>
        /// <param name="list">List of records to be processed. Type of these records should be of Input type.</param>
        /// <param name="batchSize">Size of the batch to be processed at one time.</param>
        /// <param name="transform">Block that transforms input records to an Output type.</param>
        /// <param name="action">Block that performs action on transformed values. Used to save initial sequence of records.</param>
        public static void BatchExecute<T, T1>(IList<T> list, int batchSize, Func<IList<T>, T1> transform, Action<T1> action, CancellationToken cancellationToken)
        {
            if (list == null || !list.Any())
            {
                return;
            }

            var transformBlockOptions = new ExecutionDataflowBlockOptions { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken };
            var transformBlock = new TransformBlock<IList<T>, T1>(
                transform,
                transformBlockOptions);

            var outputBlock = new ActionBlock<T1>(action);

            _ = transformBlock.LinkTo(outputBlock, new DataflowLinkOptions { PropagateCompletion = true });

            var totalCount = list.Count;
            var batchCounter = 0;

            do
            {
                var batchRecords = list.Skip(batchCounter).Take(batchSize).ToList();
                _ = transformBlock.Post(batchRecords);
                batchCounter += batchRecords.Count;
            } while (batchCounter < totalCount);

            transformBlock.Complete();
            Task.Run(async () => await outputBlock.Completion.ConfigureAwait(false), cancellationToken).Wait(cancellationToken);
        }
    }
}
