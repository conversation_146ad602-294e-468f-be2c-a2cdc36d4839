namespace inRiver.Core.Services
{
    using System.Net.Http.Headers;
    using System.Net.Http;
    using System.Threading.Tasks;
    using System.Threading;
    using System;

    public sealed class BearerTokenHandler : DelegatingHandler
    {
        private readonly IAccessTokenRetriever accessTokenRetriever;

        public BearerTokenHandler(IAccessTokenRetriever accessTokenRetriever)
        {
            this.accessTokenRetriever = accessTokenRetriever;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
            CancellationToken cancellationToken)
        {

            if (request.Headers.Authorization is null)
            {
                // Extract client name from request properties or headers
                if (!request.Properties.TryGetValue("ClientName", out var clientNameObj) || !(clientNameObj is string clientName))
                {
                    throw new InvalidOperationException("Client name must be provided.");
                }
                var accessToken = await this.accessTokenRetriever.GetAccessTokenAsync(clientName, cancellationToken).ConfigureAwait(false);
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            }
            return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
        }

    }
}
