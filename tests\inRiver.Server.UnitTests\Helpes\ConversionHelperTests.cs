namespace inRiver.Server.UnitTests.Helpers
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using FluentAssertions;
    using inRiver.Core.Persistance;
    using inRiver.iPMC.Persistance;
    using inRiver.Remoting.Dto;
    using inRiver.Server.Helpers;
    using Newtonsoft.Json;
    using Xunit;
    using RemotingEntity = inRiver.Remoting.Objects.Entity;
    using RemotingEntityType = inRiver.Remoting.Objects.EntityType;
    using RemotingField = inRiver.Remoting.Objects.Field;
    using RemotingLocaleString = inRiver.Remoting.Objects.LocaleString;

    public class ConversionHelperTests
    {
        public class DoNotThrowIfPathTraversalIsNotDetectedTests
        {
            [Theory]
            [ClassData(typeof(ConversionHelperTestCases))]
            public void WhenConvertingTypesWithSerializationAndMap_ThenResultsShouldNotBeEquivalentAfterChange(ConversionHelperTestCaseBase conversionHelperTestCase)
            {
                // Arrange
                var objectJson = File.ReadAllText(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestFiles", "ConversionHelperTests", conversionHelperTestCase.TestFileName));
                var inputObject = JsonConvert.DeserializeObject(objectJson, conversionHelperTestCase.InputType, IPMCPersistanceDataConverter.JsonConverters);

                // Act
                var outputObject = CallMap(conversionHelperTestCase.OutputType, inputObject);
                var expectedObject = CallConvert(conversionHelperTestCase.OutputType, inputObject);

                AssertAreEquivalent(expectedObject, outputObject);
                conversionHelperTestCase.ChangeObject(outputObject);

                // Assert
                AssertAreNotEquivalent(expectedObject, outputObject);
            }

            private static void AssertAreEquivalent(object expected, object actual)
            {
                actual.Should().BeEquivalentTo(
                    expected,
                    opt => {
                        opt.Using<string>(ctx => ctx.Subject.ToString().Should().BeEquivalentTo(ctx.Expectation.ToString()));
                        opt.Excluding(o => o.Type == typeof(string) && o.Name == nameof(Field.Data));

                        return opt;
                    });
            }

            private static void AssertAreNotEquivalent(object expected, object actual)
            {
                actual.Should().NotBeEquivalentTo(
                    expected,
                    opt => {
                        opt.Using<RemotingLocaleString>(ctx => ctx.Subject.ToString().Should().NotBeEquivalentTo(ctx.Expectation.ToString())).WhenTypeIs<RemotingLocaleString>();
                        opt.Using<LocaleString>(ctx => ctx.Subject.ToString().Should().NotBeEquivalentTo(ctx.Expectation.ToString())).WhenTypeIs<LocaleString>();
                        opt.Excluding(o => o.Type == typeof(object) && o.Name == nameof(Field.Data));

                        return opt;
                    });
            }

            private static object CallMap(Type type, object input) => CallConverstion(typeof(ConversionHelper), nameof(ConversionHelper.Map), type, input);

            private static object CallConvert(Type type, object input) => CallConverstion(typeof(IPMCPersistanceDataConverter), nameof(IPMCPersistanceDataConverter.ConvertTo), type, input);

            private static object CallConverstion(Type conversionType, string conversionMethodName, Type resultType, object input)
            {
                var method = conversionType.GetMethod(conversionMethodName);
                var genericMethod = method.MakeGenericMethod(resultType);
                return genericMethod.Invoke(null, new[] { input });
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1002:Do not expose generic lists", Justification = "<Exact type is needed for correct assertion")]
        public class ConversionHelperTestCases : IEnumerable<object[]>
        {
            public static void ChangeLocaleString(List<EntityType> entityTypes)
            {
                var entityType = entityTypes.First(e => e.Id == "abc");
                var fieldSet = entityType.FieldSets.First(fs => fs.Id == "10011");
                fieldSet.Name[new CultureInfo("en")] += "Break";
            }

            public static void ChangeFieldSet(List<EntityType> entityTypes)
            {
                var entityType = entityTypes.First(e => e.Id == "abc");
                var fieldSet = entityType.FieldSets.First(fs => fs.Id == "10011");
                fieldSet.Id += "Break";
            }

            public static void ChangeEntityType(List<EntityType> entityTypes)
            {
                var entityType = entityTypes.First(e => e.Id == "abc");
                entityType.IsLinkEntityType = true;
            }

            public static void ChangeEntityLink(List<EntityType> entityTypes)
            {
                var entityType = entityTypes.First(e => e.Id == "Activity");
                var linkType = entityType.LinkTypes.First(fs => fs.Id == "ActivityActivity");
                linkType.Index = 100;
            }

            public static void ChangeFieldType(List<EntityType> entityTypes)
            {
                var entityType = entityTypes.First(e => e.Id == "Activity");
                var fieldType = entityType.FieldTypes.First(fs => fs.Id == "ActivityDescription");
                fieldType.CategoryId += "break";
            }

            public static void ChangeFieldSetting(List<EntityType> entityTypes)
            {
                var entityType = entityTypes.First(e => e.Id == "Activity");
                var fieldType = entityType.FieldTypes.First(fs => fs.Id == "ActivityDescription");
                fieldType.Settings["IncludedInPlannerQuickEdit"] = "false";
            }

            public static void ChangeRemotingLocaleString(List<RemotingEntityType> remotingEntityTypes)
            {
                var entityType = remotingEntityTypes.First(e => e.Id == "abc");
                var fieldSet = entityType.FieldSets.First(fs => fs.Id == "10011");
                fieldSet.Name[new CultureInfo("en")] += "Break";
            }

            public static void ChangeRemotingFieldSet(List<RemotingEntityType> remotingEntityTypes)
            {
                var entityType = remotingEntityTypes.First(e => e.Id == "abc");
                var fieldSet = entityType.FieldSets.First(fs => fs.Id == "10011");
                fieldSet.Id += "Break";
            }

            public static void ChangeRemotingEntityType(List<RemotingEntityType> remotingEntityTypes)
            {
                var entityType = remotingEntityTypes.First(e => e.Id == "abc");
                entityType.IsLinkEntityType = true;
            }

            public static void ChangeRemotingEntityLink(List<RemotingEntityType> remotingEntityTypes)
            {
                var entityType = remotingEntityTypes.First(e => e.Id == "Activity");
                var linkType = entityType.LinkTypes.First(fs => fs.Id == "ActivityActivity");
                linkType.Index = 100;
            }

            public static void ChangeRemotingFieldType(List<RemotingEntityType> remotingEntityTypes)
            {
                var entityType = remotingEntityTypes.First(e => e.Id == "Activity");
                var fieldType = entityType.FieldTypes.First(fs => fs.Id == "ActivityDescription");
                fieldType.CategoryId += "break";
            }

            public static void ChangeRemotingFieldSetting(List<RemotingEntityType> remotingEntityTypes)
            {
                var entityType = remotingEntityTypes.First(e => e.Id == "Activity");
                var fieldType = entityType.FieldTypes.First(fs => fs.Id == "ActivityDescription");
                fieldType.Settings["IncludedInPlannerQuickEdit"] = "false";
            }

            public static void ChangeRemotingEntityFieldType(List<RemotingField> remotingFields)
            {
                var field = remotingFields.First(f => f.FieldType.Id == "Approved");
                field.FieldType.Index += 100;
            }

            public static void ChangeEntityEntityType(Entity entity)
            {
                entity.EntityType.Id += "break";
            }

            public static void ChangeEntity(Entity entity)
            {
                entity.LoadLevel = LoadLevel.DataOnly;
            }

            public static void ChangeEntityFieldType(Entity entity)
            {
                var field = entity.Fields.First(f => f.FieldType.Id == "DoubleNumberId");
                field.FieldType.Index += 100;
            }

            public static void ChangeEntityFields(Entity entity)
            {
                var field = entity.Fields.First(f => f.FieldType.Id == "DoubleNumberId");
                field.LastModified = field.LastModified.AddDays(1);
            }

            public static void ChangeEntityField(Entity entity)
            {
                entity.DisplayName.Data = null;
            }

            public static void ChangeEntitySegment(Entity entity)
            {
                entity.Segment.Name += "break";
            }

            public static void ChangeDtoEntity(DtoEntity dtoEntity)
            {
                dtoEntity.LoadLevel = Remoting.Objects.LoadLevel.DataAndLinks;
            }

            public static void ChangeDtoEntityFields(List<DtoField> dtoFields)
            {
                var field = dtoFields.First(f => f.FieldTypeId == "Approved");
                field.LastModified += "break";
            }

            public static void ChangeDtoEntityField(DtoEntity dtoEntity)
            {
                dtoEntity.DisplayName.Data = null;
            }

            public static void ChangeDtoEntitySegment(DtoEntity dtoEntity)
            {
                dtoEntity.Segment.Name += "break";
            }

            public static void ChangeSystemQuery(SystemQuery systemQuery)
            {
                systemQuery.CreatedBy += "break";
            }

            public static void ChangeCriteria(Criteria criteria)
            {
                criteria.Language += "break";
            }

            public IEnumerator<object[]> GetEnumerator()
            {
                yield return new object[]
                {
                    new ConversionHelperTestCase<List<EntityType>, List<RemotingEntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeRemotingLocaleString,
                        TestName = nameof(ChangeRemotingLocaleString)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<EntityType>, List<RemotingEntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeRemotingFieldSet,
                        TestName = nameof(ChangeRemotingFieldSet)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<EntityType>, List<RemotingEntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeRemotingEntityType,
                        TestName = nameof(ChangeRemotingEntityType)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<EntityType>, List<RemotingEntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeRemotingEntityLink,
                        TestName = nameof(ChangeRemotingEntityLink)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<EntityType>, List<RemotingEntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeRemotingFieldType,
                        TestName = nameof(ChangeRemotingFieldType)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<EntityType>, List<RemotingEntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeRemotingFieldSetting,
                        TestName = nameof(ChangeRemotingFieldSetting)
                    }
                };
                yield return new object[]
                {
                    new ConversionHelperTestCase<List<RemotingEntityType>, List<EntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeLocaleString,
                        TestName = nameof(ChangeLocaleString)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<RemotingEntityType>, List<EntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeFieldSet,
                        TestName = nameof(ChangeFieldSet)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<RemotingEntityType>, List<EntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeEntityType,
                        TestName = nameof(ChangeEntityType)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<RemotingEntityType>, List<EntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeEntityLink,
                        TestName = nameof(ChangeEntityLink)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<RemotingEntityType>, List<EntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeFieldType,
                        TestName = nameof(ChangeFieldType)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<RemotingEntityType>, List<EntityType>>
                    {
                        TestFileName = "entitytype.json",
                        ChangeOutput = ChangeFieldSetting,
                        TestName = nameof(ChangeFieldSetting)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<Field>, List<RemotingField>>
                    {
                        TestFileName = "entityFields.json",
                        ChangeOutput = ChangeRemotingEntityFieldType,
                        TestName = nameof(ChangeRemotingEntityFieldType)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<List<Field>, List<DtoField>>
                    {
                        TestFileName = "entityFields.json",
                        ChangeOutput = ChangeDtoEntityFields,
                        TestName = nameof(ChangeDtoEntityFields)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<Entity, DtoEntity>
                    {
                        TestFileName = "entity.json",
                        ChangeOutput = ChangeDtoEntity,
                        TestName = nameof(ChangeDtoEntity)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<Entity, DtoEntity>
                    {
                        TestFileName = "entity.json",
                        ChangeOutput = ChangeDtoEntityField,
                        TestName = nameof(ChangeDtoEntityField)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<Entity, DtoEntity>
                    {
                        TestFileName = "entity.json",
                        ChangeOutput = ChangeDtoEntitySegment,
                        TestName = nameof(ChangeDtoEntitySegment)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<RemotingEntity, Entity>
                    {
                        TestFileName = "remotingEntity.json",
                        ChangeOutput = ChangeEntityEntityType,
                        TestName = nameof(ChangeEntityEntityType)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<RemotingEntity, Entity>
                    {
                        TestFileName = "remotingEntity.json",
                        ChangeOutput = ChangeEntity,
                        TestName = nameof(ChangeEntity)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<RemotingEntity, Entity>
                    {
                        TestFileName = "remotingEntity.json",
                        ChangeOutput = ChangeEntityFieldType,
                        TestName = nameof(ChangeEntityFieldType)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<RemotingEntity, Entity>
                    {
                        TestFileName = "remotingEntity.json",
                        ChangeOutput = ChangeEntityFields,
                        TestName = nameof(ChangeEntityFields)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<RemotingEntity, Entity>
                    {
                        TestFileName = "remotingEntity.json",
                        ChangeOutput = ChangeEntityField,
                        TestName = nameof(ChangeEntityField)
                    }
                };

                yield return new object[]
                {
                    new ConversionHelperTestCase<RemotingEntity, Entity>
                    {
                        TestFileName = "remotingEntity.json",
                        ChangeOutput = ChangeEntitySegment,
                        TestName = nameof(ChangeEntitySegment)
                    }
                };
            }

            IEnumerator IEnumerable.GetEnumerator() => this.GetEnumerator();
        }

        public class ConversionHelperTestCase<TInput, TOutput> : ConversionHelperTestCaseBase
            where TInput : class
            where TOutput : class
        {
            public override Type InputType => typeof(TInput);

            public override Type OutputType => typeof(TOutput);

            public Action<TOutput> ChangeOutput { get; set; }

            public override Action<object> ChangeObject => (o) => {
                var outputObject = o as TOutput;

                if (o is null)
                {
                    throw new ArgumentException("Object is not of expected type");
                }

                this.ChangeOutput(outputObject);
            };
        }

        public abstract class ConversionHelperTestCaseBase
        {
            public abstract Type InputType { get; }

            public abstract Type OutputType { get; }

            public string TestFileName { get; set; }

            public string TestName { get; set; }

            public abstract Action<object> ChangeObject { get; }

            public override string ToString() => this.TestName;
        }
    }
}
