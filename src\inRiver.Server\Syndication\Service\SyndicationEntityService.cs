namespace inRiver.Server.Syndication.Service
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Repository;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Helpers;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Service.Interfaces;

    public class SyndicationEntityService : ISyndicationEntityService
    {
        private readonly IDataPersistance dataContext;
        private readonly DataRepository dataRepository;
        private readonly ModelRepository modelRepository;
        private readonly IResourceExportService resourceExportService;

        public SyndicationEntityService(RequestContext context, IResourceExportService resourceExportService)
        {
            this.dataContext = context.DataPersistance;
            this.resourceExportService = resourceExportService;
            this.dataRepository = new DataRepository(context);
            this.modelRepository = new ModelRepository(context);
        }

        /// <summary>
        /// Gets the list of entity ids inside the selected work area.
        /// </summary>
        /// <param name="workAreaId"> Id of the syndication work area.</param>
        /// <returns>List of entity ids inside syndication work area.</returns>
        /// <exception cref="SyndicateException">Throws a <see cref="SyndicateException"/> if selected work area is empty.</exception>
        public IEnumerable<int> GetWorkAreaEntityIds(string workAreaId)
        {
            var guid = Guid.Parse(workAreaId);

            var folder = this.dataContext.GetSharedWorkAreaFolder(guid, true);

            var entityIds = folder.IsQuery ? this.dataRepository.Search(folder.Query) : folder.FolderEntities;
            return entityIds.Any()
                ? entityIds
                : throw new SyndicateException($"Selected work area is empty: {folder.Name} ({workAreaId}).");
        }

        public async Task<IEnumerable<DtoEntity>> GetEntitiesAsync(IEnumerable<int> ids)
        {
            var entities = await this.dataContext.GetEntitiesAsync(ids);

            return entities;
        }

        /// <summary>
        /// Selects inriver entities from work area that match work area entity in mapping (including first and second related entities).
        /// If image fields are mapped in the current mapping, these fields are selected and added to the result list of inriver entities as well.
        /// </summary>
        /// <param name="entityIds">All work area entity ids.</param>
        /// <param name="mapManager">Syndication map manager.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>List of inriver entities related to current syndication including first and second related entities.</returns>
        /// <exception cref="SyndicateException">
        /// Throws a <see cref="SyndicateException"/> if entities in work area don't match set work area entity in mapping.
        /// </exception>
        public IList<InRiverEntity> GetInRiverEntities(IEnumerable<int> entityIds, MapManager mapManager, CancellationToken cancellationToken)
        {
            var dtoEntities = this.GetSyndicationDtoEntities(entityIds.ToList(), cancellationToken);
            var entities = this.GetEntitiesFromDtos(dtoEntities, cancellationToken);

            var workareaEntitiesExist = entities.ToList()
                .GroupBy(entity => entity.EntityType.Id)
                .ToDictionary(x => x.Key, x => x.ToList())
                .TryGetValue(mapManager.WorkareaEntityTypeId, out var workareaEntities);

            if (!workareaEntitiesExist)
            {
                var error = $"Entities in work area don't match set work area entity in selected mapping. Work area entity should be: {mapManager.WorkareaEntityTypeId}.";
                throw new SyndicateException(error);
            }

            var languages = this.dataContext.GetAllLanguages();
            var result = new List<InRiverEntity>();
            if (mapManager.OutputEntityTypeId == mapManager.WorkareaEntityTypeId && !mapManager.FirstRelatedIsSet)
            {
                result = workareaEntities.Select(MapManager.GetInRiverEntity).ToList();
            }
            else if (mapManager.FirstRelatedIsSet && !mapManager.SecondRelatedIsSet)
            {
                result = this.GetFirstRelatedInRiverEntities(workareaEntities, mapManager, languages, cancellationToken);
            }
            else if (mapManager.FirstRelatedIsSet && mapManager.SecondRelatedIsSet)
            {
                result = this.GetSecondRelatedInRiverEntities(workareaEntities, mapManager, languages, cancellationToken);
            }
            else
            {
                return result;
            }

            var entityTypesWithImagesMapped = GetEntityTypesWithMappedImageFields(mapManager);
            if (entityTypesWithImagesMapped.Any())
            {
                this.AddImageFields(result, entityTypesWithImagesMapped, languages, cancellationToken);
            }

            return result;
        }

        public IList<InRiverEntity> ConvertExportContainersToEntityModels(IEnumerable<ExportContainer> exportContainers) =>
            exportContainers.Select(x => new InRiverEntity
            {
                Id = x.Id,
                RelatedEntities = new List<InRiverEntity>(),
                Fields = x.Fields.Where(y => y.FieldTypeId != null).Select(y => new InRiverField
                {
                    Data = string.IsNullOrEmpty(y.Data?.ToString()) ? null : y.Data,
                    FieldType = new InRiverFieldType
                    {
                        FieldTypeId = y.Id,
                        DataType = y.MapFieldType?.DataType ?? "String"
                    }
                }).ToList()
            }).ToList();

        private IList<DtoEntity> GetSyndicationDtoEntities(IList<int> ids, CancellationToken cancellationToken) => ids == null || !ids.Any()
            ? new List<DtoEntity>()
            : this.dataContext.GetEntitiesWithData(ids.Distinct().ToList(), cancellationToken);

        private List<Entity> GetEntitiesFromDtos(IList<DtoEntity> dtoEntities, CancellationToken cancellationToken)
        {
            var result = new List<Entity>();

            if (dtoEntities == null || !dtoEntities.Any())
            {
                return result;
            }

            var allEntityTypes = this.modelRepository.GetAllEntityTypes();
            var allLinkTypes = this.modelRepository.GetAllLinkTypes();

            ParallelTaskExecutor.BatchExecute<DtoEntity, IList<Entity>>(
                dtoEntities,
                batchSize: 100,
                transform: batchEntities => DtoFactory.EntitiesFromDtos(batchEntities.ToList(), allEntityTypes, allLinkTypes),
                action: entities => result.AddRange(entities),
                cancellationToken);

            return result;
        }

        private bool EntityTypeIsChild(string linkEntityTypeId, string entityTypeId, List<CultureInfo> languages) =>
            this.dataContext.GetLinkType(linkEntityTypeId, languages)?.TargetEntityTypeId == entityTypeId;

        private List<InRiverEntity> GetFirstRelatedInRiverEntities(
            IEnumerable<Entity> entities,
            MapManager mapManager,
            List<CultureInfo> languages,
            CancellationToken cancellationToken)
        {
            var result = new List<InRiverEntity>();
            var workareaEntities = entities.ToList();
            var firstLinkEntityTypeId = mapManager.FirstLinkEntityTypeId;
            var workareaEntityTypeId = mapManager.WorkareaEntityTypeId;
            var mainIsChildToFirst = this.EntityTypeIsChild(firstLinkEntityTypeId, workareaEntityTypeId, languages);

            this.SetLinksForEntities(workareaEntities, firstLinkEntityTypeId, cancellationToken);

            if (mapManager.OutputEntityTypeId == workareaEntityTypeId)
            {
                foreach (var entity in workareaEntities)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var inRiverEntity = MapManager.GetInRiverEntity(entity);
                    inRiverEntity.RelatedEntities = new List<InRiverEntity>();

                    foreach (var linkedEntity in entity.Links.Select(link => mainIsChildToFirst ? link.Source : link.Target))
                    {
                        linkedEntity.Fields = this.dataContext.GetFullFieldsForEntity(linkedEntity);
                        inRiverEntity.RelatedEntities.Add(MapManager.GetInRiverEntity(linkedEntity));
                    }

                    result.Add(inRiverEntity);
                }
            }
            else
            {
                foreach (var entity in workareaEntities)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var relatedInRiverEntity = MapManager.GetInRiverEntity(entity);

                    foreach (var outputEntity in entity.Links.Select(link => mainIsChildToFirst ? link.Source : link.Target))
                    {
                        outputEntity.Fields = this.dataContext.GetFullFieldsForEntity(outputEntity);

                        var inRiverEntity = MapManager.GetInRiverEntity(outputEntity);
                        inRiverEntity.RelatedEntities = new List<InRiverEntity> { relatedInRiverEntity };

                        result.Add(inRiverEntity);
                    }
                }
            }

            return result;
        }

        private List<InRiverEntity> GetSecondRelatedInRiverEntities(
            IEnumerable<Entity> entities,
            MapManager mapManager,
            List<CultureInfo> languages,
            CancellationToken cancellationToken)
        {
            var result = new List<InRiverEntity>();
            var outputEntityTypeId = mapManager.OutputEntityTypeId;
            var workareaEntities = entities.ToList();

            var mainIsChildToFirst = this.EntityTypeIsChild(mapManager.FirstLinkEntityTypeId, mapManager.WorkareaEntityTypeId, languages);
            var firstIsChildToSecond = this.EntityTypeIsChild(mapManager.SecondLinkEntityTypeId, mapManager.FirstRelatedEntityTypeId, languages);

            this.SetLinksForEntities(workareaEntities, mapManager.FirstLinkEntityTypeId, cancellationToken);

            foreach (var entity in workareaEntities)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var relatedEntities = new List<InRiverEntity>();
                var links = mainIsChildToFirst ? entity.InboundLinks : entity.OutboundLinks;

                var firstRelatedEntities = links.Select(link => mainIsChildToFirst ? link.Source : link.Target).ToList();
                this.SetLinksForEntities(firstRelatedEntities, mapManager.SecondLinkEntityTypeId, cancellationToken);

                foreach (var firstRelatedEntity in firstRelatedEntities)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var secondRelatedEntities = firstRelatedEntity.Links.Select(relatedLink =>
                        firstIsChildToSecond ? relatedLink.Source : relatedLink.Target);
                    firstRelatedEntity.Links = null;
                    firstRelatedEntity.Fields = this.dataContext.GetFullFieldsForEntity(firstRelatedEntity);

                    foreach (var secondRelatedEntity in secondRelatedEntities)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        secondRelatedEntity.Fields = this.dataContext.GetFullFieldsForEntity(secondRelatedEntity);

                        if (outputEntityTypeId == mapManager.SecondRelatedEntityTypeId)
                        {
                            var inRiverEntity = MapManager.GetInRiverEntity(secondRelatedEntity);
                            inRiverEntity.RelatedEntities = new List<InRiverEntity>
                            {
                                MapManager.GetInRiverEntity(entity),
                                MapManager.GetInRiverEntity(firstRelatedEntity)
                            };
                            result.Add(inRiverEntity);
                        }
                        else
                        {
                            relatedEntities.Add(MapManager.GetInRiverEntity(secondRelatedEntity));
                        }
                    }

                    if (outputEntityTypeId == mapManager.FirstRelatedEntityTypeId)
                    {
                        var inRiverEntity = MapManager.GetInRiverEntity(firstRelatedEntity);
                        relatedEntities.Insert(0, MapManager.GetInRiverEntity(entity));
                        inRiverEntity.RelatedEntities = relatedEntities;
                        result.Add(inRiverEntity);
                        relatedEntities = new List<InRiverEntity>();

                    }
                    else
                    {
                        relatedEntities.Add(MapManager.GetInRiverEntity(firstRelatedEntity));
                    }
                }

                if (outputEntityTypeId == mapManager.WorkareaEntityTypeId)
                {
                    var inRiverEntity = MapManager.GetInRiverEntity(entity);
                    inRiverEntity.RelatedEntities = relatedEntities;
                    result.Add(inRiverEntity);
                }

            }

            return result;
        }

        private void SetLinksForEntities(IList<Entity> workareaEntities, string linkTypeId, CancellationToken cancellationToken)
        {
            const int batchSize = 10;
            var allEntityTypes = this.modelRepository.GetAllEntityTypes();
            var allLinkTypes = this.modelRepository.GetAllLinkTypes();

            ParallelTaskExecutor.BatchExecute<Entity, (IList<Entity> entities, IList<DtoLink> links)>(
                workareaEntities,
                batchSize: batchSize,
                transform: entities => {
                    var entityIds = entities.Select(entity => entity.Id).ToList();
                    var entityLinks = this.dataContext.GetLinksForEntitiesByLinkType(entityIds, linkTypeId);
                    return (entities, entityLinks);
                },
                action: result => {
                    var (entities, links) = result;
                    var sourceLinks = links.GroupBy(l => l.Source.Id).ToDictionary(x => x.Key, x => x.ToList());
                    var targetLinks = links.GroupBy(l => l.Target.Id).ToDictionary(x => x.Key, x => x.ToList());

                    foreach (var entity in entities)
                    {
                        var sourceLinksExist = sourceLinks.TryGetValue(entity.Id, out var entitySourceLinks);
                        var targetLinksExist = targetLinks.TryGetValue(entity.Id, out var entityTargetLinks);

                        var currentEntityLinks = (sourceLinksExist ? entitySourceLinks : new List<DtoLink>())
                            .Concat(targetLinksExist ? entityTargetLinks : new List<DtoLink>())
                            .ToList();
                        entity.Links = DtoFactory.LinksFromDtos(currentEntityLinks, allLinkTypes, allEntityTypes);
                        entity.LoadLevel = LoadLevel.DataAndLinks;
                    }
                },
                cancellationToken);
        }

        private static List<string> GetEntityTypesWithMappedImageFields(MapManager mapManager)
        {
            var entityTypes = new List<string>();

            if (mapManager.IsImageMapped(mapManager.WorkareaEntityTypeId))
            {
                entityTypes.Add(mapManager.WorkareaEntityTypeId);
            }

            if (mapManager.IsImageMapped(mapManager.FirstRelatedEntityTypeId))
            {
                entityTypes.Add(mapManager.FirstRelatedEntityTypeId);
            }

            if (mapManager.IsImageMapped(mapManager.SecondRelatedEntityTypeId))
            {
                entityTypes.Add(mapManager.SecondRelatedEntityTypeId);
            }

            return entityTypes;
        }

        private void AddImageFields(
            IList<InRiverEntity> inRiverEntities,
            ICollection<string> entityTypesWithImagesMapped,
            List<CultureInfo> languages,
            CancellationToken cancellationToken)
        {
            var inRiverEntitiesWithImagesMapped = inRiverEntities
                .Where(entity => entityTypesWithImagesMapped.Contains(entity.EntityTypeId));
            var relatedEntitiesWithImagesMapped = inRiverEntities
                .SelectMany(entity => entity.RelatedEntities == null || !entity.RelatedEntities.Any()
                    ? new List<InRiverEntity>()
                    : entity.RelatedEntities.GroupBy(e => e.Id)
                        .Select(entities => entities.First()))
                .Where(entity => entityTypesWithImagesMapped.Contains(entity.EntityTypeId));
            var entitiesWithImagesMapped = inRiverEntitiesWithImagesMapped
                .Concat(relatedEntitiesWithImagesMapped)
                .ToList();

            this.AddImageFieldsToEntities(entitiesWithImagesMapped, languages, cancellationToken);
        }

        private void AddImageFieldsToEntities(IList<InRiverEntity> inRiverEntities, List<CultureInfo> languages, CancellationToken cancellationToken)
        {
            var entityIds = inRiverEntities.Select(entity => entity.Id).ToList();
            var allResources = this.dataContext.GetResourceLinksForEntities(entityIds);
            var allResourceIds = allResources.Select(link => link.Target.Id).ToList();
            var allResourceEntities = this.GetEntitiesFromDtos(this.GetSyndicationDtoEntities(allResourceIds, cancellationToken), cancellationToken);
            cancellationToken.ThrowIfCancellationRequested();

            this.resourceExportService.ValidateResourceFiles(allResourceEntities);

            var resourceEntitiesDict = allResourceEntities
                .GroupBy(entity => entity.Id)
                .ToDictionary(x => x.Key, x => x.ToList());

            var entityResources = allResources
                .GroupBy(link => link.Source.Id)
                .ToDictionary(x => x.Key, x => x.ToList());

            foreach (var inRiverEntity in inRiverEntities)
            {
                cancellationToken.ThrowIfCancellationRequested();

                object mainImageFieldData = null;
                object mainImageFieldUrlData = null;

                var entityResourcesExist = entityResources
                    .TryGetValue(inRiverEntity.Id, out var entityResourceLinks);
                var resourceIds = entityResourcesExist ? entityResourceLinks.Select(link => link.Target.Id).ToHashSet() : new HashSet<int>();

                var resourceEntities = resourceEntitiesDict.Keys.Where(key => resourceIds.Contains(key))
                    .SelectMany(x => resourceEntitiesDict.GetValueOrDefault(x)).ToList();

                if (inRiverEntity.MainPictureId != null)
                {
                    var mainImage = resourceEntities.FirstOrDefault(x => x.MainPictureId == inRiverEntity.MainPictureId);
                    if (mainImage != null)
                    {
                        mainImageFieldData = MapManager.GetImageAsJson(mainImage, languages);
                        mainImageFieldUrlData = mainImage.MainPictureUrl;
                    }
                }

                object imagesFieldData = MapManager.GetImagesWithLinksAsJson(entityResourceLinks, resourceEntities, languages);

                AddImageFieldData(inRiverEntity, "Image", mainImageFieldData);
                AddImageFieldData(inRiverEntity, "ImageURL", mainImageFieldUrlData);
                AddImageFieldData(inRiverEntity, "Images", imagesFieldData);
            }
        }

        private static void AddImageFieldData(InRiverEntity inRiverEntity, string imageFieldType, object data)
        {
            var inRiverField = new InRiverField
            {
                FieldType = new InRiverFieldType
                {
                    FieldTypeId = $"{inRiverEntity.EntityTypeId}{imageFieldType}",
                    DataType = "String"
                },
                Data = data
            };
            if (inRiverEntity.Fields.All(x => x.FieldType.FieldTypeId != inRiverField.FieldType.FieldTypeId))
            {
                inRiverEntity.Fields.Add(inRiverField);
            }
        }
    }
}
