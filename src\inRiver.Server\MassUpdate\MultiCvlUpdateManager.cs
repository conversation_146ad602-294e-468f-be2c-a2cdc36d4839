namespace inRiver.Server.MassUpdate
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Core.Enum;
    using inRiver.Core.Models.inRiver;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Util;
    using inRiver.Server.FieldChange;
    using inRiver.Server.Repository;

    public class MultiCvlUpdateManager
    {
        private readonly IDictionary<UpdateApproach, ICalculateMultiCvlValueStrategy> calculateMultiCvlValueStrategies;

        public MultiCvlUpdateManager(IFieldRepository fieldRepository)
        {
            this.calculateMultiCvlValueStrategies = new Dictionary<UpdateApproach, ICalculateMultiCvlValueStrategy>
            {
                { UpdateApproach.Append, new AppendMultiCvlValueStrategy(fieldRepository) },
                { UpdateApproach.Remove, new RemoveMultiCvlValueStrategy(fieldRepository) },
                { UpdateApproach.Replace, new ReplaceMultiCvlValueStrategy() }
            };
        }

        public string CalculateMultiCvlValue(UpdateApproach? updateApproach, string newValue, int entityId, string fieldTypeId)
        {
            if (!updateApproach.HasValue)
            {
                throw new ArgumentNullException(nameof(updateApproach));
            }

            _ = this.calculateMultiCvlValueStrategies.TryGetValue(updateApproach.Value, out var calculateMultiCvlValueStrategy);
            return calculateMultiCvlValueStrategy == null
                ? throw new ArgumentException("Unsupported UpdateApproach.", nameof(updateApproach))
                : calculateMultiCvlValueStrategy.Calculate(newValue, entityId, fieldTypeId);
        }

        public IList<DtoField> ProcessEntityFields(IList<UpdateFieldModel> fields, Remoting.Objects.EntityType entityType, int entityId)
        {
            var entityFields = new List<DtoField>();
            if (fields == null)
            {
                return entityFields;
            }

            foreach (var massUpdateField in fields)
            {
                var fieldType = entityType?.FieldTypes.FirstOrDefault(fieldTypeModel => fieldTypeModel.Id == massUpdateField.Field.FieldTypeId);
                if (fieldType == null)
                {
                    throw new ArgumentException($"Entity with id {entityId} does not contain field type with id {massUpdateField.Field.FieldTypeId}");
                }

                // For multi-values fields we need to calculate a new value depending on the update approach.
                entityFields.Add(fieldType.Multivalue && massUpdateField.UpdateApproach.HasValue && !Utility.StringIsInriverExpression(fieldType.ExpressionSupport, massUpdateField.Field.Data as string)
                    ? this.ProcessMultiValueFieldData(massUpdateField, entityId)
                    : massUpdateField.Field);
            }

            return entityFields;
        }

        private DtoField ProcessMultiValueFieldData(UpdateFieldModel massUpdateField, int entityId)
        {
            var newFieldData = this.CalculateMultiCvlValue(
                massUpdateField.UpdateApproach,
                massUpdateField.Field.Data,
                entityId,
                massUpdateField.Field.FieldTypeId);
            return new DtoField
            {
                FieldTypeId = massUpdateField.Field.FieldTypeId,
                DataType = massUpdateField.Field.DataType,
                EntityId = massUpdateField.Field.EntityId,
                LastModified = massUpdateField.Field.LastModified,
                Revision = massUpdateField.Field.Revision,
                Data = newFieldData
            };
        }
    }
}
