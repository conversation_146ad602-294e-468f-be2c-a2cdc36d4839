namespace inRiver.iPMC.Persistance.Tests.Config
{
    public class Database
    {
        public static string DataConnectionString =>
            "Data Source=tcp:pmceuwlocalsql01.database.windows.net,1433;Initial Catalog=localintegrationtestlongrunningjob;User Id=pmcsqllocalsvc;Password=********************;Max Pool Size=180;ConnectRetryCount=4;ConnectRetryInterval=15;MultipleActiveResultSets=False;Connection Timeout=30";

        public static string LogConnectionString =>
            "Data Source=tcp:pmceuwlocalsql01.database.windows.net,1433;Initial Catalog=localeuwloglocal;User Id=pmcsqllocalsvc;Password=********************;Max Pool Size=180;ConnectRetryCount=4;ConnectRetryInterval=15;MultipleActiveResultSets=False;Connection Timeout=30";
    }
}
