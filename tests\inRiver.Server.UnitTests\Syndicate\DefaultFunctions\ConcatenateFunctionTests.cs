namespace inRiver.Server.UnitTests.Syndicate.DefaultFunctions
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using FluentAssertions;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Script;
    using inRiver.Server.Syndication.Script.DefaultFunctions;
    using Newtonsoft.Json;
    using Xunit;

    public class ConcatenateFunctionTests
    {
        public static readonly object[][] OldConcatFunctionValues =
        {
            new object[] { "Product name string", "ProductNameString", string.Empty, "null,\"ProductNameLS\",\"en\"", "Product name stringen value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "\",\"", "\"en\",\"ProductNameLS\",\"en\"", "en value,en value" },
            new object[] { "Product name string", "ProductNameString", "\"-abc-\"", "null,\"ProductNameLS\",\"en\"", "Product name string-abc-en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "\",\"", "null,\"ProductNameLS\",null", "ar value,ar value" },
            new object[] { "Product name string", "ProductNameString", "\",\"", "null,\"ProductNameEmpty\",null", "Product name string" },
            new object[] { null, "ProductNameEmpty", "\",\"", "null,\"ProductNameEmpty\",null", string.Empty },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "\",\"", "\"fr\",\"ProductNameLS\",\"en\"", "en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "\",\"", "\"en\",\"ProductNameLS\",\"fr\"", "en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "\",\"", "\"fr\",\"ProductNameLS\",\"fr\"", string.Empty },
        };

        public static readonly object[][] NewConcatFunctionValues =
        {
            new object[] { "Product name string", "ProductNameString", null, "-", null, null, "/", null, "en", "ProductNameLS", "Product name string-en value/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", null, "-", null, "sv", "/", null, "en", "ProductNameLS", "sv value-en value/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", null, null, null, null, "/", null, null, "ProductNameLS", "ar valuear value/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "abc", "-", null, null, "/", null, "en", "ProductNameLS", "abcar value-en value/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "abc", "-", null, null, "/", null, null, "ProductNameEmpty", "abcar value-/" },
            new object[] { null, "ProductNameEmpty", "abc", "-", null, null, "/", null, null, "ProductNameEmpty", "abc-/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "abc", "-", null, "fr", "/", null, "en", "ProductNameLS", "abc-en value/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "abc", "-", null, "en", "/", null, "fr", "ProductNameLS", "abcen value-/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "abc", "-", null, "fr", "/", null, "fr", "ProductNameLS", "abc-/" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", null, null, null, "fr", null, null, "fr", "ProductNameLS", string.Empty },
            new object[] { "black", "ProductNameCVLSingle", "abc", "-", "stringCVL", null, "/", "localeStringCVL", "en", "ProductNameCVLMulti", "abcBlack-en black;en green/" },
            new object[] { "black", "ProductNameCVLSingle", "abc", "-", "localeStringCVL", "en", "/", "localeStringCVL", "fr", "ProductNameCVLMulti", "abcen black-/" },
            new object[] { "black", "ProductNameCVLSingle", "abc", "-", "stringCVL", null, "/", "localeStringCVL", "sv", "ProductNameCVLMulti", "abcBlack-sv black/" },
            new object[] { "black;green", "ProductNameCVLMulti", "abc", "-", "localeStringCVL", null, "/", null, null, "ProductNameString", "abcar black;ar green-Product name string/" },
            new object[] { null, "ProductNameCVLMulti", "abc", "-", "localeStringCVL", null, "/", null, null, "ProductNameString", "abc-Product name string/" },
        };

        [Theory]
        [MemberData(nameof(OldConcatFunctionValues))]
        public void OldConcatenate_ShouldReturnConcatValue(object mainValue, string mainValueFieldTypeId, string separator, string values, string expectedResult)
        {
            // Arrange
            var json = $"{{\"transformations\":[{{\"function\":{{\"name\":\"Concatenate\",\"args\":[{separator}],\"values\":[{values}]}}}}]}}";
            var transformationManager = new TransformationManager(json, context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = mainValueFieldTypeId
            };

            var concatenateExecutor = new ConcatenateFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: null);

            // Act
            var result = concatenateExecutor.Execute();

            // Assert
            result.Should().Be(expectedResult);
        }

        [Fact]
        public void OldConcatenate_WrongNumberOfElements_ShouldThrowException()
        {
            // Arrange
            var mainValue = "Product name string";
            var json = "{\"transformations\":[{\"function\":{\"name\":\"Concatenate\",\"args\":[\",\"],\"values\":[null,\"en\"]}}]}";
            var transformationManager = new TransformationManager(json, context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = "ProductNameString"
            };

            var concatenateExecutor = new ConcatenateFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: null);

            // Act
            Action act = () => concatenateExecutor.Execute();

            // Assert
            act.Should().Throw<SyndicateException>()
                .WithMessage("Data structure of concatenate function should contain 3 elements.");
        }

        [Theory]
        [MemberData(nameof(NewConcatFunctionValues))]
        public void NewConcatenate_ShouldReturnConcatValue(
            object mainValue,
            string mainFieldTypeId,
            string prefix,
            string separator1,
            string cvl1,
            string language1,
            string separator2,
            string cvl2,
            string language2,
            string fieldTypeId2,
            string expectedResult)
        {
            // Arrange
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            ExportManager.CvlIdsDictionary = new ConcurrentDictionary<string, string>();
            var transformJson = new TransformationJson
            {
                Transformation = new List<Transformation>
                {
                    new Transformation
                    {
                        Function = new Function
                        {
                            Name = "Concatenate",
                            Args = Array.Empty<string>(),
                            Values = new[]
                            {
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 0,
                                    Separator = prefix,
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 1,
                                    Separator = separator1,
                                    CvlId = cvl1,
                                    Language = language1
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 2,
                                    Separator = separator2,
                                    FieldTypeId = fieldTypeId2,
                                    CvlId = cvl2,
                                    Language = language2
                                }),
                            }
                        }
                    }
                }
            };
            var transformationManager = new TransformationManager(JsonConvert.SerializeObject(transformJson), context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = mainFieldTypeId
            };

            var concatenateExecutor = new ConcatenateFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: DefaultFunctionsData.RequestContext);

            // Act
            var result = concatenateExecutor.Execute();

            // Assert
            result.Should().Be(expectedResult);
        }

        [Fact]
        public void NewConcatenate_CVLIdNotSpecified_ShouldGetCvlIdByFieldTypeId()
        {
            // Arrange
            const string mainValue = "green;black";
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            ExportManager.CvlIdsDictionary = new ConcurrentDictionary<string, string>();
            var transformJson = new TransformationJson
            {
                Transformation = new List<Transformation>
                {
                    new Transformation
                    {
                        Function = new Function
                        {
                            Name = "Concatenate",
                            Args = Array.Empty<string>(),
                            Values = new[]
                            {
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 0,
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 1,
                                    Separator = "-",
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 2,
                                    FieldTypeId = "ProductNameString"
                                }),
                            }
                        }
                    }
                }
            };
            var transformationManager = new TransformationManager(JsonConvert.SerializeObject(transformJson), context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = "ProductNameCVLMulti"
            };

            var concatenateExecutor = new ConcatenateFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: DefaultFunctionsData.RequestContext);

            // Act
            var result = concatenateExecutor.Execute();

            // Assert
            result.Should().Be("Green;Black-Product name string");
        }

        [Fact]
        public void NewConcatenate_CVLIdNotFound_ShouldThrowException()
        {
            // Arrange
            const string mainValue = "black;green";
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            ExportManager.CvlIdsDictionary = new ConcurrentDictionary<string, string>();
            var transformJson = new TransformationJson
            {
                Transformation = new List<Transformation>
                {
                    new Transformation
                    {
                        Function = new Function
                        {
                            Name = "Concatenate",
                            Args = Array.Empty<string>(),
                            Values = new[]
                            {
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 0,
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 1,
                                    Separator = "-",
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 2,
                                    FieldTypeId = "ProductNameString"
                                }),
                            }
                        }
                    }
                }
            };
            var transformationManager = new TransformationManager(JsonConvert.SerializeObject(transformJson), context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = "ProductNameCVLSingle"
            };

            var concatenateExecutor = new ConcatenateFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: DefaultFunctionsData.RequestContext);

            // Act
            Action act = () => concatenateExecutor.Execute();

            // Assert
            act.Should().Throw<SyndicateException>()
                .WithMessage("CVL id is not specified for field of type CVL.");
        }

        [Fact]
        public void NewConcatenate_InvalidCVLKey_ShouldThrowException()
        {
            // Arrange
            const string mainValue = "black;yellow";
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            ExportManager.CvlIdsDictionary = new ConcurrentDictionary<string, string>();
            var transformJson = new TransformationJson
            {
                Transformation = new List<Transformation>
                {
                    new Transformation
                    {
                        Function = new Function
                        {
                            Name = "Concatenate",
                            Args = Array.Empty<string>(),
                            Values = new[]
                            {
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 0,
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 1,
                                    Separator = "-",
                                    CvlId = "stringCVL"
                                }),
                                JsonConvert.SerializeObject(new ConcatenateFunctionField
                                {
                                    Index = 2,
                                    FieldTypeId = "ProductNameString"
                                }),
                            }
                        }
                    }
                }
            };
            var transformationManager = new TransformationManager(JsonConvert.SerializeObject(transformJson), context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = "ProductNameCVLMulti"
            };

            var concatenateExecutor = new ConcatenateFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: DefaultFunctionsData.RequestContext);

            // Act
            Action act = () => concatenateExecutor.Execute();

            // Assert
            act.Should().Throw<SyndicateException>()
                .WithMessage("Concatenate function - Invalid CVL key: yellow, CVL id: stringCVL.");
        }
    }
}
