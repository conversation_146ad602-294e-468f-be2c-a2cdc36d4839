namespace inRiver.Core.Http
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models.Augmenta;
    using inRiver.Remoting.Objects;
    using Models;

    public interface IAugmentaHttpClient
    {
        Task<PreProcessedEntityResult> PreProcessAsync(
            string eventName,
            Entity entity,
            IReadOnlyList<EntityType> entityTypes,
            string customerSafeName,
            string environmentSafeName,
            CancellationToken cancellationToken);

        Task<PreProcessedLinkResult> PreProcessAsync(
            string eventName,
            Link link,
            string customerSafeName,
            string environmentSafeName,
            CancellationToken cancellationToken);
    }
}
