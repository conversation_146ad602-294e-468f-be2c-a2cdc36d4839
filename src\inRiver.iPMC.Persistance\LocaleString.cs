namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Collections.Generic;
    using System.Data.SqlTypes;
    using System.Globalization;
    using System.Xml.Linq;
    using Newtonsoft.Json;

    public class LocaleString
    {
        private Dictionary<CultureInfo, string> stringMap;

        public Dictionary<CultureInfo, string> StringMap => this.stringMap ?? (this.stringMap = new Dictionary<CultureInfo, string>());

        public LocaleString()
        {
        }

        public LocaleString(SqlXml sqlXml)
        {
            using (var xmlReader = sqlXml.CreateReader())
            {
                xmlReader.MoveToContent();

                if (xmlReader.EOF)
                {
                    return;
                }

                var rootElement = XElement.Load(xmlReader);

                foreach (var valueElement in rootElement.Elements())
                {
                    var language = valueElement.Attribute("language")?.Value;
                    if (language != null)
                    {
                        this[CultureInfo.GetCultureInfo(language)] = valueElement.Value;
                    }
                }
            }
        }

        [JsonIgnore]
        public List<CultureInfo> Languages
        {
            get
            {
                return new List<CultureInfo>(this.StringMap.Keys);
            }

            set
            {
                lock (this)
                {
                    foreach (CultureInfo cultureInfo in value)
                    {
                        this.StringMap.Add(cultureInfo, string.Empty);
                    }
                }
            }
        }

        public string this[CultureInfo ci]
        {
            get
            {
                if (ci != null && this.StringMap.ContainsKey(ci))
                {
                    return this.StringMap[ci];
                }

                return null;
            }

            set
            {
                lock (this)
                {
                    this.StringMap[ci] = value;
                }
            }
        }

        public static bool IsNullOrEmpty(LocaleString localeString)
        {
            if (localeString?.Languages == null)
            {
                return true;
            }

            if (localeString.Languages.Count == 0)
            {
                return true;
            }

            foreach (CultureInfo ci in localeString.Languages)
            {
                if (!string.IsNullOrEmpty(localeString[ci]))
                {
                    return false;
                }
            }

            return true;
        }

        public override string ToString()
        {
            lock (this)
            {
                if (this.Languages == null)
                {
                    return base.ToString();
                }

                if (this.Languages.Count == 0)
                {
                    return base.ToString();
                }

                string returnString = string.Empty;

                List<CultureInfo> cultureInfos = new List<CultureInfo>();

                cultureInfos.AddRange(this.StringMap.Keys);
                cultureInfos.Sort(new CultureInfoSorter());

                foreach (CultureInfo ci in cultureInfos)
                {
                    returnString += string.Format("{0}:{1},", ci, this.StringMap[ci]);
                }

                return returnString.Substring(0, returnString.Length - 1);
            }
        }

        private class CultureInfoSorter : IComparer<CultureInfo>
        {
            public int Compare(CultureInfo x, CultureInfo y)
            {
                return string.Compare(x.ToString(), y.ToString(), StringComparison.Ordinal);
            }
        }
    }
}
