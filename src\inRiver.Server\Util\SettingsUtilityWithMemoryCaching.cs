namespace inRiver.Server.Util
{
    using inRiver.Core.Models;
    using Microsoft.Extensions.Caching.Memory;
    using System.ServiceModel;
    using System;
    using inRiver.Configuration.Core.Repository;
    using Log;
    using Request;

    public class SettingsUtilityWithMemoryCaching
    {
        private readonly IMemoryCache cache;
        public string Caller { get; }

        protected RequestContext Context { get; }

        public SettingsUtilityWithMemoryCaching(RequestContext requestContext, IMemoryCache memoryCache, string caller)
        {
            this.cache = memoryCache;
            this.Caller = caller;
            this.Context = requestContext;
        }

        public string GetEnvironmentSetting(string name, TimeSpan? timeSpan = null)
        {
            try
            {
                string correspondingValue = string.Empty;
                try
                {
                    this.cache.TryGetValue(this.Context.EnvironmentId + "_" + name, out correspondingValue);
                    if (!string.IsNullOrWhiteSpace(correspondingValue))
                    {
                        return correspondingValue; // return from cache
                    }
                }
                catch (Exception ex)
                {
                    this.Context.LogNear(LogLevel.Error, $"An unknown server error occurred when trying to get environmentSetting from caching for Customer/Env: {this.Context.CustomerSafeName}/{this.Context.EnvironmentSafeName}", ex);
                }

                var apiCaller = new ApiCaller { Module = this.Caller, Username = this.Context.Username };

                var settingsRepository = new EnvironmentSettingsRepository(
                    new inRiver.Configuration.Core.Persistance.EnvironmentSettingsPersistance(
                        this.Context.ConfigurationConnectionString,
                        this.Context.ReadOnlyConfigDatabaseConnectionString,
                        new Log.CommonLogging("system", this.Context.LogConnectionString),
                        apiCaller),
                    apiCaller);

                var environmentSetting = settingsRepository.GetEnvironmentSetting(name, this.Context.EnvironmentId);
                if (environmentSetting == null)
                {
                    Serilog.Log.Warning($"Could not find Env Setting : {name} for env {this.Context.EnvironmentId} in DB");
                    correspondingValue = string.Empty;
                    this.cache.Set(this.Context.EnvironmentId + "_" + name, correspondingValue);
                }
                else
                {
                    this.cache.Set(this.Context.EnvironmentId + "_" + environmentSetting.Name, environmentSetting.Value, new MemoryCacheEntryOptions().SetAbsoluteExpiration(timeSpan ?? new TimeSpan(0, 0, 0, 60)));
                    correspondingValue = environmentSetting.Value;
                }

                return correspondingValue;
            }
            catch (FaultException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.Context.LogNear(LogLevel.Error, $"An unknown server error occurred when trying to get environmentSetting for Customer/Env: {this.Context.CustomerSafeName}/{this.Context.EnvironmentSafeName}", ex);
                throw new FaultException($"An unknown server error has occurred");
            }
        }
    }
}

