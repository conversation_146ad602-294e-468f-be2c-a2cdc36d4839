namespace inRiver.Server.EventPublishing
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Net;
    using System.Net.Http;
    using inRiver.Core.Models;
    using inRiver.Core.Persistance;
    using inRiver.Remoting.Connect;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;
    using Newtonsoft.Json;
    using Polly;
    using Polly.Extensions.Http;

    public static class EventPublisher
    {
        // ReSharper disable once InconsistentNaming
        public static void NotifyCVLValueAdded(RequestContext context, string cvlId, string cvlValueKey)
        {
            var action = CVLValueEvent.CVLValueAdded;
            var data = new Dictionary<string, string>()
            {
               { "cvlId", cvlId },
               { "cvlValueKey", cvlValueKey }
            };

            PublishEvent(context, action, data, context.Username);
        }

        // ReSharper disable once InconsistentNaming
        public static void NotifyCVLValueUpdated(RequestContext context, string cvlId, string cvlValueKey)
        {
            var action = CVLValueEvent.CVLValueUpdated;
            var data = new Dictionary<string, string>()
            {
               { "cvlId", cvlId },
               { "cvlValueKey", cvlValueKey }
            };

            PublishEvent(context, action, data, context.Username);
        }

        // ReSharper disable once InconsistentNaming
        public static void NotifyCVLValueDeleted(RequestContext context, string cvlId, string cvlValueKey)
        {
            var action = CVLValueEvent.CVLValueDeleted;
            var data = new Dictionary<string, string>()
            {
               { "cvlId", cvlId },
               { "cvlValueKey", cvlValueKey }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyAllCVLValuesDeletedForCVL(RequestContext context, string cvlId)
        {
            var action = CVLValueEvent.CVLValueDeletedAll;
            var data = new Dictionary<string, string>()
            {
               { "cvlId", cvlId }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityAdded(RequestContext context, Entity entity)
        {
            new CompletenessActionManager(context).EntityCreated(entity.Id);

            var action = EntityEvent.EntityAdded;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entity.Id.ToString() },
               { "entityTypeId", entity.EntityType.Id }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityDeleted(RequestContext context, int entityId, string entityTypeId, string eventData)
        {
            var action = EntityEvent.EntityDeleted;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "data", eventData },
               { "entityTypeId", entityTypeId }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityUpdated(RequestContext context, int entityId, string entityTypeId, string eventData)
        {
            var action = EntityEvent.EntityUpdated;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "data", eventData },
               { "entityTypeId", entityTypeId }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityLocked(RequestContext context, int entityId, string entityTypeId)
        {
            var action = EntityEvent.EntityLocked;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "entityTypeId", entityTypeId }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityUnlocked(RequestContext context, int entityId, string entityTypeId)
        {
            var action = EntityEvent.EntityUnLocked;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "entityTypeId", entityTypeId }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationFieldUpdated(RequestContext context, int entityId, string entityTypeId, string fieldName)
        {
            var action = EntityEvent.EntitySpecificationFieldUpdated;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "entityTypeId", entityTypeId },
               { "fieldName", fieldName },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationFieldAdded(RequestContext context, int entityId, string entityTypeId, string fieldName)
        {
            var action = EntityEvent.EntitySpecificationFieldAdded;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "entityTypeId", entityTypeId },
               { "fieldName", fieldName },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyFieldSetUpdated(RequestContext context, int entityId, string entityTypeId, string fieldSetId)
        {
            var action = EntityEvent.EntityFieldsetUpdated;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "entityTypeId", entityTypeId },
               { "fieldSetId", fieldSetId },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityCommentAdded(RequestContext context, int entityId, string entityTypeId, int commentId)
        {
            var action = EntityEvent.EntityCommentAdded;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "entityTypeId", entityTypeId },
               { "commentId", commentId.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyLinkActivated(RequestContext context, DtoLink link)
        {
            var action = LinkEvent.LinkActivated;
            var data = new Dictionary<string, string>()
            {
               { "linkId", link.Id.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyLinkInactivated(RequestContext context, DtoLink link)
        {
            var action = LinkEvent.LinkInactivated;
            var data = new Dictionary<string, string>()
            {
               { "linkId", link.Id.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyLinkAdded(RequestContext context, DtoLink link)
        {
            var action = LinkEvent.LinkAdded;
            var data = new Dictionary<string, string>()
            {
               { "linkId", link.Id.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyLinkDeleted(RequestContext context, DtoLink link)
        {
            var action = LinkEvent.LinkDeleted;
            var data = new Dictionary<string, string>()
            {
               { "linkId", link.Id.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyLinkUpdated(RequestContext context, DtoLink link)
        {
            var action = LinkEvent.LinkUpdated;
            var data = new Dictionary<string, string>()
            {
               { "linkId", link.Id.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyCompletenessRuleComplete(RequestContext context, int entityId, int definitionId, int groupId, int ruleId)
        {
            new CompletenessActionManager(context).CompletenessRuleComplete(entityId, definitionId, groupId, ruleId);

            var action = CompletenessEvent.RuleComplete;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "definitionId", definitionId.ToString() },
               { "groupId", groupId.ToString() },
               { "ruleId", ruleId.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyCompletenessRuleIncomplete(RequestContext context, int entityId, int definitionId, int groupId, int ruleId)
        {
            var action = CompletenessEvent.RuleIncomplete;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "definitionId", definitionId.ToString() },
               { "groupId", groupId.ToString() },
               { "ruleId", ruleId.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityComplete(RequestContext context, int entityId)
        {
            new CompletenessActionManager(context).EntityComplete(entityId);

            var action = CompletenessEvent.EntityComplete;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyEntityInComplete(RequestContext context, int entityId)
        {
            var action = CompletenessEvent.EntityInComplete;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyCompletenessGroupComplete(RequestContext context, int entityId, int definitionId, int groupId)
        {
            new CompletenessActionManager(context).CompletenessGroupComplete(entityId, definitionId, groupId);

            var action = CompletenessEvent.GroupComplete;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "definitionId", definitionId.ToString() },
               { "groupId", groupId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyCompletenessGroupIncomplete(RequestContext context, int entityId, int definitionId, int groupId)
        {
            var action = CompletenessEvent.GroupIncomplete;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "definitionId", definitionId.ToString() },
               { "groupId", groupId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationTemplateFieldAdded(RequestContext context, string templateId)
        {
            var action = SpecificationEvent.SpecificationTemplateFieldAdded;
            var data = new Dictionary<string, string>()
            {
               { "templateId", templateId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationTemplateFieldUpdated(RequestContext context, string templateId)
        {
            var action = SpecificationEvent.SpecificationTemplateFieldUpdated;
            var data = new Dictionary<string, string>()
            {
               { "templateId", templateId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationTemplateFieldDeleted(RequestContext context, string templateId)
        {
            var action = SpecificationEvent.SpecificationTemplateFieldDeleted;
            var data = new Dictionary<string, string>()
            {
               { "templateId", templateId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationCategoryAdded(RequestContext context, string categoryId)
        {
            var action = SpecificationEvent.SpecificationCategoryAdded;
            var data = new Dictionary<string, string>()
            {
               { "categoryId", categoryId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationCategoryUpdated(RequestContext context, string categoryId)
        {
            var action = SpecificationEvent.SpecificationCategoryUpdated;
            var data = new Dictionary<string, string>()
            {
               { "categoryId", categoryId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySpecificationCategoryDeleted(RequestContext context, string categoryId)
        {
            var action = SpecificationEvent.SpecificationCategoryDeleted;
            var data = new Dictionary<string, string>()
            {
               { "categoryId", categoryId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelPublish(RequestContext context, int channelId)
        {
            var action = ChannelEvent.ChannelPublish;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelUnPublish(RequestContext context, int channelId)
        {
            var action = ChannelEvent.ChannelUnPublish;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelSynchronize(RequestContext context, int channelId)
        {
            var action = ChannelEvent.ChannelSynchronize;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelEntityDelete(RequestContext context, int channelId, int entityId, string xml)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelEntityDeleted;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "entityId", entityId.ToString() },
               { "xml", xml.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelEntityUpdate(RequestContext context, int channelId, int entityId, string eventData)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelEntityUpdated;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "entityId", entityId.ToString() },
               { "data", eventData },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelEntityAdded(RequestContext context, int channelId, int entityId)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelEntityAdded;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "entityId", entityId.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelEntityFieldsetUpdate(RequestContext context, int channelId, int entityId, string fieldSetId)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelEntityFieldSetUpdated;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "entityId", entityId.ToString() },
               { "fieldSetId", fieldSetId.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelEntitySpecificationFieldAdded(RequestContext context, int channelId, int entityId, string fieldName)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelEntitySpecificationFieldAdded;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "entityId", entityId.ToString() },
               { "fieldName", fieldName.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelEntitySpecificationFieldUpdated(RequestContext context, int channelId, int entityId, string fieldName)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelEntitySpecificationFieldUpdated;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "entityId", entityId.ToString() },
               { "fieldName", fieldName.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelLinkAdded(RequestContext context, int channelId, DtoLink link)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelLinkAdded;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelLinkDeleted(RequestContext context, int channelId, DtoLink link)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelLinkDeleted;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyChannelLinkUpdated(RequestContext context, int channelId, DtoLink link)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelLinkUpdated;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "linkSourceId", link.Source.Id.ToString() },
               { "linkTargetId", link.Target.Id.ToString() },
               { "linkTypeId", link.LinkTypeId },
               { "linkEntityId", link.LinkEntity?.Id.ToString() },
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifyAssortmentCopiedInChannel(RequestContext context, int channelId, int assortmentId, int targetId, string targetType)
        {
            if (!context.ExtensionManager.ChannelIsPublished(channelId))
            {
                return;
            }

            var action = ChannelEvent.ChannelAssortmentCopiedInChannel;
            var data = new Dictionary<string, string>()
            {
               { "channelId", channelId.ToString() },
               { "assortmentId", assortmentId.ToString() },
               { "targetId", targetId.ToString() },
               { "targetType", targetType }
            };

            PublishEvent(context, action, data, context.Username);
        }

        public static void NotifySegmentationChanged(RequestContext context, int entityId, int previousSegmentationValue)
        {
            var action = SegmentationEvent.EntitySegmentationChanged;
            var data = new Dictionary<string, string>()
            {
               { "entityId", entityId.ToString() },
               { "previousSegmentationValue", previousSegmentationValue.ToString() }
            };

            PublishEvent(context, action, data, context.Username);
        }

        private static void PublishEvent(RequestContext requestContext, string action, Dictionary<string, string> data, string username)
        {
            var sw = Stopwatch.StartNew();

            try
            {
                var payload = JsonConvert.SerializeObject(new { action, data, username });

                using (var response = HttpPolicyExtensions
                    .HandleTransientHttpError()
                    .WaitAndRetry(
                        6,
                        retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                        (ex, timeSpan, retryCount, context)
                            => requestContext.SystemLogging.Warning($"Failed to publish event to Messaging Service ({MessagingServiceClient.BaseUrlForLogging}). Action: {action}. Retry attempt {retryCount}", requestContext.Module, requestContext.Username))
                    .Execute(() => {
                        var tmpResponse = MessagingServiceClient.PublishEvent(payload, requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, requestContext.Username);
                        if (tmpResponse.StatusCode == HttpStatusCode.NotFound && tmpResponse.ReasonPhrase == "FABRIC_E_SERVICE_DOES_NOT_EXIST")
                        {
                            throw new HttpRequestException(); // FABRIC_E_SERVICE_DOES_NOT_EXIST should be treated as a transient error, rendering a retry
                        }

                        return tmpResponse;
                    }))
                {
                    response.EnsureSuccessStatusCode();
                }
            }
            catch (Exception ex)
            {
                requestContext.SystemLogging.Error($"Failed to publish event to Messaging Service ({MessagingServiceClient.BaseUrlForLogging}). Action: {action}. Elapsed: {sw.Elapsed.TotalSeconds} seconds.", ex, requestContext.Module, requestContext.Username);
                requestContext.Logging.Error($"Failed when publishing event '{action}'", ex, requestContext.Module, requestContext.Username);
            }
        }
    }
}
