﻿<?xml version="1.0" encoding="utf-8"?>
<Application xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" Name="fabric:/LongRunningJob" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <Parameters>
    <Parameter Name="ASPNETCORE_ENVIRONMENT" Value="Development" />
    <Parameter Name="KeyVaultBaseUrl" Value="https://pmceuwlocalkv01.vault.azure.net/" />
    <Parameter Name="KeyVaultServerSettingsEncryptionKeyName" Value="serversettings-encryption-key" />
    <Parameter Name="StackConfigSecretName" Value="LocalStackConfig" />
    <Parameter Name="RequiredHttps" Value="false" />
    <Parameter Name="DataApiUrl" Value="http://localhost:8485/" />
    <Parameter Name="SMTP_SEND_USER" Value="<EMAIL>" />
    <Parameter Name="SMTP_SEND_USER_NAME" Value="inRiver" />
    <Parameter Name="ServicePlacementConstraints" Value="" />
    <Parameter Name="ServicePlacementConstraintsNT3" Value="" />
    <Parameter Name="InstrumentationKey" Value="0ead79b9-b0f2-405c-9990-b2b8b9ac5caf" />
    <Parameter Name="GeoLocation" Value="localhost" />
    <Parameter Name="Stack" Value="localhost" />
    <Parameter Name="LongRunningJobActorService_PartitionCount" Value="1" />
    <Parameter Name="LongRunningJobActorService_TargetReplicaSetSize" Value="1" />
    <Parameter Name="LongRunningJobActorService_MinReplicaSetSize" Value="1" />
    <Parameter Name="LongRunningJobService_InstanceCount" Value="1" />
    <Parameter Name="AZURE_CLIENT_ID" Value="e158c80d-5c04-47b0-b49c-932968c8545d" />
    <Parameter Name="AZURE_TENANT_ID" Value="5c732d90-72ba-4e70-9173-a254edfcd1fb" />
    <Parameter Name="AZURE_CLIENT_SECRET" Value="****************************************" />
    <Parameter Name="AzureServicesAuthConnectionString" Value="RunAs=App;AppId=e158c80d-5c04-47b0-b49c-932968c8545d;TenantId=5c732d90-72ba-4e70-9173-a254edfcd1fb;AppKey=****************************************" />
    <Parameter Name="LogLevel" Value="Verbose" />
    <Parameter Name="LongRunningJobWorkerService_ServicePlacementConstraints" Value="" />
  </Parameters>
</Application>