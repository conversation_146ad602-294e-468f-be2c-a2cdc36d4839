namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;
    using System.Data.SqlClient;
    using inRiver.Log;

    public class PersistanceFieldType : BasePersistance, IPersistanceFieldType
    {
        public PersistanceFieldType(
            string connectionString, 
            ICommonLogging logInstance,
            IContentSegmentPermissionProvider contentSegmentProvider)
            
            : base(connectionString, logInstance, contentSegmentProvider)
        {

        }

        public FieldType GetFieldType(string id, bool includeSettings = false)
        {
            FieldType fieldType = null;

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, [Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, [Index], [Description], TrackChanges, [ExpressionSupport] FROM FieldType WHERE Id = @Id";
                command.Parameters.AddWithValue("@Id", id);
                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        fieldType = GetFieldTypeFromSqlDataReader(reader);
                    }
                }

                if (fieldType == null)
                {
                    return null;
                }

                if (includeSettings)
                {
                    fieldType.Settings = GetFieldTypeSettings(id);
                }
            }

            return fieldType;
        }

        public List<FieldType> GetAllFieldTypes(bool includeSettings = false)
        {
            var fieldTypes = new List<FieldType>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, [Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, [Index], [Description], TrackChanges, [ExpressionSupport] FROM FieldType";

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var fieldType = GetFieldTypeFromSqlDataReader(reader);

                        fieldTypes.Add(fieldType);
                    }
                }
            }

            return includeSettings ? GetFieldTypeSettingsFromFieldTypes(fieldTypes) : fieldTypes;
        }

        public Dictionary<string, List<FieldType>> GetAllFieldTypesGrouped()
        {
            var fieldTypes = new Dictionary<string, List<FieldType>>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, [Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, [Index], [Description], TrackChanges, [ExpressionSupport] FROM FieldType";

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var fieldType = GetFieldTypeFromSqlDataReader(reader);

                        if (!fieldTypes.ContainsKey(fieldType.EntityTypeId))
                        {
                            fieldTypes.Add(fieldType.EntityTypeId, new List<FieldType>());
                        }

                        fieldTypes[fieldType.EntityTypeId].Add(fieldType);
                    }
                }

                var settings = this.GetAllFieldTypeSettings();

                foreach (var entityTypeId in fieldTypes.Keys)
                {
                    foreach (var fieldType in fieldTypes[entityTypeId])
                    {
                        fieldType.Settings = settings.ContainsKey(fieldType.Id) ? settings[fieldType.Id] : new Dictionary<string, string>();
                    }
                }
            }

            return fieldTypes;
        }

        public List<FieldType> GetFieldTypesForEntityType(string entityTypeId, bool includeSettings = false)
        {
            var fieldTypes = new List<FieldType>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, " +
                                      "[Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, " +
                                      "IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, " +
                                      "[Index], [Description], TrackChanges, [ExpressionSupport] " +
                                      "FROM FieldType " +
                                      "WHERE EntityTypeId = @EntityTypeId";

                command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var fieldType = GetFieldTypeFromSqlDataReader(reader);

                        fieldTypes.Add(fieldType);
                    }
                }
            }

            return includeSettings ? GetFieldTypeSettingsFromFieldTypes(fieldTypes) : fieldTypes;
        }

        public Dictionary<string, string> GetFieldTypeSettings(string fieldTypeId)
        {
            var settings = new Dictionary<string, string>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT [Key], Value FROM FieldType_Settings WHERE FieldTypeId = @FieldTypeId";
                command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        settings.Add(reader.GetString(0), reader.GetString(1));
                    }
                }
            }

            return settings;
        }

        public Dictionary<string, Dictionary<string, string>> GetAllFieldTypeSettings()
        {

            var settings = new Dictionary<string, Dictionary<string, string>>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT FieldTypeId, [Key], Value FROM FieldType_Settings";


                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var fieldTypeId = reader.GetString(0);
                        var key = reader.GetString(1);
                        var value = reader.GetString(2);

                        if (!settings.ContainsKey(fieldTypeId))
                        {
                            settings.Add(fieldTypeId, new Dictionary<string, string>());
                        }

                        settings[fieldTypeId].Add(key, value);
                    }

                }

            }

            return settings;
        }

        private FieldType GetFieldTypeFromSqlDataReader(SqlDataReader reader)
        {
            var fieldType = new FieldType { Id = reader.GetString(0) };

            var xmlName = reader.GetSqlXml(1);
            fieldType.Name = new LocaleString(xmlName);

            fieldType.EntityTypeId = reader.GetString(2);
            fieldType.CategoryId = reader.GetString(3);

            if (!reader.IsDBNull(4))
            {
                fieldType.CVLId = reader.GetString(4);
            }

            fieldType.Mandatory = reader.GetBoolean(5);
            fieldType.Unique = reader.GetBoolean(6);

            if (!reader.IsDBNull(7))
            {
                fieldType.DefaultValue = reader.GetString(7);
            }

            fieldType.Hidden = reader.GetBoolean(8);
            fieldType.ReadOnly = reader.GetBoolean(9);
            fieldType.IsDisplayName = reader.GetBoolean(10);
            fieldType.IsDisplayDescription = reader.GetBoolean(11);
            fieldType.ExcludeFromDefaultView = reader.GetBoolean(12);
            fieldType.Multivalue = reader.GetBoolean(13);
            fieldType.DataType = reader.GetString(14);
            fieldType.Index = reader.GetInt32(15);

            if (!reader.IsDBNull(16))
            {
                var xmlDescription = reader.GetSqlXml(16);
                fieldType.Description = new LocaleString(xmlDescription);
            }

            fieldType.TrackChanges = reader.GetBoolean(17);
            fieldType.ExpressionSupport = reader.GetBoolean(18);

            return fieldType;
        }

        private List<FieldType> GetFieldTypeSettingsFromFieldTypes(List<FieldType> fieldTypes)
        {
            var settings = this.GetAllFieldTypeSettings();

            foreach (var fieldType in fieldTypes)
            {
                fieldType.Settings = settings.ContainsKey(fieldType.Id) ? settings[fieldType.Id] : new Dictionary<string, string>();
            }

            return fieldTypes;
        }
    }
}
