[CmdletBinding()]
param (
    [Parameter()][string]$Configuration
)

if ($Configuration.CompareTo('Development-')) {
    $publishProfileDir = Convert-Path "${PsScriptRoot}\..\src\LongRunningJob\PublishProfiles"

    $oneNodePublishProfileTemplate = Join-Path $publishProfileDir 'Local.1Node.Template.xml'
    $oneNodePublishProfile = Join-Path $publishProfileDir 'Local.1Node.xml'

    $fiveNodePublishProfileTemplate = Join-Path $publishProfileDir 'Local.5Node.Template.xml'
    $fiveNodePublishProfile = Join-Path $publishProfileDir 'Local.5Node.xml'


    ((Get-Content $oneNodePublishProfileTemplate).Replace("[[Configuration]]", $Configuration)) | Out-File $oneNodePublishProfile -Encoding utf8
    ((Get-Content $fiveNodePublishProfileTemplate).Replace("[[Configuration]]", $Configuration)) | Out-File $fiveNodePublishProfile -Encoding utf8
}
