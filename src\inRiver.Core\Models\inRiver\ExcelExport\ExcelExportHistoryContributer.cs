﻿namespace inRiver.Core.Models.inRiver.ExcelExport
{
    public class ExcelExportHistoryContributer
    {
        public int Id { get; set; }

        /// <summary>
        /// Username should always return either Email or combination of FirstName and LastName (If Email =! null)
        /// </summary>
        public string Username => !string.IsNullOrEmpty(this.Email) ? Email : FirstName + "_" + LastName;

        public string Email { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public override string ToString()
        {
            if (!string.IsNullOrEmpty(Username))
            {
                return Username;
            }

            return base.ToString();
        }
    }
}
