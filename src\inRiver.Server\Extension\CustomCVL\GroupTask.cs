namespace inRiver.Server.Extension.CustomCVL
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;

    using inRiver.Remoting.Extension;
    using inRiver.Remoting.Extension.Interface;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;

    public class GroupTask : ICustomValueList
    {
        private readonly RequestContext requestContext;

        public inRiverContext Context { get; set; }

        public string Id { get; set; }

        public string Test()
        {
            return string.Empty;
        }

        public GroupTask(RequestContext requestContext)
        {
            this.requestContext = requestContext;
            this.Id = "GroupTask";
        }

        public List<CVLValue> GetAllCVLValues()
        {
            List<CVLValue> values = new List<CVLValue>();

            foreach (Role role in this.requestContext.DataPersistance.GetAllRoles())
            {
                CVLValue value = new CVLValue();
                value.CVLId = this.Id;
                value.Key = role.Id.ToString(CultureInfo.InvariantCulture);
                value.Index = 0;
                value.Value = role.Name;

                values.Add(value);
            }

            values.Sort(new CVLValueComparer());

            int index = 0;

            values.ForEach(delegate (CVLValue value) { value.Index = index++; });

            return values;
        }

        public Dictionary<string, string> DefaultSettings => new Dictionary<string, string>();

        public CVLValue GetCVLValueByKey(string key)
        {
            Role role = this.requestContext.DataPersistance.GetRole(Convert.ToInt32(key));

            if (role == null)
            {
                return null;
            }

            CVLValue value = new CVLValue();
            value.CVLId = this.Id;
            value.Key = role.Id.ToString(CultureInfo.InvariantCulture);
            value.Index = 0;
            value.Value = role.Name;

            return value;
        }
    }
}
