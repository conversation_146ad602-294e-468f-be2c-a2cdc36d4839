<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
    <add key="ipmc.nuget" value="https://pkgs.dev.azure.com/inriver/_packaging/inRiver_NuGet/nuget/v3/index.json" />
    <add key="ipmc.nuget.staging" value="https://inriver.pkgs.visualstudio.com/_packaging/inRiver_NuGet_Staging%40Local/nuget/v3/index.json" />
  </packageSources>
  <disabledPackageSources>
    <add key="inriver.nuget" value="true" />
  </disabledPackageSources>
</configuration>
