namespace inRiver.Server.Syndication
{
    using System.Collections.Generic;
    using System.Linq;

    public static class MapEnumerationExtensions
    {
        public static string GetMatchingEnumValuesString(this List<MapEnumeration> enumerations, string foundData)
        {
            if (foundData != null)
            {
                var enumValues = enumerations
                    .Where(x => {
                        var foundDataList = foundData.Split(';');

                        return x.FieldValue != null
                                && foundDataList.Any(y => x.FieldValue == y ||
                                                            x.FieldValue.Contains($";{y};") ||
                                                            x.FieldValue.StartsWith($"{y};") ||
                                                            x.FieldValue.EndsWith($";{y}"));
                    })
                    .Select(x => x.EnumValue)
                    .ToList();

                return enumValues.Any()
                    ? string.Join(";", enumValues)
                    : null;
            }
            else
            {
                return foundData;
            }
        }
    }
}
