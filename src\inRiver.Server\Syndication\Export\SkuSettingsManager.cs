namespace inRiver.Server.Syndication.Export
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Core.Constants;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Mapping;
    using Newtonsoft.Json;

    public class SkuSettingsManager
    {
        private const char EntityFieldDelimiter = ';';

        private const char EntityDelimiter = '|';

        public SkuSettingsManager(bool isSkuEnabled, RequestContext context, MapContainer mapContainer)
        {
            this.IsSkuEnabled = isSkuEnabled;
            if (!isSkuEnabled || context == null || mapContainer == null)
            {
                return;
            }

            var serverSettings = this.GetSkuServerSettings(context);
            var serverSKUField = this.GetEntitySkuFieldFromServerSettings(serverSettings);
            this.SetEntitySkuField(serverSKUField, mapContainer);
            var serverSkuSchema = this.GetSchemaSkuFieldsFromServerSettings(serverSettings);
            this.SetSchemaSkuFields(serverSkuSchema);
        }

        public bool IsSkuEnabled { get; private set; }

        public IList<string> SchemaSkuFields { get; private set; } = new List<string>();

        public string EntitySkuField { get; private set; }

        public bool ShouldProcessSkuField() => this.IsSkuEnabled && !string.IsNullOrEmpty(this.EntitySkuField) && this.SchemaSkuFields.Any();

        public bool DoesSchemaContainField(string fieldTypeId) => this.IsSkuEnabled && this.SchemaSkuFields.Contains(fieldTypeId);

        private IDictionary<string, string> GetSkuServerSettings(RequestContext context)
        {
            var skuSettingKeys = new List<string>() { ServerConstants.SKU_FIELD, ServerConstants.SKU_SCHEMA };
            return context.DataPersistance.GetServerSettings(skuSettingKeys);
        }

        private string GetEntitySkuFieldFromServerSettings(IDictionary<string, string> serverSettings)
        {
            return serverSettings.Where(x => x.Key == ServerConstants.SKU_FIELD)
                .Select(y => y.Value)
                .FirstOrDefault();
        }

        private void SetEntitySkuField(string serverSKUField, MapContainer mapContainer)
        {
            if (!string.IsNullOrEmpty(serverSKUField))
            {
                var skuEntities = serverSKUField.Split(EntityDelimiter);
                var entitySKUFieldKeyValue = skuEntities.ToList()
                    .Find(i => i.StartsWith($"{mapContainer.EntityTypeId}{EntityFieldDelimiter}", StringComparison.InvariantCultureIgnoreCase));
                var splitVal = entitySKUFieldKeyValue?.Split(EntityFieldDelimiter);
                this.EntitySkuField = splitVal.Length > 1 ? splitVal[1] : string.Empty;
            }
        }

        private string GetSchemaSkuFieldsFromServerSettings(IDictionary<string, string> serverSettings)
        {
            return serverSettings
                .Where(x => x.Key == ServerConstants.SKU_SCHEMA)
                .Select(y => y.Value).FirstOrDefault();
        }

        private void SetSchemaSkuFields(string serverSkuSchema)
        {
            if (!string.IsNullOrEmpty(serverSkuSchema))
            {
                var skuSchema = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(serverSkuSchema);
                this.SchemaSkuFields = skuSchema.Keys.ToList();
            }
        }
    }
}
