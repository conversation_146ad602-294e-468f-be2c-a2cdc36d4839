﻿namespace inRiver.Core.Models.inRiver
{
    public class LinkType
        : IIdentifierAsStringInterface
    {
        public string Id { get; set; }

        public string SourceEntityTypeId { get; set; }

        public LocaleString SourceName { get; set; }

        public string TargetEntityTypeId { get; set; }

        public LocaleString TargetName { get; set; }

        public string LinkEntityTypeId { get; set; }

        public int Index { get; set; }

        public override string ToString()
        {
            if (string.IsNullOrWhiteSpace(this.Id))
            {
                return base.ToString();
            }

            return this.Id;
        }
    }
}
