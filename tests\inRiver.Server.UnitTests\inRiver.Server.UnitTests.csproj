<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FakeItEasy" version="4.8.0" />
    <PackageReference Include="FluentAssertions" version="6.4.0" />
    <PackageReference Include="inRiver.Log" version="2.1.1" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.0.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="xunit" version="2.4.1" />
    <PackageReference Include="xunit.abstractions" version="2.0.3" />
    <PackageReference Include="xunit.analyzers" version="0.10.0" />
    <PackageReference Include="xunit.assert" version="2.4.1" />
    <PackageReference Include="xunit.core" version="2.4.1" />
    <PackageReference Include="xunit.extensibility.core" version="2.4.1" />
    <PackageReference Include="xunit.extensibility.execution" version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" version="2.4.1" />
    <PackageReference Include="xunit.runner.console" version="2.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\inRiver.Server\inRiver.Server.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="TestFiles\ConversionHelperTests\mappingDtoData.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TestFiles\ConversionHelperTests\entityFields.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TestFiles\ConversionHelperTests\entity.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TestFiles\ConversionHelperTests\entitytype.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TestFiles\ConversionHelperTests\remotingEntity.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
