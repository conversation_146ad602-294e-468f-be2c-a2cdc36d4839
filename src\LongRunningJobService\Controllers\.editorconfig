[*.cs]

# Async controller methods should not have suffix Async.
dotnet_naming_rule.async_controller_methods_does_not_end_in_async.symbols = any_async_methods
dotnet_naming_rule.async_controller_methods_does_not_end_in_async.style = no_async_suffix
dotnet_naming_rule.async_controller_methods_does_not_end_in_async.severity = error

dotnet_naming_style.no_async_suffix.required_prefix =
dotnet_naming_style.no_async_suffix.required_suffix = 
dotnet_naming_style.no_async_suffix.capitalization = pascal_case
dotnet_naming_style.no_async_suffix.word_separator = 
