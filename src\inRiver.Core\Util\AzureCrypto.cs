namespace inRiver.Core.Util
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security;
    using System.Text;
    using System.Threading.Tasks;
    using Azure.Identity;
    using Azure.Security.KeyVault.Keys;
    using Azure.Security.KeyVault.Keys.Cryptography;

    public class AzureCrypto : ICrypto
    {
        // The maximum size of data in bytes that can be encrypted using the RSA OAEP type with 4096 bits key.
        // 4096bits / 8 - 42bytes = 470 bytes
        // 42bytes - O<PERSON>EP padding
        private const int EncryptionStringMaxLength = 470;

        private static readonly EncryptionAlgorithm RsaOaepAlgorithm = EncryptionAlgorithm.RsaOaep;

        private readonly KeyClient keyClient;

        private readonly CryptographyClient cryptoClient;

        private readonly KeyVaultKey encryptionKey;

        public AzureCrypto(Uri keyVaultUri, string keyVaultKeyName)
        {
            var credentials = new DefaultAzureCredential();
            this.keyClient = new KeyClient(keyVaultUri, credentials);
            this.encryptionKey = this.GetEncryptionKeyAsync(keyVaultKeyName).GetAwaiter().GetResult();
            this.cryptoClient = new CryptographyClient(this.encryptionKey.Id, credentials);
        }

        public async Task<string> EncryptStringAsync(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            var permittedOperations = this.encryptionKey.KeyOperations;
            if (!permittedOperations.Contains(KeyOperation.Encrypt))
            {
                throw new SecurityException("Unable to encrypt. The key does not have permission to encrypt.");
            }

            var encryptedBatches = await Task.WhenAll(this.GetBatches(input, EncryptionStringMaxLength)
                .Select(async batch => {
                    var batchAsByteArray = Encoding.UTF8.GetBytes(batch);
                    var encryptResult = await this.cryptoClient
                        .EncryptAsync(RsaOaepAlgorithm, batchAsByteArray).ConfigureAwait(false);

                    return Convert.ToBase64String(encryptResult.Ciphertext);
                }));

            return string.Join(",", encryptedBatches);
        }

        public async Task<string> DecryptStringAsync(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            var permittedOperations = this.encryptionKey.KeyOperations;
            if (!permittedOperations.Contains(KeyOperation.Decrypt))
            {
                throw new SecurityException("Unable to decrypt. The key does not have permission to decrypt.");
            }

            var decryptedString = string.Empty;
            var encryptedBatches = input.Split(',');
            foreach (var encryptedBatch in encryptedBatches)
            {
                var inputAsByteArray = Convert.FromBase64String(encryptedBatch);
                var decryptResult = await this.cryptoClient.DecryptAsync(RsaOaepAlgorithm, inputAsByteArray).ConfigureAwait(false);
                decryptedString += Encoding.Default.GetString(decryptResult.Plaintext);
            }

            return decryptedString;
        }

        private async Task<KeyVaultKey> GetEncryptionKeyAsync(string keyVaultKeyName)
        {
            KeyVaultKey key = await this.keyClient.GetKeyAsync(keyVaultKeyName).ConfigureAwait(false);
            if (key == null)
            {
                throw new ArgumentException("Unable to encrypt or decrypt value. The encryption key was not found.");
            }

            return key;
        }

        private IEnumerable<string> GetBatches(string text, int batchSize)
        {
            for (var offset = 0; offset < text.Length; offset += batchSize)
            {
                var size = Math.Min(batchSize, text.Length - offset);
                yield return text.Substring(offset, size);
            }
        }
    }
}
