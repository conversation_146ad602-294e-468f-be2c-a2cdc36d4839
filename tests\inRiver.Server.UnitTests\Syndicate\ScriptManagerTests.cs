namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using FluentAssertions;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Syndication;
    using Xunit;

    public class ScriptManagerTests
    {
        private const string FunctionWithRestApi = "function main(args) { return inriverctx.RestApiGet(); }";
        private const string FunctionWithoutRestApi = "function main(args) { return args[0]; }";

        [Fact]
        public void TestClearScriptBasicFunction()
        {
            var args = new string[] { "99" };
            var result = ScriptManager.Execute("function main(args) { return args[0]-1; }", args, null, null);

            Assert.Equal(98, (int)result);
        }

        [Fact]
        public void TestClearScriptToUpperFunction()
        {
            var values = new object[] { "text should be caps" };
            var result = ScriptManager.Execute("function main(args, values) { return values[0].toUpperCase(); }", null, values, null);

            Assert.Equal("TEXT SHOULD BE CAPS", result.ToString().ToUpper(CultureInfo.InvariantCulture));
        }

        [Fact]
        public void TestClearScriptConcatenateFunction()
        {
            var args = new string[] { " " };
            var values = new object[] { "Shirt", "Many colors available" };
            var result = ScriptManager.Execute("function main(args, values) { var separator = args.length > 0 ? args[0] : ''; return values.join(separator); }", args, values, null);

            Assert.Equal("Shirt Many colors available", result.ToString());
        }

        [Fact]
        public void TestClearScriptGetLocalStringValueFunction()
        {
            var ls = new LocaleString();
            ls[new CultureInfo("sv")] = "Utbildning, i Malmö";
            ls[new CultureInfo("en")] = "Training, in Malmoe";

            var languages = new List<CultureInfo> { new CultureInfo("sv"), new CultureInfo("en") };

            var values = new object[] { ls.ToDictionary(languages) };
            var result = ScriptManager.Execute("function main(args, values) { return values[0]['en']; }", null, values, null);

            Assert.Equal("Training, in Malmoe", result.ToString());
        }

        [Theory]
        [InlineData(FunctionWithRestApi, "result string")]
        [InlineData(FunctionWithRestApi, null)]
        [InlineData(FunctionWithoutRestApi, "result string")]
        [InlineData(FunctionWithoutRestApi, null)]
        [InlineData(null, null)]
        public void ValidateScriptResult_ShouldNotThrowException(string script, object functionResultData)
        {
            // Act
            Action act = () => ScriptManager.ValidateScriptResult(script, functionResultData);

            // Assert
            act.Should().NotThrow<Exception>();
        }

        [Fact]
        public void ValidateScriptResult_RestApiFunctionSizeIsMoreThan1Mb_ShouldThrowExceptionWithCorrectMessage()
        {
            // Arrange
            var largeScriptResult = new string(new char[1248576]); // 1.2 Mb

            // Act
            Action act = () => ScriptManager.ValidateScriptResult(FunctionWithRestApi, largeScriptResult);

            // Assert
            act.Should().Throw<Exception>().WithMessage("The response from a custom function containing REST API calls cannot exceed 1 MB.");
        }

        [Fact]
        public void ValidateScriptResult_FunctionSizeIsLessThan1Mb_ShouldNotThrowException()
        {
            // Arrange
            var largeScriptResult = new string(new char[1248576]); // 1.2 Mb

            // Act
            Action act = () => ScriptManager.ValidateScriptResult(FunctionWithoutRestApi, largeScriptResult);

            // Assert
            act.Should().NotThrow<Exception>();
        }
    }
}
