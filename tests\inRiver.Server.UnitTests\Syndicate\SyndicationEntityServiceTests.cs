namespace inRiver.Server.UnitTests.Syndicate
{
    using System.Collections.Generic;
    using System.Linq;
    using FakeItEasy;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Service;
    using Xunit;

    public class SyndicationEntityServiceTests
    {
        [Fact]
        public void ConvertExportContainersToEntityModels_ValidExportContainers_ShouldReturnEntitiesWithFields()
        {
            // Arrange
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField { Id = "Field1", Data = "Value1", FieldTypeId = "Type1", MapFieldType = new MapFieldType { DataType = "String" } },
                        new ExportField { Id = "Field2", Data = "Value2", FieldTypeId = "Type2", MapFieldType = new MapFieldType { DataType = "Int" } },
                        new ExportField { Id = "Field3", Data = "Value3", FieldTypeId = null }
                    }
                }
            };
            var service = GetService();

            // Act
            var result = service.ConvertExportContainersToEntityModels(exportContainers);

            // Assert
            Assert.Single(result);
            var entity = result.FirstOrDefault();
            Assert.Equal(1, entity.Id);
            Assert.NotNull(entity.Fields);
            Assert.Equal(2, entity.Fields.Count);
            Assert.All(entity.Fields, f => Assert.NotNull(f.FieldType));
            Assert.Equal("Field1", entity.Fields[0].FieldType.FieldTypeId);
            Assert.Equal("String", entity.Fields[0].FieldType.DataType);
            Assert.Equal("Field2", entity.Fields[1].FieldType.FieldTypeId);
            Assert.Equal("Int", entity.Fields[1].FieldType.DataType);
        }

        [Fact]
        public void ConvertExportContainersToEntityModels_EmptyInput_ShouldReturnEmptyList()
        {
            // Arrange
            var service = GetService();

            // Act
            var result = service.ConvertExportContainersToEntityModels(new List<ExportContainer>());

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public void ConvertExportContainersToEntityModels_FieldTypeIdNull_ShouldSkipField()
        {
            // Arrange
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 2,
                    Fields = new List<ExportField>
                    {
                        new ExportField { Id = "FieldA", Data = "ValueA", FieldTypeId = null }
                    }
                }
            };
            var service = GetService();

            // Act
            var result = service.ConvertExportContainersToEntityModels(exportContainers);

            // Assert
            Assert.Single(result);
            Assert.Empty(result.First().Fields);
        }

        private static SyndicationEntityService GetService()
        {
            var context = new Request.RequestContext
            {
                DataPersistance = null,
                Username = "system",
            };

            return new SyndicationEntityService(context, null);
        }
    }
}
