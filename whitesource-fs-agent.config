####################################################################
# WhiteSource FS-Agent configuration file
####################################################################
##########################################
# GENERAL SCAN MODE: Files and Package Managers
##########################################
 
checkPolicies=false
forceCheckAllDependencies=false
forceUpdate=false
forceUpdate.failBuildOnPolicyViolation=false
offline=false
#ignoreSourceFiles=true
#ignoreCertificateCheck= 
#scanComment=
#updateInventory=false
#resolveAllDependencies=false
  
#projectPerFolder=true
#projectPerFolderIncludes= 
#projectPerFolderExcludes= 
  
#wss.connectionTimeoutMinutes=60
# Change the below URL to your WhiteSource server.
# Use the 'WhiteSource Server URL' which can be retrieved
# from your 'Profile' page on the 'Server URLs' panel.
# Then, add the '/agent' path to it.
wss.url=https://app-eu.whitesourcesoftware.com/agent
 
npm.resolveDependencies=false
#npm.ignoreSourceFiles=false
#npm.includeDevDependencies=true
#npm.runPreStep=true
#npm.ignoreNpmLsErrors=true
#npm.ignoreScripts=true
#npm.yarnProject=true
#npm.accessToken=

bower.resolveDependencies=false
#bower.ignoreSourceFiles=true
#bower.runPreStep=true
  
nuget.resolvePackagesConfigFiles=true
#nuget.resolveCsProjFiles=false
#nuget.resolveDependencies=false
#nuget.restoreDependencies=true
#nuget.ignoreSourceFiles=true
#nuget.runPreStep=true
  
python.resolveDependencies=false
#python.ignoreSourceFiles=false
#python.ignorePipInstallErrors=true
#python.installVirtualenv=true
#python.resolveHierarchyTree=false
#python.requirementsFileIncludes=requirements.txt
#python.resolveSetupPyFiles=true
#python.runPipenvPreStep=true
#python.pipenvDevDependencies=true
#python.IgnorePipenvInstallErrors=true
  
#maven.ignoredScopes=test provided
maven.resolveDependencies=false
#maven.ignoreSourceFiles=true
#maven.aggregateModules=true
#maven.ignorePomModules=false
#maven.runPreStep=true
#maven.ignoreMvnTreeErrors=true
 
#gradle.ignoredScopes=
gradle.resolveDependencies=false
#gradle.runAssembleCommand=false
#gradle.runPreStep=true
#gradle.ignoreSourceFiles=true
#gradle.aggregateModules=true
#gradle.preferredEnvironment=wrapper
#gradle.runPreStep=true

paket.resolveDependencies=false
#paket.ignoredGroups=
#paket.ignoreSourceFiles=false
#paket.runPreStep=true
#paket.exePath=

go.resolveDependencies=false
#go.collectDependenciesAtRuntime=true
#go.dependencyManager= 
#go.ignoreSourceFiles=true
#go.glide.ignoreTestPackages=false
#go.gogradle.enableTaskAlias=true

ruby.resolveDependencies = false
#ruby.ignoreSourceFiles = false
#ruby.installMissingGems = true
#ruby.runBundleInstall = true
#ruby.overwriteGemFile = true

sbt.resolveDependencies=false
#sbt.ignoreSourceFiles=true
#sbt.aggregateModules=true
#sbt.runPreStep=true
#sbt.targetFolder=

php.resolveDependencies=false
#php.runPreStep=true
#php.includeDevDependencies=true

#html.resolveDependencies=false

cocoapods.resolveDependencies=false
#cocoapods.runPreStep=true
#cocoapods.ignoreSourceFiles=false

hex.resolveDependencies=false
#hex.runPreStep=true
#hex.ignoreSourceFiles=false
#hex.aggregateModules=true

##################################
# Organization tokens:
##################################
apiKey=58ddcf211f5945768f52aa9227bfca34d03dbe816d54432cb2454b91c1afdd36

#userKey is required if WhiteSource administrator has enabled "Enforce user level access" option
#userKey=
  
projectName=
projectVersion=
projectToken=
  
productName=
productVersion=
productToken=
#updateType=APPEND
#requesterEmail=<EMAIL>
   
#########################################################################################
# Includes/Excludes Glob patterns - PLEASE USE ONLY ONE EXCLUDE LINE AND ONE INCLUDE LINE
#########################################################################################
includes=**/*.dll **/*.cs **/*.nupkg **/*.js
  
#includes=**/*.m **/*.mm  **/*.js **/*.php
#includes=**/*.jar
#includes=**/*.gem **/*.rb
#includes=**/*.dll **/*.cs **/*.nupkg
#includes=**/*.tgz **/*.deb **/*.gzip **/*.rpm **/*.tar.bz2
#includes=**/*.zip **/*.tar.gz **/*.egg **/*.whl **/*.py
  
## Exclude file extensions or specific directories by adding **/*.<extension> or **<excluded_dir>/**
excludes=**/*sources.jar **/*javadoc.jar
  
case.sensitive.glob=false
followSymbolicLinks=true
   
##################################
# Archive Properties
##################################
#archiveExtractionDepth=2
#archiveIncludes=**/*.war **/*.ear
#archiveExcludes=**/*sources.jar
   
##################################
# Proxy settings
##################################
#proxy.host=
#proxy.port=
#proxy.user=
#proxy.pass=
   
##################################
# SCM settings
##################################
#scm.type=
#scm.user=
#scm.pass=
#scm.ppk=
#scm.url=
#scm.branch=
#scm.tag=
#scm.npmInstall=
#scm.npmInstallTimeoutMinutes=
#scm.repositoriesFile=
  
##############################################
# SCAN MODE: Linux package manager settings
##############################################
#scanPackageManager=true
 
##################################
# SCAN MODE: Docker images
##################################
#docker.includes=.*.*
#docker.excludes=.*.*
#docker.scanImages=true
#docker.pull.enable=true
#docker.pull.images=.*.*
#docker.pull.maxImages=10
#docker.pull.tags=.*.*
#docker.pull.digest=
#docker.delete.force=true
#docker.login.sudo=false

#docker.aws.enable=true
#docker.aws.registryIds=