namespace inRiver.Server.Syndication.Script
{
    using System.Collections.Generic;
    using System.Globalization;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;

    public class ScriptContextDealer : IScriptContextDealer
    {
        private readonly IScriptContext scriptContext;

        public ScriptContextDealer(RequestContext requestContext, IList<CultureInfo> languages, SkuSettingsManager skuSettingsManager)
        {
            this.scriptContext = new ScriptContext(requestContext, languages, skuSettingsManager);
        }

        public IScriptContext GetScriptContext(InRiverEntity entity, string skuId)
        {
            if (this.scriptContext != null)
            {
                this.scriptContext.SetInRiverEntity(entity);
                this.scriptContext.SetCurrentSkuId(skuId);
            }

            return this.scriptContext;
        }

        public void Dispose() => this.scriptContext.Dispose();
    }
}
