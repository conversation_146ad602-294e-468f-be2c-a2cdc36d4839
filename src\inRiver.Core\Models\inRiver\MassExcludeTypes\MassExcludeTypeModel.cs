namespace inRiver.Core.Models.inRiver.MassExcludeTypes
{
    using global::inRiver.Core.Enum;
    using System.Collections.Generic;

    public class MassExcludeTypeModel
    {
        public ExcludeType ExcludeType { get; set; }
        public string EntityTypeId { get; set; }
        public MassExcludeFieldTypeModel ExcludeFieldTypeModel { get; set; }
        public string LinkTypeId { get; set; }

    }
}
