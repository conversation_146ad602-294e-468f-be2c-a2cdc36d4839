namespace LongRunningJobService.Api
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using inRiver.Api.Data.Client;
    using inRiver.Configuration.Core.Service;
    using inRiver.Core.Http;
    using inRiver.Core.Models;
    using inRiver.Core.Services;
    using inRiver.Server.Configuration;
    using inRiver.Server.Service;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Inriver.StackEssentials;
    using Inriver.StackEssentials.Config;
    using Inriver.StackEssentials.DependencyInjection;
    using LongRunningJob.Core.Cache;
    using LongRunningJobActor.Abstraction;
    using LongRunningJobActor.Services;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.Extensions.Caching.Memory;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;
    using Microsoft.Extensions.Hosting;
    using Serilog;
    using Serilog.Core;
    using Serilog.Events;
    using Services;
    using Telemetry.Initializers;

    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            this.Configuration = configuration;

            var loggingLevelSwitch = new LoggingLevelSwitch(Enum.TryParse("Verbose", out LogEventLevel level) ? level : LogEventLevel.Information);
            var newConfig = TelemetryConfiguration.CreateDefault();
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.ControlledBy(loggingLevelSwitch)
                .WriteTo.ApplicationInsights(newConfig, TelemetryConverter.Traces)
                .Enrich.FromLogContext()
                .CreateLogger();

            StackEssentialsInitializer.Init(this.Configuration["KeyVaultBaseUrl"], this.Configuration["StackConfigSecretName"]);

            StaticConfigurationProvider.ConnectServiceUrl = this.Configuration.GetValue<string>("ConnectServiceUrl");
            StaticConfigurationProvider.CompressionServiceUrl = this.Configuration.GetValue<string>("CompressionServiceUrl");
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();
            var telemetryConfiguration = new TelemetryConfiguration
            {
                InstrumentationKey = this.Configuration["ApplicationInsights:InstrumentationKey"]
            };
            telemetryConfiguration.TelemetryInitializers.Add(new CloudRoleNameTelemetryInitializer(LongRunningJobActor.Constants.CloudRoleName));

            var telemetryClient = new TelemetryClient(telemetryConfiguration);
            services.AddStackConfig(stackConfigOptions => {
                stackConfigOptions.KeyVaultBaseUrl = this.Configuration["KeyVaultBaseUrl"];
                stackConfigOptions.StackConfigSecretName = this.Configuration["StackConfigSecretName"];
            });
            services.AddSingleton(telemetryClient);
            services.AddSingleton(new LongRunningJobCache());
            services.AddSingleton(new CancellationTokenSource());
            services.AddTransient<ISyndicationService, SyndicationService>();
            services.AddTransient<ISyndicationConfigService, LocalApiSyndicationConfig>();
            services.AddTransient<ISyndicationJobResultService, SyndicationJobResultService>();

            var keyVaultBaseUrl = this.Configuration["KeyVaultBaseUrl"];

            var namedAuth0Options = new Dictionary<string, Auth0Options>();
            var auth0Options = new Auth0Options
            {
                BaseAddress = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-domain").GetAwaiter().GetResult(),
                ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-id").GetAwaiter().GetResult(),
                ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-secret").GetAwaiter().GetResult(),
                Audience = this.Configuration["OAuth:Audience"],
            };
            namedAuth0Options.Add("Auth0Client", auth0Options);

            try
            {
                var outputAdapterAuth0Options = new Auth0Options
                {
                    BaseAddress = this.Configuration["Auth0Domain"],
                    ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-id").GetAwaiter().GetResult(),
                    ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-secret").GetAwaiter().GetResult(),
                    Audience = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-audience").GetAwaiter().GetResult(),
                };
                namedAuth0Options.Add("OutputAdapterAuth0Client", outputAdapterAuth0Options);
            }
            catch
            {
            }

            services.AddSingleton<IDictionary<string, Auth0Options>>(namedAuth0Options);

            services.TryAddTransient<BearerTokenHandler>();

            services.AddTransient<IAccessTokenRetriever, AccessTokenRetriever>();

            _ = services.AddHttpClient<IAugmentaHttpClient, AugmentaHttpClient>(c =>
                    c.WithBaseAddress(this.Configuration["Augmenta:ApiBaseAddress"])
                        .WithAcceptJsonHeader()
                        .WithTimeout(TimeSpan.FromMinutes(5)))
                .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

            _ = services.AddHttpClient<IOutputAdapterHttpClient, OutputAdapterHttpClient>(c =>
                    c.WithBaseAddress(this.Configuration["OutputAdapter:ApiBaseAddress"])
                        .WithAcceptJsonHeader()
                        .WithTimeout(TimeSpan.FromMinutes(5)))
                .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

            _ = services.AddMemoryCache();

            _ = services.AddHttpClient("Auth0Client", (provider, client) => {
                var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["Auth0Client"].BaseAddress;
                client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
            });

            _ = services.AddHttpClient("OutputAdapterAuth0Client", (provider, client) => {
                var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["OutputAdapterAuth0Client"].BaseAddress;
                client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
            });

            StaticServiceProvider.Configure(services.BuildServiceProvider());
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseAuthorization();

            app.UseEndpoints(endpoints => endpoints.MapControllers());
        }
    }
}
