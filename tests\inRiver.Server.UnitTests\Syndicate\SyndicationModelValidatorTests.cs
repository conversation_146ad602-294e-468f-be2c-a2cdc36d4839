namespace inRiver.Server.UnitTests.Syndicate
{
    using FakeItEasy;
    using inRiver.Configuration.Core.Repository;
    using inRiver.Core.Models.inRiver;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Service;
    using Xunit;

    public class SyndicationModelValidatorTests
    {
        [Fact]
        public void Validate_RunDsaSyndicationTrueAndDsaMappingIdNull_ShouldThrowSyndicateException()
        {
            // Arrange
            var repoFake = A.Fake<IEnvironmentSettingsRepository>();
            var validator = new SyndicationModelValidator(repoFake);
            var model = new SyndicationModel { RunDsaSyndication = true, DsaMappingId = null };
            var context = new RequestContext();

            // Act
            var ex = Assert.Throws<SyndicateException>(() => validator.Validate(context, model));

            // Assert
            Assert.Equal($"{nameof(model.DsaMappingId)} can't be empty when running syndication in DSA mode", ex.Message);
        }

        [Fact]
        public void Validate_RunDsaSyndicationTrueAndEvaluateSettingNull_ShouldThrowSyndicateException()
        {
            // Arrange
            var repoFake = A.Fake<IEnvironmentSettingsRepository>();
            A.CallTo(() => repoFake.GetEnvironmentSetting(A<string>._, A<int>._)).Returns(null as EnvironmentSetting);
            var validator = new SyndicationModelValidator(repoFake);
            var model = new SyndicationModel { RunDsaSyndication = true, DsaMappingId = 1 };
            var context = new RequestContext();

            // Act
            var ex = Assert.Throws<EvaluateIsDisabledException>(() => validator.Validate(context, model));

            // Assert
            Assert.Equal("Unable to run syndication in DSA mode when Evaluate is disabled", ex.Message);
        }

        [Fact]
        public void Validate_RunDsaSyndicationTrueAndEvaluateSettingFalse_ShouldThrowSyndicateException()
        {
            // Arrange
            var repoFake = A.Fake<IEnvironmentSettingsRepository>();
            A.CallTo(() => repoFake.GetEnvironmentSetting(A<string>._, A<int>._))
                .Returns(new EnvironmentSetting { Value = "false" });
            var validator = new SyndicationModelValidator(repoFake);
            var model = new SyndicationModel { RunDsaSyndication = true, DsaMappingId = 1 };
            var context = new RequestContext();

            // Act
            var ex = Assert.Throws<EvaluateIsDisabledException>(() => validator.Validate(context, model));

            // Assert
            Assert.Equal("Unable to run syndication in DSA mode when Evaluate is disabled", ex.Message);
        }

        [Fact]
        public void Validate_RunDsaSyndicationTrueAndEvaluateSettingTrue_ShouldNotThrow()
        {
            // Arrange
            var repoFake = A.Fake<IEnvironmentSettingsRepository>();
            A.CallTo(() => repoFake.GetEnvironmentSetting(A<string>._, A<int>._))
                .Returns(new EnvironmentSetting { Value = "true" });
            var validator = new SyndicationModelValidator(repoFake);
            var model = new SyndicationModel { RunDsaSyndication = true, DsaMappingId = 1 };
            var context = new RequestContext();

            // Act & Assert
            validator.Validate(context, model);
        }

        [Fact]
        public void Validate_RunDsaSyndicationFalse_ShouldNotThrow()
        {
            // Arrange
            var repoFake = A.Fake<IEnvironmentSettingsRepository>();
            var validator = new SyndicationModelValidator(repoFake);
            var model = new SyndicationModel { RunDsaSyndication = false };
            var context = new RequestContext();

            // Act & Assert
            validator.Validate(context, model);
        }
    }
}
