namespace LongRunningJobService.Code
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using DocumentFormat.OpenXml.Spreadsheet;
    using inriver.Expressions.Client.Constants;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Enum;
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Models.inRiver.ApplyExpressions;
    using inRiver.Core.Models.inRiver.DeleteAllLinksForLinkType;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Core.Models.inRiver.MassExcludeTypes;
    using inRiver.Core.Models.inRiver.MassUpdate;
    using inRiver.Core.Persistance;
    using inRiver.Core.Repository;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Extension;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Enums;
    using inRiver.Server.Syndication.Models;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Inriver.StackEssentials.Config;
    using LongRunningJob.Core.Cache;
    using LongRunningJob.Core.Constants;
    using LongRunningJobActor.Abstraction;
    using LongRunningJobActor.Interfaces;
    using LongRunningJobActor.Services;
    using Microsoft.ServiceFabric.Actors;
    using Microsoft.ServiceFabric.Actors.Client;
    using Serilog;
    using LongRunningJob = inRiver.Core.Models.inRiver.LongRunningJob;

    public class LongRunningJobManager
    {
        private const string SystemUserName = "system";

        private static readonly Uri LongRunningJobActorUri = new Uri("fabric:/LongRunningJob/LongRunningJobActorService");

        private readonly JobRepository jobRepository;
        private readonly EnvironmentContextData environmentContext;
        private readonly LongRunningJobCache longRunningJobCache;

        public LongRunningJobManager(string customerSafeName, string environmentSafeName)
        {
            Log.Information(
                "Creating LongRunningJobManager for {customerSafeName}/{environmentSafeName}",
                customerSafeName,
                environmentSafeName);

            this.environmentContext = CustomerEnvironmentManager.Instance.GetContextForEnvironment(
                customerSafeName,
                environmentSafeName,
                StackConfig.Instance.ReadOnlyConfigDatabaseConnectionString.UnScramble());

            var apiCaller = new ApiCaller { Module = "LongRunningJobManager", Username = "system" };

            this.jobRepository = new JobRepository(
                IPMCPersistanceFactory.GetInstance(
                    this.environmentContext.ConnectionString,
                    apiCaller,
                    this.environmentContext.EntityModel,
                    this.environmentContext.EnvironmentId),
                apiCaller);

            this.longRunningJobCache = new LongRunningJobCache();
        }

        public void InitialiseCompletenessJob(string entityTypeId)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.CalculateCompleteness, entityTypeId, LongRunningJobScope.Common, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ExecuteCalculateCompletenessAsync(this.environmentContext, longRunningJobId.Value, entityTypeId);
        }

        public void InitialiseRebuildChannelJob(int channelId)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.RebuildChannel, channelId.ToString(), LongRunningJobScope.Common, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ExecuteRebuildChannel(this.environmentContext, longRunningJobId.Value, channelId);
        }

        public void InitialiseSynchronizeChannelJob(int channelId)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.SynchronizeChannel, channelId.ToString(), LongRunningJobScope.Common, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ExecuteSynchronizeChannel(this.environmentContext, longRunningJobId.Value, channelId);
        }

        public int InitialiseExcelImportJob(string username, ExcelImportModel excelImportModel)
        {
            var context = new RequestContext(this.environmentContext);
            context.Log(inRiver.Remoting.Log.LogLevel.Information, $"ExcelImport Start - {excelImportModel.BatchId}");

            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.ExcelImport, excelImportModel.BatchId, LongRunningJobScope.Personal, username, out var longRunningJobId))
            {
                context.Log(inRiver.Remoting.Log.LogLevel.Information, $"ExcelImport JobId null {excelImportModel.BatchId}");
                return -1;
            }

            context.Log(inRiver.Remoting.Log.LogLevel.Information, $"ExcelImport Kicking tasks off {excelImportModel.BatchId}");

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ExcelImport(this.environmentContext, longRunningJobId.Value, username, excelImportModel);

            return longRunningJobId.Value;
        }

        public void InitialiseExcelExportJob(string username, ExcelExportModel excelExportModel)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.ExcelExport, username, LongRunningJobScope.Personal, username, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ExcelExportAsync(this.environmentContext, longRunningJobId.Value, username, excelExportModel);
        }

        public void InitialiseExcelExportHistoryJob(string username, ExcelExportHistoryModel excelExportHistoryModel)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.ExcelExportHistory, username, LongRunningJobScope.Personal, username, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ExcelExportHistory(this.environmentContext, longRunningJobId.Value, username, excelExportHistoryModel);
        }

        public SyndicationResponseModel InitialiseRunSyndicationJob(string username, SyndicationModel syndicationModel)
        {
            if (syndicationModel.IdentifierType != SyndicationIdentifierType.None)
            {
                syndicationModel.Id = syndicationModel.IdentifierType.GetSyndicationIdentifierByIdentifierType(syndicationModel);
            }

            if (syndicationModel.RunPreview)
            {
                var reviewId = Guid.NewGuid();
                _ = this.CreateSyndicationReviewLongRunningJobActor(reviewId)?.RunReviewAsync(this.environmentContext, syndicationModel, reviewId);

                return new SyndicationResponseModel(reviewId);
            }
            else
            {
                var scope = username == SystemUserName ? LongRunningJobScope.Common : LongRunningJobScope.Personal;
                if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.RunSyndicate, syndicationModel.Id.ToString(), scope, username, out var longRunningJobId, syndicationModel.IdentifierType.GetSyndicationIdentifierTypeString()))
                {
                    return new SyndicationResponseModel(-1);
                }

                _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.RunSyndicateAsync(this.environmentContext, longRunningJobId.Value, syndicationModel, username);

                return new SyndicationResponseModel(longRunningJobId.Value);
            }
        }

        public void InitialiseMassUpdateJob(string username, MassUpdateModel massUpdateModel)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.MassUpdate, username, LongRunningJobScope.Personal, username, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.MassUpdate(this.environmentContext, username, longRunningJobId.Value, massUpdateModel);
        }

        public void InitialiseContentStoreMassExcludeTypes(string username, string identifier, MassExcludeTypeModel massExcludeTypeModel)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.MassExcludeType, identifier, LongRunningJobScope.Personal, username, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ContentStoreMassExcludeTypesAsync(this.environmentContext, longRunningJobId.Value, massExcludeTypeModel);
        }

        public void InitialiseDeleteAllLinksForLinkType(string username, DeleteAllLinksForLinkTypeModel deleteAllLinksForLinkTypeModel)
        {
            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.DeleteAllLinksForLinkType, username, LongRunningJobScope.Personal, username, out var longRunningJobId))
            {
                return;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.DeleteAllLinksForLinkType(this.environmentContext, username, longRunningJobId.Value, deleteAllLinksForLinkTypeModel);
        }

        public bool InitialiseApplyExpressionsJob(string username, ApplyExpressionsModel model)
        {
            var identifier = $"{model.EntityTypeId}::{model.Target}";
            if (model.Target == ExpressionTarget.FIELDTYPEID)
            {
                identifier += $"::{model.FieldTypeId}";
            }

            if (!this.TryCreateLongRunningJob(LongRunningJobsJobType.ApplyExpressions, identifier, LongRunningJobScope.Personal, username, out var longRunningJobId))
            {
                return false;
            }

            _ = this.CreateLongRunningJobActor(longRunningJobId.Value)?.ApplyExpressions(this.environmentContext, username, longRunningJobId.Value, model);
            return true;
        }

        public void CancelJob(int longRunningJobId)
        {
            var actorId = GenerateActorId(
                this.environmentContext.CustomerSafeName,
                this.environmentContext.EnvironmentSafeName,
                longRunningJobId).GetStringId();

            this.longRunningJobCache.SetJobCancelledState(actorId);
            this.jobRepository.UpdateLongRunningJobState(longRunningJobId, LongRunningJobsStatus.Cancelled);
        }

        private static ActorId
            GenerateActorId(string customerSafeName, string environmentSafeName, int longRunningJobId) =>
            new ActorId($"{customerSafeName}-{environmentSafeName}-{longRunningJobId}");

        /// <summary>
        /// IdentifierType will be null for all the long running jobs.
        /// If Syndication Id is 0 meaning no syndication exists then syndication should be done on SyndicationFormat Id,
        /// in this case FileFormatId is set in Identifier and this IdentifierType specifies what data we have in Identifier.
        /// </summary>
        public bool TryCreateLongRunningJob(string jobType, string identifier, LongRunningJobScope scope, string startedBy, [NotNullWhen(true)] out int? jobId, string identifierType = null)
        {
            jobId = null;
            if (this.jobRepository.StartedJobExists(jobType, identifier, identifierType))
            {
                Log.Information("Job {jobType} ID: {identifier} is already running.", jobType, identifier);
                return false;
            }

            //Check for conflicting Jobs
            ConflictingJobType.ConflictingJobTypes.TryGetValue(jobType, out string conflictingJobType);
            if (!String.IsNullOrEmpty(conflictingJobType) && this.jobRepository.StartedJobExists(conflictingJobType))
            {
                Log.Warning("Job {jobType} ID: {identifier} is going to run with conflicting job {conflictingJobType}.", jobType, identifier, conflictingJobType);
            }

            jobId = this.jobRepository.InsertLongRunningJob(new LongRunningJob
            {
                JobType = jobType,
                Identifier = identifier,
                IdentifierType = identifierType,
                State = LongRunningJobsStatus.Queued,
                Metadata = null,
                Scope = scope,
                StartedBy = startedBy,
            });
            return true;
        }

        private bool TryCreateLongRunningJob(string jobType, string identifier, LongRunningJobScope scope, [NotNullWhen(true)] out int? jobId) =>
            this.TryCreateLongRunningJob(jobType, identifier, scope, null, out jobId);

        private ILongRunningJobActor CreateLongRunningJobActor(int longRunningJobId) =>
            ActorProxy.Create<ILongRunningJobActor>(
                GenerateActorId(
                    this.environmentContext.CustomerSafeName,
                    this.environmentContext.EnvironmentSafeName,
                    longRunningJobId),
                LongRunningJobActorUri);

        private ILongRunningJobActor CreateSyndicationReviewLongRunningJobActor(Guid syndicationReviewId) =>
           ActorProxy.Create<ILongRunningJobActor>(
               new ActorId($"{this.environmentContext.CustomerSafeName}-{this.environmentContext.EnvironmentSafeName}-{syndicationReviewId}"),
               LongRunningJobActorUri);
    }
}
