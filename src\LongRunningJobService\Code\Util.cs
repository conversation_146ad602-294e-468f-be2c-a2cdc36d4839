namespace LongRunningJobService.Code
{
    using System.Fabric;

    public class Util
    {
        public static string StackConfigSecretName => GetConfigParameter("StackConfigSecretName");

        public static string KeyVaultBaseUrl => GetConfigParameter("KeyVaultBaseUrl");
        
        public static string DataApiUrl => GetConfigParameter("DataApiUrl");

        public static string DataJobServiceUrl => GetConfigParameter("DataJobServiceUrl");

        public static string ExpressionWorkerServiceUrl => GetConfigParameter("ExpressionWorkerServiceUrl");

        public static string MessagingServiceUrl => GetConfigParameter("MessagingServiceUrl");

        internal static string GetInstrumentationKey() => GetConfigParameter("InstrumentationKey");

        public static string GetRegion() => GetConfigParameter("GeoLocation");

        public static string GetStack() => GetConfigParameter("Stack");

        public static string GetStackGroup() => GetConfigParameter("StackGroup");

        internal static string GetLogLevel()
        {
            return GetConfigParameter("LogLevel");
        }

        private static string GetConfigParameter(string input)
        {
            var configurationPackage = FabricRuntime.GetActivationContext().GetConfigurationPackageObject("Config");
            var parameter = configurationPackage.Settings.Sections["Configuration"].Parameters[input];
            return parameter.Value;
        }
    }
}
