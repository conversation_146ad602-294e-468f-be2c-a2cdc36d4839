namespace inRiver.Server.UnitTests.MassUpdate
{
    using System;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Server.FieldChange;
    using inRiver.Server.Repository;
    using Xunit;

    public class BaseMultiCvlValueStrategyTests
    {
        private const int EntityId = 1;

        private const string FieldTypeId = "fieldTypeId";

        [Fact]
        public void GetOldValues_FieldTypeHasValues_ReturnsArrayOfOldValues()
        {
            // Arrange
            const string oldMultiValues = "value1;value2;value3";
            var fieldRepository = A.Fake<IFieldRepository>();
            A.CallTo(() => fieldRepository.GetFieldValue(EntityId, FieldTypeId))
                .Returns(oldMultiValues);
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var oldValues = baseStrategy.GetOldValues(EntityId, FieldTypeId);

            // Assert
            oldValues.Should().Equal("value1", "value2", "value3");
        }

        [Fact]
        public void GetOldValues_FieldTypeHasNullValue_ReturnsEmptyArray()
        {
            // Arrange
            const string oldMultiValues = null;
            var fieldRepository = A.Fake<IFieldRepository>();
            A.CallTo(() => fieldRepository.GetFieldValue(EntityId, FieldTypeId))
                .Returns(oldMultiValues);
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var oldValues = baseStrategy.GetOldValues(EntityId, FieldTypeId);

            // Assert
            oldValues.Should().Equal(Array.Empty<string>());
        }

        [Fact]
        public void GetOldValues_FieldTypeHasEmptyStringValue_ReturnsEmptyArray()
        {
            // Arrange
            var oldMultiValues = string.Empty;
            var fieldRepository = A.Fake<IFieldRepository>();
            A.CallTo(() => fieldRepository.GetFieldValue(EntityId, FieldTypeId))
                .Returns(oldMultiValues);
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var oldValues = baseStrategy.GetOldValues(EntityId, FieldTypeId);

            // Assert
            oldValues.Should().Equal(Array.Empty<string>());
        }

        [Fact]
        public void ParseNewValues_NewValueIsNotEmpty_ReturnsArrayOfNewValues()
        {
            // Arrange
            const string newValue = "value1;value2;value3";
            var fieldRepository = A.Fake<IFieldRepository>();
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var parsedValues = baseStrategy.ParseNewValues(newValue);

            // Assert
            parsedValues.Should().Equal(new[] { "value1", "value2", "value3" });
        }

        [Fact]
        public void ParseNewValues_NewValuesIsNull_ReturnsEmptyArray()
        {
            // Arrange
            const string newValue = null;
            var fieldRepository = A.Fake<IFieldRepository>();
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var parsedValues = baseStrategy.ParseNewValues(newValue);

            // Assert
            parsedValues.Should().Equal(Array.Empty<string>());
        }

        [Fact]
        public void ParseNewValues_NewValuesIsEmptyString_ReturnsEmptyArray()
        {
            // Arrange
            var newValue = string.Empty;
            var fieldRepository = A.Fake<IFieldRepository>();
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var parsedValues = baseStrategy.ParseNewValues(newValue);

            // Assert
            parsedValues.Should().Equal(Array.Empty<string>());
        }

        [Fact]
        public void ConvertToString_InputParameterIsNotEmpty_ReturnsStringWithCorrectDelimiter()
        {
            // Arrange
            var newValues = new[] { "value1", "value2", "value3" };
            var fieldRepository = A.Fake<IFieldRepository>();
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var stringValues = baseStrategy.ConvertToString(newValues);

            // Assert
            stringValues.Should().Be("value1;value2;value3");
        }

        [Fact]
        public void ConvertToString_InputParameterIsNull_ReturnsEmptyString()
        {
            // Arrange
            var fieldRepository = A.Fake<IFieldRepository>();
            var baseStrategy = A.Fake<BaseMultiCvlValueStrategy>(
                options => options.WithArgumentsForConstructor(
                    () => new AppendMultiCvlValueStrategy(fieldRepository)));

            // Act
            var stringValues = baseStrategy.ConvertToString(null);

            // Assert
            stringValues.Should().Be(string.Empty);
        }
    }
}
