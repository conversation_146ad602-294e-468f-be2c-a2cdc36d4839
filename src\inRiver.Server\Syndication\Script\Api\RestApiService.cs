namespace inRiver.Server.Syndication.Script.Api
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using inRiver.Core.Http;
    using inRiver.Server.Extension;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using Newtonsoft.Json;
    using Serilog;

    public class RestApiService : IRestApiService
    {
        private readonly IHttpClient httpClient;

        private readonly IRestApiSettingsService restApiSettingsService;

        private readonly IRestApiResponseService apiResponseService;

        private readonly IRestApiHeadersService apiHeaderService;

        private readonly IRestApiCacheService restApiCacheService;

        private readonly RequestContext requestContext;

        public RestApiService(
            IHttpClient httpClient,
            IRestApiSettingsService restApiSettingsService,
            IRestApiResponseService apiResponseService,
            IRestApiHeadersService apiHeaderService,
            IRestApiCacheService restApiCacheService,
            RequestContext requestContext)
        {
            this.httpClient = httpClient;
            this.restApiSettingsService = restApiSettingsService;
            this.apiResponseService = apiResponseService;
            this.apiHeaderService = apiHeaderService;
            this.requestContext = requestContext;
            this.restApiCacheService = restApiCacheService;
        }

        public string Fetch(HttpMethod httpMethod, string endpointAlias, int cacheExpiryTimeMinutes, string requestPath = null, object body = null)
        {
            var responseString = string.Empty;
            if (!this.restApiSettingsService.IsActivatedAlias(endpointAlias))
            {
                throw new SyndicateApiException($"Rest api requests for alias '{endpointAlias}' not activated.");
            }

            var aliasSettings = this.restApiSettingsService.GetAliasSettings(endpointAlias);
            var requestUrl = aliasSettings.Url.CreateUrl(requestPath);
            try
            {
                responseString = this.restApiCacheService.GetOrAddAsync(
                    GetCacheKey(httpMethod, requestUrl, body),
                    async () => await this.ExecuteRequestAsync(httpMethod, requestUrl, aliasSettings.Headers, body),
                    cacheExpiryTimeMinutes).GetAwaiter().GetResult();
            }
            catch (SyndicateApiException)
            {
                throw;
            }
            catch (Exception exception)
            {
                this.HandleHttpClientException(exception);
            }

            return responseString;
        }

        private async Task<string> ExecuteRequestAsync(HttpMethod httpMethod, string requestUrl, IDictionary<string, string> headers, object body = null)
        {
            requestUrl.ValidateUrl(mustContainPathAndQuery: true);
            using var requestMessage = new HttpRequestMessage(httpMethod, requestUrl);
            if (body != null)
            {
                requestMessage.Content = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");
            }

            this.apiHeaderService.SetHeaders(requestMessage, headers);
            var response = this.httpClient.Send(requestMessage);

            return await this.apiResponseService.HandleResponseAsync(response, httpMethod);
        }

        private static string GetCacheKey(HttpMethod method, string requestPath, object body = null) => $"{method.Method}:{requestPath}:{(body == null ? null : JsonConvert.SerializeObject(body))}";

        private void HandleHttpClientException(Exception exception)
        {
            Log.Error(
                exception,
                "Unexpected exception when performing API call from Syndication: {customerSafeName}/{environmentSafeName}.",
                this.requestContext?.CustomerSafeName,
                this.requestContext?.EnvironmentSafeName);
            if (exception.InnerException is TaskCanceledException)
            {
                throw new SyndicateApiException("Exception when calling API. Timeout exception.");
            }

            throw new SyndicateApiException($"Unexpected exception when making API call. {exception.Message}");
        }
    }
}
