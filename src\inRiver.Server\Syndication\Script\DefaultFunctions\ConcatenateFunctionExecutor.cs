namespace inRiver.Server.Syndication.Script.DefaultFunctions
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using inRiver.Core.Util;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using Newtonsoft.Json;

    public class ConcatenateFunctionExecutor
    {
        public ConcatenateFunctionExecutor(
            TransformationManager transformationManager,
            FoundField foundField,
            InRiverEntity entity,
            object mainValue,
            bool enableSku,
            SkuInjector skuInjector,
            string skuId,
            IEnumerable<CultureInfo> serverLanguages,
            RequestContext context)
        {
            this.TransformationManager = transformationManager;
            this.FoundField = foundField;
            this.Entity = entity;
            this.MainValue = mainValue;
            this.EnableSKU = enableSku;
            this.SkuInjector = skuInjector;
            this.SkuId = skuId;
            this.Context = context;
            this.DefaultLanguage = serverLanguages.FirstOrDefault()?.Name;
        }

        private TransformationManager TransformationManager { get; }

        private FoundField FoundField { get; }

        private InRiverEntity Entity { get; }

        private object MainValue { get; }

        private bool EnableSKU { get; }

        private SkuInjector SkuInjector { get; }

        private string SkuId { get; }

        private string DefaultLanguage { get; }

        private RequestContext Context { get; }

        public string Execute()
        {
            var args = this.TransformationManager.Args;
            var values = this.TransformationManager.Values;

            // old concatenate function version where we had only two fields.
            // args contained separator value, and values contained 3 elements: [firstFieldLanguage, secondFieldTypeId, secondFieldLanguage].
            var isOldConcatFunction = args.Any() || (values.Any() && !TryDeserialize<ConcatenateFunctionField>(values.First(), out _));
            if (isOldConcatFunction)
            {
                var separator = args.Any() ? args.First() : string.Empty;
                var concatValues = this.GetOldConcatenateValues(values);
                return string.Join(separator, concatValues.Select(value => value == null ? string.Empty : value.ToString()));
            }

            // expanded version of concatenate function. Each element is a serialized ConcatenateFunctionField object.
            var resultFields = this.GetConcatenateValues(values);
            return string.Join(string.Empty, resultFields.Select(field => $"{field.value}{field.separator}"));
        }

        private IEnumerable<object> GetOldConcatenateValues(IEnumerable<string> values)
        {
            var funcValues = values == null ? new List<string>() : values.ToList();
            if (!funcValues.Any() || funcValues.Count != 3)
            {
                throw new SyndicateException("Data structure of concatenate function should contain 3 elements.");
            }

            var concatValues = new List<object>();
            var firstField = TransformationManager.IsFieldLocaleString(this.Entity, this.FoundField)
                ? TransformationManager.GetLocaleStringMainValue(this.MainValue, language: funcValues.ElementAt(0) ?? this.DefaultLanguage)
                : TransformationManager.GetTransformedMainValue(this.MainValue);
            if (firstField != null)
            {
                concatValues.Add(firstField);
            }

            var fieldTypeId = funcValues.ElementAt(1);
            var language = funcValues.ElementAt(2) ?? this.DefaultLanguage;
            var foundInRiverField = TransformationManager.GetInRiverField(this.Entity, fieldTypeId);
            var secondField = TransformationManager.GetLocaleStringValue(
                foundInRiverField,
                fieldTypeId,
                language,
                this.EnableSKU,
                this.SkuInjector,
                this.SkuId);
            if (secondField != null)
            {
                concatValues.Add(secondField);
            }

            return concatValues;
        }

        private IEnumerable<(string value, string separator)> GetConcatenateValues(IEnumerable<string> values)
        {
            var concatValues = new List<(string, string)>();
            var fields = GetSortedConcatenateFields(values);

            foreach (var field in fields)
            {
                // field with index 0 represents Prefix value.
                if (field.Index == 0)
                {
                    concatValues.Add((string.Empty, field.Separator));
                    continue;
                }

                // field with index 1 represents currently processed main field.
                if (field.Index == 1)
                {
                    var firstField = this.GetMainFieldValue(field);
                    concatValues.Add((firstField?.ToString(), field.Separator));
                    continue;
                }

                // fields with other indexes represent concatenated fields.
                var foundInRiverField = TransformationManager.GetInRiverField(this.Entity, field.FieldTypeId);
                var fieldValue = TransformationManager.IsFieldOfSpecifiedDataType(foundInRiverField, DataType.CVL)
                    ? this.GetCvlValue(field, field.FieldTypeId, foundInRiverField?.Data)
                    : TransformationManager.GetLocaleStringValue(
                        foundInRiverField,
                        field.FieldTypeId,
                        field.Language ?? this.DefaultLanguage,
                        this.EnableSKU,
                        this.SkuInjector,
                        this.SkuId);
                concatValues.Add((fieldValue?.ToString(), field.Separator));
            }

            return concatValues;
        }

        private object GetMainFieldValue(ConcatenateFunctionField field)
        {
            var foundInRiverField = TransformationManager.GetInRiverField(this.Entity, this.FoundField?.FieldTypeId);

            if (TransformationManager.IsFieldOfSpecifiedDataType(foundInRiverField, DataType.CVL))
            {
                return this.GetCvlValue(field, this.FoundField?.FieldTypeId, this.MainValue);
            }

            return TransformationManager.IsFieldOfSpecifiedDataType(foundInRiverField, DataType.LocaleString)
                ? TransformationManager.GetLocaleStringMainValue(this.MainValue, field.Language ?? this.DefaultLanguage)
                : TransformationManager.GetTransformedMainValue(this.MainValue);
        }

        private string GetCvlValue(ConcatenateFunctionField field, string fieldTypeId, object value)
        {
            var cvlId = this.GetCvlId(field.CvlId, fieldTypeId);

            if (!ExportManager.CvlValuesDictionary.TryGetValue(cvlId, out var cvlValuesDictionary))
            {
                var cvlValues = this.Context.DataPersistance.GetCVLValuesForCVL(cvlId) ?? new List<CVLValue>();
                cvlValuesDictionary = cvlValues
                    .GroupBy(cvl => cvl.Key)
                    .ToDictionary(x => x.Key, x => x.FirstOrDefault());
                ExportManager.CvlValuesDictionary.TryAdd(cvlId, cvlValuesDictionary);
            }

            if (value == null || string.IsNullOrEmpty(value.ToString()))
            {
                return null;
            }

            var keys = value.ToString().Split(';');
            var values = keys.Select(key => {
                if (!cvlValuesDictionary.TryGetValue(key, out var cvlValue))
                {
                    throw new SyndicateException($"Concatenate function - Invalid CVL key: {key}, CVL id: {cvlId}.");
                }

                return cvlValue.Value is LocaleString localeString
                    ? localeString[new CultureInfo(field.Language ?? this.DefaultLanguage)]
                    : cvlValue.Value;
            }).ToList();

            return values.All(cvlValue => string.IsNullOrEmpty(cvlValue?.ToString()))
                ? string.Empty
                : string.Join(';', values.Where(x => !string.IsNullOrEmpty(x?.ToString())).Select(x => x.ToString()));
        }

        private string GetCvlId(string cvlId, string fieldTypeId)
        {
            if (!string.IsNullOrEmpty(cvlId))
            {
                return cvlId;
            }

            if (ExportManager.CvlIdsDictionary.TryGetValue(fieldTypeId, out var cvl))
            {
                return cvl;
            }

            cvl = this.Context.DataPersistance.GetCvlIdForFieldType(fieldTypeId);
            if (string.IsNullOrEmpty(cvl))
            {
                throw new SyndicateException("CVL id is not specified for field of type CVL.");
            }

            ExportManager.CvlIdsDictionary.TryAdd(fieldTypeId, cvl);

            return cvl;
        }

        private static IEnumerable<ConcatenateFunctionField> GetSortedConcatenateFields(IEnumerable<string> values)
        {
            var concatFields = new List<ConcatenateFunctionField>();
            foreach (var field in values)
            {
                if (TryDeserialize<ConcatenateFunctionField>(field, out var concatField))
                {
                    concatFields.Add(concatField);
                }
            }

            return concatFields.OrderBy(x => x.Index).ToList();
        }

        private static bool TryDeserialize<T>(string value, out T result)
        {
            if (value == null)
            {
                result = default;
                return false;
            }

            var isDeserialized = true;
            var settings = new JsonSerializerSettings
            {
                Error = (sender, args) => {
                    isDeserialized = false;
                    args.ErrorContext.Handled = true;
                },
            };
            result = JsonConvert.DeserializeObject<T>(value, settings);
            return isDeserialized;
        }
    }
}
