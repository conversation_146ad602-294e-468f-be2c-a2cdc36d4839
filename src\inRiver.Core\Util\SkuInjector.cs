namespace inRiver.Core.Util
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Xml.Linq;
    using System.Xml.XPath;
    using Newtonsoft.Json.Linq;

    public abstract class SkuInjector
    {
        public abstract List<string> GetSkuOutputFieldNames();

        public abstract List<string> GetAllSkuIds();

        public abstract string GetValueForSkuOutputFieldName(string skuId, string outputFieldName);

        public static SkuInjector CreateSkuInjector(string dataString, string skuConfiguration, List<string> explicitSkuFields)
        {
            try
            {
                var xDoc = XDocument.Parse(dataString);
                if (xDoc.XPathSelectElements("SKUs/SKU/Data").Count() >= 1)
                {
                    return new SkuInjectorXmlType2(dataString, GetSkuFieldConfiguration(skuConfiguration), explicitSkuFields);
                }
            }
            catch { }

            try
            {
                var xDoc = XDocument.Parse(dataString);
                if (xDoc.XPathSelectElements("SKUs/SKU").Count() >= 1)
                {
                    return new SkuInjectorXmlType1(dataString, GetSkuFieldConfiguration(skuConfiguration), explicitSkuFields);
                }
            }
            catch { }

            try
            {
                if (!String.IsNullOrEmpty(dataString))
                {
                    dataString = dataString.Replace("\u200B", "");
                    var jSONObjects = JArray.Parse(dataString);
                    if (jSONObjects.HasValues)
                    {
                        return new SkuInjectorJSONType(dataString, GetSkuFieldConfiguration(skuConfiguration), explicitSkuFields);
                    }
                }
            }
            catch { }
            try
            {
                // Last resort, if there are configured sku fields, make sure they are created with empty values
                var skuFieldConfiguration = GetSkuFieldConfiguration(skuConfiguration);
                if (!string.IsNullOrEmpty(skuFieldConfiguration))
                {
                    return new SkuInjectorColumnsWithEmptyValues(skuFieldConfiguration);
                }
            }
            catch { }

            return null;
        }

        private static string GetSkuFieldConfiguration(string skuConfiguration)
        {
            try
            {
                if (!string.IsNullOrEmpty(skuConfiguration))
                {
                    var skuFieldConfiguration = JObject.Parse(skuConfiguration).Value<string>("skuFieldConfiguration");
                    if (!string.IsNullOrEmpty(skuFieldConfiguration))
                    {
                        return skuFieldConfiguration;
                    }
                }
            }
            catch { }

            return null;
        }
    }

    public abstract class SkuInjectorBase : SkuInjector
    {
        protected string _skuData;
        protected const string _skuIdFieldName = "SkuId";
        protected const string _skuNameFieldName = "SkuName";
        protected Dictionary<string, string> _configuredSkuFields = new Dictionary<string, string>();   // input field name and output field name
        protected Dictionary<string, Dictionary<string, string>> _skuValues = new Dictionary<string, Dictionary<string, string>>();   // SKU id, meta field name and value

        public SkuInjectorBase(string skuData, string skuFieldConfiguration, List<string> explicitSkuFields)
        {
            _skuData = skuData;
            SetUpConfiguredFields(skuFieldConfiguration, explicitSkuFields ?? new List<string>());
            CreateAllSkuValues();
        }

        protected abstract void CreateAllSkuValues();

        public override List<string> GetSkuOutputFieldNames()
        {
            if(_configuredSkuFields != null)
            {
                return _configuredSkuFields.Values.ToList();
            }
            else
            {
                var allOutputFieldNames = new List<string>();
                foreach (var skuEntry in _skuValues)
                {
                    foreach (var outputFieldName in skuEntry.Value.Keys)
                    {
                        if (!allOutputFieldNames.Contains(outputFieldName))
                        {
                            allOutputFieldNames.Add(outputFieldName);
                        }
                    }
                }

                return allOutputFieldNames;
            }
        }

        protected void SetUpConfiguredFields(string skuConfiguration, List<string> explicitSkuOutputFields)
        {
            if(skuConfiguration != null)
            {
                try
                {
                    var tmpFields = skuConfiguration.Split(';');
                    var tmpDict = new Dictionary<string, string>();
                    foreach (var tmpField in tmpFields)
                    {
                        var tmpKeyValue = tmpField.Split('=');
                        if (tmpKeyValue.Count() == 2)
                        {
                            tmpDict.Add(tmpKeyValue[0], tmpKeyValue[1]);
                        }
                        else
                        {
                            tmpDict.Add(tmpKeyValue[0], tmpKeyValue[0]);
                        }
                    }

                    if (tmpDict.Count() > 0)
                    {
                        if(explicitSkuOutputFields.Count > 0)
                        {
                            // Exclude fields that are not supplied explicitly
                            tmpDict = tmpDict.Where(x => explicitSkuOutputFields.Contains(x.Value)).ToDictionary(x => x.Key, x => x.Value);
                        }

                        _configuredSkuFields = tmpDict;
                        return;
                    }
                }
                catch { }
            }

            _configuredSkuFields = null;
        }

        public override string GetValueForSkuOutputFieldName(string skuId, string outputFieldName)
        {
            if (_skuValues.ContainsKey(skuId))
            {
                var fieldsForSkuId = _skuValues[skuId];
                if (fieldsForSkuId.ContainsKey(outputFieldName))
                {
                    return fieldsForSkuId[outputFieldName];
                }

            }

            return "";
        }

        protected string GetSkuOutputFieldName(string skuInputFieldName)
        {
            if (_configuredSkuFields != null)
            {
                if (_configuredSkuFields.ContainsKey(skuInputFieldName))
                {
                    var outputName = _configuredSkuFields[skuInputFieldName];
                    if (!string.IsNullOrEmpty(outputName))
                    {
                        return outputName;
                    }

                    return skuInputFieldName; // No output name for this field, use input name instead
                }

                return null; // The field will not be included
            }

            return skuInputFieldName; // No configuration means we use input names as outputs
        }

        public override List<string> GetAllSkuIds()
        {
            return _skuValues.Keys.ToList();
        }
    }

    public class SkuInjectorJSONType : SkuInjectorBase
    {
        //Example data:  "[ { \"Size\": \"S\", \"UPC\": \"123456789\", \"Weight\": \"0.996\", \"Height\": \"0.111\", \"_id\": \"1J793_100\" }, { \"Size\": \"M\", \"UPC\": \"12345\", \"Weight\": \"1.999\", \"Height\": \"0.123\", \"_id\": \"1J793_105\" }, { \"Size\": \"L\", \"UPC\": \"12347867\", \"Weight\": \"1.888\", \"Height\": \"0.222\", \"_id\": \"1J793_106\" } ]";
        public SkuInjectorJSONType(string skuData, string skuFieldConfiguration, List<string> explicitSkuFields) : base(skuData, skuFieldConfiguration, explicitSkuFields)
        {

        }

        protected override void CreateAllSkuValues()
        {
            try
            {
                var jSONObjects = JArray.Parse(_skuData);
                int skuIdCount = 1;
                foreach (JObject root in jSONObjects)
                {
                    var skuId = (skuIdCount++).ToString();
                    var fieldValues = new Dictionary<string, string>(); //  output field name and value
                    var skuIdOutputFieldName = GetSkuOutputFieldName(_skuIdFieldName);
                    if (skuIdOutputFieldName != null) // might not be included in the config
                    {
                        fieldValues.Add(skuIdOutputFieldName, skuId);
                    }
                    foreach (KeyValuePair<String, JToken> tag in root)
                    {
                        var outputFieldName = GetSkuOutputFieldName(tag.Key.ToString());
                        if (!string.IsNullOrEmpty(outputFieldName))
                        {
                            var fieldValue = tag.Value.ToString();
                            fieldValues.Add(outputFieldName, fieldValue);
                        }
                    }
                    _skuValues.Add(skuId, fieldValues);
                }
            }
            catch { }
        }
    }

    public class SkuInjectorXmlType1 : SkuInjectorBase
    {
        // Example data: @"<SKUs> <SKU id=""1J793_100""> <ItemSize>100</ItemSize> <ItemUPC>1234</ItemUPC> <ItemWeight>0.996</ItemWeight> </SKU> <SKU id=""1J793_105""> <ItemSize>105</ItemSize> <ItemUPC>12345</ItemUPC> <ItemWeight>1.021</ItemWeight> </SKU> </SKUs>";
        public SkuInjectorXmlType1(string skuData, string skuFieldConfiguration, List<string> explicitSkuFields) : base(skuData, skuFieldConfiguration, explicitSkuFields)
        {

        }

        protected override void CreateAllSkuValues()
        {
            try
            {
                var xDoc = XDocument.Parse(_skuData);

                foreach (var skuEntry in xDoc.XPathSelectElements("SKUs/SKU"))
                {
                    var skuId = skuEntry.Attribute("id").Value;
                    var fieldValues = new Dictionary<string, string>(); //  output field name and value
                    
                    var skuIdOutputFieldName = GetSkuOutputFieldName(_skuIdFieldName);
                    if (skuIdOutputFieldName != null) // might not be included in the config
                    {
                        fieldValues.Add(skuIdOutputFieldName, skuId);
                    }

                    foreach (var value in skuEntry.Elements())
                    {
                        var outputFieldName = GetSkuOutputFieldName(value.Name.ToString());
                        if (!string.IsNullOrEmpty(outputFieldName))
                        {
                            var fieldValue = value.Value;
                            fieldValues.Add(outputFieldName, fieldValue);
                        }
                    }

                    _skuValues.Add(skuId, fieldValues);
                }
            }
            catch { }
        }
    }

    public class SkuInjectorXmlType2 : SkuInjectorBase
    {
        // EXAMPLE: var dataString = @"<SKUs> <SKU id=""111""> <Name>Name1</Name> <Data> <Key1>Value1</Key1> <Key2>Value2</Key2> </Data> </SKU> <SKU id=""112""> <Name>Name2</Name> <Data> <Key1>Value1</Key1> <Key2>Value2</Key2> </Data> </SKU> </SKUs>";
        public SkuInjectorXmlType2(string skuData, string skuFieldConfiguration, List<string> explicitSkuFields) : base(skuData, skuFieldConfiguration, explicitSkuFields)
        {

        }

        protected override void CreateAllSkuValues()
        {
            try
            {
                var xDoc = XDocument.Parse(_skuData);

                foreach (var skuEntry in xDoc.XPathSelectElements("SKUs/SKU"))
                {
                    var skuId = skuEntry.Attribute("id").Value;
                    var skuName = skuEntry.XPathSelectElement("Name").Value;
                    var fieldValues = new Dictionary<string, string>(); // output field name and value

                    var skuIdOutputFieldName = GetSkuOutputFieldName(_skuIdFieldName);
                    if(skuIdOutputFieldName != null) // might not be included in the config
                    {
                        fieldValues.Add(skuIdOutputFieldName, skuId);
                    }

                    var skuNameOutputFieldName = GetSkuOutputFieldName(_skuNameFieldName);
                    if (skuNameOutputFieldName != null) // might not be included in the config
                    {
                        fieldValues.Add(skuNameOutputFieldName, skuName);
                    }

                    foreach (var value in skuEntry.XPathSelectElement("Data").Elements())
                    {
                        var outputFieldName = GetSkuOutputFieldName(value.Name.ToString());
                        if (!string.IsNullOrEmpty(outputFieldName))
                        {
                            var fieldValue = value.Value;
                            fieldValues.Add(outputFieldName, fieldValue);
                        }
                    }

                    _skuValues.Add(skuId, fieldValues);
                }
            }
            catch { }
        }
    }

    public class SkuInjectorColumnsWithEmptyValues : SkuInjectorBase
    {
        public SkuInjectorColumnsWithEmptyValues(string skuFieldConfiguration) : base(null, skuFieldConfiguration, null)
        {

        }

        protected override void CreateAllSkuValues()
        {
            try
            {
                var fieldValues = new Dictionary<string, string>(); // output field name and value
                foreach(var col in _configuredSkuFields)
                {
                    fieldValues.Add(col.Value, null);
                }

                _skuValues.Add("", fieldValues);
            }
            catch { }
        }
    }
}
