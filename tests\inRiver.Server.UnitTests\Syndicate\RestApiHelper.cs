namespace inRiver.Server.UnitTests.Syndicate
{
    using System.Collections.Concurrent;
    using FakeItEasy;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Syndication.Script.Api;
    using inRiver.Server.Syndication.Script.Api.Models;

    public class RestApiHelper
    {
        public static RestApiCacheService CreateRestApiCacheService(ConcurrentDictionary<string, CacheModel> cache = null)
        {
            var cloudBlobManager = A.Fake<ICloudBlobManager>();

            return A.Fake<RestApiCacheService>(options => options.WithArgumentsForConstructor(() => new RestApiCacheService(cloudBlobManager, cache)));
        }
    }
}
