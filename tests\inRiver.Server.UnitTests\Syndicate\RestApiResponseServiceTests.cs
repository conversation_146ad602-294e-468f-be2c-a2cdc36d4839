namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using FluentAssertions;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Script.Api;
    using Xunit;

    public class RestApiResponseServiceTests
    {
        public const string DefaultResponseContent = "Content";

        private readonly HttpResponseMessage successResponseMessage = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(DefaultResponseContent, Encoding.UTF8, "application/json")
        };

        [Theory]
        [InlineData("Get")]
        [InlineData("Delete")]
        [InlineData("Post")]
        [InlineData("Put")]
        [InlineData("Patch")]
        public async Task HandleResponseAsync_SuccessStatusCode_ShouldReturnCorrectContentAsync(string httpMethod)
        {
            // Arrange
            var restApiResponseService = new RestApiResponseService();

            // Act
            var responseValue = await restApiResponseService.HandleResponseAsync(this.successResponseMessage, new HttpMethod(httpMethod)).ConfigureAwait(false);

            // Assert
            responseValue.Should().BeEquivalentTo(DefaultResponseContent);
        }

        [Theory]
        [InlineData("Get")]
        [InlineData("Delete")]
        [InlineData("Post")]
        [InlineData("Put")]
        [InlineData("Patch")]
        public void HandleResponse_UnsuccessfulStatusCode_ShouldThrowExceptionWithCorrectMessage(string httpMethod)
        {
            // Arrange
            var restApiResponseService = new RestApiResponseService();
            using var responseMessage = new HttpResponseMessage(HttpStatusCode.InternalServerError);

            // Act
            Func<Task> act = async () => await restApiResponseService.HandleResponseAsync(responseMessage, new HttpMethod(httpMethod)).ConfigureAwait(false);

            // Assert
            act.Should().ThrowAsync<SyndicateApiException>()
                .WithMessage($"Unsuccessful response code when trying to make a '{httpMethod}' API request. Response status code: {responseMessage.StatusCode}.");
        }
    }
}
