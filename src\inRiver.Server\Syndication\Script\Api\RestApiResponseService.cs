namespace inRiver.Server.Syndication.Script.Api
{
    using System.Net.Http;
    using System.Threading.Tasks;
    using inRiver.Server.Syndication.Exceptions;

    public class RestApiResponseService : IRestApiResponseService
    {
        public async Task<string> HandleResponseAsync(HttpResponseMessage responseMessage, HttpMethod httpMethod)
        {
            var responseString = string.Empty;
            if (responseMessage.IsSuccessStatusCode)
            {
                responseString = await responseMessage.Content.ReadAsStringAsync();
            }
            else
            {
                this.HandleUnsuccessfulResponse(responseMessage, httpMethod);
            }

            return responseString;
        }

        private void HandleUnsuccessfulResponse(HttpResponseMessage responseMessage, HttpMethod currentMethod)
        {
            var message = $"Unsuccessful response code when trying to make a '{currentMethod.Method}' API request {responseMessage.RequestMessage.RequestUri}. Response status code: {responseMessage.StatusCode}.";
            throw new SyndicateApiException(message);
        }
    }
}
