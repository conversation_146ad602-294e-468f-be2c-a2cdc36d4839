namespace Telemetry.Constants
{
    public static class Dimension
    {
        public static string Environment { get; } = "environment";

        public static string Method { get; } = "method";

        public static string Success { get; } = "success";

        public static string JobId => "jobid";

        public static string ReviewId => "reviewId";

        public static string Status => "status";

        public static string ExtensionId => "extensionId";

        public static string ExecutionTimeInMs => "executionTimeInMs";

        public static string ExecutionTimeInMsPerEntity => "executionTimeInMsPerEntity";

        public static string NumberOfEntities => "numberOfEntities";

        public static string NumberOfDefaultFunctions => "numberOfDefaultFunctions";

        public static string NumberOfCustomFunctions => "numberOfCustomFunctions";

        public static string Username => "Username";
    }
}
