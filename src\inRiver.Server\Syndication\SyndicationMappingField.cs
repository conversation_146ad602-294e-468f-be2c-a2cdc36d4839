namespace inRiver.Server.Syndication
{
    using System.Collections.Generic;

    public class SyndicationMappingField
    {
        public int? MappingFieldId { get; set; }

        public int MappingFormatId { get; set; }

        public int MappingId { get; set; }

        public string MappingFormatName { get; set; }

        public string MappingFormatImageUrl { get; set; }

        public string MappingName { get; set; }

        public string WorkareaEntityTypeId { get; set; }

        public string FirstRelatedEntityTypeId { get; set; }

        public string SecondRelatedEntityTypeId { get; set; }

        public int MappingFormatFieldId { get; set; }

        public string EntityTypeId { get; set; }

        public string FieldTypeId { get; set; }

        public string FieldDataType { get; set; }

        public string MapFieldTypeId { get; set; }

        public string MapPath { get; set; }

        public List<MapEnumeration> Enumerations { get; set; }

        public int? ConverterId { get; set; }

        public string Converter { get; set; }

        public string Script { get; set; }

        public bool IsCustom { get; set; }

        public string Args { get; set; }

        public string UnitType { get; set; }

        public string UnitCvl { get; set; }

        public string UnitDefaultValue { get; set; }

        public string UnitValue { get; set; }

        public string DataType { get; set; }

        public bool Mandatory { get; set; }

        public bool Unique { get; set; }

        public bool Recommended { get; set; }

        public int? MaxLength { get; set; }

        public int? MappingFieldGroupId { get; set; }

        public string DefaultValue { get; set; }

        public string MetaData { get; set; }

        public string MappingFormatFileName { get; set; }

        public bool EnableSKU { get; set; }

        public string Description { get; set; }

        public bool CvlCompleteness { get; set; }

        public int SortOrder { get; set; }
    }
}
