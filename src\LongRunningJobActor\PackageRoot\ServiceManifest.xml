﻿<?xml version="1.0" encoding="utf-8"?>
<ServiceManifest xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" Name="LongRunningJobActorPkg" Version="1.0.0" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <ServiceTypes>
    <StatefulServiceType ServiceTypeName="LongRunningJobActorServiceType" HasPersistedState="true">
      <Extensions>
        <Extension Name="__GeneratedServiceType__" GeneratedId="7a6e292f-5c20-43e2-85d1-984afb669ab6|Persisted">
          <GeneratedNames xmlns="http://schemas.microsoft.com/2015/03/fabact-no-schema">
            <DefaultService Name="LongRunningJobActorService" />
            <ReplicatorEndpoint Name="LongRunningJobActorServiceReplicatorEndpoint" />
            <ReplicatorConfigSection Name="LongRunningJobActorServiceReplicatorConfig" />
            <ReplicatorSecurityConfigSection Name="LongRunningJobActorServiceReplicatorSecurityConfig" />
            <ServiceEndpointV2_1 Name="LongRunningJobActorServiceEndpointV2_1" />
          </GeneratedNames>
        </Extension>
      </Extensions>
    </StatefulServiceType>
  </ServiceTypes>
  <CodePackage Name="Code" Version="1.0.0">
    <EntryPoint>
      <ExeHost>
        <Program>LongRunningJobActor.exe</Program>
      </ExeHost>
    </EntryPoint>
    <EnvironmentVariables>
      <EnvironmentVariable Name="AzureServicesAuthConnectionString" Value="" />
      <EnvironmentVariable Name="AZURE_CLIENT_ID" Value="" />
      <EnvironmentVariable Name="AZURE_TENANT_ID" Value="" />
      <EnvironmentVariable Name="AZURE_CLIENT_SECRET" Value="" />
    </EnvironmentVariables>
  </CodePackage>
  <ConfigPackage Name="Config" Version="1.0.0" />
  <Resources>
    <Endpoints>
      <Endpoint Name="LongRunningJobActorServiceReplicatorEndpoint" />
      <Endpoint Name="LongRunningJobActorServiceEndpointV2_1" />
    </Endpoints>
  </Resources>
  <!-- The content will be generated during build -->
</ServiceManifest>