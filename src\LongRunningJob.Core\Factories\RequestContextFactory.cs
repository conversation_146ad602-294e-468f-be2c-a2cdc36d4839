namespace LongRunningJob.Core.Factories
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading.Tasks;
    using inRiver.Server.Request;
    using Inriver.StackEssentials.Abstractions;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Config;
    using Microsoft.Extensions.Options;

    public class RequestContextFactory : IRequestContextFactory
    {
        private readonly IStackConfig stackConfig;
        private readonly ICustomerEnvironmentRepository customerEnvironmentRepository;
        private readonly SendGridSettings sendGridSettings;
        private readonly InriverServiceSettings inriverServiceSettings;

        public RequestContextFactory(IStackConfig stackConfig, IOptions<SendGridSettings> sendGridSettings, IOptions<InriverServiceSettings> inriverServiceSettings, ICustomerEnvironmentRepository customerEnvironmentRepository)
        {
            this.stackConfig = stackConfig;
            this.customerEnvironmentRepository = customerEnvironmentRepository;
            this.sendGridSettings = sendGridSettings.Value;
            this.inriverServiceSettings = inriverServiceSettings.Value;
        }

        public async Task<RequestContext> CreateAsync(string customerSafename, string environmentSafename)
        {
            var environmentContext = await this.customerEnvironmentRepository.GetAsync(customerSafename, environmentSafename);
            return this.Create(environmentContext);
        }

        private RequestContext Create(EnvironmentContextData environmentContext)
        {
            var context = new RequestContext(environmentContext)
            {
                Username = "system",
                DataLanguage = new CultureInfo("en"),
                ModelLanguage = new CultureInfo("en"),
                Roles = new List<string>(),
                Permissions = new List<string>(),
                Module = "LongRunningJob",
                ConfigurationConnectionString = this.stackConfig.ConfigurationDatabaseConnectionString.UnScramble(),
                ReadOnlyConfigDatabaseConnectionString = this.stackConfig.ReadOnlyConfigDatabaseConnectionString.UnScramble(),
                TokenServiceUrl = this.inriverServiceSettings.TokenServiceAddress,
                RequiredHttps = this.inriverServiceSettings.RequiredHttps,
                SendGridApiKey = this.stackConfig.SendGridApiKey.UnScramble(),
                SmtpSendUser = this.sendGridSettings.SmtpSendUser,
                SmtpSendUserName = this.sendGridSettings.SmtpSendUserName
            };

            return context;
        }
    }
}
