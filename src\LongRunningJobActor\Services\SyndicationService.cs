namespace LongRunningJobActor.Services
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using Abstraction;
    using Code;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Enum;
    using inRiver.Core.EnvironmentSettings;
    using inRiver.Core.Http;
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Persistance;
    using inRiver.Core.Repository;
    using inRiver.Remoting.Log;
    using inRiver.Server.Managers;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Repository;
    using inRiver.Server.Request;
    using inRiver.Server.Service;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Enums;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Inriver.StackEssentials.Config;
    using LongRunningJob.Core.Cache;
    using LongRunningJob.Core.Constants;
    using LongRunningJob.Core.Utility;
    using Serilog;
    using Dimension = Telemetry.Metrics.Constants.Dimension;

    public class SyndicationService : ISyndicationService
    {
        private const int DefaultSyndicationBatchSize = 100;

        private readonly LongRunningJobCache longRunningJobCache;

        private readonly CancellationTokenSource tokenSource;

        private readonly ISyndicationConfigService syndicationConfigService;

        private readonly ISyndicationJobResultService syndicationJobResultService;

        private JobRepository jobRepository;

        private LongRunningJobActorHelper jobActorHelper;

        private readonly IOutputAdapterHttpClient outputAdapterHttpClient;

        public SyndicationService(LongRunningJobCache longRunningJobCache, CancellationTokenSource tokenSource, ISyndicationConfigService syndicationConfigService, ISyndicationJobResultService syndicationJobResultService)
        {
            this.longRunningJobCache = longRunningJobCache;
            this.tokenSource = tokenSource;
            this.syndicationConfigService = syndicationConfigService;
            this.syndicationJobResultService = syndicationJobResultService;
            this.outputAdapterHttpClient = StaticServiceProvider.GetRequiredService<IOutputAdapterHttpClient>();
        }

        public async Task RunSyndicateInternalAsync(EnvironmentContextData environmentContextData, int longRunningJobId, SyndicationModel syndicationModel, string actorId, string stackGroup, string username)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);
            var jobMetadataManager = new JobMetadataManager();
            var customerSafeName = environmentContextData?.CustomerSafeName ?? string.Empty;
            var environmentSafeName = environmentContextData?.EnvironmentSafeName ?? string.Empty;

            try
            {
                new SyndicationModelValidator(RepositoryFactory.GetEnvironmentSettingsRepository("LongRunningJob", requestContext.Username, requestContext)).Validate(requestContext, syndicationModel);
                this.CreateJobRepository(environmentContextData, requestContext.Username);
                var syndicationChannelService = new SyndicationChannelService(requestContext);

                _ = jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.MappingName, syndicationModel.MappingName);
                if (!string.IsNullOrEmpty(syndicationModel.WorkareaName))
                {
                    _ = jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.WorkAreaName, syndicationModel.WorkareaName);
                }

                if (!string.IsNullOrEmpty(syndicationModel.ExtensionDisplayName))
                {
                    _ = jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.OutputName, syndicationModel.ExtensionDisplayName);
                }

                if (syndicationModel.ChannelId.HasValue)
                {
                    var channelName = await syndicationChannelService.GetChannelNameAsync(syndicationModel.ChannelId.Value);
                    _ = jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.ChannelName, channelName);
                }

                _ = jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.DisplayName, syndicationModel.Name);
                _ = jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.IsResourceExportEnabled, syndicationModel.IsResourceExportEnabled);
                _ = jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.DisableResourceExportLimitPreCheck, syndicationModel.DisableResourceExportLimitPreCheck);

                // Set status to Running
                this.jobActorHelper.UpdateSyndicationJobStateAndMetadata(actorId, longRunningJobId, LongRunningJobsStatus.Running, jobMetadataManager.GetMetadata());
                var resourceExportService = new ResourceExportService(requestContext, jobMetadataManager, new ResourceExportParsingService(), new CompressionManager(requestContext), syndicationModel);
                var syndicationRepository = new SyndicationRepository(requestContext, resourceExportService, jobMetadataManager, this.syndicationJobResultService, syndicationChannelService);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var extensionManager = new ExtensionManager(requestContext);
                var settings = new Dictionary<string, string>();
                if (syndicationModel.MappingSource is SyndicationMappingSource.Extension ||
                    syndicationModel.OutputDestination is SyndicationOutputDestination.Extension)
                {
                    settings = extensionManager.GetExtensionSettings(syndicationModel.ExtensionId);
                }

                var enableCompression = this.HasEnabledExtensionSetting(settings, "EnableCompression");
                var enableHeader = this.HasEnabledExtensionSetting(settings, "EnableHeader");

                // Get generic data
                this.tokenSource.Token.ThrowIfCancellationRequested();
                var syndicateJobBatchSize = this.GetSyndicationBatchSize(requestContext);
                var exportResult = await syndicationRepository.GetExportResultAsync(syndicationModel, enableCompression, enableHeader, syndicateJobBatchSize, EnvironmentGidHelper.GetEnvironmentGid(stackGroup, customerSafeName, environmentSafeName), this.tokenSource.Token);

                // Run resource export
                Log.Information($"Starting HandleResourceExportAsync with {exportResult?.NumberOfEntities} entities.");
                await resourceExportService.HandleResourceExportAsync(exportResult.ResourceData, this.tokenSource.Token);
                if (resourceExportService.IsResourceExportAllowed)
                {
                    this.jobActorHelper.UpdateSyndicationJobStateAndMetadata(actorId, longRunningJobId, LongRunningJobsStatus.Running, jobMetadataManager.GetMetadata());
                }

                // Run data through extension/output adapter
                if (syndicationModel.OutputDestination == SyndicationOutputDestination.OutputAdapter)
                {
                    if (syndicationModel.DsaMappingId.HasValue)
                    {
                        Log.Information("Sending DSA Syndication data to Output Adapter");
                        await this.outputAdapterHttpClient.PutDsaProductsAsync(longRunningJobId, environmentSafeName, customerSafeName, username, syndicationModel.MappingId, syndicationModel.DsaMappingId.Value, exportResult.Data, CancellationToken.None);
                    }
                    else
                    {
                        Log.Information("Sending Syndication data to Output Adapter");
                        await this.outputAdapterHttpClient.PutProducts(
                            longRunningJobId,
                            environmentSafeName,
                            customerSafeName,
                            EnvironmentGidHelper.GetEnvironmentGid(stackGroup, customerSafeName, environmentSafeName),
                            syndicationModel.MappingId,
                            exportResult.Data,
                            CancellationToken.None);
                    }

                    this.jobActorHelper.UpdateLongRunningJobState(actorId, longRunningJobId, LongRunningJobsStatus.Finished);
                    this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.Success, LongRunningJobsStatus.Finished);
                }
                else
                {
                    Log.Information("Starting CallSyndicationOutputExtensionAsync.");
                    var syndicationExtensionData = new SyndicationExtensionData
                    {
                        JsonData = exportResult.Data,
                        SyndicationName = syndicationModel.Name,
                        LongRunningJobId = longRunningJobId,
                        NumberOfEntities = exportResult.NumberOfEntities,
                        Settings = settings,
                        EnableCompression = enableCompression,
                        Metadata = jobMetadataManager.GetMetadata()
                    };
                    await extensionManager.CallSyndicationOutputExtensionAsync(syndicationModel.ExtensionId, syndicationExtensionData, this.tokenSource.Token);
                    this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.Success, LongRunningJobsStatus.Running);
                }
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.RunSyndicate);
                jobMetadataManager.Update(LongRunningJobMetaDataKeys.Message, $"Syndicate job with id: {longRunningJobId} was cancelled.");
                this.jobActorHelper?.UpdateSyndicationJobStateAndMetadata(actorId, longRunningJobId, LongRunningJobsStatus.Cancelled, jobMetadataManager.GetMetadata());
                this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.Success, LongRunningJobsStatus.Cancelled);
            }
            catch (SyndicateException ex)
            {
                jobMetadataManager.Update(LongRunningJobMetaDataKeys.Message, ex.Message);
                requestContext.Log(LogLevel.Error, $"Error occurred in RunSyndicateAsync, customerEnvironment: {customerSafeName}/{environmentSafeName}, longRunningJobId: {longRunningJobId}, Exception: {ex.Message}");
                this.jobActorHelper?.UpdateSyndicationJobStateAndMetadata(actorId, longRunningJobId, LongRunningJobsStatus.Error, jobMetadataManager.GetMetadata());
                this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.CustomerFailure, LongRunningJobsStatus.Error);
            }
            catch (TimeoutException ex)
            {
                jobMetadataManager.Update(LongRunningJobMetaDataKeys.Message, ex.Message);
                requestContext.Log(LogLevel.Error, $"Error occurred in RunSyndicateAsync, customerEnvironment: {customerSafeName}/{environmentSafeName}, longRunningJobId: {longRunningJobId}, Exception: {ex.Message}");
            }
            catch (Exception e)
            {
                jobMetadataManager.Update(LongRunningJobMetaDataKeys.Message, "Unexpected error occurred when running Syndicate job.");
                Log.Error(e, "Error occurred in RunSyndicateAsync for Customer: {customerSafeName}/{environmentSafeName}. longRunningJobId: {longRunningJobId}", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName, longRunningJobId);
                this.jobActorHelper?.UpdateSyndicationJobStateAndMetadata(actorId, longRunningJobId, LongRunningJobsStatus.Error, jobMetadataManager.GetMetadata());
                this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.Failed, LongRunningJobsStatus.Error);
            }
            finally
            {
                var status = this.syndicationJobResultService.GetStatusMetric();
                var isExtensionOutputDestination = syndicationModel.OutputDestination == SyndicationOutputDestination.Extension;
                if (!isExtensionOutputDestination || status.Equals(LongRunningJobsStatus.Error, StringComparison.InvariantCultureIgnoreCase))
                {
                    var numberOfEntities = this.syndicationJobResultService.GetNumberOfEntitiesMetric();
                    jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.NumberOfEntities, numberOfEntities);
                    this.jobActorHelper?.UpdateSyndicationJobStateAndMetadata(actorId, longRunningJobId, status, jobMetadataManager.GetMetadata());
                }
            }
        }

        public async Task RunReviewInternalAsync(EnvironmentContextData environmentContextData, SyndicationModel syndicationModel, string stackGroup, Guid reviewId)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);
            var customerSafeName = environmentContextData?.CustomerSafeName ?? string.Empty;
            var environmentSafeName = environmentContextData?.EnvironmentSafeName ?? string.Empty;

            try
            {
                new SyndicationModelValidator(RepositoryFactory.GetEnvironmentSettingsRepository("LongRunningJob", requestContext.Username, requestContext)).Validate(requestContext, syndicationModel);
                var jobMetadataManager = new JobMetadataManager();
                this.CreateJobRepository(environmentContextData, requestContext.Username);
                var syndicationChannelService = new SyndicationChannelService(requestContext);

                var resourceExportService = new ResourceExportService(requestContext, jobMetadataManager, new ResourceExportParsingService(), new CompressionManager(requestContext), syndicationModel);
                var syndicationRepository = new SyndicationRepository(requestContext, resourceExportService, jobMetadataManager, this.syndicationJobResultService, syndicationChannelService);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var syndicateJobBatchSize = this.GetSyndicationBatchSize(requestContext);
                await syndicationRepository.GetAndSaveReviewResultAsync(syndicationModel, syndicateJobBatchSize, EnvironmentGidHelper.GetEnvironmentGid(stackGroup, customerSafeName, environmentSafeName), reviewId, this.tokenSource.Token);
                this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.Success, LongRunningJobsStatus.Finished);
            }
            catch (SyndicateException ex)
            {
                requestContext.Log(LogLevel.Error, $"Error occurred in RunReviewInternalAsync, customerEnvironment: {customerSafeName}/{environmentSafeName}, reviewId: {reviewId}, Exception: {ex.Message}");
                this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.CustomerFailure, LongRunningJobsStatus.Error);
            }
            catch (Exception e)
            {
                Log.Error(e,
                    "Error occurred in RunReviewInternalAsync for Customer: {customerSafeName}/{environmentSafeName}. review id: {reviewId}",
                    environmentContextData?.CustomerSafeName,
                    environmentContextData?.EnvironmentSafeName,
                    reviewId);
                this.syndicationJobResultService.SetActorMethodStateAndJobStatus(ActorMethodState.Failed, LongRunningJobsStatus.Error);
            }
        }

        private RequestContext GetRequestContextForEnvironment(EnvironmentContextData environmentContext)
        {
            if (environmentContext == null)
            {
                Log.Error("Environment context data is null.");
                return null;
            }

            try
            {
                var context = new RequestContext(environmentContext)
                {
                    Username = "system",
                    DataLanguage = new CultureInfo("en"),
                    ModelLanguage = new CultureInfo("en"),
                    Roles = new List<string>(),
                    Permissions = new List<string>(),
                    Module = "LongRunningJob",
                    EnvironmentId = environmentContext.EnvironmentId,
                    EnvironmentSafeName = environmentContext.EnvironmentSafeName,
                    CustomerId = environmentContext.CustomerId,
                    CustomerSafeName = environmentContext.CustomerSafeName,
                    ConnectionString = environmentContext.ConnectionString,
                    LogConnectionString = environmentContext.LogConnectionString,
                    LogTable = environmentContext.LogTable,
                    ConfigurationConnectionString = StackConfig.Instance.ConfigurationDatabaseConnectionString.UnScramble(),
                    ReadOnlyConfigDatabaseConnectionString = StackConfig.Instance.ReadOnlyConfigDatabaseConnectionString.UnScramble(),
                    AssetServiceUrl = Util.CheckIfFabricUrlAndParse(environmentContext.AssetServiceInternalUrl),
                    TokenServiceUrl = this.syndicationConfigService.TokenServiceAddress(),
                    RequiredHttps = this.syndicationConfigService.GetRequiredHttps(),
                    SendGridApiKey = StackConfig.Instance.SendGridApiKey.UnScramble(),
                    SmtpSendUser = this.syndicationConfigService.SMTPSendUser(),
                    SmtpSendUserName = this.syndicationConfigService.SMTPSendUserName(),
                    EntityModel = environmentContext.EntityModel
                };

                return context;
            }
            catch (Exception e)
            {
                Log.Error(e, "Unexpected error occurred when trying to get RequestContext for Customer: {customerSafeName}/{environmentSafeName}.", environmentContext.CustomerSafeName, environmentContext.EnvironmentSafeName);
                return null;
            }
        }

        private void CreateJobRepository(EnvironmentContextData environmentContextData, string username)
        {
            var apiCaller = new ApiCaller()
            {
                Username = username,
                Module = nameof(SyndicationService)
            };
            var inRiverPersistance = IPMCPersistanceFactory.GetInstance(
                environmentContextData.ConnectionString,
                apiCaller,
                environmentContextData.EntityModel,
                environmentContextData.EnvironmentId);

            this.jobRepository = new JobRepository(inRiverPersistance, apiCaller);
            this.jobActorHelper = new LongRunningJobActorHelper(this.jobRepository, this.longRunningJobCache);
        }

        private void LogJobIsCancelled(RequestContext requestContext, int jobId, string jobType)
        {
            var logMessage = $"{jobType} job with id: {jobId} was cancelled";
            requestContext.Log(LogLevel.Information, logMessage);
            Log.Information(logMessage, jobId);
        }

        private bool HasEnabledExtensionSetting(IReadOnlyDictionary<string, string> settings, string extensionSetting) => settings.TryGetValue(extensionSetting, out var value) && value.Equals("true", StringComparison.InvariantCultureIgnoreCase);

        private int GetSyndicationBatchSize(RequestContext requestContext)
        {
            var environmentSettingsRepository = RepositoryFactory.GetEnvironmentSettingsRepository("LongRunningJob", requestContext.Username, requestContext);
            var syndicationBatchSizeSetting = environmentSettingsRepository.GetEnvironmentSetting("SYNDICATION_BATCH_SIZE", requestContext.EnvironmentId);


            if (syndicationBatchSizeSetting != null && int.TryParse(syndicationBatchSizeSetting.Value, out var syndicationBatchSize))
            {
                return syndicationBatchSize;
            }

            return DefaultSyndicationBatchSize;
        }
    }
}
