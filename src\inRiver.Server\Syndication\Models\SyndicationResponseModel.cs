namespace inRiver.Server.Syndication.Models
{
    using System;

    /// <summary>
    /// Syndication job response model.
    /// </summary>
    public class SyndicationResponseModel
    {
        /// <summary>
        /// Gets long running job id, if created.
        /// </summary>
        public int? LongRunningJobId { get; }

        /// <summary>
        /// Gets review id if the job was run in review mode.
        /// </summary>
        public Guid? ReviewId { get; }

        /// <summary>
        /// Gets a value indicating whether indicates whether the job ran in review mode.
        /// </summary>
        public bool RunReview { get; }

        public SyndicationResponseModel(int longRunningJobId)
        {
            this.LongRunningJobId = longRunningJobId;
            this.RunReview = false;
        }

        public SyndicationResponseModel(Guid reviewId)
        {
            this.ReviewId = reviewId;
            this.RunReview = true;
        }
    }
}
