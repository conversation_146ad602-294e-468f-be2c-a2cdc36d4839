namespace inRiver.Configuration.Core.Repository
{
    using inRiver.Core.Models;
    using Persistance;

    public class BaseConfigurationCoreRepository
    {
        public BaseConfigurationCoreRepository(BaseConfigurationCorePersistance configurationPersistance, ApiCaller apiCaller)
        {
            this.Persistance = configurationPersistance;

            if (apiCaller != null)
            {
                this.ApiCaller = configurationPersistance.GetApiCaller();

                this.EnvironmentSettingsPersistance =
                    new EnvironmentSettingsPersistance(
                        this.Persistance.ConnectionString,
                        this.Persistance.ReadOnlyConnectionString,
                        this.Persistance.GetCommonLogging(),
                        this.ApiCaller);
            }
        }

        public BaseConfigurationCorePersistance Persistance { get; }

        public IEnvironmentSettingsPersistance EnvironmentSettingsPersistance { get; }

        public ApiCaller ApiCaller { get; }
    }
}
