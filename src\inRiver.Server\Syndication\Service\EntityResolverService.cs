namespace inRiver.Server.Syndication.Service
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Service.Interfaces;

    public class EntityResolverService : IEntityResolverService
    {
        private readonly ISyndicationEntityService entityService;
        private readonly IChannelEntityResolverService channelEntityResolverService;

        public EntityResolverService(ISyndicationEntityService entityService, IChannelEntityResolverService channelEntityResolverService)
        {
            this.entityService = entityService;
            this.channelEntityResolverService = channelEntityResolverService;
        }

        public async Task<IList<int>> GetEntityIdsAsync(SyndicationModel syndicationModel, string mappingEntityType, IList<ExportContainer> previousExportContainers = null)
        {
            if (previousExportContainers != null && previousExportContainers.Any())
            {
                return previousExportContainers.Select(c => c.Id).ToList();
            }

            if (string.IsNullOrEmpty(syndicationModel.WorkareaId) && !syndicationModel.EntityIds.Any() && !syndicationModel.ChannelId.HasValue)
            {
                throw new SyndicateException("WorkareaId, EntityIds or ChannelId must be specified.");
            }

            if (syndicationModel.ChannelId.HasValue)
            {
                var channelEntityIds = await this.channelEntityResolverService.GetEntityIdsFromChannelAsync(
                    syndicationModel.ChannelId.Value,
                    syndicationModel.EntityIds.ToList(),
                    mappingEntityType);
                if (!channelEntityIds.Any())
                {
                    throw new SyndicateException($"Entities in channel/node don't match set entity type in selected mapping. Entity type should be: {mappingEntityType}.");
                }

                return channelEntityIds;
            }

            return syndicationModel.EntityIds.Any()
                ? syndicationModel.EntityIds.ToList()
                : this.entityService.GetWorkAreaEntityIds(syndicationModel.WorkareaId).ToList();
        }
    }
}
