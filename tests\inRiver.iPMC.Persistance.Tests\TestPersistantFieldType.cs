namespace inRiver.iPMC.Persistance.Tests
{
    using Constants;
    using Xunit;

    [Collection("Persistance Collection")]
    public class TestPersistantFieldType : IClassFixture<PersistanceFixture>
    {
        private static PersistanceFixture _persistanceFixture;

        public TestPersistantFieldType(PersistanceFixture persistanceFixture)
        {
            if (_persistanceFixture == null)
            {
                _persistanceFixture = persistanceFixture;
            }
        }

        [Theory]
        [InlineData(false)]
        [InlineData(true)]
        public void TestGetFieldType(bool includeSettings)
        {
            var getFieldTypeWithoutSettings = _persistanceFixture.MockPersistentFieldType.GetFieldType(KnownDbRecords.FieldTypeId, includeSettings);

            Assert.NotNull(getFieldTypeWithoutSettings);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void TestGetAllFieldTypes(bool includeSettings)
        {
            var getAllFieldTypesWithSettings = _persistanceFixture.MockPersistentFieldType
                .GetAllFieldTypes(includeSettings);

            Assert.NotNull(getAllFieldTypesWithSettings);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void TestGetFieldTypesForEntityType(bool includeSettings)
        {
            var getFieldTypesForEntityType = _persistanceFixture.MockPersistentFieldType
                .GetFieldTypesForEntityType(KnownDbRecords.EntityTypeId, includeSettings);

            Assert.NotNull(getFieldTypesForEntityType);
        }

        [Fact]
        public void TestGetFieldTypeSettings()
        {
            var getFieldTypeSettings = _persistanceFixture.MockPersistentFieldType.GetFieldTypeSettings(KnownDbRecords.FieldTypeWithSettings);

            Assert.NotNull(getFieldTypeSettings);
        }

        [Fact]
        public void TestGetAllFieldTypeSettings()
        {
            var getAllFieldTypeSettings = _persistanceFixture.MockPersistentFieldType.GetAllFieldTypeSettings();

            Assert.NotNull(getAllFieldTypeSettings);
        }

    }
}
