{"MappingId": 88, "MappingName": "Test", "WorkareaEntityTypeId": "<PERSON><PERSON>", "FirstRelatedEntityTypeId": "Product", "FirstLinkEntityTypeId": null, "SecondRelatedEntityTypeId": null, "SecondLinkEntityTypeId": null, "OutputEntityTypeId": null, "EnableSKU": false, "FormatId": 40, "ImageUrl": null, "DefaultLanguage": null, "MappingModelList": [{"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "Id", "inRiverDataType": "String", "FormatField": "id", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 8, "MaxLength": 16, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": "\\d+", "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": "description1"}, {"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "Color", "inRiverDataType": null, "FormatField": "color", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 0, "MaxLength": 0, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": "description2"}, {"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "Size", "inRiverDataType": null, "FormatField": "size", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 0, "MaxLength": 0, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": null}, {"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "ItemName", "inRiverDataType": "String", "FormatField": "description", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 1, "MaxLength": 30, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": null}, {"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "ItemName", "inRiverDataType": null, "FormatField": "title", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 1, "MaxLength": 120, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": null}, {"inRiverEntityTypeId": null, "inRiverFieldTypeId": null, "inRiverDataType": null, "FormatField": "color", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 0, "MaxLength": 0, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": "description6"}, {"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "Gender", "inRiverDataType": null, "FormatField": "gender", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 1, "MaxLength": 50, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": null}, {"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "ItemMetaData", "inRiverDataType": null, "FormatField": "metadata", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 1, "MaxLength": 4500, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": null, "MinInstances": null, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": null}, {"inRiverEntityTypeId": "<PERSON><PERSON>", "inRiverFieldTypeId": "TestLocaleString", "inRiverDataType": null, "FormatField": "language", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": false, "Unique": false, "FormatDataType": "String", "MinLength": 0, "MaxLength": 500, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": 25, "MinInstances": 1, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": null}, {"inRiverEntityTypeId": "Product", "inRiverFieldTypeId": "ProductName", "inRiverDataType": null, "FormatField": "product", "FormatFieldId": null, "DefaultValue": null, "ConverterArgs": null, "ConverterClass": null, "ConverterId": null, "Recommended": false, "Mandatory": true, "Unique": false, "FormatDataType": "String", "MinLength": 0, "MaxLength": 150, "Category": "CategoryName", "MaxValue": null, "MinValue": null, "MaxInstances": 10, "MinInstances": 1, "DecimalFormat": null, "RegEx": null, "ConditionalRule": null, "ChildAttributes": null, "Format": "best-buy", "UnitType": null, "UnitCvl": null, "UnitDefaultValue": null, "GroupRequiredFields": null, "EnumerationValues": null, "Path": null, "Description": null}]}