namespace inRiver.Server.Error
{
    using System;
    using System.ServiceModel;
    using inRiver.Remoting.Exceptions;
    using inRiver.Remoting.Faults;
    using inRiver.Remoting.Log;
    using inRiver.Server.Request;

    internal class ErrorUtility
    {
        internal static ArgumentException GetArgumentException(string method, string argument, string message) =>
            new ArgumentException(message, argument);

        internal static DataAccessException GetDataAccessException(string message, Exception innerException) =>
            new DataAccessException(message, innerException);

        internal static OperationCanceledException GetOperationCanceledException(string message) =>
            new OperationCanceledException(message);

        internal static SecurityException GetSecurityException(string message) =>
            new SecurityException(message);

        internal static FaultException GetArgumentFault(string method, string argument, string message)
        {
            ArgumentFault af = new ArgumentFault { Method = method, Argument = argument, Message = message };

            return new FaultException<ArgumentFault>(af, new FaultReason(message));
        }

        internal static FaultException GetOperationCancelledFault(string message)
        {
            OperationCancelledFault ocf = new OperationCancelledFault { Message = message };

            return new FaultException<OperationCancelledFault>(ocf, new FaultReason(message));
        }

        internal static FaultException GetSecurityFault(string message)
        {
            SecurityFault sf = new SecurityFault { Message = message };

            return new FaultException<SecurityFault>(sf, new FaultReason(message));
        }
    }
}
