namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Linq;
    using inRiver.Core.Enum;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Remoting.Log;
    using Serilog;

    partial class inRiverPersistance
    {
        public List<ExcelExportHistoryResultModel> GetExcelExportHistoryForSupplier(int supplierId)
        {
            List<ExcelExportHistoryResultModel> resultList = new List<ExcelExportHistoryResultModel>();

            List<int> contributerIds = new List<int>();

            try
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    using (SqlCommand command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT supplier.[Name] as batch_name, supplier.UploadedBy as supplier_uploadedby, " +
                                              "supplier.Uploaded as supplier_uploaded, supplier.ImportedBy as supplier_importedby, " +
                                              "entity.Imported as entity_imported, entity.EntityId as entityid, entity.[Name] as entity_name, " +
                                              "field.FieldType as fieldtype, field.[Data] as field_data, entity.[Status] as entity_status, field.IsSpecificationField as IsSpecificationField, " +
                                              "field.[OldData] as field_olddata, field.[ApprovalStatus] as field_approval_status, field.[ApprovalStatusUsername] as field_approval_status_username " +
                                              "FROM [xConnectSupplierImportHistory] supplier " +
                                              "inner join [xConnectSupplierImportHistoryEntity] entity " +
                                              "ON entity.BatchId = supplier.BatchId " +
                                              "inner join [xConnectSupplierImportHistoryField] field " +
                                              "ON entity.Id = field.ImportedEntityId " +
                                              "WHERE SupplierId = @SupplierId";

                        command.Parameters.Add("@SupplierId", SqlDbType.Int).Value = supplierId;

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (!reader.HasRows)
                            {
                                reader.Close();
                                connection.Close();

                                return new List<ExcelExportHistoryResultModel>();
                            }

                            while (reader.Read())
                            {
                                var historyResultModel = new ExcelExportHistoryResultModel();

                                historyResultModel.BatchName = reader.GetString(reader.GetOrdinal("batch_name"));


                                historyResultModel.UploadedByUserId = reader.GetInt32(reader.GetOrdinal("supplier_uploadedby"));

                                if (!contributerIds.Contains(historyResultModel.UploadedByUserId))
                                {
                                    contributerIds.Add(historyResultModel.UploadedByUserId);
                                }

                                //UploadedBy will be populated in a method (GetContributerNamesFromIds) below.
                                historyResultModel.UploadedBy = string.Empty;
                                historyResultModel.UploadedDate = reader.GetDateTime(reader.GetOrdinal("supplier_uploaded"));
                                historyResultModel.ImportedBy = reader.GetString(reader.GetOrdinal("supplier_importedby"));

                                historyResultModel.ImportedDate = reader.GetDateTime(reader.GetOrdinal("entity_imported"));
                                historyResultModel.EntityId = reader.GetInt32(reader.GetOrdinal("entityid"));
                                historyResultModel.DisplayName = reader.GetString(reader.GetOrdinal("entity_name"));

                                historyResultModel.FieldTypeId = reader.GetString(reader.GetOrdinal("fieldtype"));
                                historyResultModel.FieldDataBefore = string.Empty;

                                if (!reader.IsDBNull(reader.GetOrdinal("field_data")))
                                {
                                    historyResultModel.FieldDataAfter = reader.GetString(reader.GetOrdinal("field_data"));
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("field_olddata")))
                                {
                                    historyResultModel.FieldDataBefore = reader.GetString(reader.GetOrdinal("field_olddata"));
                                }

                                if (reader.IsDBNull(reader.GetOrdinal("entity_status")))
                                {
                                    historyResultModel.EntityStatus = ImportStatus.Unknown;
                                }
                                else
                                {
                                    ImportStatus status = ImportStatus.Unknown;
                                    if (Enum.TryParse(reader.GetString(reader.GetOrdinal("entity_status")), out status))
                                    {
                                        historyResultModel.EntityStatus = status;
                                    }
                                }

                                if (reader.IsDBNull(reader.GetOrdinal("field_approval_status")))
                                {
                                    historyResultModel.FieldApprovalStatus = null;
                                }
                                else
                                {
                                    historyResultModel.FieldApprovalStatus = reader.GetInt32(reader.GetOrdinal("field_approval_status"));
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("field_approval_status_username")))
                                {
                                    historyResultModel.FieldApprovalStatusUsername = reader.GetString(reader.GetOrdinal("field_approval_status_username"));
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("IsSpecificationField")))
                                {
                                    historyResultModel.IsSpecificationField =
                                        reader.GetBoolean(reader.GetOrdinal("IsSpecificationField"));
                                }

                                resultList.Add(historyResultModel);
                            }

                            connection.Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetExcelExportHistoryForSupplier caught an unexpected exception");
                throw;
            }

            if (contributerIds.Any())
            {
                SetUploadedByFromUserProperty(contributerIds, resultList);
            }

            return resultList;
        }

        public List<ExcelExportHistoryResultModel> GetExcelExportHistoryForEntity(int entityId)
        {
            List<ExcelExportHistoryResultModel> resultList = new List<ExcelExportHistoryResultModel>();

            List<int> contributerIds = new List<int>();

            try
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    using (SqlCommand command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT supplier.[Name] as batch_name, supplier.UploadedBy as supplier_uploadedby, " +
                                              "supplier.Uploaded as supplier_uploaded, supplier.ImportedBy as supplier_importedby, " +
                                              "entity.Imported as entity_imported, entity.EntityId as entityid, entity.[Name] as entity_name, " +
                                              "field.FieldType as fieldtype, field.[Data] as field_data, entity.[Status] as entity_status, field.IsSpecificationField as IsSpecificationField, " +
                                              "field.[OldData] as field_olddata " +
                                              "FROM [xConnectSupplierImportHistory] supplier " +
                                              "inner join [xConnectSupplierImportHistoryEntity] entity " +
                                              "ON entity.BatchId = supplier.BatchId " +
                                              "inner join [xConnectSupplierImportHistoryField] field " +
                                              "ON entity.Id = field.ImportedEntityId " +
                                              "WHERE entity.EntityId = @EntityId";

                        command.Parameters.Add("@EntityId", SqlDbType.Int).Value = entityId;

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (!reader.HasRows)
                            {
                                reader.Close();
                                connection.Close();

                                return new List<ExcelExportHistoryResultModel>();
                            }

                            while (reader.Read())
                            {
                                var historyResultModel = new ExcelExportHistoryResultModel();

                                historyResultModel.BatchName = reader.GetString(reader.GetOrdinal("batch_name"));


                                historyResultModel.UploadedByUserId = reader.GetInt32(reader.GetOrdinal("supplier_uploadedby"));

                                if (!contributerIds.Contains(historyResultModel.UploadedByUserId))
                                {
                                    contributerIds.Add(historyResultModel.UploadedByUserId);
                                }

                                //UploadedBy will be populated in a method (GetContributerNamesFromIds) below.
                                historyResultModel.UploadedBy = string.Empty;
                                historyResultModel.UploadedDate = reader.GetDateTime(reader.GetOrdinal("supplier_uploaded"));
                                historyResultModel.ImportedBy = reader.GetString(reader.GetOrdinal("supplier_importedby"));

                                historyResultModel.ImportedDate = reader.GetDateTime(reader.GetOrdinal("entity_imported"));
                                historyResultModel.EntityId = reader.GetInt32(reader.GetOrdinal("entityid"));
                                historyResultModel.DisplayName = reader.GetString(reader.GetOrdinal("entity_name"));

                                historyResultModel.FieldTypeId = reader.GetString(reader.GetOrdinal("fieldtype"));

                                historyResultModel.FieldDataBefore = string.Empty;

                                if (!reader.IsDBNull(reader.GetOrdinal("field_data")))
                                {
                                    historyResultModel.FieldDataAfter = reader.GetString(reader.GetOrdinal("field_data"));
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("field_olddata")))
                                {
                                    historyResultModel.FieldDataBefore = reader.GetString(reader.GetOrdinal("field_olddata"));
                                }

                                if (reader.IsDBNull(reader.GetOrdinal("entity_status")))
                                {
                                    historyResultModel.EntityStatus = ImportStatus.Unknown;
                                }
                                else
                                {
                                    ImportStatus status = ImportStatus.Unknown;
                                    if (Enum.TryParse(reader.GetString(reader.GetOrdinal("entity_status")), out status))
                                    {
                                        historyResultModel.EntityStatus = status;
                                    }
                                }

                                if (!reader.IsDBNull(reader.GetOrdinal("IsSpecificationField")))
                                {
                                    historyResultModel.IsSpecificationField =
                                        reader.GetBoolean(reader.GetOrdinal("IsSpecificationField"));
                                }

                                resultList.Add(historyResultModel);
                            }

                            connection.Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetExcelExportHistoryForEntity caught an unexpected exception");
                throw;
            }

            if (contributerIds.Any())
            {
                SetUploadedByFromUserProperty(contributerIds, resultList);
            }

            return resultList;
        }

        private void SetUploadedByFromUserProperty(List<int> contributerIds, List<ExcelExportHistoryResultModel> resultList)
        {
            Dictionary<int, ExcelExportHistoryContributer> contributerIdAndName = GetContributersNameFromIds(contributerIds);

            foreach (ExcelExportHistoryResultModel resultModel in resultList)
            {
                if (contributerIdAndName.ContainsKey(resultModel.UploadedByUserId))
                {
                    resultModel.UploadedBy = contributerIdAndName[resultModel.UploadedByUserId].Username;
                }
            }
        }

        private Dictionary<int, ExcelExportHistoryContributer> GetContributersNameFromIds(List<int> contributerIds)
        {
            Dictionary<int, ExcelExportHistoryContributer> result = new Dictionary<int, ExcelExportHistoryContributer>();

            try
            {
                using (SqlConnection connection = new SqlConnection(this.ReadonlyConfigConnectionString))
                {
                    using (SqlCommand command = connection.CreateCommand())
                    {

                        command.CommandText = "SELECT Id, FirstName, LastName, Email FROM [xConnectUser] " +
                                              $"WHERE Id IN ({string.Join(",", contributerIds)})";

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var contributer = new ExcelExportHistoryContributer();

                                contributer.Id = reader.GetInt32(reader.GetOrdinal("Id"));
                                contributer.Email = reader.GetString(reader.GetOrdinal("Email"));
                                contributer.FirstName = reader.GetString(reader.GetOrdinal("FirstName"));
                                contributer.LastName = reader.GetString(reader.GetOrdinal("LastName"));

                                if (!result.ContainsKey(contributer.Id))
                                {
                                    result.Add(contributer.Id, contributer);
                                }
                            }
                        }

                        connection.Close();
                    }
                }
            }
            catch (Exception ex)    
            {
                Log.Error(ex, "GetContributersNameFromIds caught an unexpected exception");
                throw;
            }

            return result;
        }
    }
}
