namespace inRiver.Server.Syndication.Script
{
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.iPMC.Persistance;

    public static class ScriptContextHelper
    {
        public static bool IsRelatedEntityFieldsInputValid(string entityName, string field, string[] linkTypeIds, int[] entityLinkPositions) =>
            !string.IsNullOrEmpty(entityName) &&
            entityName.Length <= 64 &&
            !string.IsNullOrEmpty(field) &&
            field.Length <= 64 &&
            linkTypeIds != null &&
            linkTypeIds.Any() &&
            entityLinkPositions != null &&
            linkTypeIds.Length == entityLinkPositions.Length &&
            entityLinkPositions.All(IsValidLinkPosition);

        public static string GetRelatedEntityFieldValue(IList<SyndicationRelatedEntityFieldValue> fieldValues, string relatedEntityId)
        {
            if (fieldValues is null || fieldValues.Count == 0)
            {
                return string.Empty;
            }

            if (!string.IsNullOrEmpty(relatedEntityId))
            {
                if (!int.TryParse(relatedEntityId, out var entityId))
                {
                    return string.Empty;
                }

                var value = fieldValues.FirstOrDefault(fieldValue => fieldValue.EntityId == entityId)?.FieldValue;
                return value ?? string.Empty;
            }

            if (fieldValues.Count == 1)
            {
                return fieldValues.FirstOrDefault()?.FieldValue ?? string.Empty;
            }

            const string quote = "\"";
            const string doubleQuote = "\"\"";
            var resultValues = fieldValues.Select(fieldValue => $"{doubleQuote}{fieldValue.FieldValue.Replace(quote, doubleQuote)}{doubleQuote}");
            return $"{quote}[{string.Join(",", resultValues)}]{quote}";
}

        // Link position can be either 0 or 1, where 0 is the parent relation and 1 is the child relation.
        private static bool IsValidLinkPosition(int position) => position == 0 || position == 1;
    }
}
