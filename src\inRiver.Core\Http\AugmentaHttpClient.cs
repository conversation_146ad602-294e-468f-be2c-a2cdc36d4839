namespace inRiver.Core.Http
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Text.Json;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Extension.Augmenta;
    using inRiver.Core.Models.Augmenta;
    using inRiver.Remoting.Objects;

    public class AugmentaHttpClient : IAugmentaHttpClient
    {
        private readonly HttpClient httpClient;

        private static readonly JsonSerializerOptions camelCaseAndCaseInsensitiveJsonSerializerOptions =
            new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            };

        public AugmentaHttpClient(HttpClient httpClient)
        {
            this.httpClient = httpClient;
        }

        public async Task<PreProcessedEntityResult> PreProcessAsync(
            string eventName,
            Entity entity,
            IReadOnlyList<EntityType> entityTypes,
            string customerSafeName,
            string environmentSafeName,
            CancellationToken cancellationToken)
        {
            var endpointUri =
                new Uri(
                    $"api/{customerSafeName}/{environmentSafeName}/preprocessors/entity/{eventName.ToEntityCommandName()}",
                    UriKind.Relative);

            using (var request = new HttpRequestMessage(HttpMethod.Post, endpointUri))
            {
                request.Properties["ClientName"] = "Auth0Client"; // Set the client name before sending the request
                request.Content = new StringContent(JsonSerializer.Serialize(entity.ToEntityInputModel()),
                    Encoding.UTF8, "application/json");

                var response = await this.httpClient.SendAsync(request, cancellationToken).ConfigureAwait(false);
                var entityResponseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                if ((int)response.StatusCode is 422)
                {
                    var problem = JsonSerializer.Deserialize<ErrorContent>(entityResponseContent,
                        camelCaseAndCaseInsensitiveJsonSerializerOptions);

                    return new PreProcessedEntityResult
                    {
                        Cancel = true,
                        Message = problem.Title,
                    };
                }

                if (response.StatusCode is HttpStatusCode.BadRequest)
                {
                    return new PreProcessedEntityResult
                    {
                        Cancel = true,
                        Message = entityResponseContent,
                    };
                }

                _ = response.EnsureSuccessStatusCode();
                var entityPreProcessed =
                    JsonSerializer.Deserialize<EntityResponse>(entityResponseContent,
                        camelCaseAndCaseInsensitiveJsonSerializerOptions);

                return new PreProcessedEntityResult
                {
                    Entity = UpdateEntityAfterPreProcessing(entity, entityPreProcessed.Data, entityTypes),
                };
            }
        }

        public async Task<PreProcessedLinkResult> PreProcessAsync(
            string eventName,
            Link link,
            string customerSafeName,
            string environmentSafeName,
            CancellationToken cancellationToken)
        {
            var endpointUri = new Uri(
                $"api/{customerSafeName}/{environmentSafeName}/preprocessors/link/{eventName.ToLinkCommandName()}",
                UriKind.Relative);

            using (var request = new HttpRequestMessage(HttpMethod.Post, endpointUri))
            {
                request.Properties["ClientName"] = "Augmenta"; // Set the client name before sending the request
                request.Content = new StringContent(JsonSerializer.Serialize(link.ToLinkInputModel()), Encoding.UTF8,
                    "application/json");

                var response = await this.httpClient.SendAsync(request, cancellationToken).ConfigureAwait(false);
                var responseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                if ((int)response.StatusCode is 422) // HttpStatusCode.UnprocessableEntity
                {
                    var problem = JsonSerializer.Deserialize<ErrorContent>(responseContent,
                        camelCaseAndCaseInsensitiveJsonSerializerOptions);

                    return new PreProcessedLinkResult
                    {
                        Cancel = true,
                        Message = problem.Title,
                    };
                }

                if (response.StatusCode is HttpStatusCode.BadRequest)
                {
                    var problem = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                    return new PreProcessedLinkResult
                    {
                        Cancel = true,
                        Message = problem,
                    };
                }

                _ = response.EnsureSuccessStatusCode();
                var linkPreProcessed =
                    JsonSerializer.Deserialize<LinkResponse>(responseContent,
                        camelCaseAndCaseInsensitiveJsonSerializerOptions);

                return new PreProcessedLinkResult
                {
                    Link = UpdateLinkAfterPreProcessing(link, linkPreProcessed.Data),
                };
            }
        }

        private static Link UpdateLinkAfterPreProcessing(Link link, LinkOutputModel linkPreProcessed)
        {
            if (linkPreProcessed == null)
            {
                return null;
            }

            link.Index = linkPreProcessed.Index;
            link.Inactive = linkPreProcessed.Inactive;
            return link;
        }

        private static Entity UpdateEntityAfterPreProcessing(Entity entity, EntityOutputModel entityPreProcessed,
            IEnumerable<EntityType> entityTypes)
        {
            if (entityPreProcessed == null)
            {
                return null;
            }

            var entityType = entityTypes.FirstOrDefault<EntityType>(et =>
                string.Equals(et.Id, entityPreProcessed.EntityTypeId, StringComparison.Ordinal));
            if (entityType == null)
            {
                return null;
            }

            entity.Id = entityPreProcessed.Id;
            entity.EntityType = entityType;
            entity.FieldSetId = entityPreProcessed.FieldSetId;
            entity.Fields?.Where(x => !( x is null ))
                .ToList()
                .ForEach(field =>
                    field.UpdateFieldData(
                        entityPreProcessed.Fields.First(fieldOutputModel =>
                            string.Equals(fieldOutputModel.FieldTypeId, field.FieldType.Id, StringComparison.Ordinal)),
                        entityType.FieldTypes.First(fieldType =>
                            string.Equals(fieldType.Id, field.FieldType.Id, StringComparison.Ordinal)).DataType));
            entity.Segment.Id = entityPreProcessed.SegmentId;

            return entity;
        }
    }
}
