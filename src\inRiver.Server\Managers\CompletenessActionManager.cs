namespace inRiver.Server.Managers
{
    using System;
    using System.Globalization;
    using System.Linq;
    using System.Text.RegularExpressions;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Repository;
    using inRiver.Server.Request;

    internal class CompletenessActionManager
    {
        private CompletenessRepository unsafeCompletenessRepository;

        private CompletenessRepository CompletenessRepository
            =>
                this.unsafeCompletenessRepository
                ?? (this.unsafeCompletenessRepository = new CompletenessRepository(this.context));

        private DataRepository unsafeDataRepository;

        private DataRepository DataRepository
            => this.unsafeDataRepository ?? (this.unsafeDataRepository = new DataRepository(this.context));

        private ModelRepository unsafeModelRepository;

        private ModelRepository ModelRepository
            => this.unsafeModelRepository ?? (this.unsafeModelRepository = new ModelRepository(this.context));

        private UserRepository unsafeUserRepository;

        private UserRepository UserRepository
            => this.unsafeUserRepository ?? (this.unsafeUserRepository = new UserRepository(this.context));

        private UtilityRepository unsafeUtilityRepository;

        private UtilityRepository UtilityRepository
            => this.unsafeUtilityRepository ?? (this.unsafeUtilityRepository = new UtilityRepository(this.context));

        private readonly RequestContext context;

        public CompletenessActionManager(RequestContext context)
        {
            this.context = context;
        }

        public void CompletenessRuleComplete(int entityId, int definitionId, int groupId, int ruleId)
        {
            foreach (var completenessAction in
                this.CompletenessRepository.GetCompletenessActionsByDefAndRule(
                    definitionId,
                    ruleId,
                    CompletenessAction.ActionTriggerRuleComplete))
            {
                this.AddTask(completenessAction, entityId);
            }
        }

        public void CompletenessGroupComplete(int entityId, int definitionId, int groupId)
        {
            foreach (var completenessAction in
                this.CompletenessRepository.GetCompletenessActionsByDefAndGroup(
                    definitionId,
                    groupId,
                    CompletenessAction.ActionTriggerGroupComplete))
            {
                this.AddTask(completenessAction, entityId);
            }
        }

        public void EntityComplete(int entityId)
        {
            var entity = this.DataRepository.GetEntity(entityId, LoadLevel.Shallow);
            var definitionId = this.CompletenessRepository.GetCompletenessDefinitionIdForEntityType(entity.EntityTypeId);

            if (!definitionId.HasValue)
            {
                return;
            }

            foreach (var completenessAction in
                this.CompletenessRepository.GetCompletenessActionsByDefAndRule(
                    definitionId.Value,
                    -1,
                    CompletenessAction.ActionTriggerEntityComplete))
            {
                this.AddTask(completenessAction, entityId);
            }
        }

        public void EntityCreated(int entityId)
        {
            var entity = this.DataRepository.GetEntity(entityId, LoadLevel.Shallow);
            var definitionId = this.CompletenessRepository.GetCompletenessDefinitionIdForEntityType(entity.EntityTypeId);

            if (!definitionId.HasValue)
            {
                return;
            }

            foreach (var completenessAction in
                this.CompletenessRepository.GetCompletenessActionsByDefAndRule(
                    definitionId.Value,
                    -1,
                    CompletenessAction.ActionTriggerEntityCreated))
            {
                this.AddTask(completenessAction, entityId);
            }
        }

        private void AddTask(CompletenessAction completenessAction, int entityId)
        {
            var targetEntity = this.DataRepository.GetFullEntity(entityId, LoadLevel.Shallow);

            if (completenessAction.ActionType?.Contains("task") ?? true)
            {
                var entityType = this.ModelRepository.GetEntityType("Task");
                var taskEntity = Entity.CreateEntity(entityType);

                var field = taskEntity.DisplayName;
                if (field != null)
                {
                    field.Data = completenessAction.TaskName;
                }
                else if (taskEntity.GetField("TaskName") != null)
                {
                    field = taskEntity.GetField("TaskName");
                    field.Data = completenessAction.TaskName;
                }

                field = taskEntity.DisplayDescription;
                if (field != null)
                {
                    field.Data = completenessAction.TaskDescription;
                }
                else if (taskEntity.GetField("TaskDescription") != null)
                {
                    field = taskEntity.GetField("TaskDescription");

                    field.Data = completenessAction.TaskDescription;
                }

                field = taskEntity.GetField("TaskCreatedBy");
                field.Data = completenessAction.TaskCreatedBy;

                field = taskEntity.GetField("TaskAssignedTo");
                field.Data = completenessAction.TaskAssignedTo;

                field = taskEntity.GetField("TaskStatus");
                field.Data = "New";

                taskEntity.Id = this.DataRepository.AddEntity(taskEntity).Id;

                var linkTypes = this.ModelRepository.GetLinkTypesForEntityType("Task");

                var taskLinkType =
                    linkTypes.FirstOrDefault(
                        linkType =>
                            linkType.SourceEntityTypeId == "Task"
                            && linkType.TargetEntityTypeId == targetEntity.EntityType.Id);

                if (taskLinkType == null)
                {
                    return;
                }

                var link = new Link { LinkType = taskLinkType, Target = targetEntity, Source = taskEntity };

                this.DataRepository.AddLink(link);
            }

            if (completenessAction.ActionType == null || !completenessAction.ActionType.Contains("email"))
            {
                return;
            }

            var user = this.UserRepository.GetUserByUsername(completenessAction.TaskAssignedTo);
            var userSettings = this.UserRepository.GetAllUserSettings(completenessAction.TaskAssignedTo);
            var language = userSettings["DataLanguage"];
            var cultureInfo = new CultureInfo(language);
            var body = $"{completenessAction.TaskDescription}/n/r{targetEntity.EntityType.Name[cultureInfo]} {this.GetDisplayName(targetEntity, cultureInfo)}";
#pragma warning disable 4014 // The mail can be sent asynchronous
            this.UtilityRepository.SendMail(completenessAction.TaskName, body, user.Email, false);
#pragma warning restore 4014
        }

        public string GetDisplayName(Entity entity, CultureInfo cultureInfo)
        {
            var defaultValue = entity.Id.ToString(cultureInfo);

            if (entity.DisplayName == null)
            {
                return GetFormattedDefaultValue(defaultValue);
            }

            var fieldData = entity.DisplayName.Data;
            var fieldType = entity.DisplayName.FieldType;

            string value;

            if (fieldData == null)
            {
                value = string.Empty;
            }
            else if (fieldType.DataType == DataType.Boolean)
            {
                value = ((bool)fieldData).ToString(cultureInfo);
            }
            else if (fieldType.DataType == DataType.CVL)
            {
                var keys = fieldData.ToString().Split(';');
                var cvlValues = keys.Select(key => new CVLValue
                    {
                        CVLId = fieldType.CVLId,
                        Key = key
                    })
                    .ToList();

                value = string.Join(
                    ";",
                    cvlValues.Select(
                        cvlValue =>
                            cvlValue != null ? this.GetCvlDisplayValue(cvlValue.CVLId, cvlValue.Key, cultureInfo) : "-"));
            }
            else if (fieldType.DataType == DataType.DateTime)
            {
                value = GetDisplayDateTime((DateTime)fieldData, cultureInfo);
            }
            else if (fieldType.DataType == DataType.Double)
            {
                value = ((double)fieldData).ToString(cultureInfo);
            }
            else if (fieldType.DataType == DataType.Integer)
            {
                value = ((int)fieldData).ToString(cultureInfo);
            }
            else if (fieldType.DataType == DataType.LocaleString)
            {
                value = GetDisplayValue((LocaleString)fieldData, null, cultureInfo);
            }
            else if (fieldType.DataType == DataType.Xml)
            {
                return ((string)fieldData).Replace("<", "&lt;").Replace(">", "&gt");
            }
            else
            {
                value = (string)fieldData;
            }

            value = GetSafeDisplayValue(value);

            if (string.IsNullOrEmpty(value))
            {
                value = GetFormattedDefaultValue(defaultValue);
            }

            return value;
        }

        public string GetCvlDisplayValue(string cvlId, string cvlKey, CultureInfo cultureInfo)
        {
            var cvlValue = this.ModelRepository.GetCVLValueByKey(cvlKey, cvlId);

            if (cvlValue == null)
            {
                return string.Empty;
            }

            var value = cvlValue.Value?.ToString();

            if (string.IsNullOrEmpty(value))
            {
                return "[" + cvlValue.Key + "]";
            }

            if (cvlValue.Value.GetType() != typeof(LocaleString))
            {
                return value;
            }

            var localeString = (LocaleString)cvlValue.Value;
            return GetDisplayValue(localeString, cvlValue.Key, cultureInfo);
        }

        public static string GetDisplayValue(LocaleString localeString, string defaultValue, CultureInfo cultureInfo)
        {
            return string.IsNullOrEmpty(localeString?[cultureInfo])
                       ? GetFormattedDefaultValue(defaultValue)
                       : localeString[cultureInfo];
        }

        private static string GetFormattedDefaultValue(string defaultValue)
        {
            return string.IsNullOrEmpty(defaultValue) ? string.Empty : GetSafeDisplayValue($"[{defaultValue}]");
        }

        private static string GetSafeDisplayValue(string displayValue)
        {
            return Regex.Replace(displayValue, "<.*?>", string.Empty);
        }

        public static string GetDisplayDateTime(DateTime dateTime, CultureInfo cultureInfo)
        {
            return dateTime.ToString("g", cultureInfo);
        }
    }
}
