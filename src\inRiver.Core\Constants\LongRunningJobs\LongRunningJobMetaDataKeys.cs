namespace inRiver.Core.Constants.LongRunningJobs
{
    public static class LongRunningJobMetaDataKeys
    {
        public const string ResourceZipMetadata = "resourceZipMetadata";

        public const string MappingName = "mappingName";

        public const string WorkAreaName = "workareaName";

        public const string DisplayName = "displayName";

        public const string FinishedWithErrors = "finishedWithErrors";

        public const string IsResourceExportEnabled = "isResourceExportEnabled";

        public const string DisableResourceExportLimitPreCheck = "disableResourceExportLimitPreCheck";

        public const string Message = "message";

        public const string ChannelName = "channelName";

        public const string ChannelNodeName = "channelNodeName";

        public const string OutputName = "outputName";

        public const string NumberOfEntities = "numberOfEntities";
    }
}
