namespace inRiver.Server.Syndication.Script
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Diagnostics.CodeAnalysis;
    using System.Dynamic;
    using System.Globalization;
    using System.Linq;
    using System.Net.Http;
    using inRiver.Core.Models;
    using inRiver.Core.Util;
    using inRiver.iPMC.Persistance;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.DataAccess.ThirdDataLayer;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Script.Api;
    using Newtonsoft.Json;
    using Field = inRiver.Remoting.Objects.Field;
    using LocaleString = inRiver.Remoting.Objects.LocaleString;

    /// <summary>
    /// This class is used by the RunSyndicate job.
    /// An instance of the class is added to a V8ScriptEngine and thus the methods in this class are accessible from there.
    /// </summary>
    [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "All public methods of this class can be called from JS code of syndication custom functions.")]
    public class ScriptContext : IScriptContext
    {
        private readonly Lazy<IRestApiService> lazyRestApiService;

        private readonly Lazy<IRestApiCacheService> lazyRestApiCacheService;

        private readonly IMaskReplacementService maskReplacementService;

        private InRiverEntity inriverEntity;

        private IDictionary<string, IDictionary<int, IList<SyndicationRelatedEntityFieldValue>>> cachedFields;

        private RequestContext context;
        private IList<CultureInfo> languages;
        private Dictionary<string, string> semanticSoftlinkHits;
        public const string DEFAULT_RETVAL_SEPARATOR = ";";
        public const char DEFAULT_CVL_SEPARATOR = ';';
        private readonly SkuSettingsManager skuSettingsManager;
        private string currentSkuId;

        public ScriptContext(RequestContext ctx, IList<CultureInfo> serverLanguages, SkuSettingsManager skuSettingsManager)
        {
            this.context = ctx ?? throw new ArgumentNullException(nameof(ctx));
            this.languages = serverLanguages ?? throw new ArgumentNullException(nameof(serverLanguages));
            this.skuSettingsManager = skuSettingsManager;
            this.lazyRestApiCacheService = new Lazy<IRestApiCacheService>(() => new RestApiCacheService(new CloudBlobManager(ctx, "syndicate-cache")));
            this.lazyRestApiService = new Lazy<IRestApiService>(() => new RestApiService(
                HttpClientManager.GetExistingOrDefaultHttpClient("syndicationRestApiClient"),
                new RestApiSettingsService(this.context, new AzureCrypto(AppSettings.KeyVaultBaseUrl, AppSettings.KeyVaultServerSettingsEncryptionKeyName)),
                new RestApiResponseService(),
                new RestApiHeadersService(),
                this.RestApiCacheService,
                this.context));
            this.maskReplacementService = new MaskReplacementService();
        }

        private IRestApiService RestApiService => this.lazyRestApiService.Value;

        private IRestApiCacheService RestApiCacheService => this.lazyRestApiCacheService.Value;

        public bool IsValid() => this.languages != null && this.inriverEntity != null;

        public void SetInRiverEntity(InRiverEntity entity) => this.inriverEntity = entity;

        public void SetCurrentSkuId(string skuId) => this.currentSkuId = skuId;

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetSoftLinkEntityFieldValue(string currentFieldValue, string softLinkEntityTypeId, string matchValueFieldTypeId, string returnValueFieldTypeId)
        {
            return this.GetSoftLinkEntitiesFieldValues(currentFieldValue, softLinkEntityTypeId, matchValueFieldTypeId, returnValueFieldTypeId, null);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetSoftLinkEntitiesFieldValues(string currentFieldValue, string softLinkEntityTypeId, string matchValueFieldTypeId, string returnValueFieldTypeId, string retValSeparator = DEFAULT_RETVAL_SEPARATOR)
        {

            bool isValidParams = !string.IsNullOrWhiteSpace(softLinkEntityTypeId) && !string.IsNullOrWhiteSpace(matchValueFieldTypeId) && !string.IsNullOrWhiteSpace(returnValueFieldTypeId);


            if (!this.IsValid() || !isValidParams || currentFieldValue == null || (retValSeparator != null && !Char.TryParse(retValSeparator, out char retValSepChar)))
            {
                return null;
            }

            //only split if there is a separator.
            string[] values = (retValSeparator == null) ? new string[] { currentFieldValue } : currentFieldValue.Split(DEFAULT_CVL_SEPARATOR);

            if (values.Length < 1)
            {
                return null;
            }

            string returnValue = string.Empty;
            bool isFirstHit = true;
            retValSeparator = retValSeparator ?? DEFAULT_RETVAL_SEPARATOR;

            foreach (string currentValue in values)
            {
                string storageKey = currentValue + softLinkEntityTypeId + matchValueFieldTypeId + returnValueFieldTypeId;
                string hitValue = null;
                bool isFound = GetStoredEntityIfExists(storageKey, out hitValue);

                if (!isFound)
                {
                    InRiverField fieldWithRetVal = GetFieldFromPersistance(currentValue, softLinkEntityTypeId, matchValueFieldTypeId, returnValueFieldTypeId);

                    if (fieldWithRetVal == null)
                    {
                        continue;
                    }

                    StoreSoftLinkEntity(storageKey, fieldWithRetVal);
                    hitValue = GetInriverFieldValue(fieldWithRetVal);
                    isFound = true;
                }

                if (isFound)
                {
                    hitValue = hitValue ?? string.Empty;

                    returnValue += (isFirstHit) ? hitValue : retValSeparator + hitValue;
                    isFirstHit = false;
                }

            }

            return returnValue;

        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntityFieldValue(string fieldTypeId)
        {
            return this.GetInriverFieldValue(this.GetCurrentEntityField(fieldTypeId));
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntitySkuFieldValue(string fieldTypeId)
        {
            if (this.skuSettingsManager.DoesSchemaContainField(fieldTypeId))
            {
                var skuField = this.GetCurrentEntityField(this.skuSettingsManager.EntitySkuField);
                if (skuField?.Data != null)
                {
                    var skuInjector = SkuInjector.CreateSkuInjector(skuField.Data.ToString(), string.Empty, this.skuSettingsManager.SchemaSkuFields.ToList());
                    return skuInjector.GetValueForSkuOutputFieldName(this.currentSkuId, fieldTypeId);
                }
            }

            return string.Empty;
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntityFieldDisplayValue(string fieldTypeId, string language)
        {
            return this.GetInriverFieldDisplayValue(this.GetCurrentEntityField(fieldTypeId), language);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntityFieldName(string fieldTypeId, string language)
        {
            return this.GetInriverFieldName(this.GetCurrentEntityField(fieldTypeId), language);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentRelatedEntitiesFieldValue(string fieldTypeId, string entityTypeId)
        {
            return this.GetInriverFieldValue(this.GetCurrentRelatedEntitiesField(fieldTypeId, entityTypeId));
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentRelatedEntitiesFieldDisplayValue(string fieldTypeId, string entityTypeId, string language)
        {
            return this.GetInriverFieldDisplayValue(this.GetCurrentRelatedEntitiesField(fieldTypeId, entityTypeId), language);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentRelatedEntitiesFieldName(string fieldTypeId, string entityTypeId, string language)
        {
            return this.GetInriverFieldName(this.GetCurrentRelatedEntitiesField(fieldTypeId, entityTypeId), language);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntitySpecificationFieldValue(string specificationFieldTypeId)
        {
            return this.GetInriverFieldValue(GetCurrentEntitySpecificationField(specificationFieldTypeId));
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntitySpecificationFieldDisplayValue(string specificationFieldTypeId, string language)
        {
            return this.GetInriverFieldDisplayValue(GetCurrentEntitySpecificationField(specificationFieldTypeId), language);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntitySpecificationFieldDisplayValueDefaultFunction(string specificationFieldTypeId, string entityTypeId, string language, string unit)
        {
            if (this.inriverEntity == null)
            {
                return string.Empty;
            }

            SpecificationField field;

            if (this.inriverEntity.EntityTypeId == entityTypeId)
            {
                field = GetCurrentEntitySpecificationField(specificationFieldTypeId);
            }
            else
            {
                field = GetRelatedEntitySpecificationField(specificationFieldTypeId, entityTypeId);
            }

            return this.GetInriverFieldDisplayValueDefaultFunction(field, language, unit);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntitySpecificationFieldName(string specificationFieldTypeId, string language)
        {
            return this.GetInriverFieldName(GetCurrentEntitySpecificationField(specificationFieldTypeId), language);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntitySpecificationFields()
        {
            if (this.inriverEntity == null)
            {
                return string.Empty;
            }

            var specFields = this.context.DataPersistance.GetSpecificationFieldsForEntity(this.inriverEntity.Id);

            var listOfDynamics = specFields.Where(field => field.SpecificationFieldType != null).Select(field => {
                var obj = new ExpandoObject() as IDictionary<string, object>;
                obj.Add(nameof(field.SpecificationFieldType.Id), field.SpecificationFieldType.Id);
                obj.Add(nameof(field.SpecificationFieldType.Name), field.SpecificationFieldType.Name);
                obj.Add(nameof(field.SpecificationFieldType.Unit), field.SpecificationFieldType.Unit);
                return obj;
            }).ToList();

            return (listOfDynamics.Any()) ? JsonConvert.SerializeObject(listOfDynamics) : string.Empty;
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentRelatedEntitiesSpecificationFieldValue(string specificationFieldTypeId, string entityTypeId)
        {
            var result = this.GetRelatedEntitiesSpecificationFields(specificationFieldTypeId, entityTypeId)?.Select(field => this.GetInriverFieldValue(field));
            return (result != null && result.Any()) ? JsonConvert.SerializeObject(result) : string.Empty;
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentRelatedEntitiesSpecificationFieldDisplayValue(string specificationFieldTypeId, string entityTypeId, string language)
        {
            var result = this.GetRelatedEntitiesSpecificationFields(specificationFieldTypeId, entityTypeId)?.Select(field => this.GetInriverFieldDisplayValue(field, language));
            return (result != null && result.Any()) ? JsonConvert.SerializeObject(result) : string.Empty;
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentRelatedEntitiesSpecificationFieldName(string specificationFieldTypeId, string entityTypeId, string language)
        {
            var relatedSpecificationField = this.GetRelatedEntitiesSpecificationFields(specificationFieldTypeId, entityTypeId)?.FirstOrDefault();

            return this.GetInriverFieldName(relatedSpecificationField, language);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntityFieldSetId() => this.inriverEntity?.FieldSetId ?? string.Empty;

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetCurrentEntityLinkEntitiesFieldValue(string fieldTypeId, string linkTypeId)
        {
            List<dynamic> listOfDynamics = null;

            if (this.inriverEntity == null)
            {
                return string.Empty;
            }

            var linkList = context.DataPersistance.GetFullLinksForEntity(this.inriverEntity.Id);

            if (linkList == null)
            {
                return string.Empty;
            }

            var links = linkList.Where(lnk => lnk.LinkType?.Id == linkTypeId && lnk.LinkEntity != null && lnk.Source != null && lnk.Target != null);
            var linkEntities = links.Select(l => l.LinkEntity).Distinct().ToList();
            var linkEntityFieldDict = new Dictionary<int, Field>();
            listOfDynamics = new List<dynamic>();
            foreach (var linkEntity in linkEntities)
            {
                linkEntityFieldDict[linkEntity.Id] = this.context.DataPersistance.GetFullField(linkEntity.Id, linkEntity.EntityType?.Id, fieldTypeId, new DateTime());
            }
            foreach (var link in links)
            {
                var obj = new ExpandoObject() as IDictionary<string, object>;
                if (link.Source.EntityType.Id != this.inriverEntity.EntityTypeId)
                {
                    obj.Add(nameof(link.Source.Id), link.Source.Id);
                }
                else
                {
                    obj.Add(nameof(link.Target.Id), link.Target.Id);
                }

                var field = linkEntityFieldDict[link.LinkEntity.Id];
                obj.Add(fieldTypeId, this.GetInriverFieldValue(field));
                listOfDynamics.Add(obj);
            }

            return (listOfDynamics != null && listOfDynamics.Any()) ? JsonConvert.SerializeObject(listOfDynamics) : string.Empty;
        }

        //max depth level from the output entity
        static int maxDepth = 5;
        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string GetExtraRelatedEntityFieldDisplayValue(string entityTypeId, string fieldTypeId, string language, string links, string parentRelations, string relatedEntityId = null)
        {
            if (string.IsNullOrEmpty(entityTypeId) ||
                string.IsNullOrEmpty(fieldTypeId) ||
                string.IsNullOrEmpty(links) ||
                string.IsNullOrEmpty(parentRelations))
            {
                return string.Empty;
            }

            if (this.context.EntityModel == 2)
            {
                var result = this.GetFieldsWithValues3DL(entityTypeId, fieldTypeId, links, parentRelations, language);
                var fieldValuesForCurrentEntityExist = result.TryGetValue(this.inriverEntity.Id, out var fieldValues);

                return fieldValuesForCurrentEntityExist
                    ? ScriptContextHelper.GetRelatedEntityFieldValue(fieldValues, relatedEntityId)
                    : string.Empty;
            }
            else
            {
                var fieldValuesByConfigurationKey = this.GetOrAddCachedFieldsWithValues(entityTypeId, fieldTypeId, links, parentRelations, language);
                var fieldValuesForCurrentEntityExist = fieldValuesByConfigurationKey.TryGetValue(this.inriverEntity.Id, out var fieldValues);

                return fieldValuesForCurrentEntityExist
                    ? ScriptContextHelper.GetRelatedEntityFieldValue(fieldValues, relatedEntityId)
                    : string.Empty;
            }
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string RestApiGet(string endpointAlias, string requestPath = null, object cacheExpiryTimeMinutes = null)
        {
            var (replacedRequestPath, _, maskIsUsed) = this.maskReplacementService.Replace(this.inriverEntity.Id, requestPath, body: null);
            var parsedCacheExpiryTimeMinutes = this.RestApiCacheService.ParseExpiryTime(cacheExpiryTimeMinutes, maskIsUsed);

            return this.RestApiService.Fetch(HttpMethod.Get, endpointAlias, parsedCacheExpiryTimeMinutes, replacedRequestPath);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string RestApiDelete(string endpointAlias, string requestPath = null, object cacheExpiryTimeMinutes = null)
        {
            var (replacedRequestPath, _, maskIsUsed) = this.maskReplacementService.Replace(this.inriverEntity.Id, requestPath, body: null);
            var parsedCacheExpiryTimeMinutes = this.RestApiCacheService.ParseExpiryTime(cacheExpiryTimeMinutes, maskIsUsed);

            return this.RestApiService.Fetch(HttpMethod.Delete, endpointAlias, parsedCacheExpiryTimeMinutes, replacedRequestPath);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string RestApiPost(string endpointAlias, string requestPath = null, object body = null, object cacheExpiryTimeMinutes = null)
        {
            var (replacedRequestPath, replacedBody, maskIsUsed) = this.maskReplacementService.Replace(this.inriverEntity.Id, requestPath, body);
            var parsedCacheExpiryTimeMinutes = this.RestApiCacheService.ParseExpiryTime(cacheExpiryTimeMinutes, maskIsUsed);

            return this.RestApiService.Fetch(HttpMethod.Post, endpointAlias, parsedCacheExpiryTimeMinutes, replacedRequestPath, replacedBody);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string RestApiPut(string endpointAlias, string requestPath = null, object body = null, object cacheExpiryTimeMinutes = null)
        {
            var (replacedRequestPath, replacedBody, maskIsUsed) = this.maskReplacementService.Replace(this.inriverEntity.Id, requestPath, body);
            var parsedCacheExpiryTimeMinutes = this.RestApiCacheService.ParseExpiryTime(cacheExpiryTimeMinutes, maskIsUsed);

            return this.RestApiService.Fetch(HttpMethod.Put, endpointAlias, parsedCacheExpiryTimeMinutes, replacedRequestPath, replacedBody);
        }

        [SuppressMessage("ReSharper", "UnusedMember.Global", Justification = "Used implicitly by RunSyndicate job. See class documenation")]
        public string RestApiPatch(string endpointAlias, string requestPath = null, object body = null, object cacheExpiryTimeMinutes = null)
        {
            var (replacedRequestPath, replacedBody, maskIsUsed) = this.maskReplacementService.Replace(this.inriverEntity.Id, requestPath, body);
            var parsedCacheExpiryTimeMinutes = this.RestApiCacheService.ParseExpiryTime(cacheExpiryTimeMinutes, maskIsUsed);

            return this.RestApiService.Fetch(HttpMethod.Patch, endpointAlias, parsedCacheExpiryTimeMinutes, replacedRequestPath, replacedBody);
        }

        public void Dispose()
        {
            if (this.lazyRestApiCacheService.IsValueCreated)
            {
                this.RestApiCacheService.ClearCacheAsync().GetAwaiter().GetResult();
            }
        }

        #region Privates
        private InRiverField GetCurrentEntityField(string fieldTypeId)
        {
            return inriverEntity?.Fields?.SingleOrDefault(x => x.FieldType?.FieldTypeId == fieldTypeId);
        }

        private InRiverField GetCurrentRelatedEntitiesField(string fieldTypeId, string entityTypeId)
        {
            return inriverEntity?.
                RelatedEntities?.FirstOrDefault(p => p.EntityTypeId == entityTypeId)?.
                Fields?.SingleOrDefault(x => x.FieldType?.FieldTypeId == fieldTypeId);
        }

        private SpecificationField GetCurrentEntitySpecificationField(string specificationFieldTypeId)
        {
            if (this.inriverEntity == null)
            {
                return null;
            }
            return context.DataPersistance.GetSpecificationField(this.inriverEntity.Id, specificationFieldTypeId);
        }

        private SpecificationField GetRelatedEntitySpecificationField(string specificationFieldTypeId, string relatedEntityTypeId)
        {
            if (this.inriverEntity == null)
            {
                return null;
            }

            var relatedEntity = this.inriverEntity.RelatedEntities?.FirstOrDefault(p => p.EntityTypeId == relatedEntityTypeId);

            return relatedEntity != null ? this.context.DataPersistance.GetSpecificationField(relatedEntity.Id, specificationFieldTypeId) : null;
        }

        private List<SpecificationField> GetRelatedEntitiesSpecificationFields(string specificationFieldTypeId, string relatedEntityTypeId)
        {
            if (this.inriverEntity == null || this.inriverEntity.RelatedEntities == null)
            {
                return null;
            }

            var selectedRelatedEntities = this.inriverEntity.RelatedEntities.FindAll(p => p.EntityTypeId == relatedEntityTypeId);

            return selectedRelatedEntities?.Select(relatedEntity => context.DataPersistance.GetSpecificationField(relatedEntity.Id, specificationFieldTypeId)).Where(field => field != null).ToList();
        }

        private string GetInriverFieldValue(Field field)
        {
            if (field == null || field.Data == null || field.FieldType == null)
            {
                return null;
            }

            var iField = new InRiverField
            {
                Data = field.Data,
                FieldType = new InRiverFieldType
                {
                    DataType = field.FieldType.DataType,
                    FieldTypeId = field.FieldType.Id
                }
            };

            return this.GetInriverFieldValue(iField);
        }

        private string GetInriverFieldName(InRiverField iField, string language)
        {
            if (iField == null || iField.FieldType == null || string.IsNullOrEmpty(language))
            {
                return string.Empty;
            }

            return this.context.DataPersistance.GetFieldType(iField.FieldType.FieldTypeId)?.Name[new CultureInfo(language)];
        }

        private string GetInriverFieldName(SpecificationField sField, string language)
        {
            string fieldName = string.Empty;

            if (sField != null && !string.IsNullOrEmpty(language))
            {
                fieldName = sField.SpecificationFieldType?.Name[new CultureInfo(language)];
            }

            return fieldName;
        }

        private string GetInriverFieldValue(SpecificationField field)
        {
            return this.GetInriverFieldValue(GetInriverFieldFromSpecificationField(field));
        }

        private string GetInriverFieldDisplayValue(SpecificationField field, string language)
        {
            if (field == null || field.Data == null)
            {
                return string.Empty;
            }

            string value;

            if (field.SpecificationFieldType?.DataType == DataType.CVL)
            {
                value = this.GetDisplayValueFromCVLKeys(field.Data.ToString(), field.SpecificationFieldType?.CVLId, language);
            }
            else if (field.SpecificationFieldType?.DataType == DataType.LocaleString)
            {
                value = ((LocaleString)field.Data)[new CultureInfo(language)];
            }
            else
            {
                value = this.GetInriverFieldValue(field);
            }

            return value;
        }

        private string GetInriverFieldDisplayValueDefaultFunction(SpecificationField field, string language, string unit)
        {
            var value = this.GetInriverFieldDisplayValue(field, language);
            return !string.IsNullOrEmpty(value) ? (value + ' ' + unit).Trim() : string.Empty;
        }

        private InRiverField GetInriverFieldFromSpecificationField(SpecificationField field)
        {
            if (field == null || field.Data == null || field.SpecificationFieldType == null)
            {
                return null;
            }

            return new InRiverField
            {
                Data = field.Data,
                FieldType = new InRiverFieldType
                {
                    DataType = field.SpecificationFieldType.DataType,
                    FieldTypeId = field.SpecificationFieldType.Id
                }
            };
        }

        private string GetInriverFieldValue(InRiverField field)
        {
            string value = string.Empty;

            if (field != null && field.Data != null && field.FieldType?.DataType == DataType.LocaleString)
            {
                value = JsonConvert.SerializeObject(((LocaleString)field.Data).ToDictionary(this.languages.ToList()));
            }
            else if (field != null && field.Data != null)
            {
                value = field.Data.ToString();
            }

            return value;
        }

        private string GetInriverFieldDisplayValue(InRiverField field, string language)
        {
            if (field == null || field.Data == null)
            {
                return string.Empty;
            }

            string value = string.Empty;

            if (field.FieldType?.DataType == DataType.CVL)
            {
                var fieldType = this.context.DataPersistance.GetFieldType(field.FieldType?.FieldTypeId);
                value = this.GetDisplayValueFromCVLKeys(field.Data.ToString(), fieldType?.CVLId, language);
            }
            else
            {
                return this.GetInriverFieldValue(field);
            }

            return value;
        }

        private string GetDisplayValueFromCVLKeys(string cvlKeys, string cvlId, string language)
        {

            if (string.IsNullOrEmpty(cvlKeys) || string.IsNullOrEmpty(cvlId))
            {
                return string.Empty;
            }

            List<string> listOfCVLKeys = cvlKeys.Split(';').ToList();
            List<CVLValue> listOfCVLValues = new List<CVLValue>();

            //Getting ListOfCVLValues from DataPersistance
            if (listOfCVLKeys.Count > 1)
            {
                listOfCVLValues = context.DataPersistance.GetCVLValuesForCVL(cvlId);
            }
            else
            {
                CVLValue cvlValue = context.DataPersistance.GetCVLValueByKey(listOfCVLKeys.First(), cvlId);

                if (cvlValue != null)
                {
                    listOfCVLValues.Add(cvlValue);
                }
            }

            List<string> cvlDisplayValues = listOfCVLKeys.Select(key => {
                CVLValue cvlValue = listOfCVLValues?.Where(cvlVal => cvlVal.Key.Equals(key, StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();
                string displayValue = (cvlValue == null) ? $"Invalid: [{key}]" : $"[{key}]";

                if (cvlValue != null && cvlValue.Value != null)
                {
                    if (cvlValue.Value.GetType() == typeof(LocaleString))
                    {
                        CultureInfo ci = (!string.IsNullOrEmpty(language)) ? new CultureInfo(language) : null;
                        displayValue = (ci == null) ? $"[{cvlValue.Key}]" : ((LocaleString)cvlValue.Value)[ci];
                    }
                    else
                    {
                        displayValue = cvlValue.Value.ToString();
                    }
                    if (string.IsNullOrEmpty(displayValue))
                    {
                        displayValue = $"[{cvlValue.Key}]";
                    }
                }

                return displayValue;

            }).ToList();

            return string.Join(";", cvlDisplayValues);
        }

        private IDictionary<int, IList<SyndicationRelatedEntityFieldValue>> GetOrAddCachedFieldsWithValues(
            string entityName,
            string fieldTypeId,
            string links,
            string parentRelations,
            string language)
        {
            var configurationKey = links + fieldTypeId + language;
            this.cachedFields ??= new Dictionary<string, IDictionary<int, IList<SyndicationRelatedEntityFieldValue>>>();
            if (this.cachedFields.TryGetValue(configurationKey, out var fieldValuesByKeyPrefix))
            {
                return fieldValuesByKeyPrefix;
            }

            var linkTypeIds = links.Split(',');
            if (linkTypeIds.Length > maxDepth)
            {
                return new Dictionary<int, IList<SyndicationRelatedEntityFieldValue>>();
            }

            var entityLinkPositions = parentRelations.Split(',').Select(int.Parse).ToArray();
            var fields = new Dictionary<int, IList<SyndicationRelatedEntityFieldValue>>();
            if (ScriptContextHelper.IsRelatedEntityFieldsInputValid(entityName, fieldTypeId, linkTypeIds, entityLinkPositions))
            {
                fields = this.context.DataPersistance.GetRelatedEntityFields(entityName, fieldTypeId, linkTypeIds, entityLinkPositions, language)
                    .ToDictionary(x => x.Key, x => x.Value);
            }

            this.cachedFields.Add(configurationKey, fields);

            return fields;
        }

        private IDictionary<int, IList<SyndicationRelatedEntityFieldValue>> GetFieldsWithValues3DL(
            string entityTypeId,
            string fieldTypeId,
            string links,
            string parentRelations,
            string language)
        {
            var linkTypeIds = links.Split(',');
            if (linkTypeIds.Length > maxDepth)
            {
                return new Dictionary<int, IList<SyndicationRelatedEntityFieldValue>>();
            }

            var entityLinkPositions = parentRelations.Split(',').Select(int.Parse).ToArray();
            if (!ScriptContextHelper.IsRelatedEntityFieldsInputValid(entityTypeId, fieldTypeId, linkTypeIds, entityLinkPositions))
            {
                return new Dictionary<int, IList<SyndicationRelatedEntityFieldValue>>();
            }

            var fields = (this.context.DataPersistance as IPMCServer3DLPersistanceAdapter).GetRelatedEntityFieldsForEntity(
                this.inriverEntity.Id,
                fieldTypeId,
                linkTypeIds,
                entityLinkPositions,
                language);
            return fields;
        }

        private InRiverField GetFieldFromPersistance(string currentFieldValue, string softLinkEntityTypeId, string matchValueFieldTypeId, string returnValueFieldTypeId)
        {
            InRiverField returnField = null;
            int? entityId = context.DataPersistance.GetEntityIdByUniqueValue(matchValueFieldTypeId, currentFieldValue.ToString());

            DtoEntity entity = (entityId.HasValue) ? context.DataPersistance.GetEntityWithData(entityId.Value) : null;

            if (entity == null)
            {
                return null;
            }

            var matchingFields = entity.Fields?.Where(f => f.FieldTypeId == matchValueFieldTypeId && f.Data != null && f.Data.Equals(currentFieldValue));

            if (matchingFields == null || !matchingFields.Any())
            {
                return null;
            }

            Field fieldWithRetVal = context.DataPersistance.GetFullField(entityId.Value, softLinkEntityTypeId, returnValueFieldTypeId, new DateTime());

            returnField = new InRiverField
            {
                Data = fieldWithRetVal?.Data,
                FieldType = new InRiverFieldType
                {
                    DataType = fieldWithRetVal?.FieldType?.DataType
                }
            };

            return returnField;
        }

        private void StoreSoftLinkEntity(string key, InRiverField field)
        {
            if (this.semanticSoftlinkHits == null)
            {
                this.semanticSoftlinkHits = new Dictionary<string, string>();
            }

            if (!this.semanticSoftlinkHits.ContainsKey(key))
            {
                this.semanticSoftlinkHits.Add(key, GetInriverFieldValue(field));
            }
        }

        private bool GetStoredEntityIfExists(string key, out string outFieldValue)
        {
            outFieldValue = null;

            if (this.semanticSoftlinkHits != null && this.semanticSoftlinkHits.TryGetValue(key, out outFieldValue))
            {
                return true;
            }

            return false;
        }

        #endregion
    }
}
