namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Mapping;

    public class FakePersistance
    {
        public static IEnumerable<InRiverEntity> CreateFakeEntities(string entityTypeId)
        {
            var entities = new List<InRiverEntity>
            {
                new InRiverEntity()
                {
                    EntityTypeId = entityTypeId,
                    DisplayName = "Item one",
                    ParentId = 101,
                    Id = 1,
                    Fields = new List<InRiverField>
                    {
                        new InRiverField()
                        {
                            Data = "1",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Id",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Blue",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Color",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "M",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Size",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Man",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Gender",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Skjorta",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemName",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Många fina färger",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemDescription",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Black",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ButtonColor",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Item 1 MetaData",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemMetaData",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "{ \"stringMap\": { \"en\": \"Cotton\", \"en-US\": \"\", \"sv\": \"\" } }",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "TestLocaleString",
                                DataType = Remoting.Objects.DataType.LocaleString
                            }
                        }
                    }
                },

                new InRiverEntity()
                {
                    EntityTypeId = entityTypeId,
                    DisplayName = "Item two",
                    Id = 2,
                    Fields = new List<InRiverField>
                    {
                        new InRiverField()
                        {
                            Data = "2",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Id",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Yellow",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Color",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "XL",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Size",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Woman",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Gender",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Skjorta",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemName",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Många fina färger",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemDescription",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Black",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ButtonColor",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Item 2 Meta Data",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemMetaData",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "{ \"stringMap\": { \"en\": \"Cotton\", \"en-US\": \"\", \"sv\": \"\" } }",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "TestLocaleString",
                                DataType = Remoting.Objects.DataType.LocaleString
                            }
                        }
                    }
                },

                new InRiverEntity()
                {
                    EntityTypeId = entityTypeId,
                    DisplayName = "Item three",
                    Id = 3,
                    Fields = new List<InRiverField>
                    {
                        new InRiverField()
                        {
                            Data = "3",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Id",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Red",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Color",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "L",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Size",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Uni",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "Gender",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Skjorta",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemName",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Många fina färger",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemDescription",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Black",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ButtonColor",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "Item 3 Meta Data",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "ItemMetaData",
                                DataType = Remoting.Objects.DataType.String
                            }
                        },
                        new InRiverField()
                        {
                            Data = "{ \"stringMap\": { \"en\": \"Cotton\", \"en-US\": \"\", \"sv\": \"\" } }",
                            FieldType = new InRiverFieldType()
                            {
                                FieldTypeId = "TestLocaleString",
                                DataType = Remoting.Objects.DataType.LocaleString
                            }
                        }
                    }
                }
            };

            return entities;
        }

        public static MapFormat GetMapType() =>
            new MapFormat()
            {
                Id = 1,
                Namespace = "http://base.google.com/ns/1.0",
                Prefix = "g",
                Encoding = "UTF-8",
                XmlVersion = "1.0",
                RssVersion = "2.0",
                LastModified = DateTime.Parse("2017-10-13 00:00:00", CultureInfo.InvariantCulture)
            };

        public static SyndicationMapping GetSyndicationMapping(int mappingId)
        {
            var syndicationMapping = new SyndicationMapping
            {
                MappingId = mappingId,
                WorkareaEntityTypeId = "Item",
                FirstRelatedEntityTypeId = "Product",
                SyndicationMappingFields = new List<SyndicationMappingField>
                {
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "Id",
                        MapFieldTypeId = "id",
                        MapPath = "rss/channel/item",
                        Converter = null,
                        Script = null,
                        Args = null,
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "id",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "Color",
                        MapFieldTypeId = "color",
                        MapPath = "rss/channel/item",
                        Converter = "ToUpper",
                        Script = "function main(args, values) { return values[0].toUpperCase(); }",
                        Args = "{\"transformations\":[{\"function\":{ \"name\":\"toupper\",\"args\":[],\"values\":[]}}]}",
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "color",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "Size",
                        MapFieldTypeId = "size",
                        MapPath = "rss/channel/item",
                        Converter = null,
                        Script = null,
                        Args = null,
                        UnitCvl = "Unit",
                        UnitType = "unitOfMeasure",
                        UnitDefaultValue = "cm",
                        UnitValue = "mm",
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "size",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "ItemName",
                        MapFieldTypeId = "description",
                        MapPath = "rss/channel/item",
                        Converter = "Concatenate",
                        Script = "function main(args, values) { var separator = args.length > 0 ? args[0] : ''; return values.join(separator); }",
                        Args = "{\"transformations\":[{\"function\":{ \"name\":\"concatenate\",\"args\":[\" \"],\"values\":[null,\"ItemDescription\",null]}}]}",
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "description",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "ItemName",
                        MapFieldTypeId = "title",
                        MapPath = "rss/channel/item",
                        Converter = "ToUpper",
                        Script = "function main(args, values) { return values[0].toUpperCase(); }",
                        Args = "{\"transformations\":[{\"function\":{ \"name\":\"toupper\",\"args\":[],\"values\":[]}}]}",
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "title",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = null,
                        FieldTypeId = null,
                        MapFieldTypeId = "color",
                        MapPath = "rss/channel/item/buttons",
                        Converter = "Custom",
                        Script = "function main(args, values) { return values[0]; }",
                        Args = "{\"transformations\":[{\"function\":{ \"name\":\"constant\",\"args\":[],\"values\":[\"Purple\"]}}]}",
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "color",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "Gender",
                        MapFieldTypeId = "gender",
                        MapPath = "rss/channel/item",
                        Converter = null,
                        Script = null,
                        Args = null,
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "gender",
                        MetaData = null,
                        Enumerations = new List<MapEnumeration>
                        {
                            new MapEnumeration()
                            {
                                EnumValue = "male",
                                FieldValue = "Man"
                            },
                            new MapEnumeration()
                            {
                                EnumValue = "female",
                                FieldValue = "Woman"
                            },
                            new MapEnumeration()
                            {
                                EnumValue = "unisex",
                                FieldValue = "Uni"
                            }
                        }
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "ItemMetaData",
                        MapFieldTypeId = "metadata",
                        MapPath = "rss/channel/item",
                        Converter = "Concatenate",
                        Script = "function main(args, values) { var separator = args.length > 0 ? args[0] : ''; return values.join(separator); }",
                        Args = "{\"transformations\":[{\"function\":{ \"name\":\"concatenate\",\"args\":[\" \"],\"values\":[null,\"ProductMetaData\",null]}}]}",
                        UnitCvl = "Unit",
                        UnitType = "unitOfMeasure",
                        UnitValue = "EBC",
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "metadata",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Item",
                        FieldTypeId = "TestLocaleString",
                        MapFieldTypeId = "language",
                        MapPath = "rss/channel/item",
                        Converter = null,
                        Script = null,
                        Args = null,
                        UnitCvl = null,
                        UnitType = null,
                        UnitValue = null,
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "language",
                        MetaData = null,
                    },
                    new SyndicationMappingField()
                    {
                        WorkareaEntityTypeId = "Item",
                        FirstRelatedEntityTypeId = "Product",
                        EntityTypeId = "Product",
                        FieldTypeId = "ProductName",
                        MapFieldTypeId = "product",
                        MapPath = "rss/channel/product",
                        Converter = null,
                        Script = null,
                        Args = null,
                        DataType = "String",
                        Mandatory = false,
                        Unique = false,
                        MaxLength = null,
                        DefaultValue = "product",
                        MetaData = null,
                    },
                }
            };

            return syndicationMapping;
        }

        public static IEnumerable<LinkType> GetLinkTypes() =>
            new List<LinkType>
            {
                new LinkType()
                {
                    Id = "1"
                }
            };
    }
}
