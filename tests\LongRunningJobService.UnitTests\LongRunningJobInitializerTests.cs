namespace LongRunningJobService.UnitTests
{
    using System.Threading.Tasks;
    using FakeItEasy;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJobService.Abstractions;
    using LongRunningJobService.Code;
    using Xunit;

    public class LongRunningJobInitializerTests
    {
        [Fact]
        public async Task InitialiseExcelExportJobAsync_NoPreExistingJob_ShouldCreateNewJob()
        {
            var longRunningJobRepositoryFake = A.Fake<ILongRunningJobRepository>();
            A.<PERSON>(() => longRunningJobRepositoryFake.StartedJobExistsAsync(A<string>.Ignored, A<string>.Ignored)).Returns(false);
            var longRunningJobInitializer = new LongRunningJobInitializer(A.Dummy<IJobModelRepository>(), longRunningJobRepositoryFake, <PERSON><PERSON><IJobWorkerServiceSpawner>());

            await longRunningJobInitializer.InitialiseExcelExportJobAsync("username", new ExcelExportModel());

            A.CallTo(() => longRunningJobRepositoryFake.InsertLongRunningJobAsync(A<LongRunningJob>.Ignored)).MustHaveHappened();
        }

        [Fact]
        public async Task InitialiseExcelExportJobAsync_PreExistingJobExists_ShouldNotCreateNewJob()
        {
            var longRunningJobRepositoryFake = A.Fake<ILongRunningJobRepository>();
            A.CallTo(() => longRunningJobRepositoryFake.StartedJobExistsAsync(A<string>.Ignored, A<string>.Ignored)).Returns(true);
            var longRunningJobInitializer = new LongRunningJobInitializer(A.Dummy<IJobModelRepository>(), longRunningJobRepositoryFake, A.Dummy<IJobWorkerServiceSpawner>());

            await longRunningJobInitializer.InitialiseExcelExportJobAsync("username", new ExcelExportModel());

            A.CallTo(() => longRunningJobRepositoryFake.InsertLongRunningJobAsync(A<LongRunningJob>.Ignored)).MustNotHaveHappened();
        }

        [Fact]
        public async Task InitialiseExcelExportJobAsync_NoPreExistingJob_ShouldSpawnNewJobWorkerService()
        {
            var longRunningJobRepositoryFake = A.Fake<ILongRunningJobRepository>();
            A.CallTo(() => longRunningJobRepositoryFake.StartedJobExistsAsync(A<string>.Ignored, A<string>.Ignored)).Returns(false);
            var jobWorkerServiceSpawnerFake = A.Fake<IJobWorkerServiceSpawner>();
            var longRunningJobInitializer = new LongRunningJobInitializer(A.Dummy<IJobModelRepository>(), longRunningJobRepositoryFake, jobWorkerServiceSpawnerFake);

            await longRunningJobInitializer.InitialiseExcelExportJobAsync("username", new ExcelExportModel());

            A.CallTo(() => jobWorkerServiceSpawnerFake.SpawnAsync(A<int>.Ignored)).MustHaveHappened();
        }
    }
}
