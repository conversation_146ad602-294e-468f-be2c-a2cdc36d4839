namespace LongRunningJob.Core.Factories
{
    using System.Threading.Tasks;
    using inRiver.Server.Repository;
    using LongRunningJob.Core.Abstractions;

    public class ExcelExportRepositoryFactory : IExcelExportRepositoryFactory
    {
        private readonly IRequestContextFactory requestContextFactory;
        private readonly IEnvironmentContextAccessor environmentContextAccessor;

        public ExcelExportRepositoryFactory(IRequestContextFactory requestContextFactory, IEnvironmentContextAccessor environmentContextAccessor)
        {
            this.requestContextFactory = requestContextFactory;
            this.environmentContextAccessor = environmentContextAccessor;
        }

        public async Task<IExcelExportRepository> CreateAsync()
        {
            var requestContext = await this.requestContextFactory.CreateAsync(this.environmentContextAccessor.EnvironmentContext.CustomerSafename, this.environmentContextAccessor.EnvironmentContext.EnvironmentSafename);
            return new ExcelExportRepository(requestContext);
        }
    }
}
