namespace inRiver.Configuration.Core.Repository
{
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver;
    using Persistance;

    public class EnvironmentSettingsRepository : BaseConfigurationCoreRepository, IEnvironmentSettingsRepository
    {
        public EnvironmentSettingsRepository(
            EnvironmentSettingsPersistance environmentSettingsPersistance,
            ApiCaller apiCaller)
            : base(
                new BaseConfigurationCorePersistance(
                    environmentSettingsPersistance.ConnectionString,
                    environmentSettingsPersistance.ReadOnlyConnectionString,
                    environmentSettingsPersistance.GetCommonLogging(),
                    apiCaller),
                apiCaller)
        {
        }

        public EnvironmentSetting GetEnvironmentSetting(string key, int? environmentId) => this.EnvironmentSettingsPersistance.GetEnvironmentSetting(key, environmentId);
    }
}
