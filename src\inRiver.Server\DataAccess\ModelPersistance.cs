namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Data.SqlTypes;
    using System.Globalization;
    using System.Linq;
    using System.Text;
    using Dapper;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Error;
    using inRiver.Server.Managers;
    using inRiver.Server.Util;
    using Serilog;

    public partial class inRiverPersistance
    {
        #region Entity Type

        public List<EntityType> GetAllEntityTypes()
        {
            List<EntityType> types = new List<EntityType>();

            List<CultureInfo> serverLanguages = GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, IsLinkEntityType FROM EntityType";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            EntityType entityType = new EntityType();
                            entityType.Id = reader.GetString(0);
                            SqlXml xml = reader.GetSqlXml(1);

                            entityType.Name = Utilities.XmlToLocaleString(xml.Value, serverLanguages);

                            entityType.IsLinkEntityType = reader.GetBoolean(2);

                            types.Add(entityType);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Entity Types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Entity Types", ex);
                }
            }

            Dictionary<string, List<FieldType>> fieldTypes = this.GetAllFieldTypesGrouped();
            List<LinkType> linkTypes = context.DataPersistance.GetAllLinkTypes();
            List<FieldSet> fieldSets = context.DataPersistance.GetAllFieldSets();

            foreach (EntityType entityType in types)
            {

                if (fieldTypes.ContainsKey(entityType.Id))
                {
                    entityType.FieldTypes = fieldTypes[entityType.Id];
                }
                else
                {
                    entityType.FieldTypes = new List<FieldType>();
                }

                entityType.LinkTypes = linkTypes.FindAll(lt => lt.SourceEntityTypeId.Equals(entityType.Id) || lt.TargetEntityTypeId.Equals(entityType.Id));
                entityType.FieldViews = new List<FieldView>();
                entityType.FieldSets = fieldSets.FindAll(fs => fs.EntityTypeId.Equals(entityType.Id));
            }

            return types;
        }

        public EntityType GetEntityType(string id)
        {
            EntityType entityType = null;

            List<CultureInfo> serverLanguages = this.GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, IsLinkEntityType FROM EntityType WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            entityType = new EntityType();
                            entityType.Id = reader.GetString(0);

                            SqlXml xml = reader.GetSqlXml(1);

                            entityType.Name = Utilities.XmlToLocaleString(xml.Value, serverLanguages);
                            entityType.IsLinkEntityType = reader.GetBoolean(2);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Entity Type");
                    throw ErrorUtility.GetDataAccessException(string.Format("An unexpected error occurred when getting Entity Type ({0})", id), ex);
                }
            }

            if (entityType == null)
            {
                return null;
            }

            entityType.FieldTypes = context.DataPersistance.GetFieldTypesForEntityType(entityType.Id, serverLanguages);
            entityType.LinkTypes = context.DataPersistance.GetLinkTypesForEntityType(entityType.Id, serverLanguages);
            entityType.FieldViews = context.DataPersistance.GetFieldViewsForEntityType(entityType.Id, serverLanguages);
            entityType.FieldSets = context.DataPersistance.GetFieldSetsForEntityType(entityType.Id, serverLanguages);

            return entityType;
        }

        #endregion

        #region Field Type

        public FieldType GetFieldType(string id)
        {
            FieldType fieldType = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                List<CultureInfo> serverLanguages = GetAllLanguages();

                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, [Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, [Index], [Description], TrackChanges, [ExpressionSupport] FROM FieldType WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);
                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            fieldType = new FieldType();
                            fieldType.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);
                            fieldType.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            fieldType.EntityTypeId = reader.GetString(2);
                            fieldType.CategoryId = reader.GetString(3);

                            if (!reader.IsDBNull(4))
                            {
                                fieldType.CVLId = reader.GetString(4);
                            }

                            fieldType.Mandatory = reader.GetBoolean(5);
                            fieldType.Unique = reader.GetBoolean(6);

                            if (!reader.IsDBNull(7))
                            {
                                fieldType.DefaultValue = reader.GetString(7);
                            }
                            fieldType.Hidden = reader.GetBoolean(8);
                            fieldType.ReadOnly = reader.GetBoolean(9);
                            fieldType.IsDisplayName = reader.GetBoolean(10);
                            fieldType.IsDisplayDescription = reader.GetBoolean(11);
                            fieldType.ExcludeFromDefaultView = reader.GetBoolean(12);
                            fieldType.Multivalue = reader.GetBoolean(13);
                            fieldType.DataType = reader.GetString(14);
                            fieldType.Index = reader.GetInt32(15);

                            if (!reader.IsDBNull(16))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(16);
                                fieldType.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldType.TrackChanges = reader.GetBoolean(17);
                            fieldType.ExpressionSupport = reader.GetBoolean(18);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Field Type");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Field Type", ex);
                }

                if (fieldType == null)
                {
                    return null;
                }

                fieldType.Settings = context.DataPersistance.GetFieldTypeSettings(id);
            }

            return fieldType;
        }

        public List<FieldType> GetAllFieldTypes()
        {
            List<FieldType> fieldTypes = new List<FieldType>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                List<CultureInfo> serverLanguages = GetAllLanguages();

                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, [Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, [Index], [Description], TrackChanges, [ExpressionSupport] FROM FieldType";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            FieldType fieldType = new FieldType();
                            fieldType.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);
                            fieldType.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            fieldType.EntityTypeId = reader.GetString(2);
                            fieldType.CategoryId = reader.GetString(3);

                            if (!reader.IsDBNull(4))
                            {
                                fieldType.CVLId = reader.GetString(4);
                            }

                            fieldType.Mandatory = reader.GetBoolean(5);
                            fieldType.Unique = reader.GetBoolean(6);

                            if (!reader.IsDBNull(7))
                            {
                                fieldType.DefaultValue = reader.GetString(7);
                            }

                            fieldType.Hidden = reader.GetBoolean(8);
                            fieldType.ReadOnly = reader.GetBoolean(9);
                            fieldType.IsDisplayName = reader.GetBoolean(10);
                            fieldType.IsDisplayDescription = reader.GetBoolean(11);
                            fieldType.ExcludeFromDefaultView = reader.GetBoolean(12);
                            fieldType.Multivalue = reader.GetBoolean(13);
                            fieldType.DataType = reader.GetString(14);
                            fieldType.Index = reader.GetInt32(15);

                            if (!reader.IsDBNull(16))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(16);
                                fieldType.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldType.TrackChanges = reader.GetBoolean(17);
                            fieldType.ExpressionSupport = reader.GetBoolean(18);

                            fieldTypes.Add(fieldType);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Field Types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Field Types", ex);
                }

                Dictionary<string, Dictionary<string, string>> settings = this.GetAllFieldTypeSettings();

                foreach (FieldType fieldType in fieldTypes)
                {
                    if (settings.ContainsKey(fieldType.Id))
                    {
                        fieldType.Settings = settings[fieldType.Id];
                    }
                    else
                    {
                        fieldType.Settings = new Dictionary<string, string>();
                    }
                }
            }

            return fieldTypes;
        }

        public Dictionary<string, List<FieldType>> GetAllFieldTypesGrouped()
        {
            Dictionary<string, List<FieldType>> fieldTypes = new Dictionary<string, List<FieldType>>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                List<CultureInfo> serverLanguages = this.GetAllLanguages();

                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, [Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, [Index], [Description], TrackChanges, [ExpressionSupport] FROM FieldType";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            FieldType fieldType = new FieldType();
                            fieldType.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);
                            fieldType.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            fieldType.EntityTypeId = reader.GetString(2);
                            fieldType.CategoryId = reader.GetString(3);

                            if (!reader.IsDBNull(4))
                            {
                                fieldType.CVLId = reader.GetString(4);
                            }

                            fieldType.Mandatory = reader.GetBoolean(5);
                            fieldType.Unique = reader.GetBoolean(6);

                            if (!reader.IsDBNull(7))
                            {
                                fieldType.DefaultValue = reader.GetString(7);
                            }

                            fieldType.Hidden = reader.GetBoolean(8);
                            fieldType.ReadOnly = reader.GetBoolean(9);
                            fieldType.IsDisplayName = reader.GetBoolean(10);
                            fieldType.IsDisplayDescription = reader.GetBoolean(11);
                            fieldType.ExcludeFromDefaultView = reader.GetBoolean(12);
                            fieldType.Multivalue = reader.GetBoolean(13);
                            fieldType.DataType = reader.GetString(14);
                            fieldType.Index = reader.GetInt32(15);

                            if (!reader.IsDBNull(16))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(16);
                                fieldType.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldType.TrackChanges = reader.GetBoolean(17);
                            fieldType.ExpressionSupport = reader.GetBoolean(18);

                            if (!fieldTypes.ContainsKey(fieldType.EntityTypeId))
                            {
                                fieldTypes.Add(fieldType.EntityTypeId, new List<FieldType>());
                            }

                            fieldTypes[fieldType.EntityTypeId].Add(fieldType);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Field Types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Field Types", ex);
                }

                Dictionary<string, Dictionary<string, string>> settings = this.GetAllFieldTypeSettings();

                foreach (string entityTypeId in fieldTypes.Keys)
                {
                    foreach (FieldType fieldType in fieldTypes[entityTypeId])
                    {
                        if (settings.ContainsKey(fieldType.Id))
                        {
                            fieldType.Settings = settings[fieldType.Id];
                        }
                        else
                        {
                            fieldType.Settings = new Dictionary<string, string>();
                        }
                    }
                }
            }

            return fieldTypes;
        }

        public List<FieldType> GetFieldTypesForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            List<FieldType> fieldTypes = new List<FieldType>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                List<CultureInfo> serverLanguages = languages ?? this.GetAllLanguages();

                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, EntityTypeId, CategoryId, CVLId, Mandatory, [Unique], DefaultValue, Hidden, ReadOnly, IsDisplayName, IsDisplayDescription, ExcludeFromDefaultView, Multivalue, DataType, [Index], [Description], TrackChanges, [ExpressionSupport] FROM FieldType WHERE EntityTypeId = @EntityTypeId";
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            FieldType fieldType = new FieldType();
                            fieldType.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);
                            fieldType.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            fieldType.EntityTypeId = reader.GetString(2);
                            fieldType.CategoryId = reader.GetString(3);

                            if (!reader.IsDBNull(4))
                            {
                                fieldType.CVLId = reader.GetString(4);
                            }

                            fieldType.Mandatory = reader.GetBoolean(5);
                            fieldType.Unique = reader.GetBoolean(6);

                            if (!reader.IsDBNull(7))
                            {
                                fieldType.DefaultValue = reader.GetString(7);
                            }
                            fieldType.Hidden = reader.GetBoolean(8);
                            fieldType.ReadOnly = reader.GetBoolean(9);
                            fieldType.IsDisplayName = reader.GetBoolean(10);
                            fieldType.IsDisplayDescription = reader.GetBoolean(11);
                            fieldType.ExcludeFromDefaultView = reader.GetBoolean(12);
                            fieldType.Multivalue = reader.GetBoolean(13);
                            fieldType.DataType = reader.GetString(14);
                            fieldType.Index = reader.GetInt32(15);

                            if (!reader.IsDBNull(16))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(16);
                                fieldType.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldType.TrackChanges = reader.GetBoolean(17);
                            fieldType.ExpressionSupport = reader.GetBoolean(18);

                            fieldTypes.Add(fieldType);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Field Types for Entity Type " + entityTypeId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Field Types for Entity Type " + entityTypeId, ex);
                }

                Dictionary<string, Dictionary<string, string>> settings = this.GetAllFieldTypeSettings();

                foreach (FieldType fieldType in fieldTypes)
                {
                    if (settings.ContainsKey(fieldType.Id))
                    {
                        fieldType.Settings = settings[fieldType.Id];
                    }
                    else
                    {
                        fieldType.Settings = new Dictionary<string, string>();
                    }
                }
            }

            return fieldTypes;
        }

        public Dictionary<string, string> GetFieldTypeSettings(string fieldTypeId)
        {
            Dictionary<string, string> settings = new Dictionary<string, string>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Key], Value FROM FieldType_Settings WHERE FieldTypeId = @FieldTypeId";
                    command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            settings.Add(reader.GetString(0), reader.GetString(1));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != System.Data.ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Field Types settings for Field Type " + fieldTypeId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Field Types settings for Field Type " + fieldTypeId, ex);
                }
            }

            return settings;
        }

        public Dictionary<string, Dictionary<string, string>> GetAllFieldTypeSettings()
        {
            Dictionary<string, Dictionary<string, string>> settings = new Dictionary<string, Dictionary<string, string>>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT FieldTypeId, [Key], Value FROM FieldType_Settings";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string fieldTypeId = reader.GetString(0);
                            string key = reader.GetString(1);
                            string value = reader.GetString(2);

                            if (!settings.ContainsKey(fieldTypeId))
                            {
                                settings.Add(fieldTypeId, new Dictionary<string, string>());
                            }

                            settings[fieldTypeId].Add(key, value);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != System.Data.ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Field Types settings");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Field Types settings", ex);
                }
            }

            return settings;
        }

        #endregion

        #region Link Type

        public LinkType GetLinkType(string id, List<CultureInfo> serverLanguages)
        {
            LinkType linkType = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, SourceName, SourceEntityTypeId, TargetName, TargetEntityTypeId, LinkEntityTypeId, [Index] FROM LinkType WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            linkType = new LinkType();
                            linkType.Id = reader.GetString(0);

                            SqlXml xmlSourceName = reader.GetSqlXml(1);
                            linkType.SourceName = Utilities.XmlToLocaleString(xmlSourceName.Value, serverLanguages);

                            linkType.SourceEntityTypeId = reader.GetString(2);

                            SqlXml xmlTargetName = reader.GetSqlXml(3);
                            linkType.TargetName = Utilities.XmlToLocaleString(xmlTargetName.Value, serverLanguages);

                            linkType.TargetEntityTypeId = reader.GetString(4);

                            if (!reader.IsDBNull(5))
                            {
                                linkType.LinkEntityTypeId = reader.GetString(5);
                            }

                            linkType.Index = reader.GetInt32(6);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting LinkType " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting LinkType " + id, ex);
                }
            }

            return linkType;
        }

        public LinkType GetLinkType(string id)
        {
            var serverLanguages = GetAllLanguages();
            return GetLinkType(id, serverLanguages);
        }

        public List<LinkType> GetAllLinkTypes()
        {
            List<LinkType> links = new List<LinkType>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                List<CultureInfo> serverLanguages = GetAllLanguages();

                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, SourceName, SourceEntityTypeId, TargetName, TargetEntityTypeId, LinkEntityTypeId, [Index] FROM LinkType";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            LinkType linkType = new LinkType();
                            linkType.Id = reader.GetString(0);

                            SqlXml xmlSourceName = reader.GetSqlXml(1);
                            linkType.SourceName = Utilities.XmlToLocaleString(xmlSourceName.Value, serverLanguages);

                            linkType.SourceEntityTypeId = reader.GetString(2);

                            SqlXml xmlTargetName = reader.GetSqlXml(3);
                            linkType.TargetName = Utilities.XmlToLocaleString(xmlTargetName.Value, serverLanguages);

                            linkType.TargetEntityTypeId = reader.GetString(4);

                            if (!reader.IsDBNull(5))
                            {
                                linkType.LinkEntityTypeId = reader.GetString(5);
                            }

                            linkType.Index = reader.GetInt32(6);

                            links.Add(linkType);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != System.Data.ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Link Types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Link Types", ex);
                }
            }

            return links;
        }

        public IEnumerable<LinkType> GetLinkTypes(string sourceEntityTypeId, string targetEntityTypeId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var query = @"SELECT Id, SourceName, SourceEntityTypeId, TargetName, TargetEntityTypeId, LinkEntityTypeId, [Index] 
                                  FROM LinkType 
                                  WHERE SourceEntityTypeId = @sourceEntityTypeId 
                                  AND TargetEntityTypeId = @targetEntityTypeId";

                    return connection.Query<LinkType>(query, new { sourceEntityTypeId, targetEntityTypeId });
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occured when getting all Link Types for Target: {targetEntityTypeId} and Source: {sourceEntityTypeId}");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occured when getting all Link Types for Target: {targetEntityTypeId} and Source: {sourceEntityTypeId}", ex);
                }
            }
        }

        public List<LinkType> GetLinkTypesForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            List<LinkType> links = new List<LinkType>();

            if (string.IsNullOrWhiteSpace(entityTypeId))
            {
                return links;
            }

            using (SqlConnection Connection = new SqlConnection(this.ConnectionString))
            {
                List<CultureInfo> serverLanguages = languages ?? this.GetAllLanguages();

                try
                {
                    SqlCommand command = Connection.CreateCommand();
                    command.CommandText = "SELECT Id, SourceName, SourceEntityTypeId, TargetName, TargetEntityTypeId, LinkEntityTypeId, [Index] FROM LinkType WHERE ((SourceEntityTypeId = @EntityTypeId) OR (TargetEntityTypeId = @EntityTypeId))";
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    Connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            LinkType linkType = new LinkType();
                            linkType.Id = reader.GetString(0);

                            SqlXml xmlSourceName = reader.GetSqlXml(1);
                            linkType.SourceName = Utilities.XmlToLocaleString(xmlSourceName.Value, serverLanguages);

                            linkType.SourceEntityTypeId = reader.GetString(2);

                            SqlXml xmlTargetName = reader.GetSqlXml(3);
                            linkType.TargetName = Utilities.XmlToLocaleString(xmlTargetName.Value, serverLanguages);

                            linkType.TargetEntityTypeId = reader.GetString(4);

                            if (!reader.IsDBNull(5))
                            {
                                linkType.LinkEntityTypeId = reader.GetString(5);
                            }

                            linkType.Index = reader.GetInt32(6);

                            links.Add(linkType);
                        }

                        reader.Close();
                    }

                    Connection.Close();
                }
                catch (Exception ex)
                {
                    if (Connection.State != System.Data.ConnectionState.Closed)
                        Connection.Close();

                    Log.Error(ex, "An unexpected error occurred when getting all Link Types for Entity Type");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Link Types for Entity Type", ex);
                }
            }

            return links;
        }

        #endregion

        #region Category

        public Category GetCategory(string id)
        {
            Category category = null;

            List<CultureInfo> serverLanguages = GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, [Index] FROM Category WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            category = new Category();

                            category.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);
                            category.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            category.Index = reader.GetInt32(2);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Category " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Category " + id, ex);
                }
            }

            return category;
        }

        #endregion

        #region Field View

        public List<FieldView> GetFieldViewsForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            List<FieldView> fieldViews = new List<FieldView>(); ;

            List<CultureInfo> serverLanguages = languages ?? this.GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, Description, EntityTypeId FROM FieldView WHERE EntityTypeId = @EntityTypeId";
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {

                        while (reader.Read())
                        {
                            FieldView fieldView = new FieldView();
                            fieldView.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);

                            fieldView.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            if (!reader.IsDBNull(2))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(2);
                                fieldView.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldView.EntityTypeId = reader.GetString(3);

                            fieldView.FieldTypes = GetFieldTypesForFieldView(fieldView.Id);

                            fieldViews.Add(fieldView);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Field Views for Entity Type");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Field Views for Entity Type", ex);
                }
            }

            return fieldViews;
        }

        public List<string> GetFieldTypesForFieldView(string fieldViewId)
        {
            List<string> fieldTypes = new List<string>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                List<CultureInfo> serverLanguages = GetAllLanguages();

                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT FieldTypeId FROM FieldView_FieldType WHERE FieldViewId = @FieldViewId";
                    command.Parameters.AddWithValue("@FieldViewId", fieldViewId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            fieldTypes.Add(reader.GetString(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Field Types for Field View");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Field Types for Field View", ex);
                }
            }

            return fieldTypes;
        }

        #endregion

        #region FieldSet

        public FieldSet GetFieldSet(string id)
        {
            FieldSet fieldSet = null;

            List<CultureInfo> serverLanguages = GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, Description, EntityTypeId FROM FieldSet WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            fieldSet = new FieldSet();
                            fieldSet.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);

                            fieldSet.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            if (!reader.IsDBNull(2))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(2);
                                fieldSet.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldSet.EntityTypeId = reader.GetString(3);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Field Set");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Field Set", ex);
                }
            }

            if (fieldSet == null)
            {
                return null;
            }

            fieldSet.FieldTypes = context.DataPersistance.GetFieldTypesForFieldSet(fieldSet.Id);

            return fieldSet;
        }

        public List<FieldSet> GetAllFieldSets()
        {
            List<FieldSet> fieldSets = new List<FieldSet>(); ;

            List<CultureInfo> serverLanguages = GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, Description, EntityTypeId FROM FieldSet";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            FieldSet fieldSet = new FieldSet();
                            fieldSet.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);

                            fieldSet.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            if (!reader.IsDBNull(2))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(2);
                                fieldSet.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldSet.EntityTypeId = reader.GetString(3);

                            fieldSets.Add(fieldSet);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Field Sets");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Field Sets", ex);
                }
            }

            Dictionary<string, List<string>> fieldsetFieldTypes = this.GetAllFieldSetFieldTypes();

            foreach (FieldSet fieldset in fieldSets)
            {
                if (fieldsetFieldTypes.ContainsKey(fieldset.Id))
                {
                    fieldset.FieldTypes = fieldsetFieldTypes[fieldset.Id];
                }
                else
                {
                    fieldset.FieldTypes = new List<string>();
                }
            }

            return fieldSets;
        }

        public List<FieldSet> GetFieldSetsForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            List<FieldSet> fieldSets = new List<FieldSet>(); ;

            List<CultureInfo> serverLanguages = languages ?? this.GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, Description, EntityTypeId FROM FieldSet WHERE EntityTypeId = @EntityTypeId";
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {

                        while (reader.Read())
                        {
                            FieldSet fieldSet = new FieldSet();
                            fieldSet.Id = reader.GetString(0);

                            SqlXml xmlName = reader.GetSqlXml(1);

                            fieldSet.Name = Utilities.XmlToLocaleString(xmlName.Value, serverLanguages);

                            if (!reader.IsDBNull(2))
                            {
                                SqlXml xmlDescription = reader.GetSqlXml(2);
                                fieldSet.Description = Utilities.XmlToLocaleString(xmlDescription.Value, serverLanguages);
                            }

                            fieldSet.EntityTypeId = reader.GetString(3);

                            fieldSets.Add(fieldSet);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Field Sets for Entity Type");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Field Sets for Entity Type", ex);
                }
            }

            Dictionary<string, List<string>> fieldsetFieldTypes = this.GetAllFieldSetFieldTypes();

            foreach (FieldSet fieldset in fieldSets)
            {
                if (fieldsetFieldTypes.ContainsKey(fieldset.Id))
                {
                    fieldset.FieldTypes = fieldsetFieldTypes[fieldset.Id];
                }
                else
                {
                    fieldset.FieldTypes = new List<string>();
                }
            }

            return fieldSets;
        }

        public List<string> GetFieldTypesForFieldSet(string fieldSetId)
        {
            List<string> fieldTypes = new List<string>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT FieldTypeId FROM FieldSet_FieldType WHERE FieldSetId = @FieldSetId";
                    command.Parameters.AddWithValue("@FieldSetId", fieldSetId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            fieldTypes.Add(reader.GetString(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Field Types for Field Set");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Field Types for Field Set", ex);
                }
            }

            return fieldTypes;
        }

        #endregion

        #region CVL

        public CVL GetCVL(string id)
        {
            CVL cvl = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, DataType, ParentId, CustomValueList FROM CVL WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            cvl = new CVL();

                            cvl.Id = reader.GetString(0);
                            cvl.DataType = reader.GetString(1);

                            if (reader.IsDBNull(2))
                            {
                                cvl.ParentId = null;
                            }
                            else
                            {
                                cvl.ParentId = reader.GetString(2);
                            }

                            cvl.CustomValueList = reader.GetBoolean(3);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting CVL" + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting CVL" + id, ex);
                }
            }

            return cvl;
        }

        public IList<CVL> GetAllCVLs()
        {
            var cvls = new List<CVL>();

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, DataType, ParentId, CustomValueList FROM CVL";

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var cvl = new CVL
                            {
                                Id = reader.GetString(0),
                                DataType = reader.GetString(1),
                                ParentId = reader.IsDBNull(2) ? null : reader.GetString(2),
                                CustomValueList = reader.GetBoolean(3)
                            };
                            cvls.Add(cvl);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An unexpected error occurred when getting all CVL");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all CVL", ex);
                }
            }

            return cvls;
        }

        #endregion

        public List<Core.Models.inRiver.CVLKey> GetCvlKeysByCvlId(string cvlId)
        {
            var cvlKeys = new List<Core.Models.inRiver.CVLKey>();

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT [Index], Id, [Key] FROM CVLKey WHERE Deactivated = 0 AND CvlId = @CvlId ORDER BY [Index]";
                    command.Parameters.AddWithValue("@CvlId", cvlId);

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        var indexOrdinal = reader.GetOrdinal("Index");
                        var idOrdinal = reader.GetOrdinal("Id");
                        var keyOrdinal = reader.GetOrdinal("Key");

                        while (reader.Read())
                        {
                            var cvlKey = new Core.Models.inRiver.CVLKey
                            {
                                Index = reader.GetInt32(indexOrdinal),
                                Id = reader.GetInt32(idOrdinal),
                                Key = reader.GetString(keyOrdinal)
                            };
                            cvlKeys.Add(cvlKey);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occurred when getting CVLKeys by CvlId ({cvlId})");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when getting CVLKeys by CvlId ({cvlId})", ex);
                }
            }

            return cvlKeys;
        }

        #region CVL Value

        public CVLValue GetCVLValueByKey(string key, string cvlId)
        {
            List<CultureInfo> serverLanguages = GetAllLanguages();
            CVLValue cvlValue = null;
            CVL cvl = null;
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText =
                        "SELECT [CVLValueId], [Key], [Index], ParentKey, CVLId, DataType, LastModified, DateCreated, Value, Language, Deactivated, CustomValueList FROM ViewCVLValue WHERE [Key] = @Key AND CVLId = @CVLId";
                    command.Parameters.AddWithValue("@Key", key);
                    command.Parameters.AddWithValue("@CVLId", cvlId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (!reader.HasRows)
                        {
                            return null;
                        }

                        var cvlValueIdOrdinal = reader.GetOrdinal("CVLValueId");
                        var keyOrdinal = reader.GetOrdinal("Key");
                        var indexOrdinal = reader.GetOrdinal("Index");
                        var parentKeyOrdinal = reader.GetOrdinal("ParentKey");
                        var cvlIdOrdinal = reader.GetOrdinal("CVLId");
                        var dataTypeOrdinal = reader.GetOrdinal("DataType");
                        var lastModifiedOrdinal = reader.GetOrdinal("LastModified");
                        var dateCreatedOrdinal = reader.GetOrdinal("DateCreated");
                        var valueOrdinal = reader.GetOrdinal("Value");
                        var languageOrdinal = reader.GetOrdinal("Language");
                        var deactivatedOrdinal = reader.GetOrdinal("Deactivated");
                        var customValueList = reader.GetOrdinal("CustomValueList");

                        reader.Read();
                        var dataType = reader.GetString(dataTypeOrdinal);

                        cvl = new CVL()
                        {
                            CustomValueList = reader.GetBoolean(customValueList),
                            Id = cvlId,
                            DataType = dataType,
                            ParentId = reader.IsDBNull(parentKeyOrdinal)
                                ? null
                                : reader.GetString(parentKeyOrdinal)
                        };

                        cvlValue = new CVLValue
                        {
                            Id = reader.GetInt32(cvlValueIdOrdinal),
                            Key = reader.GetString(keyOrdinal),
                            Index = reader.GetInt32(indexOrdinal),
                            ParentKey =
                                reader.IsDBNull(parentKeyOrdinal)
                                    ? null
                                    : reader.GetString(parentKeyOrdinal),
                            CVLId = reader.GetString(cvlIdOrdinal),
                            LastModified = reader.GetDateTime(lastModifiedOrdinal),
                            DateCreated = reader.GetDateTime(dateCreatedOrdinal),
                            Deactivated = reader.GetBoolean(deactivatedOrdinal)
                        };

                        if (dataType == DataType.LocaleString)
                        {
                            var localeString = new LocaleString(serverLanguages);

                            do
                            {
                                if (!reader.IsDBNull(languageOrdinal) && !reader.IsDBNull(valueOrdinal))
                                {
                                    localeString[new CultureInfo(reader.GetString(languageOrdinal))] =
                                        reader.GetString(valueOrdinal);
                                }
                            } while (reader.Read());

                            cvlValue.Value = localeString;
                        }
                        else
                        {
                            if (!reader.IsDBNull(valueOrdinal))
                            {
                                cvlValue.Value = reader.GetString(valueOrdinal);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An unexpected error occurred when getting CVL value " + key);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting CVL value " + key,
                        ex);
                }
                finally
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }
                }
            }

            return cvl.CustomValueList
                    ? new ExtensionManager(this.context).GetCustomCVLValueByKey(cvlId, key, cvl)
                    : cvlValue;
        }
        public List<CVLValue> GetCVLValueByKeys(string[] keys, string cvlId, string selectedLanguage, List<CultureInfo> serverLanguages)
        {
            List<CVLValue> cvlValues = new List<CVLValue>();
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    StringBuilder sb = new StringBuilder();
                    int index = 1;
                    foreach (string k in keys)
                    {
                        sb.Append((sb.Length <= 0 ? "" : ",") + "@Key" + index.ToString());
                        command.Parameters.AddWithValue("@Key" + index.ToString(), k);
                        index++;
                    }

                    command.CommandText =
                        " SELECT [Id],[DataType],[ParentId],[CustomValueList] FROM CVL WHERE Id = @CVLId; " +
                        " SELECT [CVLValueId], [Key], [Index], ParentKey, CVLId, DataType, LastModified, DateCreated, Value, Language, Deactivated, CustomValueList " +
                        " FROM ViewCVLValue " +
                        " WHERE [Key] IN (" + sb.ToString() + ") " +
                        " AND CVLId = @CVLId " +
                        " AND (Language IS NULL OR Language = @SelectedLanguage)";
                    command.Parameters.AddWithValue("@CVLId", cvlId);
                    command.Parameters.AddWithValue("@SelectedLanguage", selectedLanguage);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();
                            var customValueList = reader.GetBoolean(reader.GetOrdinal("CustomValueList"));
                            if (customValueList)
                            {
                                GetCustomCVLValuesForSpecifiedKey(reader, keys, cvlId, cvlValues);
                            }
                            else
                            {
                                reader.NextResult();
                                if (!reader.HasRows) { return null; }

                                GetNonCustomCVLValuesForSpecifiedKey(reader, cvlValues);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An unexpected error occurred when getting CVL values for Keys " + string.Join("','", keys));
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting CVL value for Keys " + string.Join("','", keys), ex);
                }
                finally
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }
                }
            }
            return cvlValues;
        }

        private static void GetNonCustomCVLValuesForSpecifiedKey(SqlDataReader reader, List<CVLValue> cvlValues)
        {
            var cvlValueIdOrdinal = reader.GetOrdinal("CVLValueId");
            var keyOrdinal = reader.GetOrdinal("Key");
            var indexOrdinal = reader.GetOrdinal("Index");
            var cvlIdOrdinal = reader.GetOrdinal("CVLId");
            var lastModifiedOrdinal = reader.GetOrdinal("LastModified");
            var dateCreatedOrdinal = reader.GetOrdinal("DateCreated");
            var valueOrdinal = reader.GetOrdinal("Value");
            var deactivatedOrdinal = reader.GetOrdinal("Deactivated");
            var parentKeyOrdinal = reader.GetOrdinal("ParentKey");

            reader.Read();
            do
            {
                cvlValues.Add(new CVLValue
                {
                    Id = reader.GetInt32(cvlValueIdOrdinal),
                    Key = reader.GetString(keyOrdinal),
                    Index = reader.GetInt32(indexOrdinal),
                    ParentKey = reader.IsDBNull(parentKeyOrdinal) ? null : reader.GetString(parentKeyOrdinal),
                    CVLId = reader.GetString(cvlIdOrdinal),
                    LastModified = reader.GetDateTime(lastModifiedOrdinal),
                    DateCreated = reader.GetDateTime(dateCreatedOrdinal),
                    Deactivated = reader.GetBoolean(deactivatedOrdinal),
                    Value = reader.GetString(valueOrdinal)
                });
            } while (reader.Read());
        }

        private void GetCustomCVLValuesForSpecifiedKey(SqlDataReader reader, string[] keys, string cvlId, List<CVLValue> cvlValues)
        {
            var dataTypeOrdinal = reader.GetOrdinal("DataType");
            var parentIdOrdinal = reader.GetOrdinal("ParentId");
            var customValueListOrdinal = reader.GetOrdinal("CustomValueList");
            CVL cvl = new CVL()
            {
                CustomValueList = reader.GetBoolean(customValueListOrdinal),
                Id = cvlId,
                DataType = reader.GetString(dataTypeOrdinal),
                ParentId = reader.IsDBNull(parentIdOrdinal) ? null : reader.GetString(parentIdOrdinal)
            };

            foreach (var key in keys)
            {
                cvlValues.Add(new ExtensionManager(this.context).GetCustomCVLValueByKey(cvlId, key, cvl));
            }
        }

        public List<CVLValue> GetExistingCVLKeysByKeyList(List<string> keys, CVL cvl)
        {
            if (cvl == null)
            {
                return new List<CVLValue>();
            }

            if (keys.Count == 0)
            {
                return new List<CVLValue>();
            }

            List<CVLValue> existingKeys = new List<CVLValue>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.Parameters.AddWithValue("@CVLId", cvl.Id);

                    var keyBatches = Core.Util.Utilities.SplitListIntoBatches(keys, 100);
                    int index = 1;
                    foreach (var keyBatch in keyBatches)
                    {
                        string paramName = string.Empty;
                        string parameterList = string.Empty;
                        foreach (string key in keyBatch)
                        {
                            paramName = "@KeyParam" + index++;
                            command.Parameters.AddWithValue(paramName, key); //Making individual parameter for every key  
                            parameterList += paramName + ",";
                        }

                        parameterList = parameterList.Remove(parameterList.LastIndexOf(",", StringComparison.Ordinal), 1);

                        command.CommandText = $"SELECT [Key] FROM ViewCVLValue WHERE [Key] in ({parameterList}) AND CVLId = @CVLId";

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var keyOrdinal = reader.GetOrdinal("Key");
                                string key = reader.GetString(keyOrdinal);
                                existingKeys.Add(new CVLValue { Key = key, CVLId = cvl.Id });
                            }

                            reader.Close();
                        }

                        connection.Close();
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting CVLValues for a list of keys");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting CVLValues for a list of keys", ex);
                }
            }

            return existingKeys;
        }

        public string GetCvlIdForFieldType(string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return null;
            }

            string cvlId = null;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandText = "SELECT CVLId FROM FieldType WHERE Id = @Id";
                command.Parameters.AddWithValue("@Id", fieldTypeId);

                connection.Open();
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read() && !reader.IsDBNull(0))
                    {
                        cvlId = reader.GetString(0);
                    }
                }
            }

            return cvlId;
        }

        public List<CVLValue> GetCVLValuesForCVL(string cvlId)
        {
            CVL cvl = this.GetCVL(cvlId);

            if (cvl == null)
            {
                return new List<CVLValue>();
            }

            if (cvl.CustomValueList)
            {
                return new ExtensionManager(this.context).GetAllCustomValuesForCVL(cvlId, cvl);
            }

            Dictionary<string, CVLValue> cvlValues = new Dictionary<string, CVLValue>();
            List<CultureInfo> serverLanguages = new List<CultureInfo>();

            if (cvl.DataType == DataType.LocaleString)
            {
                serverLanguages = this.GetAllLanguages();
            }

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT [Key], [Index], ParentKey, CVLId, DataType, LastModified, DateCreated, Value, Language, CVLValueId, Deactivated FROM ViewCVLValue WHERE [CVLId] = @CVLId";
                    command.Parameters.AddWithValue("@CVLId", cvlId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string key = reader.GetString(0);

                            CVLValue cvlValue;

                            cvlValues.TryGetValue(key, out cvlValue);

                            if (cvlValue == null)
                            {
                                cvlValue = new CVLValue();
                                cvlValue.Key = key;
                                cvlValue.Index = reader.GetInt32(1);

                                if (!reader.IsDBNull(2))
                                {
                                    cvlValue.ParentKey = reader.GetString(2);
                                }

                                cvlValue.CVLId = reader.GetString(3);
                                cvlValue.LastModified = reader.GetDateTime(5);
                                cvlValue.DateCreated = reader.GetDateTime(6);
                                cvlValue.Id = reader.GetInt32(9);
                                cvlValue.Deactivated = reader.GetBoolean(reader.GetOrdinal("Deactivated"));

                                cvlValues.Add(key, cvlValue);
                            }

                            if (cvl.DataType == DataType.LocaleString)
                            {
                                LocaleString ls = cvlValue.Value as LocaleString;

                                if (ls == null)
                                {
                                    ls = new LocaleString(serverLanguages);
                                }

                                if (!reader.IsDBNull(8))
                                {
                                    ls[new CultureInfo(reader.GetString(8))] = reader.GetString(7);
                                }

                                cvlValue.Value = ls;
                            }
                            else
                            {
                                if (!reader.IsDBNull(7))
                                {
                                    cvlValue.Value = reader.GetString(7);
                                }
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting values for CVL " + cvlId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting values for CVL " + cvlId, ex);
                }
            }

            return cvlValues.Values.ToList();
        }

        #endregion

        #region Private Methods

        private Dictionary<string, List<string>> GetAllFieldSetFieldTypes()
        {
            Dictionary<string, List<string>> fieldSetFieldTypes = new Dictionary<string, List<string>>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT FieldSetId, FieldTypeId FROM FieldSet_FieldType";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string fieldSetId = reader.GetString(0);
                            string fieldTypeId = reader.GetString(1);

                            if (!fieldSetFieldTypes.ContainsKey(fieldSetId))
                            {
                                fieldSetFieldTypes.Add(fieldSetId, new List<string>());
                            }

                            if (!fieldSetFieldTypes[fieldSetId].Contains(fieldTypeId))
                            {
                                fieldSetFieldTypes[fieldSetId].Add(fieldTypeId);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Fieldset Field Types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Fieldset Field Types", ex);
                }
            }

            return fieldSetFieldTypes;
        }

        #endregion
    }
}
