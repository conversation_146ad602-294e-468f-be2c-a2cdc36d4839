namespace inRiver.Server.Helpers
{
    using System;
    using System.Collections.Generic;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Util;
    using inRiver.Server.Extension;

    public static class FieldRevisionHistoryHelper
    {
        public static (List<Field> revisionFields, List<Field> historyFields) GetUpdatedFieldsForFieldRevisionHistory(List<Field> changedFields, List<Field> persistedFields)
        {
            var updatedFieldsForRevisionHistory = new List<Field>();
            var historyFields = new List<Field>();
            foreach (var updatedField in changedFields)
            {
                if (!updatedField.FieldType.TrackChanges)
                {
                    continue;
                }

                if (Utility.StringIsInriverExpression(updatedField.FieldType.ExpressionSupport, updatedField.Data as string))
                {
                    updatedFieldsForRevisionHistory.Add(updatedField);
                    continue;
                }

                var field = updatedField.Clone() as Field;
                var persistedField = persistedFields.Find(f => f.FieldType.Id == field.FieldType.Id);
                var historyField = persistedField?.Clone() as Field;
                if (updatedField.FieldType.DataType != DataType.LocaleString)
                {
                    updatedFieldsForRevisionHistory.Add(field);
                    if (historyField != null)
                    {
                        historyFields.Add(historyField);
                    }
                    continue;
                }

                var fieldData = field.Data as LocaleString;
                if (fieldData == null)
                {
                    updatedFieldsForRevisionHistory.Add(field);
                    if (historyField?.Data != null)
                    {
                        historyFields.Add(historyField);
                    }
                    continue;
                }

                var historyFieldData = historyField?.Data as LocaleString;
                if (historyField != null && historyFieldData != null)
                {
                    historyFieldData.RemoveEmptyCultures();

                    foreach (var ci in fieldData.Languages)
                    {
                        var value = fieldData[ci] ?? string.Empty;
                        if (!historyFieldData.ContainsCulture(ci))
                        {
                            continue;
                        }

                        if (value.Equals(historyFieldData[ci] ?? string.Empty, StringComparison.Ordinal))
                        {
                            // do not track this culture
                            fieldData.RemoveCulture(ci);
                            historyFieldData.RemoveCulture(ci);
                        }
                    }

                    historyFields.Add(historyField);
                }

                updatedFieldsForRevisionHistory.Add(field);
            }

            return (updatedFieldsForRevisionHistory, historyFields);
        }
    }
}
