﻿namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Api.Data.Client;
    using inRiver.Core.Models.inRiver;
    using inRiver.Remoting.Dto;

    /// <summary>
    /// Documentation.
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        public override Field GetField(int entityId, string fieldTypeId)
            => DtoFieldToField(this.GetFieldsInternal(entityId, new List<string> { fieldTypeId }).FirstOrDefault());

        public override List<Field> GetFieldsForEntity(Entity entity)
        {
            var result = this.GetFieldsInternal(entity.Id);

            return result.Select(x => DtoFieldToField(x)).ToList();
        }

        public override bool ChannelIsPublished(int channelId)
        {
            var field = GetFieldsInternal(channelId, new List<string> { "ChannelPublished" }).FirstOrDefault();

            if (field == null)
            {
                return false;
            }

            if (bool.TryParse(field.Data, out var result))
            {
                return result;
            }

            return false;
        }

        private static Field DtoFieldToField(DtoField f)
        {
            return f == null ?
                null :
                new Field
                {
                    EntityId = f.EntityId,
                    FieldTypeId = f.FieldTypeId,
                    Revision = f.Revision,
                    LastModified = f.LastModified,
                    Data = f.Data
                };
        }

        private List<DtoField> GetFieldsInternal(int entityId, IList<string> fieldTypeIdsToFetch = null)
            => InRiverDataApiClient.GetFields(this.authInfo, entityId, fieldTypeIdsToFetch);
    }
}

