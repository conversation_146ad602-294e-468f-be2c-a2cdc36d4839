namespace Telemetry.Metrics
{
    using System.Diagnostics;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.Metrics;
    using Serilog;

    public static class MetricUtil
    {
        private const int SeriesCountLimit = 1000;
        private const int ValuesPerDimensionLimit = 1000;
        public const string DefaultDimensionValue = "-";

        static MetricUtil()
        {
            TelemetryClientInstance = new TelemetryClient();
        }

        public static Stopwatch CreateOrReset(this Stopwatch stopwatch)
        {
            if (stopwatch is null)
            {
                return new Stopwatch();
            }

            stopwatch.Reset();
            return stopwatch;
        }

        internal static readonly TelemetryClient TelemetryClientInstance;

        internal static readonly MetricConfiguration MetricConfiguration = new MetricConfiguration(
            SeriesCountLimit,
            ValuesPerDimensionLimit,
            new MetricSeriesConfigurationForMeasurement(false));

        internal static string CustomerEnvironmentString(string customerSafeName, string environmentSafeName)
            => $"{customerSafeName}/{environmentSafeName}";

        internal static void LogMetricNotAdded(string serviceName, string methodName, long elapsedMilliseconds)
            => Log.Information(
                "Metric for the method {methodName} was not added. Service: {serviceName}. Elapsed time: {elapsedMilliseconds}.",
                methodName,
                serviceName,
                elapsedMilliseconds);

        internal static string GetNumberRange(this int number, int rangeSize, int maxValue = 100000)
        {
            if (number is 0)
            {
                return number.ToString();
            }

            for (var startValue = 1; startValue < maxValue; startValue += rangeSize)
            {
                if (number >= startValue && number < startValue + rangeSize)
                {
                    return $"{startValue}-{startValue + rangeSize - 1}";
                }
            }

            return $">{maxValue}";
        }
    }
}
