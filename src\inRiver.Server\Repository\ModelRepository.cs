namespace inRiver.Server.Repository
{
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.Extension;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;

    public class ModelRepository
    {
        private readonly IDataPersistance dataContext;

        private readonly RequestContext context;

        public ModelRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;

            this.context = context;
        }

        #region Entity Types

        public List<EntityType> GetAllEntityTypes()
        {
            return this.dataContext.GetAllEntityTypes();
        }

        public EntityType GetEntityType(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return null;
            }

            return this.dataContext.GetEntityType(id);
        }

        #endregion

        #region Link Types

        public List<LinkType> GetAllLinkTypes()
        {
            return this.dataContext.GetAllLinkTypes();
        }

        public List<LinkType> GetLinkTypesForEntityType(string entityTypeId)
        {
            return this.dataContext.GetLinkTypesForEntityType(entityTypeId);
        }

        #endregion

        #region Categories

        public List<Category> GetCategoriesForEntityType(string entityTypeId)
        {
            List<Category> categories = new List<Category>();
            EntityType entityType = this.dataContext.GetEntityType(entityTypeId);

            if (entityType == null)
            {
                return new List<Category>();
            }

            entityType.FieldTypes.ForEach(
                delegate (FieldType fieldType)
                {
                    if (!categories.Exists(c => c.Id == fieldType.CategoryId))
                    {
                        categories.Add(this.dataContext.GetCategory(fieldType.CategoryId));
                    }
                });

            return categories;
        }

        #endregion

        #region Field View

        #endregion

        #region Field Types

        #endregion

        #region CVL

        public CVL GetCVL(string id) => this.dataContext.GetCVL(id);

        public IList<CVL> GetAllCVLs() => this.dataContext.GetAllCVLs();

        #endregion

        #region CVLKey
        public List<Core.Models.inRiver.CVLKey> GetCvlKeysByCvlId(string cvlId)
        {
            CVL cvl = this.GetCVL(cvlId);
            if (cvl == null)
            {
                return new List<Core.Models.inRiver.CVLKey>();
            }
            if (cvl.CustomValueList)
            {
                return new ExtensionManager(this.context).GetAllCustomValuesForCVL(cvlId, cvl).Select(x =>
                        new Core.Models.inRiver.CVLKey
                        {
                            Id = x.Id,
                            Index = x.Index,
                            Key = x.Key
                        }).ToList();
            }
            return this.dataContext.GetCvlKeysByCvlId(cvlId);
        }
        #endregion

        #region CVL Value

        public void HandleSubList(List<CVLValue> initList, ref Hashtable subParents, string key)
        {
            List<CVLValue> parentList = subParents[key] as List<CVLValue>;

            foreach (var cvlValue in parentList)
            {
                List<CVLValue> newParentList = initList.Where(i => i.ParentKey == cvlValue.Key).ToList<CVLValue>();
                if (newParentList.Count > 0)
                {
                    subParents.Add(cvlValue.Key, newParentList);

                    HandleSubList(initList, ref subParents, cvlValue.Key);
                }
            }
        }

        public CVLValue GetCVLValueByKey(string key, string cvlId)
        {
            if (key == null)
            {
                context.Log(LogLevel.Warning, "Trying to get CVL value with null key");
                throw ErrorUtility.GetArgumentException("GetCVLValueByKey", "key", "Trying to get CVL value with null key");
            }

            if (cvlId == null)
            {
                context.Log(LogLevel.Warning, "Trying to get CVL value with null CVL id");
                throw ErrorUtility.GetArgumentException(
                    "GetCVLValueByKey",
                    "cvlId",
                    "Trying to get CVL value with null CVL id");
            }

            CVL cvl = GetCVL(cvlId);

            if (cvl == null)
            {
                return null;
            }

            if (cvl.CustomValueList)
            {
                return new ExtensionManager(this.context).GetCustomCVLValueByKey(cvlId, key, cvl);
            }

            return this.dataContext.GetCVLValueByKey(key, cvlId);
        }

        public List<CVLValue> GetCVLValuesForCVL(string cvlId)
        {
            CVL cvl = this.GetCVL(cvlId);

            if (cvl == null)
            {
                return new List<CVLValue>();
            }

            if (cvl.CustomValueList)
            {
                return new ExtensionManager(this.context).GetAllCustomValuesForCVL(cvlId, cvl);
            }

            List<CVLValue> cvlValues = this.dataContext.GetCVLValuesForCVL(cvlId);

            if (cvlValues == null)
            {
                return new List<CVLValue>();
            }

            cvlValues.Sort((a, b) => a.Index.CompareTo(b.Index));

            return cvlValues;
        }

        #endregion

        #region Field Set

        #endregion

        #region Model

        #endregion

        #region Private Methods

        #region Export Model

        #endregion

        #region Import Model

        #endregion

        #endregion
    }
}
