<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IsServiceFabricServiceProject>True</IsServiceFabricServiceProject>
    <ServerGarbageCollection>True</ServerGarbageCollection>
    <RuntimeIdentifier>win7-x64</RuntimeIdentifier>
    <TargetLatestRuntimePatch>False</TargetLatestRuntimePatch>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.ServiceFabric.Actors" Version="6.1.1653" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Core\inRiver.Core.csproj" />
    <ProjectReference Include="..\inRiver.Server\inRiver.Server.csproj" />
  </ItemGroup>

</Project>
