namespace inRiver.iPMC.Persistance.Tests
{
    using System.Collections.Generic;
    using Constants;
    using inRiver.iPMC.Persistance.Tests.Library;
    using Remoting.Extension;
    using Xunit;

    [TestCaseOrderer("inRiver.iPMC.Persistance.Tests.Library.PriorityOrderer", "inRiver.iPMC.Persistance.Tests")]
    [Collection("Persistance Collection")]
    public class TestPersistantEntity : IClassFixture<PersistanceFixture>
    {
        private static PersistanceFixture _persistanceFixture;

        private static Entity _testEntity;

        public TestPersistantEntity(PersistanceFixture persistanceFixture)
        {
            if (_persistanceFixture == null)
            {
                _persistanceFixture = persistanceFixture;
            }
        }

        [Fact, TestPriority(0)]
        public void TestAddEntity()
        {
            var entity = new Entity
            {
                EntityType = new EntityType(KnownDbRecords.EntityTypeId)
            };

            _testEntity = _persistanceFixture.MockPersistentEntity.AddEntity(entity);

            Assert.NotNull(_testEntity);
            Assert.IsType<Entity>(_testEntity);
        }

        [Fact, TestPriority(1)]
        public void TestGetEntity()
        {
            var entity = _persistanceFixture.MockPersistentEntity.GetEntity(_testEntity.Id);

            Assert.NotNull(entity);
            Assert.IsType<Entity>(entity);
        }

        [Theory, TestPriority(1)]
        [InlineData(true)]
        [InlineData(false)]
        public void TestGetFullEntity(bool includePendingDelete)
        {
            var entity = _persistanceFixture.MockPersistentEntity
                .GetFullEntity(_testEntity.Id, null, includePendingDelete);

            Assert.NotNull(entity);
            Assert.IsType<Entity>(entity);
        }

        [Fact, TestPriority(4)]
        public void TestGetEntities()
        {
            var entities = _persistanceFixture.MockPersistentEntity.GetEntities(new List<int>() { _testEntity.Id });

            Assert.NotNull(entities);

            Assert.IsType<List<Entity>>(entities);
        }

        [Theory, TestPriority(5)]
        [InlineData(null)]
        [InlineData("-1")]
        public void TestGetFieldsForEntity(string fieldTypeId)
        {
            if (!string.IsNullOrWhiteSpace(fieldTypeId) && fieldTypeId == "-1")
                fieldTypeId = KnownDbRecords.FieldTypeId;

            var entityFields = _persistanceFixture.MockPersistentEntity.GetFieldsForEntity(_testEntity, fieldTypeId);

            Assert.NotNull(entityFields);

            Assert.IsType<List<Field>>(entityFields);
        }
    }
}
