namespace inRiver.Core.Enum.Augmenta
{
    /// <summary>
    ///     The name of the command.
    /// </summary>
    public enum LinkCommandName
    {
        /// <summary>
        ///     Create a link.
        /// </summary>
        CreateLink = 0,

        /// <summary>
        ///     Update the sort order.
        /// </summary>
        UpdateSortOrder = 1,

        /// <summary>
        ///     Delete a link.
        /// </summary>
        DeleteLink = 2,
    }
}
