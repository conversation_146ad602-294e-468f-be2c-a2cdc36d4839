[{"FieldSets": [{"Description": {"StringMap": {}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "1", "Name": {"StringMap": {}}}, {"Description": {"StringMap": {"de": "dd", "en": "ddd", "es-US": "dd", "sv-SE": "ddd"}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "10011", "Name": {"StringMap": {"ar-SA": "<PERSON><PERSON>", "bg-BG": "<PERSON><PERSON>", "ca-ES": "<PERSON><PERSON>", "de": "test", "en": "aaa", "es-MX": "bbb", "es-US": "cccc", "it": "<PERSON><PERSON>", "sv-SE": "aaaa", "zh-TW": "<PERSON><PERSON>"}}}, {"Description": {"StringMap": {}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "jqueryTest", "Name": {"StringMap": {}}}, {"Description": {"StringMap": {}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "jqueryTest2", "Name": {"StringMap": {}}}, {"Description": {"StringMap": {"de": "German", "es-MX": "German"}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "Test1", "Name": {"StringMap": {"de": "German", "es-MX": "German"}}}, {"Description": {"StringMap": {"ca-ES": "English", "zh-TW": "English"}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "Test4", "Name": {"StringMap": {"ca-ES": "English", "zh-TW": "English"}}}, {"Description": {"StringMap": {"bg-BG": "Spanish (United States)", "ca-ES": "Spanish (United States)", "de": "Spanish (United States)", "es-MX": "Spanish (United States)", "es-US": "Spanish (United States)", "zh-TW": "German"}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "Test5", "Name": {"StringMap": {"bg-BG": "Spanish (United States)", "ca-ES": "Spanish (United States)", "de": "Spanish (United States)", "es-MX": "Spanish (United States)", "es-US": "Spanish (United States)", "zh-TW": "German"}}}, {"Description": {"StringMap": {"ca-ES": "English", "de": "English", "el-GR": "English", "es-MX": "English", "pl-PL": "English", "zh-TW": "English"}}, "EntityTypeId": "abc", "FieldTypes": [], "Id": "Test6", "Name": {"StringMap": {"ca-ES": "English", "de": "English", "el-GR": "English", "es-MX": "English", "pl-PL": "English", "zh-TW": "English"}}}], "FieldTypes": [], "Id": "abc", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "abc en"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Activity", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ActivityDescription", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {"IncludedInPlannerQuickEdit": "true", "IncludedInPlannerTooltip": "true"}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "DateTime", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Activity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ActivityEndDate", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "End Date"}}, "ReadOnly": false, "Settings": {"IncludedInPlannerQuickEdit": "true", "IncludedInPlannerTooltip": "true"}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Activity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ActivityName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {"IncludedInPlannerQuickEdit": "true", "IncludedInPlannerTooltip": "true"}, "TrackChanges": false, "Unique": true}, {"CategoryId": "General", "CVLId": "Users", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Activity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ActivityResponsible", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Responsible"}}, "ReadOnly": false, "Settings": {"IncludedInPlannerQuickEdit": "true", "IncludedInPlannerTooltip": "true"}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "DateTime", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Activity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ActivityStartDate", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Start Date"}}, "ReadOnly": false, "Settings": {"IncludedInPlannerQuickEdit": "true", "IncludedInPlannerTooltip": "true"}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "ActivityStatus", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Activity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ActivityStatus", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Status"}}, "ReadOnly": false, "Settings": {"IncludedInPlannerQuickEdit": "true", "IncludedInPlannerTooltip": "true", "PlannerColorConfig": "{\"New\":{\"BarColor\":\"#4ad43d\",\"TextColor\":null},\"Completed\":{\"BarColor\":\"#cc124d\",\"TextColor\":null}}"}, "TrackChanges": false, "Unique": false}], "Id": "Activity", "IsLinkEntityType": false, "LinkTypes": [{"Id": "ActivityActivity", "Index": 3, "LinkEntityTypeId": null, "SourceEntityTypeId": "Activity", "SourceName": {"StringMap": {"en": "Activity"}}, "TargetEntityTypeId": "Activity", "TargetName": {"StringMap": {"en": "Activity"}}}, {"Id": "ActivityItem", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "Activity", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Activity"}}}, {"Id": "ActivityProduct", "Index": 4, "LinkEntityTypeId": null, "SourceEntityTypeId": "Activity", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Activity"}}}, {"Id": "ActivityResource", "Index": 3, "LinkEntityTypeId": null, "SourceEntityTypeId": "Activity", "SourceName": {"StringMap": {"en": "Resource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ProductActivity", "Index": 7, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Activity"}}, "TargetEntityTypeId": "Activity", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "TaskActivity", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Task", "SourceName": {"StringMap": {"en": "Activities"}}, "TargetEntityTypeId": "Activity", "TargetName": {"StringMap": {"en": "Tasks"}}}], "Name": {"StringMap": {"en": "Activity", "sv-SE": "Aktivitet"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Assortment", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "AssortmentDescription", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Assortment", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "AssortmentName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "Assortment", "IsLinkEntityType": false, "LinkTypes": [{"Id": "aa", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Assortment", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"en": "Assortment"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "boolTestEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "boolTest", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "boolTestEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "cvlTest", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "boolTestEntity", "IsLinkEntityType": false, "LinkTypes": [{"Id": "boolTest", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "boolTest"}}, "TargetEntityTypeId": "boolTestEntity", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Channel", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChannelName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "Boolean", "DefaultValue": "False", "Description": {"StringMap": {}}, "EntityTypeId": "Channel", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChannelPublished", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Published"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "Channel", "IsLinkEntityType": false, "LinkTypes": [{"Id": "ChannelChannelNode", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "ChannelNode"}}, "TargetEntityTypeId": "ChannelNode", "TargetName": {"StringMap": {"en": "Channel"}}}, {"Id": "ChannelItem", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Channel"}}}, {"Id": "ChannelProduct", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Channel"}}}, {"Id": "NewEntityInChannel", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "NewEntity"}}, "TargetEntityTypeId": "NewOneEntity", "TargetName": {"StringMap": {}}}, {"Id": "SampleChannelCowFoodLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "Channel"}}, "TargetEntityTypeId": "SampleCowFood", "TargetName": {"StringMap": {"en": "CowFood"}}}, {"Id": "SampleChannelCowLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "Channel"}}, "TargetEntityTypeId": "SampleCow", "TargetName": {"StringMap": {"en": "Cow"}}}], "Name": {"StringMap": {"en": "Channel"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ChannelNode", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChannelNodeName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ChannelNode", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "NodeId", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Id"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ChannelNode", "IsLinkEntityType": false, "LinkTypes": [{"Id": "boolTest", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "boolTest"}}, "TargetEntityTypeId": "boolTestEntity", "TargetName": {"StringMap": {}}}, {"Id": "ChannelChannelNode", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "ChannelNode"}}, "TargetEntityTypeId": "ChannelNode", "TargetName": {"StringMap": {"en": "Channel"}}}, {"Id": "ChannelNodeChannelNode", "Index": 3, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Nodes"}}, "TargetEntityTypeId": "ChannelNode", "TargetName": {"StringMap": {"en": "Nodes"}}}, {"Id": "ChannelNodeProductTests", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Product test"}}, "TargetEntityTypeId": "ProductTest", "TargetName": {"StringMap": {"en": "Node"}}}, {"Id": "cin12345", "Index": 0, "LinkEntityTypeId": "completness<PERSON>uenode", "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "completnessissue", "TargetName": {"StringMap": {}}}, {"Id": "nodechimet", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "chimet", "TargetName": {"StringMap": {}}}, {"Id": "nodechimetanother", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "chimetanother", "TargetName": {"StringMap": {}}}, {"Id": "NodeItem", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Node"}}}, {"Id": "NodeProduct", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Node"}}}, {"Id": "NodeProductTest", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Product test"}}, "TargetEntityTypeId": "ProductTest", "TargetName": {"StringMap": {}}}, {"Id": "Resource", "Index": 0, "LinkEntityTypeId": "ResourceNode", "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Resource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "SampleNodeCowLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Node"}}, "TargetEntityTypeId": "SampleCow", "TargetName": {"StringMap": {"en": "Cow"}}}], "Name": {"StringMap": {"en": "Node"}}}, {"FieldSets": [{"Description": {"StringMap": {}}, "EntityTypeId": "chimet", "FieldTypes": ["EtName"], "Id": "chimfs", "Name": {"StringMap": {"ar-SA": "test1", "bg-BG": "test1", "ca-ES": "test1", "cs-CZ": "test2", "da-DK": "test2", "de": "test1", "en": "test11", "es-MX": "test1", "es-US": "test1", "it": "test1", "sv-SE": "test1", "zh-TW": "test1"}}}], "FieldTypes": [{"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimet", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "chimetfield", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimet", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "chimettestinclude", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimet", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "EtName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "chimet", "IsLinkEntityType": false, "LinkTypes": [{"Id": "chimetlink", "Index": 0, "LinkEntityTypeId": "chimlinket", "SourceEntityTypeId": "chimet", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "chimetanother", "TargetName": {"StringMap": {}}}, {"Id": "nodechimet", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "chimet", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"de": "chim_et_de", "en": "chim_et_en", "es-MX": "chim_et_sp", "es-US": "chim_et_es", "sv-SE": "chim_et_sv"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimetanother", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChimEtAnotherDesc", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimetanother", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChimEtAnotherName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimetanother", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "chimettestanothertestexclude", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "chimetanother", "IsLinkEntityType": false, "LinkTypes": [{"Id": "chimetlink", "Index": 0, "LinkEntityTypeId": "chimlinket", "SourceEntityTypeId": "chimet", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "chimetanother", "TargetName": {"StringMap": {}}}, {"Id": "nodechimetanother", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "chimetanother", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimlinket", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChimLinkEtDesc", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "chimlinket", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChimLinkEtName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "chimlinket", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {}}}, {"FieldSets": [], "FieldTypes": [], "Id": "chim<PERSON><PERSON><PERSON>", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "completnessissue", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "completness<PERSON>uename", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "completnessissue", "IsLinkEntityType": false, "LinkTypes": [{"Id": "cin12345", "Index": 0, "LinkEntityTypeId": "completness<PERSON>uenode", "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "completnessissue", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"en": "completnessissue"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "completness<PERSON>uenode", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "completnessissuename1", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "completness<PERSON>uenode", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "completnessissuename11221", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "completness<PERSON>uenode", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "CustomCVLTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CustomCVLTestField1", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CustomCVLTestField1"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": true}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "CustomCVLTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CustomCVLTestField2", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "CustomCVLTestField2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "MultivalueCVLTest", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "CustomCVLTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MultiCVLTest", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "MultivalueCVLTest"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "CustomCVLTest", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "CustomCVLTest"}}}, {"FieldSets": [], "FieldTypes": [], "Id": "DoubleTest", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {}}}, {"FieldSets": [{"Description": {"StringMap": {}}, "EntityTypeId": "Garbage", "FieldTypes": ["Garbage<PERSON>ame", "GarbageSignum"], "Id": "setWithGarbage", "Name": {"StringMap": {}}}, {"Description": {"StringMap": {}}, "EntityTypeId": "Garbage", "FieldTypes": ["Garbage<PERSON>ame"], "Id": "setWithoutGarbage", "Name": {"StringMap": {}}}], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Garbage", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "Garbage<PERSON>ame", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Garbage", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "GarbageSignum", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {"ShowInEntityOverview": "true"}, "TrackChanges": false, "Unique": true}], "Id": "Garbage", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Garbage"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "HtmlEmojiTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Description", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "HtmlEmojiTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Name", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "HtmlEmojiTest", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportRegularEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportRegularEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Import Regular Entity"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedDefEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularRestrictedDefField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": "default value 123", "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedDefEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RestrictedDefField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Restricted with <PERSON><PERSON><PERSON>"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportRestrictedDefEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Restricted with Def"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularRestrictedField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RestrictedField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Restricted Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportRestrictedEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Import Restricted Entity"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedMandatoryDefEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularRestrictedMandatoryDefField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": "default value 123", "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedMandatoryDefEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RestrictedMandatoryDefField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Restricted Mandatory with <PERSON><PERSON><PERSON>"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportRestrictedMandatoryDefEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Restricted M with Def"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedMandatoryEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularRestrictedMandatoryField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportRestrictedMandatoryEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RestrictedMandatoryField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Restricted Mandatory Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportRestrictedMandatoryEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Restricted M Entity"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportROEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ReadOnlyField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Read Only"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportROEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularFieldROEntity", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportROEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Import RO Entity"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportROMDefEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularROMDefField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": "default value 123", "Description": {"StringMap": {}}, "EntityTypeId": "ImportROMDefEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ROMWithDefField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Read Only + M with <PERSON><PERSON><PERSON>"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportROMDefEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "RO+M with <PERSON><PERSON><PERSON>"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportROMEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ReadOnlyMandatoryField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "ReadOnly + Mandatory Field"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportROMEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularROMField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportROMEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Import RO+M Entity"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": "default value 123", "Description": {"StringMap": {}}, "EntityTypeId": "ImportROwithDefaultEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ReadOnlyWIthDefField", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Read Only with  <PERSON><PERSON><PERSON>"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ImportROwithDefaultEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RegularFieldRODef", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Regular Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ImportROwithDefaultEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Import RO with <PERSON><PERSON>ult En<PERSON>"}}}, {"FieldSets": [{"Description": {"StringMap": {"ar-SA": "Arabic (Saudi Arabia)", "bg-BG": "Bulgarian (Bulgaria)", "ca-ES": "Catalan (Catalan)", "cs-CZ": "German", "da-DK": "Danish (Denmark)", "de": "German", "de-DE": "German (Germany)", "el-GR": "Greek (Greece)", "en": "English", "es-MX": "German", "es-US": "German", "it": "German", "pl-PL": "Polish (Poland)", "sv-SE": "Swedish (Sweden)", "zh-TW": "Chinese (Traditional, Taiwan)"}}, "EntityTypeId": "<PERSON><PERSON>", "FieldTypes": ["DateTimeTest", "ItemColor", "ItemDesc", "ItemFieldTypeExtraCVL2"], "Id": "fieldset1", "Name": {"StringMap": {"ar-SA": "Arabic (Saudi Arabia)", "bg-BG": "Bulgarian (Bulgaria)", "ca-ES": "Catalan (Catalan)", "cs-CZ": "German", "da-DK": "Danish (Denmark)", "de": "German", "de-DE": "German (Germany)", "el-GR": "Greek (Greece)", "en": "English", "es-MX": "German", "es-US": "German", "it": "German", "pl-PL": "Polish (Poland)", "sv-SE": "Swedish (Sweden)", "zh-TW": "Chinese (Traditional, Taiwan)"}}}, {"Description": {"StringMap": {"en": "fieldset2"}}, "EntityTypeId": "<PERSON><PERSON>", "FieldTypes": ["DateTimeTest", "ItemFieldTypeExtraCVL2"], "Id": "fieldset2", "Name": {"StringMap": {"en": "fieldset2"}}}], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "DateTime", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "DateTimeTest", "Index": 21, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemColor", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Color"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemCountry", "Index": 15, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ItemDesc", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"de": "GermanDescription", "en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": "DefaultVal_ItemFieldType1ForBug19356", "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": true, "Id": "ItemFieldType1ForBug19356", "Index": 12, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "ItemFieldType1ForBug19356", "sv-SE": "ItemFältTyp1FörBug19356"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Cities", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemFieldTypeExtraCVL2", "Index": 8, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "FieldTypeExtraCVL2", "sv-SE": "ExtraFältCVL2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemIncludeTest", "Index": 10, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Item Include Test"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "Level3", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemLevel3", "Index": 16, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "Level3"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Months", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Index": 14, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemName1", "Index": 18, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemNumber", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Item number"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "Quarter", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemQuarter", "Index": 13, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {"en": "json-data", "sv-SE": "json-data"}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemSKU", "Index": 11, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "ItemSKU", "sv-SE": "ItemSKU"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemStatus", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Status"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemUniqueField", "Index": 20, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Item Unique Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": true}, {"CategoryId": "Technical", "CVLId": null, "DataType": "Xml", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ItemXmlSku", "Index": 17, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "ItemXmlSku"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "JonasSQL", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "<PERSON><PERSON>", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Qty", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Quantity"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "<PERSON><PERSON>", "IsLinkEntityType": false, "LinkTypes": [{"Id": "aa", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Assortment", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {}}}, {"Id": "ActivityItem", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "Activity", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Activity"}}}, {"Id": "ChannelItem", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Channel"}}}, {"Id": "ItemProduct", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "ItemResource", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Resource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "ItemVideo", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Video", "sv-SE": "Video"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>", "sv-SE": "<PERSON><PERSON>"}}}, {"Id": "ItemVideo2", "Index": 3, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Video2"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "NodeItem", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Node"}}}, {"Id": "ProductItem2", "Index": 501, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductItem3", "Index": 503, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Item3"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Product3"}}}, {"Id": "SampleCowItemLinkType", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "SectionItem", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Section", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Section"}}}, {"Id": "SpecificationItem", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Specfication"}}, "TargetEntityTypeId": "Specification", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}], "Name": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"FieldSets": [], "FieldTypes": [], "Id": "MyNewTestEntityType", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {"en": "MyNewTestEntityType"}}}, {"FieldSets": [], "FieldTypes": [], "Id": "NewEntity", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {"en": "ServerEx"}}}, {"FieldSets": [{"Description": {"StringMap": {"de": "German", "zh-TW": "German"}}, "EntityTypeId": "NewOneEntity", "FieldTypes": ["test"], "Id": "NewOneEntityFieldSet", "Name": {"StringMap": {"de": "German", "en": "New Entity Field Set", "zh-TW": "German"}}}, {"Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "FieldTypes": ["ActiveNewEntity"], "Id": "NewOneEntityFieldSet2", "Name": {"StringMap": {"en": "New Entity Field Set 2"}}}], "FieldTypes": [{"CategoryId": "Technical", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ActiveNewEntity", "Index": 9, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Active", "sv-SE": "Active Swedish"}}, "ReadOnly": false, "Settings": {"ShowInEntityOverview": "False"}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "LocaleStringCvl", "Index": 7, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {"en": "Countries Parent", "sv-SE": "Countries Parent Swedish"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "NewCvl", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "Color New CVL", "sv-SE": "Color New CVL Swedish"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Technical", "CVLId": null, "DataType": "DateTime", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "NewDateTime", "Index": 8, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "New Date Time", "sv-SE": "New Date Time Swedish"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Technical", "CVLId": "Cities", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "NewEntityCities", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "Cities Child", "sv-SE": "Cities Child Swedish"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "NewInt1", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "New Int 1", "sv-SE": "New Int 1 Swedish"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "NewName1", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "New Name 1", "sv-SE": "New Name 1 Swedish"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": "default value 123", "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "NewOneName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "New One Name", "sv-SE": "New One Name Swedish"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "NewOneEntity", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "test", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Test", "sv-SE": "Test Swedish"}}, "ReadOnly": false, "Settings": {"ShowInEntityOverview": "True"}, "TrackChanges": true, "Unique": false}], "Id": "NewOneEntity", "IsLinkEntityType": false, "LinkTypes": [{"Id": "New2", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "NewOneEntity", "SourceName": {"StringMap": {"en": "New Entity"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Product 2"}}}, {"Id": "NewEntityInChannel", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "NewEntity"}}, "TargetEntityTypeId": "NewOneEntity", "TargetName": {"StringMap": {}}}, {"Id": "NewEntityInNewEntity", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "NewOneEntity", "SourceName": {"StringMap": {"en": "New Entity In"}}, "TargetEntityTypeId": "NewOneEntity", "TargetName": {"StringMap": {}}}, {"Id": "NewEntityProduct", "Index": 1, "LinkEntityTypeId": "NewEntity", "SourceEntityTypeId": "NewOneEntity", "SourceName": {"StringMap": {"en": "New Entity"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ResourseInNewEntity", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "NewOneEntity", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"en": "New Entity test", "it": "New Entity it test"}}}, {"FieldSets": [{"Description": {"StringMap": {"ar-SA": "Spanish (Mexico)", "bg-BG": "Spanish (Mexico)", "de": "Spanish (Mexico)", "en": "CVLFieldSet", "es-MX": "Spanish (Mexico)", "es-US": "aaa", "it": "Spanish (Mexico)", "sv-SE": "fdsass"}}, "EntityTypeId": "Product", "FieldTypes": ["ChildCVL", "ChildCVL2", "CVL1", "CVL10Locale3", "CVL8LocaleString", "CVL9Locale2", "DecimalNumber", "ParentCVL", "ProductName"], "Id": "CVLFieldSet", "Name": {"StringMap": {"ar-SA": "Spanish (Mexico)", "bg-BG": "Spanish (Mexico)", "de": "Spanish (Mexico)", "en": "CVLFieldSet", "es-MX": "Spanish (Mexico)", "es-US": "aaa", "it": "Spanish (Mexico)", "sv-SE": "fdsadd"}}}, {"Description": {"StringMap": {"es-US": "2222", "sv-SE": "1111"}}, "EntityTypeId": "Product", "FieldTypes": [], "Id": "DIYProduct", "Name": {"StringMap": {"en": "DIY", "es-US": "2222", "sv-SE": "1111"}}}, {"Description": {"StringMap": {"de": "eeee", "en": "1111", "es-MX": "bbbb", "es-US": "cccc", "it": "Spanish (Mexico)", "sv-SE": "2222"}}, "EntityTypeId": "Product", "FieldTypes": ["DecimalNumber", "ProductDescription", "ProductName", "ProductNumber"], "Id": "FieldSet3", "Name": {"StringMap": {"cs-CZ": "Arabic", "da-DK": "Arabic", "de": "eeee", "de-DE": "Arabic", "el-GR": "Arabic", "en": "1111", "es-MX": "bbbb", "es-US": "cccc", "it": "Spanish (Mexico)", "pl-PL": "Arabic", "sv-SE": "2222"}}}, {"Description": {"StringMap": {}}, "EntityTypeId": "Product", "FieldTypes": ["DecimalNumber", "ProductDescription", "ProductName"], "Id": "FieldSet5", "Name": {"StringMap": {"en": "Product Field Set"}}}], "FieldTypes": [{"CategoryId": "FileInformation", "CVLId": null, "DataType": "Boolean", "DefaultValue": "False", "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Approved", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Approved"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "ChildCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ChildCVL", "Index": 26, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "ChildCVL"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "ChildCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChildCVL2", "Index": 27, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "ChildCVL2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Color", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Color"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "CustomCvl", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CustomCVLField", "Index": 28, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "ChildTestCVL", "DataType": "CVL", "DefaultValue": "spring_jacket", "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL1", "Index": 10, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVL1Child"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL10Locale3", "Index": 19, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVL10Locale3"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "ChildChildTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL2ReadOnly", "Index": 11, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVL2ChildChild"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": "first", "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL3Mandatory", "Index": 12, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "CVL3Mandatory"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": "second", "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL4Multivalue", "Index": 13, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {"en": "CVL4Multivalue"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": "Sand", "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL5", "Index": 14, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVL5"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL6Trackable", "Index": 15, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVL6Trackable"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL7", "Index": 16, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVL7"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL8LocaleString", "Index": 17, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVL8LocaleString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL9Locale2", "Index": 18, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"ar-SA": "CVL9Locale2", "bg-BG": "CVL9Locale2", "ca-ES": "CVL9Locale2", "de": "CVL9Locale2", "en": "CVL9Locale2", "es-MX": "CVL9Locale2", "es-US": "CVL9Locale2", "it": "CVL9Locale2", "sv-SE": "CVL9Locale2", "zh-TW": "CVL9Locale2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "DecimalNumber", "Index": 44, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "DecimalNumber"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "DoubleNumberId", "Index": 32, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"da-DK": "DoubleNumber", "de": "DoubleNumber", "en": "DoubleNumber", "sv-SE": "DoubleNumber"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "FieldForSKU", "Index": 6, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MainCategory", "Index": 21, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {"en": "MainCategory", "sv-SE": "MainCategory"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "MainMenuCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MainMenu", "Index": 20, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {"en": "MainMenu", "sv-SE": "MainMenu"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "MultiCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MultiCVL", "Index": 43, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {"en": "MultiCVL"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Number2", "Index": 30, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Number 2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "MultivalueCVLTest", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ParentCVL", "Index": 25, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "ParentCVL"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "ParentTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCustomCVLTest", "Index": 9, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "CVLParent"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLLocaleString", "Index": 38, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "ProductLocaleString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLLocaleStringMultivalue", "Index": 39, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {"en": "ProductCVLLocaleStringMultivalue"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLString", "Index": 37, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "ProductCVLString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLStringMultivalue", "Index": 46, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductDescription", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductIncludeTest", "Index": 40, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductIncludeTest2", "Index": 8, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Product Include Test 2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductLocaleString", "Index": 35, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "ProductLocaleString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ProductName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"de": "Name", "de-DE": "Name", "en": "Name", "pl-PL": "Nazwa"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ProductNumber", "Index": 29, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"ar-SA": "Product Number", "bg-BG": "Product Number", "ca-ES": "Product Number", "cs-CZ": "Product Number", "da-DK": "Product Number", "de": "Product Number", "de-DE": "Product Number", "el-GR": "Product Number", "en": "Product Number", "es-MX": "Product Number", "es-US": "Product Number", "it": "Product Number", "pl-PL": "Product Number", "sv-SE": "Product Number", "zh-TW": "Product Number"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductXmlSKU", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Series", "Index": 23, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Series", "sv-SE": "Series"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SimpleDouble", "Index": 33, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "SimpleDouble"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SubCategory", "Index": 22, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "SubCategory", "sv-SE": "SubCategory"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "UniqueField", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Unique Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "uniqueFieldIntegerImportError", "Index": 42, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "uniqueFieldIntegerImportError"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": true}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "uniqueFieldStringImportError", "Index": 41, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "uniqueFieldStringImportError"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": true}], "Id": "Product", "IsLinkEntityType": false, "LinkTypes": [{"Id": "ActivityProduct", "Index": 4, "LinkEntityTypeId": null, "SourceEntityTypeId": "Activity", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Activity"}}}, {"Id": "ChannelProduct", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Channel"}}}, {"Id": "ItemProduct", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "New2", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "NewOneEntity", "SourceName": {"StringMap": {"en": "New Entity"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Product 2"}}}, {"Id": "NewEntityProduct", "Index": 1, "LinkEntityTypeId": "NewEntity", "SourceEntityTypeId": "NewOneEntity", "SourceName": {"StringMap": {"en": "New Entity"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "NodeProduct", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Node"}}}, {"Id": "ProductActivity", "Index": 7, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Activity"}}, "TargetEntityTypeId": "Activity", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductItem", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductItem2", "Index": 501, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductItem3", "Index": 503, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Item3"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Product3"}}}, {"Id": "ProductProduct", "Index": 0, "LinkEntityTypeId": "ProductToProduct", "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Source Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Target Product"}}}, {"Id": "ProductResource", "Index": 4, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Resource2"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductResource2", "Index": 7, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "ProductVideo"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ProductResource3", "Index": 9, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "ProductGif"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ProductResource6", "Index": 5, "LinkEntityTypeId": "TestId", "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "ProductResource6"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ProductSpecification", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Specification"}}, "TargetEntityTypeId": "Specification", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductToAdditionalItem", "Index": 10, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Additional Item"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductUpsell", "Index": 3, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"ar-SA": "Upsell", "bg-BG": "Upsell", "ca-ES": "Upsell", "cs-CZ": "Upsell", "da-DK": "Upsell", "de": "Upsell", "de-DE": "Upsell", "el-GR": "Upsell", "en": "Upsell", "es-MX": "Upsell", "es-US": "Upsell", "it": "Upsell", "sv-SE": "Upsell", "zh-TW": "Upsell"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Upsell to", "es-US": "Upsell to", "sv-SE": "Uppsell till"}}}, {"Id": "ProductVideo", "Index": 5, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Video"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "Res", "Index": 12, "LinkEntityTypeId": "ResourceNode", "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "SectionProduct", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Section", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {}}}, {"Id": "TaskProduct", "Index": 0, "LinkEntityTypeId": "NewEntity", "SourceEntityTypeId": "Task", "SourceName": {"StringMap": {"ar-SA": "Some Product", "de": "Some Product", "en": "Some Product", "es-MX": "Some Product", "es-US": "Some Product", "it": "Some Product", "sv-SE": "Some Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Task"}}}, {"Id": "TestLinkType", "Index": 4, "LinkEntityTypeId": "NewEntity", "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"en": "Product"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductResource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductAmazonTemplate", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "ProductAmazonTemplate"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductResource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceAmazonAltText", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Resource Amazon Alt Text"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductResource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceAmazonDescription", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Resource Amazon Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "ResourceAmazonImageType", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductResource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceAmazonImageType", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Resource Amazon Image Type"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductResource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceAmazonTitle", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Resource Amazon Title"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "ProductResource", "IsLinkEntityType": false, "LinkTypes": [{"Id": "ResourceProductResource", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ProductResource", "SourceName": {"StringMap": {"en": "ProductResource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Resource"}}}], "Name": {"StringMap": {"en": "Product Resource"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": "1.1", "Description": {"StringMap": {}}, "EntityTypeId": "ProductTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "DoubleField", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Double"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Categorisation", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductShortDescription", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Product Short Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductTestBool", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "ERPInformation", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductTestName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Product test Name"}}, "ReadOnly": false, "Settings": {"ShowInEntityOverview": "True"}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductTest", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductTestNumber", "Index": 2, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Product Test Number"}}, "ReadOnly": false, "Settings": {"ShowInEntityOverview": "true"}, "TrackChanges": true, "Unique": true}], "Id": "ProductTest", "IsLinkEntityType": false, "LinkTypes": [{"Id": "ChannelNodeProductTests", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Product test"}}, "TargetEntityTypeId": "ProductTest", "TargetName": {"StringMap": {"en": "Node"}}}, {"Id": "NodeProductTest", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Product test"}}, "TargetEntityTypeId": "ProductTest", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"en": "Product test"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductToProduct", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CommonProductDescription", "Index": 1, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ProductToProduct", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CommonProductName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "ProductToProduct", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {"en": "RealatedProduct"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Publication", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PublicationName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "Publication", "IsLinkEntityType": false, "LinkTypes": [{"Id": "PublicationSection", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Publication", "SourceName": {"StringMap": {"en": "Section"}}, "TargetEntityTypeId": "Section", "TargetName": {"StringMap": {"en": "Publication"}}}], "Name": {"StringMap": {"en": "Publication"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Details", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Resource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ActiveResource", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Active"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "File", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Resource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceFileId", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "File Id"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Resource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceFilename", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "File name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Resource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceImageMap", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Resource", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ResourceMimeType", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Mime type"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "Resource", "IsLinkEntityType": false, "LinkTypes": [{"Id": "ActivityResource", "Index": 3, "LinkEntityTypeId": null, "SourceEntityTypeId": "Activity", "SourceName": {"StringMap": {"en": "Resource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ItemResource", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Resource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "ItemVideo", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Video", "sv-SE": "Video"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>", "sv-SE": "<PERSON><PERSON>"}}}, {"Id": "ItemVideo2", "Index": 3, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Video2"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "ProductItem", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductResource", "Index": 4, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Resource2"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductResource2", "Index": 7, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "ProductVideo"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ProductResource3", "Index": 9, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "ProductGif"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ProductResource6", "Index": 5, "LinkEntityTypeId": "TestId", "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "ProductResource6"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ProductToAdditionalItem", "Index": 10, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Additional Item"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "ProductVideo", "Index": 5, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Video"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "Res", "Index": 12, "LinkEntityTypeId": "ResourceNode", "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "Resource", "Index": 0, "LinkEntityTypeId": "ResourceNode", "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Resource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "ResourceProductResource", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ProductResource", "SourceName": {"StringMap": {"en": "ProductResource"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Resource"}}}, {"Id": "ResourseInNewEntity", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "NewOneEntity", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}, {"Id": "SampleCowResourceLinkType", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Resource"}}}, {"Id": "TestLinkType", "Index": 4, "LinkEntityTypeId": "NewEntity", "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"en": "Resource"}}}, {"FieldSets": [], "FieldTypes": [], "Id": "ResourceNode", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {"en": "ResourceNode"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCalf", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCalfLocaleDescription", "Index": 2, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCalf", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCalfLocaleName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCalf", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCalfUsedAsTestDataTagSoDontChange", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Tag"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "SampleCalf", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Calf"}}}, {"FieldSets": [{"Description": {"StringMap": {"en": "All cow number fields"}}, "EntityTypeId": "SampleCow", "FieldTypes": ["SampleCowAge", "SampleCowHeight"], "Id": "SampleCowBaseNumberFieldSet", "Name": {"StringMap": {"en": "Cow number field set"}}}], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "SampleCowAge", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Age"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": "Default Description", "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowDescription", "Index": 2, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "SampleCowHeight", "Index": 7, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Height"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "SampleCowOutsideStatus", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowIsOutside", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "IsOutside"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowLocaleStallName", "Index": 6, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Local stall name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "SampleCowName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowUsedAsTestDataTagSoDontChange", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Tag"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "DateTime", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCow", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowVerificationDate", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Verification date"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "SampleCow", "IsLinkEntityType": false, "LinkTypes": [{"Id": "SampleChannelCowLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "Channel"}}, "TargetEntityTypeId": "SampleCow", "TargetName": {"StringMap": {"en": "Cow"}}}, {"Id": "SampleCowItemLinkType", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}, {"Id": "SampleCowMilkLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "SampleMilk", "TargetName": {"StringMap": {"en": "Milk"}}}, {"Id": "SampleCowResourceLinkType", "Index": 1, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "Resource", "TargetName": {"StringMap": {"en": "Resource"}}}, {"Id": "SampleCowSpecificationLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "Specification", "TargetName": {"StringMap": {"en": "Specification"}}}, {"Id": "SampleNodeCowLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "ChannelNode", "SourceName": {"StringMap": {"en": "Node"}}, "TargetEntityTypeId": "SampleCow", "TargetName": {"StringMap": {"en": "Cow"}}}], "Name": {"StringMap": {"en": "Cow"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCowFood", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowFoodAmount", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Amount"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCowFood", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowFoodName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCowFood", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowFoodUniqueTag", "Index": 3, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Unique tag"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": true}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleCowFood", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleCowFoodUsedAsTestDataTagSoDontChange", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Tag"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "SampleCowFood", "IsLinkEntityType": false, "LinkTypes": [{"Id": "SampleChannelCowFoodLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Channel", "SourceName": {"StringMap": {"en": "Channel"}}, "TargetEntityTypeId": "SampleCowFood", "TargetName": {"StringMap": {"en": "CowFood"}}}], "Name": {"StringMap": {"en": "Cow Food"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleHorseLinkType", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleHorseLinkTypeAge", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Age"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleHorseLinkType", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleHorseLinkTypeDescription", "Index": 2, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "SampleCowOutsideStatus", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleHorseLinkType", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleHorseLinkTypeIsOutside", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "IsOutside"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleHorseLinkType", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleHorseLinkTypeName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleHorseLinkType", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleHorseLinkTypeUsedAsTestDataTagSoDontChange", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Tag"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "SampleHorseLinkType", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleMilk", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleMilkAmount", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Amount"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleMilk", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleMilkDescription", "Index": 2, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "SampleMilkTypes", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleMilk", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleMilkMilkMultiType", "Index": 6, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "Milk Multi type"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "SampleMilkshakeTypes", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleMilk", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleMilkMilkshakeType", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Milkshake type"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "SampleMilkTypes", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleMilk", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleMilkMilkType", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Milk type"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleMilk", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleMilkName", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SampleMilk", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SampleMilkUsedAsTestDataTagSoDontChange", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Tag"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "SampleMilk", "IsLinkEntityType": false, "LinkTypes": [{"Id": "SampleCowMilkLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "SampleMilk", "TargetName": {"StringMap": {"en": "Milk"}}}], "Name": {"StringMap": {"en": "Milk"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": "guid", "Description": {"StringMap": {}}, "EntityTypeId": "Section", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SectionId", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "SectionId"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Section", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SectionName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "Section", "IsLinkEntityType": false, "LinkTypes": [{"Id": "PublicationSection", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Publication", "SourceName": {"StringMap": {"en": "Section"}}, "TargetEntityTypeId": "Section", "TargetName": {"StringMap": {"en": "Publication"}}}, {"Id": "Section", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Section", "SourceName": {"StringMap": {"en": "Section"}}, "TargetEntityTypeId": "Section", "TargetName": {"StringMap": {"en": "Section"}}}, {"Id": "SectionItem", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Section", "SourceName": {"StringMap": {"en": "<PERSON><PERSON>"}}, "TargetEntityTypeId": "<PERSON><PERSON>", "TargetName": {"StringMap": {"en": "Section"}}}, {"Id": "SectionProduct", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Section", "SourceName": {"StringMap": {}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {}}}], "Name": {"StringMap": {"en": "Section"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Authoring", "CVLId": "HangPortalZero", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SimpleEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SimpleEntityCustomCVLTestExtension", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "HangPortalZero"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SimpleEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SimpleStringField", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "SimpleStringField"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "SimpleEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "SimpleEntity"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "SIze", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Size", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "SIze", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Size"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Specification", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SpecificationCvlKey", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Specification CVL Key", "sv-SE": "Specification CVL Key"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Specification", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SpecificationId", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Specification Id", "sv-SE": "Specification Id"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Specification", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SpecificationName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Specification", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SpecificationNamee", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Specification Name", "sv-SE": "Specification Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "Specification", "IsLinkEntityType": false, "LinkTypes": [{"Id": "ProductSpecification", "Index": 2, "LinkEntityTypeId": null, "SourceEntityTypeId": "Product", "SourceName": {"StringMap": {"en": "Specification"}}, "TargetEntityTypeId": "Specification", "TargetName": {"StringMap": {"en": "Product"}}}, {"Id": "SampleCowSpecificationLinkType", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "SampleCow", "SourceName": {"StringMap": {"en": "Cow"}}, "TargetEntityTypeId": "Specification", "TargetName": {"StringMap": {"en": "Specification"}}}, {"Id": "SpecificationItem", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "<PERSON><PERSON>", "SourceName": {"StringMap": {"en": "Specfication"}}, "TargetEntityTypeId": "Specification", "TargetName": {"StringMap": {"en": "<PERSON><PERSON>"}}}], "Name": {"StringMap": {"en": "Specification"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Details", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ForSearchHintDouble", "Index": 9, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "For Search Hint Double"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ForSearchHintInteger", "Index": 8, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "For Search Hint integer"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "RWNROMand", "Index": 10, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {}}, "ReadOnly": true, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "GroupTask", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "TaskAssignedGroupTask", "Index": 7, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Group task"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "Users", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "TaskAssignedTo", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Assigned to"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "Users", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "TaskCreatedBy", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Created by"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "TaskDescription", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "DateTime", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "TaskDueDate", "Index": 6, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Due date"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "TaskName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "General", "CVLId": "TaskStatus", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "Task", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "TaskStatus", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Status"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "Task", "IsLinkEntityType": false, "LinkTypes": [{"Id": "TaskActivity", "Index": 0, "LinkEntityTypeId": null, "SourceEntityTypeId": "Task", "SourceName": {"StringMap": {"en": "Activities"}}, "TargetEntityTypeId": "Activity", "TargetName": {"StringMap": {"en": "Tasks"}}}, {"Id": "TaskProduct", "Index": 0, "LinkEntityTypeId": "NewEntity", "SourceEntityTypeId": "Task", "SourceName": {"StringMap": {"ar-SA": "Some Product", "de": "Some Product", "en": "Some Product", "es-MX": "Some Product", "es-US": "Some Product", "it": "Some Product", "sv-SE": "Some Product"}}, "TargetEntityTypeId": "Product", "TargetName": {"StringMap": {"en": "Task"}}}], "Name": {"StringMap": {"en": "Task title"}}}, {"FieldSets": [], "FieldTypes": [], "Id": "Test", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "& <> \""}}}, {"FieldSets": [], "FieldTypes": [], "Id": "test_ET_20220624K_PASZKOWSKI3", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {"en": "20220624K_PASZKOWSKI3"}}}, {"FieldSets": [], "FieldTypes": [], "Id": "TestId", "IsLinkEntityType": true, "LinkTypes": [], "Name": {"StringMap": {"en": "TestName"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "FileInformation", "CVLId": "MultivalueCVLTest", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglArea", "Index": 11, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "Product Area"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "Users", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglAuthor", "Index": 25, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Author"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": "Cities", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglCategory", "Index": 27, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "City"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Proposal", "CVLId": "Cities", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglCategoryProposal", "Index": 26, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Proposed City"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Technical", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglCommunityArticle", "Index": 16, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Primary Community Article ID"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "DateTime", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglDate", "Index": 13, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Next review Date"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Technical", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglDescription", "Index": 17, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Proposal", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglDescriptionProposal", "Index": 19, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Proposed Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Technical", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglExample", "Index": 22, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Example"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Proposal", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglExampleProposal", "Index": 23, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Proposed Example"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": "guid", "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglId", "Index": 10, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "PPGL ID"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true}, {"CategoryId": "FileInformation", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglName", "Index": 14, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Name"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true}, {"CategoryId": "Technical", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglRationale", "Index": 20, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Rationale"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Proposal", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglRationaleProposal", "Index": 21, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Proposed <PERSON><PERSON><PERSON>"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": "ActivityStatus", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglStatus", "Index": 12, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Status"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglSubmittedBy", "Index": 24, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Submitted By"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Proposal", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglTypeProposal", "Index": 18, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Proposed Country"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "Details", "CVLId": "Users", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestPPGL", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "PpglValidatedBy", "Index": 15, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "Validated By"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "TestPPGL", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "TestPPGL"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": "Blue", "Description": {"StringMap": {}}, "EntityTypeId": "TestProductId", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MandatoryCVLFieldId", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "MandatoryCVLField"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "MultivalueCVLTest", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestProductId", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MultiCVLFieldId", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": true, "Name": {"StringMap": {"en": "MultiCVLField"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestProductId", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SingleCVLField2Id", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "SingleCVLField2"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": false, "Unique": false}, {"CategoryId": "Details", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "TestProductId", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SingleCVLFieldId", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"StringMap": {"en": "SingleCVLField"}}, "ReadOnly": true, "Settings": {}, "TrackChanges": false, "Unique": false}], "Id": "TestProductId", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "TestProduct"}}}, {"FieldSets": [], "FieldTypes": [{"CategoryId": "General", "CVLId": "ChildChildTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ThreeLevelCVLEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChildChildLevel", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Child Child Level"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": "ChildTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ThreeLevelCVLEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChildLevel", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"StringMap": {"en": "Child Level"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ThreeLevelCVLEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ColorFieldId", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Color"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}, {"CategoryId": "General", "CVLId": "ParentTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"StringMap": {}}, "EntityTypeId": "ThreeLevelCVLEntity", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ParentLevel", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"StringMap": {"en": "Parent Level"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false}], "Id": "ThreeLevelCVLEntity", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "Three Levels CVL Entity"}}}, {"FieldSets": [], "FieldTypes": [], "Id": "VetonTest", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"StringMap": {"en": "removing to walk around import"}}}]