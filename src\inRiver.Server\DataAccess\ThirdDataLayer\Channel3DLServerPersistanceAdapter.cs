﻿namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Remoting.Dto;

    /// <summary>
    /// Contains Channel related functionality.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public override void ReloadChannel(int id)
            => InRiverDataApiClient.RebuildChannel(this.GetAuthInfo(), id);

        public override bool AddChannelLink(DtoLink link, int channelId)
            => InRiverDataApiClient.AddChannelLink(this.GetAuthInfo(), link, channelId);

        public override bool IsExcludedByChannelFilter(int channelId, int entityId, string linkTypeId)
            => InRiverDataApiClient.IsEntityExcludedByChannelFilter(this.GetAuthInfo(), entityId, channelId);

        public override bool IsIncludedByChannelFilter(int channelId, int entityId, string linkTypeId)
            => !InRiverDataApiClient.IsEntityExcludedByChannelFilter(this.GetAuthInfo(), entityId, channelId);
    }
}