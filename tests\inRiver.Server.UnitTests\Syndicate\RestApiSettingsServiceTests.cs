namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Core.Constants;
    using inRiver.Core.Util;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Script.Api;
    using inRiver.Server.Syndication.Script.Api.Models;
    using Newtonsoft.Json;
    using Xunit;

    public class RestApiSettingsServiceTests
    {
        private const string EncryptedEndpointAliases = "someEncryptedValue";

        [Fact]
        public void GetAliasSettings_AliasSettingsExists_ShouldReturnSettings()
        {
            // Arrange
            const string endpointAlias = "testName";
            var aliasSettings = CreateAliasSettings(endpointAlias);
            var aliasSettingsJson = JsonConvert.SerializeObject(new List<EndpointAliasSetting> { aliasSettings });
            var restApiSettingsService = CreateRestApiSettingsService(aliasSettingsJson);

            // Act
            var settings = restApiSettingsService.GetAliasSettings(endpointAlias);

            // Assert
            settings.Should().BeEquivalentTo(aliasSettings);
        }

        [Fact]
        public void GetAliasSettings_OneOfSettingsFieldsIsMissing_ShouldReturnSettings()
        {
            // Arrange
            const string endpointAlias = "testName";
            var aliasSettings = new EndpointAliasSetting
            {
                EndpointAlias = endpointAlias,
                Url = "testUrl"
            };
            var aliasSettingsJson = JsonConvert.SerializeObject(new List<EndpointAliasSetting> { aliasSettings });
            var restApiSettingsService = CreateRestApiSettingsService(aliasSettingsJson);

            // Act
            var settings = restApiSettingsService.GetAliasSettings(endpointAlias);

            // Assert
            settings.Should().BeEquivalentTo(aliasSettings);
        }

        [Fact]
        public void GetAliasSettings_AliasSettingsDoesNotExist_ShouldThrowException()
        {
            // Arrange
            const string endpointAlias = "testName";
            var endpointAliases = new List<EndpointAliasSetting>
            {
                CreateAliasSettings("anotherName")
            };
            var endpointAliasesJson = JsonConvert.SerializeObject(endpointAliases);
            var restApiSettingsService = CreateRestApiSettingsService(endpointAliasesJson);

            // Act
            Action act = () => restApiSettingsService.GetAliasSettings(endpointAlias);

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage($"Settings for alias '{endpointAlias}' were not found.");
        }

        [Fact]
        public void CreateInstanceOfRestApiSettingsService_EndpointAliasesSettingIsEmpty_ShouldThrowException()
        {
            // Arrange
            var context = new RequestContext();
            var dataPersistance = CreateDataPersistance(endpointAliases: string.Empty);
            context.DataPersistance = dataPersistance;
            var cryptoService = A.Fake<ICrypto>();

            // Act
            Action act = () => _ = new RestApiSettingsService(context, cryptoService);

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage("API endpoint aliases setting was not found.");
        }

        [Fact]
        public void CreateInstanceOfRestApiSettingsService_EndpointAliasesSettingIsNull_ShouldThrowException()
        {
            // Arrange
            var context = new RequestContext();
            var dataPersistance = CreateDataPersistance(endpointAliases: null);
            context.DataPersistance = dataPersistance;
            var cryptoService = A.Fake<ICrypto>();

            // Act
            Action act = () => _ = new RestApiSettingsService(context, cryptoService);

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage("API endpoint aliases setting was not found.");
        }

        [Fact]
        public void CreateInstanceOfRestApiSettingsService_AliasSettingsHasWrongJsonFormat_ShouldThrowException()
        {
            // Arrange
            var context = new RequestContext();
            const string wrongEndpointAliases = "[{ Name: testName\" }]";
            var dataPersistance = CreateDataPersistance(endpointAliases: EncryptedEndpointAliases);
            context.DataPersistance = dataPersistance;
            var cryptoService = A.Fake<ICrypto>();
            _ = A.CallTo(() => cryptoService.DecryptStringAsync(A<string>.Ignored))
                .Returns(Task.FromResult(wrongEndpointAliases));

            // Act
            Action act = () => _ = new RestApiSettingsService(context, cryptoService);

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage("Invalid JSON alias configuration format.");
        }

        [Fact]
        public void IsActivatedAlias_IsInactivatedFlagIsFalse_ShouldReturnTrue()
        {
            // Arrange
            const string endpointAlias = "testName";
            var aliasSettings = CreateAliasSettings(endpointAlias);
            var aliasSettingsJson = JsonConvert.SerializeObject(new List<EndpointAliasSetting> { aliasSettings });
            var restApiSettingsService = CreateRestApiSettingsService(aliasSettingsJson);

            // Act
            var isAvailable = restApiSettingsService.IsActivatedAlias(endpointAlias);

            // Assert
            isAvailable.Should().BeTrue();
        }

        [Fact]
        public void IsActivatedAlias_IsInactivatedFlagIsTrue_ShouldReturnFalse()
        {
            // Arrange
            const string endpointAlias = "testName";
            var aliasSettings = CreateAliasSettings(endpointAlias, isInactivated: true);
            var aliasSettingsJson = JsonConvert.SerializeObject(new List<EndpointAliasSetting> { aliasSettings });
            var restApiSettingsService = CreateRestApiSettingsService(aliasSettingsJson);

            // Act
            var isAvailable = restApiSettingsService.IsActivatedAlias(endpointAlias);

            // Assert
            isAvailable.Should().BeFalse();
        }

        [Fact]
        public void IsActivatedAlias_IsInactivatedFlagIsNotSpecified_ShouldReturnTrue()
        {
            // Arrange
            var aliasSettings = @"[{""EndpointAlias"":""Name1""}]";
            var restApiSettingsService = CreateRestApiSettingsService(aliasSettings);

            // Act
            var isAvailable = restApiSettingsService.IsActivatedAlias("Name1");

            // Assert
            isAvailable.Should().BeTrue();
        }

        [Fact]
        public void IsActivatedAlias_AliasSettingsNotFound_ShouldReturnFalse()
        {
            // Arrange
            var aliasSettings = @"[{""Name"":""Name""}]";
            var restApiSettingsService = CreateRestApiSettingsService(aliasSettings);

            // Act
            Action act = () => _ = restApiSettingsService.IsActivatedAlias("Name1");

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage($"Settings for alias 'Name1' were not found.");
        }

        private static IRestApiSettingsService CreateRestApiSettingsService(string endpointAliases)
        {
            var context = new RequestContext();
            var dataPersistance = CreateDataPersistance(EncryptedEndpointAliases);
            context.DataPersistance = dataPersistance;
            var cryptoService = A.Fake<ICrypto>();
            _ = A.CallTo(() => cryptoService.DecryptStringAsync(A<string>.Ignored))
                .Returns(Task.FromResult(endpointAliases));
            var restApiSettingsService = A.Fake<RestApiSettingsService>(options => options.WithArgumentsForConstructor(() => new RestApiSettingsService(context, cryptoService)));

            return restApiSettingsService;
        }

        private static IDataPersistance CreateDataPersistance(string endpointAliases)
        {
            var dataPersistance = A.Fake<IDataPersistance>();
            _ = A.CallTo(() => dataPersistance.GetServerSetting(ServerConstants.ENCRYPTED_SYNDICATION_ENDPOINT_ALIASES))
                .Returns(endpointAliases);

            return dataPersistance;
        }

        private static EndpointAliasSetting CreateAliasSettings(string endpointAlias, bool isInactivated = false) =>
            new EndpointAliasSetting
            {
                EndpointAlias = endpointAlias,
                IsInactivated = isInactivated,
                Headers = new Dictionary<string, string>
                {
                    { "headerKey1", "headerValue1" },
                    { "headerKey2", "headerValue2" }
                },
                Url = "testUrl"
            };
    }
}
