namespace inRiver.Server.Repository
{
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.Request;

    public class LongRunningJobRepository
    {
        private readonly IDataPersistance dataContext;

        public LongRunningJobRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;
        }

        public string GetStorageAccountConnectionString(int environmentId)
        {
            return this.dataContext.GetStorageAccountConnectionString(environmentId);
        }
    }
}
