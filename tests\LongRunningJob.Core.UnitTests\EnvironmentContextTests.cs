namespace LongRunningJob.Core.UnitTests
{
    using System;
    using FluentAssertions;
    using LongRunningJob.Core.Models;
    using Xunit;

    public class EnvironmentContextTests
    {
        [Fact]
        public void Constructor_AllRequiredParameters_ShouldCreateEnvironmentContext()
        {
            const int environmentId = 123;
            const string customerSafename = "testCustomer";
            const string environmentSafename = "testEnvironment";
            const string databaseConnectionString = "databaseConnectionString";
            const string storageAccountConnectionString = "storageAccountConnectionString";

            var result = new EnvironmentContext(environmentId, customerSafename, environmentSafename, databaseConnectionString,
                storageAccountConnectionString);

            result.EnvironmentId.Should().Be(environmentId);
            result.CustomerSafename.Should().Be(customerSafename);
            result.EnvironmentSafename.Should().Be(environmentSafename);
            result.DatabaseConnectionString.Should().Be(databaseConnectionString);
            result.StorageAccountConnectionString.Should().Be(storageAccountConnectionString);
        }

        [Theory]
        [InlineData("", "testEnvironment", "databaseConnectionString", "storageAccountConnectionString")]
        [InlineData("testCustomer", "", "databaseConnectionString", "storageAccountConnectionString")]
        [InlineData("testCustomer", "testEnvironment", "", "storageAccountConnectionString")]
        [InlineData("testCustomer", "testEnvironment", "databaseConnectionString", "")]
        [InlineData(null, "testEnvironment", "databaseConnectionString", "storageAccountConnectionString")]
        [InlineData("testCustomer", null, "databaseConnectionString", "storageAccountConnectionString")]
        [InlineData("testCustomer", "testEnvironment", null, "storageAccountConnectionString")]
        [InlineData("testCustomer", "testEnvironment", "databaseConnectionString", null)]
        public void Constructor_MissingRequiredParameter_ShouldThrowException(string customerSafename, string environmentSafename, string databaseConnectionString, string storageAccountConnectionString)
        {
            Action act = () => new EnvironmentContext(123, customerSafename, environmentSafename, databaseConnectionString, storageAccountConnectionString);

            act.Should().Throw<ArgumentException>();
        }
    }
}
