namespace inRiver.Server.DataAccess
{
    using inRiver.Server.DataAccess.ThirdDataLayer;
    using inRiver.Server.Request;

    public static class IPMCServerPersistanceFactory
    {
        public static IDataPersistance GetInstance(RequestContext context)
        {
            inRiverPersistance defaultPersistance = new inRiverPersistance(context, true);

            IDataPersistance persistance = defaultPersistance;

            if (context.EntityModel == 1)
            {
                persistance = new IPMCServerPersistanceAdapter(context, defaultPersistance);
            }
            else if (context.EntityModel == 2)
            {
                persistance = new IPMCServer3DLPersistanceAdapter(context, defaultPersistance);
            }

            return persistance;
        }

    }
}
