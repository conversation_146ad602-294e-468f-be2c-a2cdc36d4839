namespace inRiver.Core.Util
{
    using System.Collections.Generic;

    public static class Constants
    {
        public const string InternalDateTimeFormat = "yyyy-MM-dd HH\\:mm\\:ss";

        public static readonly List<string> AllowedHtmlTemplateTypes = new List<string> { "EnrichPDFTemplate", "EnrichPreviewTemplate", "PlanReleasePreviewTemplate", "EditTemplate", "ApplicationTemplate", "PortalTemplate", "ContentStoreTemplate", "ContentStoreCoverPage", "ContentStoreEndPage" };

        public static readonly List<string> RestrictedHtmlContent = new List<string>{"system.io"};
    }
}
