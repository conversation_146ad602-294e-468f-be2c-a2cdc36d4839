﻿
namespace inRiver.Core.Models.inRiver.ExcelImport
{
    public class FileImportColumnModel
    {
        public string Id { get; set; }

        public string ColumnName { get; set; }

        public bool IsEmpty { get; set; }

        public bool IsSystemColumn { get; set; }

        public string FieldTypeId { get; set; }

        public string FieldTypeName { get; set; }

        public bool Localized { get; set; }

        public bool Hidden { get; set; }

        public string LanguageCode { get; set; }

        public string SpecificationFieldTypeId { get; set; }

        public string SpecificationFieldTypeName { get; set; }

        public bool SpecificationLocalized { get; set; }

        public string SpecificationLanguageCode { get; set; }

        public bool IsSpecificationField { get; set; }

        public int Index { get; set; }

        public override string ToString()
        {
            if (string.IsNullOrEmpty(ColumnName))
            {
                return base.ToString();
            }

            return ColumnName;
        }
    }
}