namespace inRiver.Server.Completeness.Criteria
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;

    public class RelationsGroupCompleteCriteria
    {
        private readonly RequestContext context;

        public RelationsGroupCompleteCriteria(RequestContext context)
        {
            this.context = context;
        }

        private const string LinkTypeId = "LinkTypeId";
        private const string CompletenessGroupId = "CompletenessGroupId";

        public string Name => "Relations Group Complete";

        public List<string> SettingsKeys => new List<string> { LinkTypeId, CompletenessGroupId };

        public int GetCriteriaCompletenessPercentage(int entityId, List<CompletenessRuleSetting> settings)
        {
            CompletenessRuleSetting linkTypeSetting = settings.FirstOrDefault(s => s.Key == LinkTypeId);
            CompletenessRuleSetting completenessGroupSetting = settings.FirstOrDefault(s => s.Key == CompletenessGroupId);

            if (linkTypeSetting == null)
            {
                return 0;
            }

            if (completenessGroupSetting == null)
            {
                return 0;
            }

            string linkTypeId = linkTypeSetting.Value;
            int completenessGroupId;

            if (string.IsNullOrEmpty(linkTypeId) || !int.TryParse(completenessGroupSetting.Value, out completenessGroupId))
            {
                return 0;
            }

            List<DtoLink> links = this.context.DataPersistance.GetOutboundLinksForEntityAndLinkType(entityId, linkTypeId);

            if (links.Count == 0)
            {
                return 100;
            }

            int completeLinks = 0;

            foreach (DtoLink link in links)
            {
                if (link.Target.Completeness.HasValue)
                {
                    if (this.context.DataPersistance.GroupForEntityCompleted(link.Target.Id, completenessGroupId))
                    {
                        completeLinks++;
                    }
                }
            }

            decimal result = (completeLinks * 100m) / links.Count;

            return (int)Math.Round(result);
        }

        public async Task<int> GetCriteriaCompletenessPercentageAsync(int entityId, IEnumerable<CompletenessRuleSetting> settings)
        {
            var linkTypeSetting = settings.FirstOrDefault(s => s.Key == LinkTypeId);
            var completenessGroupSetting = settings.FirstOrDefault(s => s.Key == CompletenessGroupId);

            if (linkTypeSetting == null)
            {
                return 0;
            }

            if (completenessGroupSetting == null)
            {
                return 0;
            }

            var linkTypeId = linkTypeSetting.Value;

            if (string.IsNullOrEmpty(linkTypeId) || !int.TryParse(completenessGroupSetting.Value, out var completenessGroupId))
            {
                return 0;
            }

            var links = await this.context.DataPersistance.GetOutboundLinksForEntityAndLinkTypeAsync(entityId, linkTypeId);

            if (!links.Any())
            {
                return 100;
            }

            var completeLinks = 0;

            foreach (var link in links)
            {
                if (link.Target.Completeness.HasValue)
                {
                    if (await this.context.DataPersistance.GroupForEntityCompletedAsync(link.Target.Id, completenessGroupId))
                    {
                        completeLinks++;
                    }
                }
            }

            var result = (completeLinks * 100m) / links.Count();

            return (int)Math.Round(result);
        }
    }
}
