namespace inRiver.Remoting.Util
{
    using System;
    using System.Globalization;
    using System.Text.RegularExpressions;
    using inRiver.Remoting.Query;

    public static class TimeIntervalQueryUtil
    {
        public const string DateTimeFormatString = "yyyy-MM-dd HH:mm:ss";

        public static void GetCriteriaValueFromDatetimeInterval(Criteria criteria, bool useUTC)
        {
            if (criteria == null || criteria.Value == null || IsDatetimeAlready(criteria.Value.ToString()))
            {
                return;
            }

            var dateTimeCriteria = GetDatetimeFromIntervalValue(criteria.Value.ToString(), useUTC ? TimeZoneInfo.Utc : TimeZoneInfo.Local);

            criteria.Value = dateTimeCriteria.ToString(DateTimeFormatString, CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Transforms the datetime interval query value syntax to a formatted datetime string.
        /// </summary>
        /// <param name="intervalQueryValue"></param>
        /// <param name="useUTC">Its advisable to set this to true when the value originates from a systemQuery.</param>
        /// <returns></returns>
        public static string GetDatetimeStringFromIntervalValue(object intervalQueryValue, bool useUTC)
        {
            if (intervalQueryValue == null || string.IsNullOrWhiteSpace(intervalQueryValue.ToString()))
            {
                return null;
            }

            var datetimeCriteria = GetDatetimeFromIntervalValue(intervalQueryValue.ToString(), useUTC ? TimeZoneInfo.Utc : TimeZoneInfo.Local);
            return datetimeCriteria.ToString(DateTimeFormatString, CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// This one will use UTC by default. If the time direction is not specified (+/-), it will default to backwards (-).
        /// </summary>
        /// <param name="systemQuery">A reference to the system query object. Created and LastModified interval values will be converted to DateTime.</param>
        public static void GetSystemQueryValueFromDatetimeInterval(SystemQuery systemQuery)
        {
            if (!string.IsNullOrWhiteSpace(systemQuery.IntervalValueCreated))
            {
                systemQuery.Created = GetDatetimeFromIntervalValue(AdjustToTimeDirectionBackwards(systemQuery.IntervalValueCreated), TimeZoneInfo.Utc);
            }

            if (!string.IsNullOrWhiteSpace(systemQuery.IntervalValueLastModified))
            {
                systemQuery.LastModified = GetDatetimeFromIntervalValue(AdjustToTimeDirectionBackwards(systemQuery.IntervalValueLastModified), TimeZoneInfo.Utc);
            }
        }

        public static DateTime? GetSystemQueryValueFromDatetimeInterval(string intervalQueryValue)
        {
            return string.IsNullOrWhiteSpace(intervalQueryValue)
                ? null
                : (DateTime?)GetDatetimeFromIntervalValue(AdjustToTimeDirectionBackwards(intervalQueryValue), timeZoneStartPoint: TimeZoneInfo.Utc);
        }

        public static DateTime GetDatetimeFromIntervalValue(string? criteriaValue, TimeZoneInfo timeZoneStartPoint)
        {
            var timeDirection = 1;

            ValidateTimeIntervalCriteriaValue(criteriaValue);

            if (criteriaValue.StartsWith('-') || criteriaValue.StartsWith('+'))
            {
                timeDirection = int.Parse(criteriaValue[..1] + "1", CultureInfo.InvariantCulture);
                criteriaValue = criteriaValue[1..];
            }

            var durationAndTimeAspect = criteriaValue.Split('-'); // The dash is just a limiter in the rest of the string.

            var duration = Convert.ToInt32(durationAndTimeAspect[0], CultureInfo.InvariantCulture);
            var timeAspect = char.Parse(durationAndTimeAspect[1]);

            var dateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, timeZoneStartPoint);

            dateTime = timeAspect switch
            {
                'm' => dateTime.AddMinutes(duration * timeDirection),
                'h' => dateTime.AddHours(duration * timeDirection),
                'd' => dateTime.AddDays(duration * timeDirection),
                'w' => dateTime.AddDays(duration * 7 * timeDirection),
                'M' => dateTime.AddMonths(duration * timeDirection),
                'y' => dateTime.AddYears(duration * timeDirection),
                _ => throw new FormatException("Invalid format of the specified time range."),
            };
            return dateTime;
        }

        private static void ValidateTimeIntervalCriteriaValue(string? timeIntervalCriteriaValue)
        {
            var isValid = !string.IsNullOrWhiteSpace(timeIntervalCriteriaValue) && timeIntervalCriteriaValue.Length >= 3;

            if (!isValid)
            {
                throw new ArgumentException("Time interval value is not valid.", nameof(timeIntervalCriteriaValue));
            }

            var myRegex = new Regex(@"^[-,\+]?(\d{1,5})-([m,w,h,d,M,y]{1,1})$");

            var match = myRegex.Match(timeIntervalCriteriaValue);
            isValid = match != null && match.Groups != null && match.Groups.Count == 3;

            if (!isValid)
            {
                throw new FormatException("Invalid datetime interval query syntax");
            }

            var duration = int.Parse(match.Groups[1].Value, CultureInfo.InvariantCulture);
            var timeUnit = char.Parse(match.Groups[2].Value);

            var isTooExtreme = (timeUnit == 'y' && duration > 1000) || (timeUnit == 'M' && duration > 12000);

            if (isTooExtreme)
            {
                throw new ArgumentOutOfRangeException(nameof(timeIntervalCriteriaValue), "The time range of the query is too large.");
            }
        }

        private static string? AdjustToTimeDirectionBackwards(string? intervalValue) =>
            intervalValue != null && !intervalValue.StartsWith('-') && !intervalValue.StartsWith('+') ? "-" + intervalValue : intervalValue;

        private static bool IsDatetimeAlready(string stringToParse)
        {
            try
            {
                DateTime.ParseExact(stringToParse, DateTimeFormatString, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
