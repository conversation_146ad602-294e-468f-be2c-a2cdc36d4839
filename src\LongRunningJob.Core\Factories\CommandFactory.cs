namespace LongRunningJob.Core.Factories
{
    using inRiver.Core.Models.inRiver;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Commands;
    using LongRunningJob.Core.Constants;

    public class CommandFactory : ICommandFactory
    {
        public ICommand CreateCommand(string jobType, LongRunningJob job)
        {
            return jobType switch
            {
                LongRunningJobsJobType.ExcelExport => new ExcelExportCommand(job),
                _ => throw new System.ArgumentException($"No Command for jobtype {jobType} exists")
            };
        }
    }
}
