namespace inRiver.Server.Extension
{
    using Remoting.Objects;

    public static class DataTypeExtensions
    {
        public static LocaleString RemoveEmptyCultures(this LocaleString ls)
        {
            var languages = ls.Languages;
            foreach (var ci in languages)
            {
                if (string.IsNullOrWhiteSpace(ls[ci]))
                {
                    ls.RemoveCulture(ci);
                }
            }

            return ls;
        }
    }
}
