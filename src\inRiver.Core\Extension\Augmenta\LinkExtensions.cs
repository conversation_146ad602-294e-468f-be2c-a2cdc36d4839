﻿namespace inRiver.Core.Extension.Augmenta
{
    using inRiver.Core.Models.Augmenta;
    using inRiver.Remoting.Objects;

    public static class LinkExtensions
    {
        public static LinkInputModel ToLinkInputModel(this Link link)
            => link == null
                ? null
                : new LinkInputModel
                {
                    Id = link.Id,
                    Index = link.Index,
                    Inactive = link.Inactive,
                    LinkTypeId = link.LinkType.Id,
                    SourceEntityId = link.Source.Id,
                    TargetEntityId = link.Target.Id,
                    LinkEntityId = link.LinkEntity?.Id,
                    LastModified = link.LastModified,
                };
    }
}
