namespace inRiver.Server.Syndication.Constants
{
    public class ValidationErrorTypes
    {
        public static readonly ValidationErrorTypes MandatoryField = new ValidationErrorTypes("Mandatory");

        public static readonly ValidationErrorTypes RecommendedField = new ValidationErrorTypes("Recommended");

        public static readonly ValidationErrorTypes NumberField = new ValidationErrorTypes("Number");

        public static readonly ValidationErrorTypes EnumField = new ValidationErrorTypes("Enum");

        public string Value { get; }

        public ValidationErrorTypes(string value)
        {
            this.Value = value;
        }
    }
}
