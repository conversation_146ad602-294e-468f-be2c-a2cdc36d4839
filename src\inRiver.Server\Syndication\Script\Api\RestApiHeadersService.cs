namespace inRiver.Server.Syndication.Script.Api
{
    using System.Collections.Generic;
    using System.Net.Http;
    using inRiver.Server.Syndication.Exceptions;

    public class RestApiHeadersService : IRestApiHeadersService
    {
        public void SetHeaders(HttpRequestMessage requestMessage, IDictionary<string, string> headers)
        {
            if (requestMessage == null)
            {
                throw new SyndicateApiException("Unable to set request message headers. Request message is null.");
            }

            if (headers == null)
            {
                SetDefaultHeaders(requestMessage);
            }
            else
            {
                foreach (var (headerName, headerValue) in headers)
                {
                    if (requestMessage.Headers.Contains(headerName))
                    {
                        continue;
                    }

                    requestMessage.Headers.Add(headerName, headerValue);
                }
            }
        }

        private static void SetDefaultHeaders(HttpRequestMessage requestMessage) => requestMessage.Headers.Add("Accept", "application/json");
    }
}
