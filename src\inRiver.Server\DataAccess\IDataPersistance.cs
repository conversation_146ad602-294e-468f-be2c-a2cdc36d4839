namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Query;
    using inRiver.Server.Completeness;
    using inRiver.Server.Syndication;
    using Inriver.Expressions.Dto;
    using Syndication.Models;
    using ChannelStructure = Models.ChannelStructure;
    using SyndicationRelatedEntityFieldValue = inRiver.iPMC.Persistance.SyndicationRelatedEntityFieldValue;

    public interface IDataPersistance
    {
        #region Content Segmentation

        bool SetSegmentForEntities(List<int> entityIds, int segmentId);

        Task<IEnumerable<Segment>> GetAllSegmentsAsync();

        #endregion

        #region Entity Types

        List<EntityType> GetAllEntityTypes();

        EntityType GetEntityType(string id);

        #endregion

        #region Field Types

        List<FieldType> GetFieldTypesForEntityType(string entityTypeId, List<CultureInfo> languages = null);

        Dictionary<string, string> GetFieldTypeSettings(string fieldTypeId);

        FieldType GetFieldType(string id);

        List<FieldType> GetAllFieldTypes();

        #endregion

        #region Link Types

        LinkType GetLinkType(string id);

        LinkType GetLinkType(string id, List<CultureInfo> serverLanguages);

        List<LinkType> GetAllLinkTypes();

        List<LinkType> GetLinkTypesForEntityType(string entityTypeId, List<CultureInfo> languages = null);

        IEnumerable<LinkType> GetLinkTypes(string sourceEntityTypeId, string targetEntityTypeId);

        #endregion

        #region Languages

        List<CultureInfo> GetAllLanguages();

        bool AddLanguage(CultureInfo cultureInfo);

        bool DeleteLanguage(CultureInfo cultureInfo);

        bool DeleteAllLanguages();


        #endregion

        #region Categories

        Category GetCategory(string id);

        #endregion

        #region Field View

        List<FieldView> GetFieldViewsForEntityType(string entityTypeId, List<CultureInfo> languages = null);

        List<string> GetFieldTypesForFieldView(string fieldViewId);

        #endregion

        #region CVL

        CVL GetCVL(string id);

        IList<CVL> GetAllCVLs();

        #endregion

        List<Core.Models.inRiver.CVLKey> GetCvlKeysByCvlId(string cvlId);

        #region CVL Value

        CVLValue GetCVLValueByKey(string key, string cvlId);

        List<CVLValue> GetCVLValueByKeys(string[] keys, string cvlId, string selectedLanguage, List<CultureInfo> serverLanguages);

        List<CVLValue> GetExistingCVLKeysByKeyList(List<string> keys, CVL cvl);

        string GetCvlIdForFieldType(string fieldTypeId);

        List<CVLValue> GetCVLValuesForCVL(string cvlId);

        #endregion

        #region Entity

        DtoEntity AddEntity(Entity entity);

        DtoEntity AddEntityWithFields(Entity entity);

        List<DtoEntity> AddEntities(List<Entity> entities);

        DtoEntity GetEntity(int id);

        List<DtoEntity> GetEntities(List<int> ids);

        Task<IEnumerable<DtoEntity>> GetEntitiesAsync(IEnumerable<int> ids);

        Entity GetFullEntity(int id, EntityType entityType, bool includeDisplayInformation = true, bool includePendingDelete = false,
            bool ignoreSegmentCheck = false);

        IDictionary<int, IList<SyndicationRelatedEntityFieldValue>> GetRelatedEntityFields(
            string entityName,
            string field,
            string[] linkTypeIds,
            int[] entityLinkPosition,
            string language);

        DtoEntity GetEntityWithData(int id);

        DataTable GetEntitiesAsTable(List<int> entityIds, string entityType, List<FieldType> fields, iPMC.Persistance.ContentSegmentationEnum segmentationOption, CancellationToken cancellationToken);

        List<DtoEntity> GetEntitiesWithData(List<int> list, CancellationToken cancellationToken);
        Task<List<DtoEntity>> GetEntitiesWithDataAsync(List<int> list, CancellationToken cancellationToken);

        void SaveEntityFieldRevisionHistory(int entityId, List<Field> changedFields, bool entityIsNew,
            string userName = "system");

        void SaveUpdatedEntityFieldRevisionHistory(int entityId, List<Field> changedFields, List<Field> persistedFields, string userName = "system");

        void SaveEntityFieldRevisionHistorySynchronous(int entityId, List<Field> changedFields, bool entityIsNew);

        void UpdateEntityChangeSet(int entityId, List<Field> updatedFields);

        int? GetEntityIdByUniqueValue(string fieldTypeId, string value);

        DtoEntity SetEntityFieldSet(int entityId, string fieldSetId);

        List<int> GetAllEntityIdsForEntityType(string entityTypeId);

        List<int> GetAllEntityIdsForEntityType(string entityTypeId, CancellationToken cancellationToken);

        bool SetEntityCompleteness(int entityId, int? completeness);

        Task<bool> SetEntityCompletenessAsync(int entityId, int? completeness);

        void SetEntityCompletenessState(int entityId, int completenessDefinitionId, int groupId, int? ruleId, bool complete);

        Task SetEntityCompletenessStateAsync(int entityId, int completenessDefinitionId, int groupId, int? ruleId, bool complete);

        void ReCalculateDisplayValuesForEntity(int entityId, List<Field> updatedFields);

        bool DeleteEntity(int id);

        public List<DtoField> GetFields(int entityId, List<string> fieldTypeIds);

        #endregion

        #region Syndication

        List<MapEnumeration> GetEnumerations(List<int> formatFieldIds, int mappingId);

        MapFormat GetMapFormat(int id);

        SyndicationMapping GetMappingDetails(int mappingId);

        SyndicationMappingFunction GetMappingFunction(int id);

        #endregion

        #region Main Picture

        void ReCalculateEntityMainPicture(int entityId, string entityTypeId);

        #endregion

        #region Field

        IList<string> GetSearchHintFieldTypesByEntityId(string entityId);

        bool FieldValueAlreadyExistsForFieldType(string fieldTypeId, object value);

        void UpdateFields(List<Field> fields);

        void AddFields(List<Field> fields);

        DtoField GetField(int entityId, string fieldTypeId);

        Field GetFullField(int entityId, string entityTypeId, string fieldTypeId, DateTime entityCreated);

        object GetFieldValue(int entityId, string fieldTypeId);

        List<FieldRevision> GetFieldRevisions(int entityId, string fieldTypeId, int maxNumberOfRevisions);

        Task<object> GetFieldValueAsync(int entityId, string fieldTypeId);

        List<DtoField> GetFieldsForEntity(DtoEntity entity);

        List<Field> GetFullFieldsForEntity(Entity entity);

        #endregion

        #region Search

        List<int> LinkSearch(LinkQuery linkQuery);

        List<int> SystemSearch(SystemQuery systemQuery);

        List<int> SearchSpecification(SpecificationQuery query);

        List<int> Search(Criteria criteria, Join? joinOperator = null);

        List<int> SearchForEntitiesBasedOnCriteria(Query query);

        List<int> SearchEntityBySystemQueryAndDataQuery(SystemQuery querySystemQuery, Query queryDataQuery);

        #endregion

        #region User

        User GetUserByUsername(string username);

        Dictionary<string, string> GetAllUserSettings(int id);

        List<Role> GetRolesForUser(string username);

        List<Permission> GetPermissionsForUser(string username);

        User GetShallowUser(string username);

        List<User> GetAllShallowUsers();

        #endregion

        #region Role

        Role GetRole(int id);

        List<Role> GetAllRoles();

        #endregion

        #region Link

        DtoLink AddLink(Link link);

        bool LinkAlreadyExists(int sourceEntityId, int targetEntityId, int? linkEntityId, string linkTypeId);

        List<DtoLink> GetLinksForEntitiesByLinkType(IList<int> entityIds, string linkTypeId);

        List<DtoLink> GetLinksForEntity(int entityId, CancellationToken cancellationToken);

        Task<List<DtoLink>> GetLinksForEntityAsync(int entityId, CancellationToken cancellationToken);

        List<Link> GetFullLinksForEntity(int entityId, bool includePendingDelete = false, bool ignoreSegmentCheck = false);

        Link GetFullLink(int id);

        List<DtoLink> GetResourceLinksForEntity(int entityId);

        List<DtoLink> GetResourceLinksForEntities(IList<int> entityIds);

        DtoLink GetLink(int id);

        List<DtoLink> GetOutboundLinksForEntity(int entityId, CancellationToken cancellationToken);

        List<DtoLink> GetInboundLinksForEntity(int entityId);

        int GetLinkCountForOutboundLinkType(string linkTypeId, int sourceEntityId);

        Task<int> GetLinkCountForOutboundLinkTypeAsync(string linkTypeId, int sourceEntityId);

        List<DtoLink> GetOutboundLinksForEntityAndLinkType(int entityId, string linkTypeId);

        Task<IEnumerable<DtoLink>> GetOutboundLinksForEntityAndLinkTypeAsync(int entityId, string linkTypeId);

        List<DtoLink> GetInboundLinksForEntityAndLinkType(int entityId, string linkTypeId);

        DtoLink GetValidationLink(Link link);

        (DtoLink, bool) AddLinkIfNotExists(Link link);

        DtoLink AddLinkAt(Link link, int index);

        List<DtoLink> AddLinksToNewTask(List<Link> links);

        bool DeleteLink(int linkId);

        bool DeleteLinksAndUpdateLinksSortOrder(int entityId, string[] linkTypeIdsToIgnore);

        DtoLink UpdateLinkSortOrder(int linkId, int index);

        void UpdateLinkSortOrderOnSourceEntity(int sourceEntityId, int index, string linkTypeId);

        bool Inactivate(int id);

        bool Activate(int id);

        List<int> GetAllLinkIdsForEntityAndLinkType(int sourceEntityId, string linkTypeId, LinkDirection linkDirection);

        #endregion

        #region Link Rules

        #endregion

        #region Server

        string GetServerSetting(string key);

        Dictionary<string, string> GetServerSettings(List<string> keyList);

        #endregion

        #region Integration

        #endregion

        #region UI

        #endregion

        #region Permission

        #endregion

        #region FieldSet

        FieldSet GetFieldSet(string id);

        List<FieldSet> GetAllFieldSets();

        List<string> GetFieldTypesForFieldSet(string fieldSetId);

        List<FieldSet> GetFieldSetsForEntityType(string entityTypeId, List<CultureInfo> languages = null);

        #endregion

        #region Comment

        #endregion

        #region Specification

        SpecificationField GetSpecificationField(int specificationEntityId, string specificationFieldTypeId);

        void LinkSpecification(DtoLink link);

        void UnlinkSpecification(DtoLink link);

        SpecificationFieldType GetSpecificationFieldType(string id);

        List<SpecificationFieldType> GetSpecificationFieldTypes(List<string> ids);

        List<string> GetSpecFieldTypeIdsFromImportedHistory(List<int> supplierIds);

        List<string> GetSpecFieldTypeIdsFromImportedHistoryByEntityId(int entityId);

        List<SpecificationField> GetSpecificationFieldsForEntity(int entityId);

        #region Specification Categories

        #endregion

        #endregion

        #region Restricted Field Permission

        #endregion

        #region Completeness Definition

        CompletenessDefinition GetCompletenessDefinition(int id);

        int? GetCompletenessDefinitionIdForEntityType(string entityTypeId);

        #endregion

        #region Completeness Group

        List<CompletenessGroup> GetAllCompletenessGroupForDefinition(int defintionId);

        List<ShallowCompletenessGroup> GetShallowCompletenessGroupsForDefinition(int defintionId);

        bool GroupForEntityCompleted(int entityId, int completenessGroupId);

        Task<bool> GroupForEntityCompletedAsync(int entityId, int completenessGroupId);

        #endregion

        #region Completeness Rule

        List<CompletenessBusinessRule> GetAllCompletenessBusinessRules();

        List<CompletenessBusinessRule> GetCompletenessBusinessRulesForGroup(int groupId);

        #endregion

        #region Completeness Rule Settings

        List<CompletenessRuleSetting> GetAllCompletenessRuleSettingsForBusinessRule(int ruleId);

        #endregion

        #region Completeness Query

        List<int> SearchCompleteness(CompletenessQuery query);

        #endregion

        #region Completeness Action

        List<CompletenessAction> GetCompletenessActionsByDefinitionAndRule(int definitionId, int ruleId, string actiontrigger);

        List<CompletenessAction> GetCompletenessActionsByDefinitionAndGroup(int definitionId, int groupId, string actiontrigger);

        #endregion

        #region Completeness State

        CompletenessDetail GetEntityCompletenessDetailFromRuleId(int ruleId);

        List<CompletenessDetail> GetShallowEntityCompletenessDetails(int entityId);

        List<DtoEntity> GetRelatedCompletenessEntities(int id);

        void DeleteCompletenessStateForDefinition(int definitionId, CancellationToken cancellationToken);

        #endregion

        #region Personal Work Area Folder

        #endregion

        #region Shared Work Area Folder

        WorkAreaFolder GetSharedWorkAreaFolder(Guid id, bool includeEntities);

        bool DeleteSharedWorkAreaFolder(Guid id);

        #endregion

        #region Work Area Folder Entities

        List<int> GetEntitiesForFolder(Guid id);

        #endregion

        #region ConnectorEvents

        #endregion

        #region Channel

        void ReloadChannel(int id);

        void ReloadChannel(int id, CancellationToken cancellationToken);

        void CreateChannelStructure(int channelId, string entityTypeId, CancellationToken cancellationToken);

        List<int> GetDigitalChannelsForEntity(int entityId);

        List<int> GetAllEntityIdsForChannel(int channelId);

        Task<IList<ChannelStructure>> GetChannelStructureByEntityTypeIdsForChannelAsync(int channelId, IList<string> entityTypeIds);

        List<int> GetAllEntityIdsNotInChannel(int channelId);

        bool IsExcludedByChannelFilter(int channelId, int entityId, string linkTypeId);

        #endregion

        #region Notify Me When

        #endregion

        #region File Import Mapping

        #endregion

        #region Image Configuration

        #endregion

        #region Image Cache

        #endregion

        #region Planner View

        #endregion

        #region Connector State

        #endregion

        #region Job

        #endregion

        #region Reload Model

        #endregion

        #region ExcelExportHistory

        List<ExcelExportHistoryResultModel> GetExcelExportHistoryForSupplier(int supplierId);

        List<ExcelExportHistoryResultModel> GetExcelExportHistoryForEntity(int entityId);

        #endregion

        void SynchronizeChannel(int channelId, CancellationToken cancellationToken);

        #region Html Templates

        #endregion

        bool HasLinkRuleDefinition(int entityId, string linkTypeId);

        bool EntityExists(int entityId);

        string GetExcelModel(string username, string batchId, bool configData);

        void InsertRowIntoStagingTable(DataRow row);

        DataTable GetTableWithRowToProcessFromStagingTable(string batchId);

        void SetStagingRow(DataRow row);

        void SetImportStatus(string inRiverImportId, string status, string message);

        void SetPostProcessStatus(string inRiverImportId, string status, string message);

        List<int> GetSysIdsForImportJob(string jobId);

        Dictionary<int, List<int>> GetInboundParentsForEntitiesAndLinkType(List<int> entityIds, string linkTypeId, CancellationToken cancellationToken);

        Dictionary<int, List<int>> GetOutboundChildrenForEntitiesAndLinkType(List<int> entityIds, string linkTypeId, CancellationToken cancellationToken);

        #region Environment

        string GetStorageAccountConnectionString(int environmentId);

        #endregion

        IList<ResourceFile> GetResourceFiles(IList<int> resourceFileIds);

        #region ContentStore

        void ExcludeFieldTypes(int contentStoreId, string fieldTypes);
        void ExcludeEntityTypes(int contentStoreId, string entityTypes);
        void ExcludeLinkTypes(int contentStoreId, string linkTypes);
        Dictionary<int, string> GetAllExcludeFieldTypes();
        Dictionary<int, string> GetAllExcludeEntityTypes();
        Dictionary<int, string> GetAllExcludeLinkTypes();

        #endregion

        bool EntityHasExpression(int entityId, string target, string targetType);

        List<int> GetEntityIdsForEntityTypeWithExpressions(string entityTypeId, string target, string targetType);

        Dictionary<string, Dictionary<string, DtoExpression>> GetExpressionsForEntity(int entityId);

        public List<DtoExpression> GetExpressionsForEntityTypesByTarget(List<string> entityTypeIds, string target);

        int GetFirstLinkedEntity(int entityId, string direction, string linkTypeIds, bool inactive);

        void UpsertExpressions(List<DtoExpression> expressions);

        void DeleteExpressions(List<int> ids);

        Dictionary<string, Dictionary<string, DtoExpression>> GetExpressionsForEntityType(string entityTypeId);

        DtoExpression GetExpressionForEntityByTarget(int entityId, string target);

        void DeleteExpressionsIfExists(List<int> entityIds, string target);
    }
}
