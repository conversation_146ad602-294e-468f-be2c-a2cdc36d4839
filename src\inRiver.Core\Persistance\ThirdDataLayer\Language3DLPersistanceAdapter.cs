﻿namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Log;
    using System.Globalization;

    /// <summary>
    /// Documentation.
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        public override bool AddLanguage(string name)
        {
            var result = this._origInRiverPersistance.AddLanguage(name);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            return result;
        }

        public override bool DeleteAllLanguages()
        {
            var result = this._origInRiverPersistance.DeleteAllLanguages();
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            return result;
        }

        public override bool DeleteLanguage(string name)
        {
            var result = this._origInRiverPersistance.DeleteLanguage(name);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            return result;
        }
    }
}
