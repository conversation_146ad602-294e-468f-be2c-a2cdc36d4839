namespace LongRunningJobWorkerService
{
    using System.Threading.Tasks;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Models.inRiver;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Constants;
    using Polly;
    using Polly.Registry;

    public class LongRunningJobRepositoryResilienceDecorator : ILongRunningJobRepository
    {
        private readonly ILongRunningJobRepository longRunningJobRepository;
        private readonly IReadOnlyPolicyRegistry<string> policyRegistry;

        public LongRunningJobRepositoryResilienceDecorator(ILongRunningJobRepository longRunningJobRepository, IReadOnlyPolicyRegistry<string> policyRegistry)
        {
            this.longRunningJobRepository = longRunningJobRepository;
            this.policyRegistry = policyRegistry;
        }

        public async Task<LongRunningJob> GetAsync(int jobId)
        {
            var policy = this.GetNamedPolicyOrDefault(ResiliencePolicies.WaitAndRetryPolicy, Policy.NoOpAsync());

            return await policy.ExecuteAsync(
                (context) => this.longRunningJobRepository.GetAsync(jobId), new Context(nameof(this.GetAsync)));
        }

        public async Task UpdateStateAsync(int jobId, string state)
        {
            var policy = this.GetResiliencePolicyForJobStateUpdate(state);

            await policy.ExecuteAsync(
                (context) => this.longRunningJobRepository.UpdateStateAsync(jobId, state), new Context(nameof(UpdateStateAsync)));
        }

        public async Task UpdateStateAsync(int jobId, string state, string metadata)
        {
            var policy = this.GetResiliencePolicyForJobStateUpdate(state);

            await policy.ExecuteAsync(
                (context) => this.longRunningJobRepository.UpdateStateAsync(jobId, state, metadata), new Context(nameof(UpdateStateAsync)));
        }

        public async Task<int> InsertLongRunningJobAsync(LongRunningJob job)
        {
            var policy = this.GetNamedPolicyOrDefault(ResiliencePolicies.WaitAndRetryPolicy, Policy.NoOpAsync());

            return await policy.ExecuteAsync(
                (context) => this.longRunningJobRepository.InsertLongRunningJobAsync(job), new Context(nameof(this.InsertLongRunningJobAsync)));
        }

        public async Task<bool> StartedJobExistsAsync(string jobType, string identifier)
        {
            var policy = this.GetNamedPolicyOrDefault(ResiliencePolicies.WaitAndRetryPolicy, Policy.NoOpAsync());

            return await policy.ExecuteAsync(
                (context) => this.longRunningJobRepository.StartedJobExistsAsync(jobType, identifier), new Context(nameof(this.StartedJobExistsAsync)));
        }

        private IAsyncPolicy GetResiliencePolicyForJobStateUpdate(string jobState)
        {
            switch (jobState)
            {
                case LongRunningJobsStatus.Error:
                    return this.GetNamedPolicyOrDefault(ResiliencePolicies.SlowRetryWithExponentialBackoffPolicy, Policy.NoOpAsync());
                case LongRunningJobsStatus.Finished:
                case LongRunningJobsStatus.FinishedWithErrors:
                case LongRunningJobsStatus.Running:
                    return this.GetNamedPolicyOrDefault(ResiliencePolicies.WaitAndRetryPolicy, Policy.NoOpAsync());
                default:
                    return Policy.NoOpAsync();
            }
        }

        private IAsyncPolicy GetNamedPolicyOrDefault(string policyName, IAsyncPolicy defaultPolicy) =>
            this.policyRegistry.TryGet<IAsyncPolicy>(policyName, out var policy)
                ? policy
                : defaultPolicy;
    }
}
