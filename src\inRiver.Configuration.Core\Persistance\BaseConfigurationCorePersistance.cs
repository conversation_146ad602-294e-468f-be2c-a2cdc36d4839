namespace inRiver.Configuration.Core.Persistance
{
    using inRiver.Core.Models;
    using inRiver.Core.Persistance;
    using inRiver.Log;

    public class BaseConfigurationCorePersistance
        : BasePersistance
    {
        public BaseConfigurationCorePersistance(
            string configurationConnectionString,
            string readonlyConfigConnectionString,
            ICommonLogging systemLogInstance,
            ApiCaller apiCaller)
            : base(systemLogInstance, apiCaller)
        {
            this.ConnectionString = configurationConnectionString;
            this.ReadOnlyConnectionString = readonlyConfigConnectionString;
        }

        public string ConnectionString { get; set; }

        public string ReadOnlyConnectionString { get; set; }
    }
}
