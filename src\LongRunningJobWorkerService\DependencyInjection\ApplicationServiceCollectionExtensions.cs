namespace LongRunningJobWorkerService.DependencyInjection
{
    using System;
    using global::LongRunningJobWorkerService.Constants;
    using LongRunningJob.Core;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.CommandHandlers;
    using LongRunningJob.Core.Commands;
    using LongRunningJob.Core.Config;
    using LongRunningJob.Core.Constants;
    using LongRunningJob.Core.Factories;
    using LongRunningJob.Core.Models;
    using LongRunningJob.Core.Repositories;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.ApplicationInsights.WorkerService;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Polly;
    using Polly.Registry;
    using Serilog;
    using Telemetry.Initializers;
    using EnvironmentSettingsRepository = LongRunningJob.Core.Repositories.EnvironmentSettingsRepository;

    public static class ApplicationServiceCollectionExtensions
    {
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddSingleton<ICommandHandler<ExcelExportCommand, Result>, ExcelExportHandler>();
            services.AddSingleton<ICommandFactory, CommandFactory>();

            services.AddSingleton<IExcelExportRepositoryFactory, ExcelExportRepositoryFactory>();
            services.AddSingleton<IRequestContextFactory, RequestContextFactory>();
            services.AddSingleton<IJobRunner, JobRunner>();

            services.AddSingleton(provider => new CommandDispatcher<Result>()
                .RegisterHandler(provider.GetService<ICommandHandler<ExcelExportCommand, Result>>() ?? throw new InvalidOperationException()));

            return services;
        }

        public static IServiceCollection AddRepositories(this IServiceCollection services)
        {
            services.AddSingleton<ICustomerEnvironmentRepository, CustomerEnvironmentRepository>();
            services.AddSingleton<IJobModelRepository, JobModelBlobRepository>();
            services.AddSingleton<ILongRunningJobRepository, LongRunningJobRepository>();
            services.AddSingleton<IEnvironmentSettingsRepository, EnvironmentSettingsRepository>();

            return services;
        }

        public static IServiceCollection AddCachingDecorators(this IServiceCollection services)
        {
            services.AddMemoryCache();
            services.Decorate<ICustomerEnvironmentRepository, CustomerEnvironmentRepositoryCachingDecorator>();

            return services;
        }

        public static IServiceCollection AddApplicationOptions(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddOptions();

            services.Configure<SendGridSettings>(configuration);
            services.Configure<InriverServiceSettings>(configuration);

            return services;
        }

        public static IServiceCollection AddResiliencePolicies(this IServiceCollection services)
        {
            var slowRetryWithExponentialBackoffPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: 5,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                    (exception, timeSpan, retryCount, context) =>
                        Log.Warning($"Retry {retryCount} of {context.OperationKey}, due to {exception.Message}."));

            var waitAndRetryPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    new[]
                    {
                        TimeSpan.FromSeconds(1),
                        TimeSpan.FromSeconds(2),
                        TimeSpan.FromSeconds(3)
                    },
                    (exception, timeSpan, retryCount, context) =>
                        Log.Warning($"Retry {retryCount} of {context.OperationKey}, due to {exception.Message}."));

            var registry = new PolicyRegistry
            {
                { ResiliencePolicies.SlowRetryWithExponentialBackoffPolicy, slowRetryWithExponentialBackoffPolicy },
                { ResiliencePolicies.WaitAndRetryPolicy, waitAndRetryPolicy }
            };

            services.AddSingleton<IReadOnlyPolicyRegistry<string>>(registry);

            return services;
        }

        public static IServiceCollection AddApplicationInsightsServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<ITelemetryInitializer>(new CloudRoleNameTelemetryInitializer(LogConstants.CloudRoleName));
            var aiOptions = new ApplicationInsightsServiceOptions
            {
                InstrumentationKey = configuration["InstrumentationKey"],
                EnableAdaptiveSampling = false
            };
            services.AddApplicationInsightsTelemetryWorkerService(aiOptions);

            return services;
        }

        public static IServiceCollection AddResilienceDecorators(this IServiceCollection services)
        {
            services.Decorate<ILongRunningJobRepository, LongRunningJobRepositoryResilienceDecorator>();

            return services;
        }
    }
}
