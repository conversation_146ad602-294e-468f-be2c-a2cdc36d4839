namespace Telemetry.Metrics
{
    using System.Collections.Generic;
    using inRiver.Remoting.Objects;
    using Microsoft.ApplicationInsights.Metrics;
    using Telemetry.Metrics.Constants;

    public static class UpsertEntityDurationMetric<TService>
    {
        private static readonly string ServiceTypeName;
        private static readonly MetricIdentifier UpsertEntityDurationMetricIdentifier
            = new(
                ServiceTypeName,
                MetricName.MethodDuration,
                Dimension.Service,
                Dimension.Environment,
                Dimension.EntityModel,
                Dimension.Method,
                Dimension.EntityType,
                Dimension.EntityFieldsNumber);

        static UpsertEntityDurationMetric()
        {
            ServiceTypeName = typeof(TService).Name;
        }

        public static void TrackValue(
            long elapsedMilliseconds,
            Entity entity,
            string customer,
            string environment,
            int entityModel,
            string methodName)
        {
            var entityFieldsNumber = entity?.Fields?.Count ?? default;

            var metricAdded = MetricUtil.TelemetryClientInstance
                .GetMetric(UpsertEntityDurationMetricIdentifier, MetricUtil.MetricConfiguration)
                .TrackValue(
                    elapsedMilliseconds,
                    ServiceTypeName,
                    MetricUtil.CustomerEnvironmentString(customer, environment),
                    entityModel.ToString(),
                    methodName,
                    entity?.EntityType?.Id ?? MetricUtil.DefaultDimensionValue,
                    entityFieldsNumber.GetNumberRange(rangeSize: 10));

            if (!metricAdded)
            {
                MetricUtil.LogMetricNotAdded(ServiceTypeName, methodName, elapsedMilliseconds);
            }
        }

        public static void TrackValue(
            long elapsedMilliseconds,
            List<Field> fields,
            string entityTypeId,
            string customer,
            string environment,
            int entityModel,
            string methodName)
        {
            var entityFieldsNumber = fields?.Count ?? default;

            var metricAdded = MetricUtil.TelemetryClientInstance
                .GetMetric(UpsertEntityDurationMetricIdentifier, MetricUtil.MetricConfiguration)
                .TrackValue(
                    elapsedMilliseconds,
                    ServiceTypeName,
                    MetricUtil.CustomerEnvironmentString(customer, environment),
                    entityModel.ToString(),
                    methodName,
                    entityTypeId ?? MetricUtil.DefaultDimensionValue,
                    entityFieldsNumber.GetNumberRange(rangeSize: 10));

            if (!metricAdded)
            {
                MetricUtil.LogMetricNotAdded(ServiceTypeName, methodName, elapsedMilliseconds);
            }
        }
    }
}
