namespace inRiver.Server.UnitTests.Extensions
{
    using inRiver.Server.Extension;
    using FluentAssertions;
    using inRiver.Server.Syndication.Enums;
    using Xunit;

    public class SyndicationIdentifierTypeExtensionsTests
    {
        [Theory]
        [InlineData(SyndicationIdentifierType.FileFormatId, "FileFormatId")]
        [InlineData(SyndicationIdentifierType.None, null)]

        private void GetSyndicationIdentifierTypeString_ShouldReturnExpectedEnumValues(SyndicationIdentifierType input, string expected)
        {
            // Arrange
            //// Act
            var result = input.GetSyndicationIdentifierTypeString();

            //// Assert
            result.Should().Be(expected);
        }
    }
}
