namespace inRiver.Server.Completeness.Criteria
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Remoting.Extension;
    using inRiver.Remoting.Objects;
    using inRiver.Server.DataAccess.ExpressionUtil;
    using inRiver.Server.Request;

    public class ExpressionCriteria
    {
        private readonly RequestContext context;

        public ExpressionCriteria(RequestContext context)
        {
            this.context = context;
        }

        private const string ExpressionKey = "Expression";

        public inRiverContext Context { get; set; }

        public string Name => "Expression";

        public List<string> SettingsKeys => new List<string> { ExpressionKey };

        public int GetCriteriaCompletenessPercentage(int entityId, List<CompletenessRuleSetting> settings)
        {
            var expressionWrapper = new ExpressionWrapper(this.context.DataPersistance, this.context);
            var setting = settings.FirstOrDefault(s => s.Key == ExpressionKey);
            if (setting == null)
            {
                return 0;
            }

            var result = expressionWrapper.EvaluateExpression(entityId, setting.Value);
            if (result.Success)
            {
                if (result.Expression.Status.HasValue && result.Expression.Status == false)
                {
                    var detail = this.context.DataPersistance.GetEntityCompletenessDetailFromRuleId(setting.BusinessRuleId);
                    var groupName = detail?.GroupName[this.context.ModelLanguage];
                    var ruleName = detail?.Rules.FirstOrDefault(x => x.RuleId == setting.BusinessRuleId)?.RuleName[this.context.ModelLanguage];

                    this.context.Log(
                        Remoting.Log.LogLevel.Error,
                        $"Expression from completeness rule with name '{ruleName}' in group '{groupName}' failed with evaluation error: '{result.Expression.StatusMessage}'");
                    return 0;
                }

                if (result.ExpressionResult is string s)
                {
                    if (s.Equals("true", StringComparison.OrdinalIgnoreCase))
                    {
                        return 100;
                    }
                    else if (s.Equals("false", StringComparison.OrdinalIgnoreCase))
                    {
                        return 0;
                    }
                }

                try
                {
                    var value = Convert.ToInt32(result.ExpressionResult);
                    return value;
                }
                catch (Exception)
                {
                    var detail = this.context.DataPersistance.GetEntityCompletenessDetailFromRuleId(setting.BusinessRuleId);
                    var groupName = detail?.GroupName[this.context.ModelLanguage];
                    var ruleName = detail?.Rules.FirstOrDefault(x => x.RuleId == setting.BusinessRuleId)?.RuleName[this.context.ModelLanguage];
                    this.context.Log(
                        Remoting.Log.LogLevel.Error,
                        $"Failed to parse expression result from completeness rule with name '{ruleName}' in group '{groupName}' into an integer");
                }
            }

            return 0;
        }
    }
}
