namespace inRiver.Server.UnitTests.Syndicate
{
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Enums;
    using inRiver.Server.Syndication.Service;
    using Xunit;

    public class MappingSourceResolverServiceTests
    {
        [Fact]
        public void GetMappingSource_ApplyDsaMappingTrue_ShouldReturnOutputAdapterDsa()
        {
            // Arrange
            var model = new SyndicationModel
            {
                MappingSource = SyndicationMappingSource.OutputAdapter
            };
            var applyDsaMapping = true;

            // Act
            var result = MappingSourceResolverService.GetMappingSource(model, applyDsaMapping);

            // Assert
            Assert.Equal(SyndicationMappingSource.OutputAdapterDsa, result);
        }

        [Fact]
        public void GetMappingSource_ApplyDsaMappingFalse_ShouldReturnModelMappingSource()
        {
            // Arrange
            var model = new SyndicationModel
            {
                MappingSource = SyndicationMappingSource.Extension
            };
            var applyDsaMapping = false;

            // Act
            var result = MappingSourceResolverService.GetMappingSource(model, applyDsaMapping);

            // Assert
            Assert.Equal(SyndicationMappingSource.Extension, result);
        }
    }
}
