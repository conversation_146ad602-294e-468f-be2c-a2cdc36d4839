namespace LongRunningJob.Core.Repositories
{
    using System.Data.SqlClient;
    using System.Threading.Tasks;
    using Dapper;
    using inRiver.Server.Request;
    using Inriver.StackEssentials.Abstractions;
    using LongRunningJob.Core.Abstractions;

    public class CustomerEnvironmentRepository : ICustomerEnvironmentRepository
    {
        private readonly IStackConfig stackConfig;

        public CustomerEnvironmentRepository(IStackConfig stackConfig)
        {
            this.stackConfig = stackConfig;
        }

        public async Task<EnvironmentContextData> GetAsync(string customerSafename, string environmentSafename)
        {
            const string sql = @"SELECT
                    e.DbConnectionString as ConnectionString, 
                    e.DbConnectionStringLog as LogConnectionString, 
                    e.LogName as LogTable, 
                    e.Safename as EnvironmentSafeName, 
                    e.EnvironmentLocation,
                    e.Id as EnvironmentId, 
                    e.AssetServiceUrl, 
                    e.AssetServiceInternalUrl, 
                    e.FullName as EnvironmentFullName, 
                    e.JobServiceUrl, 
                    e.EntityModel, 
                    c.Id as CustomerId, 
                    c.Safename as CustomerSafeName,
                    e.StorageAccountConnectionString
                FROM Customer c
                INNER JOIN CustomerEnvironment ce ON ce.CustomerId = c.Id
                INNER JOIN Environment e on ce.EnvironmentId = e.Id
                WHERE c.Safename = @CustomerSafename AND e.Safename = @EnvironmentSafename";

            await using (var connection = new SqlConnection(this.stackConfig.ConfigurationDatabaseConnectionString.UnScramble()))
            {
                return await connection.QuerySingleAsync<EnvironmentContextData>(sql, new { CustomerSafename = customerSafename, EnvironmentSafename = environmentSafename });
            }
        }
    }
}
