namespace LongRunningJob.Core.Repositories
{
    using System.Data.SqlClient;
    using System.Threading.Tasks;
    using Dapper;
    using inRiver.Core.Models.inRiver;
    using LongRunningJob.Core.Abstractions;

    public class LongRunningJobRepository : ILongRunningJobRepository
    {
        private readonly IEnvironmentContextAccessor environmentContextAccessor;

        public LongRunningJobRepository(IEnvironmentContextAccessor environmentContextAccessor)
        {
            this.environmentContextAccessor = environmentContextAccessor;
        }

        public async Task<LongRunningJob> GetAsync(int jobId)
        {
            const string sql = @"
                SELECT
                    [Id],
                    [JobType],
                    [State],
                    [Date],
                    [PercentCompleted],
                    [Identifier],
                    [Metadata],
                    [Scope],
                    [UserId],
                    [EnvironmentId]
                FROM [dbo].[LongRunningJob]
                WHERE Id = @JobId";

            await using (var connection = new SqlConnection(this.environmentContextAccessor.EnvironmentContext.DatabaseConnectionString))
            {
                return await connection.QuerySingleAsync<LongRunningJob>(sql, new { JobId = jobId });
            }
        }

        public async Task UpdateStateAsync(int jobId, string state)
        {
            const string sql = @"
                    UPDATE [dbo].[LongRunningJob]
                    SET State = @State
                    WHERE Id = @JobId";

            await using (var connection = new SqlConnection(this.environmentContextAccessor.EnvironmentContext.DatabaseConnectionString))
            {
                await connection.ExecuteAsync(sql, new { State = state, JobId = jobId });
            }
        }

        public async Task UpdateStateAsync(int jobId, string state, string metadata)
        {
            const string sql = @"
                    UPDATE [dbo].[LongRunningJob]
                    SET [State] = @State, [Metadata] = @Metadata
                    WHERE Id = @JobId";

            await using (var connection = new SqlConnection(this.environmentContextAccessor.EnvironmentContext.DatabaseConnectionString))
            {
                await connection.ExecuteAsync(sql, new { State = state, Metadata = metadata, JobId = jobId });
            }
        }

        public async Task<int> InsertLongRunningJobAsync(LongRunningJob job)
        {
            const string sql = @"DECLARE @UserId INT =
                                        (SELECT Id
                                        FROM dbo.[User]
                                        WHERE Username = @StartedBy)
                                    INSERT INTO LongRunningJob ([JobType], [State], [Date], [PercentCompleted], [Identifier], [Metadata], [Scope], [UserId])
                                    VALUES (@JobType, @State, GETUTCDATE(), NULL, @Identifier, @Metadata, @Scope, @UserId)
                                    SELECT CAST(SCOPE_IDENTITY() as int)";

            await using (var connection = new SqlConnection(this.environmentContextAccessor.EnvironmentContext.DatabaseConnectionString))
            {
                return await connection.QuerySingleAsync<int>(sql, job);
            }
        }

        public async Task<bool> StartedJobExistsAsync(string jobType, string identifier)
        {
            const string sql = @"
                    SELECT 1
                    FROM [dbo].[LongRunningJob]
                    WHERE JobType = @JobType
                        AND (Identifier = @Identifier OR @Identifier IS NULL)
                        AND ([State] = 'Running' OR [State] = 'Queued')";

            await using (var connection = new SqlConnection(this.environmentContextAccessor.EnvironmentContext.DatabaseConnectionString))
            {
                return await connection.ExecuteScalarAsync<bool>(sql, new { JobType = jobType, Identifier = identifier });
            }
        }
    }
}
