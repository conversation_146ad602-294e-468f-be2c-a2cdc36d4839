namespace inRiver.Server.Managers
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Server.Error;
    using inRiver.Server.Helpers;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Repository;
    using inRiver.Server.Request;
    using Microsoft.WindowsAzure.Storage;
    using Microsoft.WindowsAzure.Storage.Blob;
    using Serilog;

    public class CloudBlobManager : ICloudBlobManager
    {
        private readonly string connection;
        private readonly RequestContext context;
        private readonly string blobRoot;
        private readonly IFileHelper fileHelper;

        public CloudBlobManager(RequestContext context, string blobType)
        {
            this.context = context ?? throw new ArgumentNullException(nameof(context));
            this.blobRoot = $"{context.CustomerSafeName}-{blobType}/{context.EnvironmentSafeName.ToLowerInvariant()}/";
            this.connection = new LongRunningJobRepository(this.context).GetStorageAccountConnectionString(context.EnvironmentId);
            this.fileHelper = new FileHelper();
        }

        private CloudStorageAccount StorageAccount
        {
            get {
                if (!CloudStorageAccount.TryParse(this.connection, out var storageAccount))
                {
                    Log.Error("BlobContainer failed to fetch storage account");
                    return null;
                }

                return storageAccount;
            }
        }

        public byte[] GetFile(string fileName)
        {
            this.fileHelper.ThrowIfPathTraversalIsDetected(fileName);

            byte[] fileBytes;
            try
            {
                var filePath = Path.Combine(this.blobRoot, fileName);

                var cloudBlobClient = this.StorageAccount.CreateCloudBlobClient();
                var cloudBlobContainer = cloudBlobClient.GetContainerReference(this.context.CustomerSafeName.ToLowerInvariant());
                var cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(filePath);

                cloudBlockBlob.FetchAttributes();
                var fileByteLength = cloudBlockBlob.Properties.Length;
                fileBytes = new byte[fileByteLength];

                _ = cloudBlockBlob.DownloadToByteArray(fileBytes, 0);
            }
            catch (Exception ex)
            {
                var message = "BlobContainer failed to get file from blob storage";
                Log.Error(ex, message);
                throw ErrorUtility.GetDataAccessException(message, ex);
            }

            return fileBytes;
        }

        public Stream GetFileStream(string fileName)
        {
            this.fileHelper.ThrowIfPathTraversalIsDetected(fileName);

            Stream stream;
            try
            {
                var filePath = Path.Combine(this.blobRoot, fileName);

                var cloudBlobClient = this.StorageAccount.CreateCloudBlobClient();
                var cloudBlobContainer = cloudBlobClient.GetContainerReference(this.context.CustomerSafeName.ToLowerInvariant());
                var cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(filePath);

                stream = cloudBlockBlob.OpenRead();
            }
            catch (Exception ex)
            {
                var message = "BlobContainer failed to get stream for blob storage";
                Log.Error(ex, message);
                throw ErrorUtility.GetDataAccessException(message, ex);
            }

            return stream;
        }

        public bool AddFile(string fileName, byte[] fileBytes)
        {
            if (fileBytes is null)
            {
                throw new ArgumentNullException(nameof(fileBytes));
            }

            this.fileHelper.ThrowIfPathTraversalIsDetected(fileName);

            try
            {
                var filePath = Path.Combine(this.blobRoot, fileName);

                var cloudBlobClient = this.StorageAccount.CreateCloudBlobClient();
                var cloudBlobContainer = cloudBlobClient.GetContainerReference(this.context.CustomerSafeName.ToLowerInvariant());

                // This workaround is needed since CreateIfNotExists sometimes returns 409: Conflict if the container already exists.
                if (!cloudBlobContainer.Exists())
                {
                    _ = cloudBlobContainer.CreateIfNotExists();
                }

                var cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(filePath);
                cloudBlockBlob.UploadFromByteArray(fileBytes, 0, fileBytes.Length);

                return true;
            }
            catch (Exception ex)
            {
                var message = "BlobContainer failed to add file to blob storage";
                Log.Error(ex, message);
                throw ErrorUtility.GetDataAccessException(message, ex);
            }
        }

        public async Task<string> DownloadTextAsync(string name)
        {
            var cloudBlobContainer = this.GetCloudBlobContainer();
            var cloudBlockBlob = this.GetCloudBlockBlobReference(cloudBlobContainer, name);

            return await cloudBlockBlob.DownloadTextAsync();
        }

        public async Task UploadTextAsync(string name, string value)
        {
            var cloudBlobContainer = this.GetCloudBlobContainer();
            _ = await cloudBlobContainer.CreateIfNotExistsAsync();
            var cloudBlockBlob = this.GetCloudBlockBlobReference(cloudBlobContainer, name);

            await cloudBlockBlob.UploadTextAsync(value);
        }

        public IEnumerable<CloudBlockBlob> GetBlobs(string path)
        {
            var cloudBlobContainer = this.GetCloudBlobContainer();
            return cloudBlobContainer
                .GetDirectoryReference(Path.Combine(this.blobRoot, path))
                .ListBlobs(useFlatBlobListing: true)
                .OfType<CloudBlockBlob>();
        }

        public async Task DeleteBlobsAsync(string path)
        {
            var cloudBlobContainer = this.GetCloudBlobContainer();
            foreach (var listBlobItem in cloudBlobContainer.GetDirectoryReference(Path.Combine(this.blobRoot, path)).ListBlobs(useFlatBlobListing: true))
            {
                var blob = (CloudBlob)listBlobItem;

                await blob.DeleteIfExistsAsync();
            }
        }

        private CloudBlobContainer GetCloudBlobContainer()
        {
            var cloudBlobClient = this.StorageAccount.CreateCloudBlobClient();
            var containerName = this.context.CustomerSafeName.ToLowerInvariant();

            return cloudBlobClient.GetContainerReference(containerName);
        }

        private CloudBlockBlob GetCloudBlockBlobReference(CloudBlobContainer cloudBlobContainer, string blockName)
        {
            var blockPath = Path.Combine(this.blobRoot, blockName);

            return cloudBlobContainer.GetBlockBlobReference(blockPath);
        }
    }
}
