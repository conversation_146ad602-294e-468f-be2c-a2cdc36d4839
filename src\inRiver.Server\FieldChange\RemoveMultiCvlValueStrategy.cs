namespace inRiver.Server.FieldChange
{
    using System.Linq;
    using inRiver.Server.Repository;

    public class RemoveMultiCvlValueStrategy : BaseMultiCvlValueStrategy
    {
        public RemoveMultiCvlValueStrategy(IFieldRepository fieldRepository)
            : base(fieldRepository)
        {
        }

        public override string Calculate(string newValue, int entityId, string fieldTypeId)
        {
            var oldFieldValues = this.GetOldValues(entityId, fieldTypeId);
            var newFieldValues = this.ParseNewValues(newValue);
            var calculatedFieldValues = oldFieldValues.Except(newFieldValues)
                .OrderBy(value => value);
            return this.ConvertToString(calculatedFieldValues);
        }
    }
}
