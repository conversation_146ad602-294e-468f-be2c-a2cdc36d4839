namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;

    public class FieldSet
    {
        public FieldSet()
        {
            this.FieldTypes = new List<string>();
        }

        public string Id { get; set; }

        public LocaleString Name { get; set; }

        public LocaleString Description { get; set; }

        public string EntityTypeId { get; set; }

        public List<string> FieldTypes { get; set; }

        public override string ToString() => string.IsNullOrWhiteSpace(this.Id) ? base.ToString() : this.Id;
    }
}
