namespace inRiver.Server.Syndication.Script.Api
{
    using System;
    using System.Collections.Concurrent;
    using System.Threading.Tasks;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Script.Api.Models;

    public class RestApiCacheService : IRestApiCacheService
    {
        private const int CacheExpiryTimeMinutesDisabled = 0;

        private const int DefaultCacheExpiryTimeMinutes = 30;

        private readonly string tempCacheFolderName = Guid.NewGuid().ToString();

        private readonly ConcurrentDictionary<string, CacheModel> cache;

        private readonly ICloudBlobManager cloudBlobManager;

        public RestApiCacheService(ICloudBlobManager cloudBlobManager, ConcurrentDictionary<string, CacheModel> cache = null)
        {
            this.cloudBlobManager = cloudBlobManager;
            this.cache = cache ?? new ConcurrentDictionary<string, CacheModel>();
        }

        public async Task<string> GetOrAddAsync(string key, Func<Task<string>> getValueFunc, int cacheExpiryTimeMinutes)
        {
            if (cacheExpiryTimeMinutes == CacheExpiryTimeMinutesDisabled)
            {
                return await getValueFunc();
            }

            if (this.cache.TryGetValue(key, out var cacheModel))
            {
                return this.CheckIfCacheIsExpired(cacheModel.ExpiryDate)
                    ? await this.GetValueAndSaveCacheDataAsync(getValueFunc, key, cacheModel.Name, cacheExpiryTimeMinutes)
                    : await this.cloudBlobManager.DownloadTextAsync(cacheModel.Name);
            }

            return await this.GetValueAndSaveCacheDataAsync(getValueFunc, key, this.CreateCachePath(Guid.NewGuid().ToString()), cacheExpiryTimeMinutes);
        }

        public Task ClearCacheAsync() => this.cloudBlobManager.DeleteBlobsAsync(this.tempCacheFolderName);

        public int ParseExpiryTime(object cacheExpiryTimeMinutes, bool maskIsUsed)
        {
            if (maskIsUsed)
            {
                return CacheExpiryTimeMinutesDisabled;
            }

            if (cacheExpiryTimeMinutes == null)
            {
                return DefaultCacheExpiryTimeMinutes;
            }

            return int.TryParse(cacheExpiryTimeMinutes.ToString(), out var parsedValue)
                ? parsedValue
                : throw new SyndicateApiException("Can not convert cacheExpiryTimeMinutes value to int.");
        }

        private DateTime CalculateExpiryTime(int cacheExpiryTimeMinutes) =>
            DateTime.UtcNow + TimeSpan.FromMinutes(cacheExpiryTimeMinutes);

        private string CreateCachePath(string cacheName) => $"{this.tempCacheFolderName}/{cacheName}";

        private bool CheckIfCacheIsExpired(DateTime expiryDate) => expiryDate < DateTime.UtcNow;

        private async Task<string> GetValueAndSaveCacheDataAsync(Func<Task<string>> getValueFunc, string key, string cacheName, int cacheExpiryTimeMinutes)
        {
            var value = await getValueFunc();
            await this.cloudBlobManager.UploadTextAsync(cacheName, value);
            _ = this.cache.AddOrUpdate(key,
                new CacheModel(cacheName, this.CalculateExpiryTime(cacheExpiryTimeMinutes)),
                (oldKey, oldValue) => new CacheModel(cacheName, this.CalculateExpiryTime(cacheExpiryTimeMinutes)));

            return value;
        }
    }
}
