namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Log;

    /// <summary>
    /// Documentation.
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        private readonly ApiClientAuthenticationInfo authInfo;
        private readonly string connectionString;
        private readonly ICommonLogging log;

        public iPMC3DLPersistanceAdapter(inRiverPersistance origInRiverPersistance, string connectionString, ICommonLogging logInstance, string username)
            : base(origInRiverPersistance, connectionString, logInstance)
        {
            this.authInfo = ApiClientAuthenticationInfo.CreateFromUsername(username, origInRiverPersistance.EnvironmentId);
            this.connectionString = connectionString;
            this.log = logInstance;
        }
    }
}
