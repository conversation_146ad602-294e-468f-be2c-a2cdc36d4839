namespace inRiver.Server.Repository
{
    using System.Collections.Generic;
    using System.Diagnostics;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.Request;
    using Telemetry.Metrics;

    public class FieldRepository : IFieldRepository
    {
        private readonly RequestContext context;

        private readonly IDataPersistance dataContext;

        private readonly DataRepository dataRepository;

        public FieldRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;
            this.context = context;
            this.dataRepository = new DataRepository(context);
        }

        public object GetFieldValue(int entityId, string fieldTypeId) => this.dataContext.GetFieldValue(entityId, fieldTypeId);

        public Field GetField(int entityId, FieldType fieldType)
        {
            var dtoField = this.dataContext.GetField(entityId, fieldType.Id);
            if (dtoField == null)
            {
                return null;
            }

            return DtoFactory.FieldFromDto(dtoField, fieldType);
        }

        public DtoEntity UpdateFieldsForEntity(List<Field> fields)
        {
            if (!this.context.UserHasPermission(UserPermission.UpdateEntity))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to update entities in inRiver");
            }

            if (fields == null)
            {
                this.context.Log(LogLevel.Warning, "Fields collection cannot be null");
                throw ErrorUtility.GetArgumentException("UpdateFieldsForEntity", "fields", "Fields collection cannot be null");
            }

            if (fields.Count == 0)
            {
                this.context.Log(LogLevel.Warning, "Fields collection cannot be empty");
                throw ErrorUtility.GetArgumentException("UpdateFieldsForEntity", "fields", "Fields collection cannot be empty");
            }

            var upsertEntityDurationMetricStopwatch = Stopwatch.StartNew();

            int entityId = fields[0].EntityId;

            if (!this.dataContext.EntityExists(entityId))
            {
                this.context.Log(LogLevel.Warning, "Entity for Fields does not exist");
                throw ErrorUtility.GetArgumentException("UpdateFieldsForEntity", "EntityId", "Entity for Fields does not exist");
            }

            foreach (Field field in fields)
            {
                if (field.FieldType == null)
                {
                    this.context.Log(LogLevel.Warning, "Trying to update field with FieldType missing");
                    throw ErrorUtility.GetArgumentException("UpdateFieldsForEntity", "FieldType", "Trying to update field with FieldType missing");
                }

                if (string.IsNullOrWhiteSpace(field.FieldType.Id))
                {
                    this.context.Log(LogLevel.Warning, "Trying to update field with FieldTypeId missing");
                    throw ErrorUtility.GetArgumentException("UpdateFieldsForEntity", "FieldTypeId", "Trying to update field with FieldTypeId missing");
                }

                if (field.EntityId != entityId)
                {
                    this.context.Log(LogLevel.Warning, "Trying to update fields from different entities");
                    throw ErrorUtility.GetArgumentException("UpdateFieldsForEntity", "EntityId", "Trying to update fields from different entities");
                }
            }

            Entity entity = new Entity();
            entity.Id = entityId;
            entity.Fields = fields;
            entity.EntityType = null;
            entity.LoadLevel = LoadLevel.DataOnly;

            upsertEntityDurationMetricStopwatch.Stop();

            var updateEntityStopwatch = new Stopwatch();
            var result = this.dataRepository.UpdateEntity(entity, updateEntityStopwatch);
            UpsertEntityDurationMetric<FieldRepository>.TrackValue(
                upsertEntityDurationMetricStopwatch.ElapsedMilliseconds + updateEntityStopwatch.ElapsedMilliseconds,
                entity,
                this.context.CustomerSafeName,
                this.context.EnvironmentSafeName,
                this.context.EntityModel,
                nameof(UpdateFieldsForEntity));
            return result;
        }
    }
}
