namespace inRiver.Server.FieldChange
{
    using System;
    using System.Collections.Generic;
    using inRiver.Server.Repository;

    public abstract class BaseMultiCvlValueStrategy : ICalculateMultiCvlValueStrategy
    {
        private const char MultiCvlValuesDelimiter = ';';

        private readonly IFieldRepository fieldRepository;

        protected BaseMultiCvlValueStrategy(IFieldRepository fieldRepository)
        {
            this.fieldRepository = fieldRepository;
        }

        public abstract string Calculate(string newValue, int entityId, string fieldTypeId);

        public string[] GetOldValues(int entityId, string fieldTypeId)
        {
            var oldFieldValuesObject = this.fieldRepository.GetFieldValue(entityId, fieldTypeId);
            return oldFieldValuesObject != null && (string) oldFieldValuesObject != string.Empty
                ? oldFieldValuesObject.ToString().Split(MultiCvlValuesDelimiter)
                : Array.Empty<string>();
        }

        public string[] ParseNewValues(string newValue) => string.IsNullOrEmpty(newValue)
            ? Array.Empty<string>()
            : newValue.Split(MultiCvlValuesDelimiter);

        public string ConvertToString(IEnumerable<string> values) => values != null
            ? string.Join(MultiCvlValuesDelimiter.ToString(), values)
            : string.Empty;
    }
}
