namespace inRiver.Server.Managers
{
    using System;
    using inRiver.Server.Request;
    using inRiver.Server.Util;
    using Inriver.StackEssentials.Caching;
    using Inriver.StackEssentials.Redis;
    using LazyCache;

    public sealed class CustomerEnvironmentManager
    {
        private readonly IAppCache lazyCache = new CachingService
        {
            DefaultCachePolicy = new CacheDefaults
        {
                DefaultCacheDurationSeconds = (int)TimeSpan.FromMinutes(5).TotalSeconds,
            },
        };

        private CustomerEnvironmentManager()
        {
        }

        public static CustomerEnvironmentManager Instance { get; } = new CustomerEnvironmentManager();

        public EnvironmentContextData GetContextForEnvironment(string customerSafeName, string environmentSafeName, string configurationConnectionString) =>
            this.lazyCache.GetOrAddAsync(
                $"{nameof(CustomerEnvironmentManager)}-{nameof(this.GetContextForEnvironment)}-{customerSafeName}/{environmentSafeName}",
                async () => await StaticEnvironmentContextStore.GetContextDataForEnvironmentAsync(customerSafeName, environmentSafeName, configurationConnectionString)).GetAwaiter().GetResult();
        }
            }
