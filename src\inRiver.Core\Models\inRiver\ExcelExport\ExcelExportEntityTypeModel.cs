namespace inRiver.Core.Models.inRiver.ExcelExport
{
    using System.Collections.Generic;

    public class ExcelExportEntityTypeModel
    {
        public string EntityTypeId { get; set; }

        public string LinkTypeId { get; set; }

        public string LinkDirection { get; set; }

        public List<string> FieldSetIds { get; set; }

        public List<string> FieldTypeIds { get; set; }
    }
}
