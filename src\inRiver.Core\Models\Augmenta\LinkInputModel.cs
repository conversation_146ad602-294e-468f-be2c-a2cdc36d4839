﻿namespace inRiver.Core.Models.Augmenta
{
    using System;

    public class LinkInputModel
    {
        public int Id { get; set; }

        public bool Inactive { get; set; }

        public string LinkTypeId { get; set; }

        public int SourceEntityId { get; set; }

        public int TargetEntityId { get; set; }

        public int? LinkEntityId { get; set; }

        public int Index { get; set; }

        public DateTime LastModified { get; set; }
    }

}