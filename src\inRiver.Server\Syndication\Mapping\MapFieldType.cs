namespace inRiver.Server.Syndication.Mapping
{
    public class MapFieldType
    {
        public string DataType { get; set; }

        public string FieldDataType { get; set; }

        public bool Mandatory { get; set; }

        public bool Unique { get; set; }

        public bool Recommended { get; set; }

        public int? MaxLength { get; set; }

        public string DefaultValue { get; set; }

        public string MetaData { get; set; }
    }
}
