################ Dotnet and TypeScript Code Style Settings
[*.{cs,csx,cake,cshtml}]

# NET style rules

# this preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0003-ide0009
dotnet_style_qualification_for_event = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_qualification_for_field = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_qualification_for_method = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_qualification_for_property = true:suggestion # has the following severity in root editorconfig: error

# Use language keywords instead of framework type names for type references. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0049
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_predefined_type_for_member_access = true:suggestion # has the following severity in root editorconfig: error

# .NET modifier preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/modifier-preferences#net-modifier-preferences
dotnet_style_require_accessibility_modifiers = always:suggestion # has the following severity in root editorconfig: error
csharp_preferred_modifier_order = public, private, protected, internal, static, extern, new, virtual, abstract, sealed, override, readonly, unsafe, volatile, async:silent: error
dotnet_style_readonly_field = true:suggestion # has the following severity in root editorconfig: error

# Parentheses preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0047-ide0048
dotnet_style_parentheses_in_arithmetic_binary_operators = always_for_clarity:suggestion
dotnet_style_parentheses_in_relational_binary_operators = always_for_clarity:suggestion
dotnet_style_parentheses_in_other_binary_operators = always_for_clarity:suggestion
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:suggestion

# .NET expression-level preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/expression-level-preferences#net-expression-level-preferences
dotnet_style_object_initializer = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_collection_initializer = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_explicit_tuple_names = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_inferred_tuple_names = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_auto_properties = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_conditional_expression_over_assignment = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_conditional_expression_over_return = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_compound_assignment = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_simplified_interpolation = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_simplified_boolean_expressions = true:suggestion # has the following severity in root editorconfig: error

# .NET null-checking preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/null-checking-preferences#net-null-checking-preferences
dotnet_style_coalesce_expression = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_null_propagation = true:suggestion # has the following severity in root editorconfig: error
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:suggestion # has the following severity in root editorconfig: error



# C# Style rules. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/language-rules

# 'var' preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0003-ide0009
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_elsewhere = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion # has the following severity in root editorconfig: error

# Expression-bodied members. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/expression-bodied-members
csharp_style_expression_bodied_accessors = when_on_single_line:suggestion # has the following severity in root editorconfig: error
csharp_style_expression_bodied_constructors = false:suggestion # has the following severity in root editorconfig: error
csharp_style_expression_bodied_indexers = true:suggestion # has the following severity in root editorconfig: error
csharp_style_expression_bodied_methods = when_on_single_line:suggestion # has the following severity in root editorconfig: error
csharp_style_expression_bodied_operators = true:suggestion # has the following severity in root editorconfig: error
csharp_style_expression_bodied_properties = true:suggestion # has the following severity in root editorconfig: error
csharp_style_expression_bodied_lambdas = true:suggestion # has the following severity in root editorconfig: error

# Pattern matching preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/pattern-matching-preferences
csharp_style_pattern_matching_over_as_with_null_check = true:suggestion # has the following severity in root editorconfig: error
csharp_style_pattern_matching_over_is_with_cast_check = true:suggestion # has the following severity in root editorconfig: error
csharp_style_prefer_switch_expression = true:suggestion # has the following severity in root editorconfig: error
csharp_style_prefer_pattern_matching = true:suggestion # has the following severity in root editorconfig: error
csharp_style_prefer_not_pattern = true:suggestion

# Expression-level preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/expression-level-preferences#c-expression-level-preferences
csharp_style_inlined_variable_declaration = true:suggestion # has the following severity in root editorconfig: error
csharp_prefer_simple_default_expression = true:suggestion # has the following severity in root editorconfig: error
csharp_style_pattern_local_over_anonymous_function = true:suggestion # has the following severity in root editorconfig: error
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_style_prefer_index_operator = true:suggestion
csharp_style_prefer_range_operator = true:suggestion
csharp_style_implicit_object_creation_when_type_is_apparent = true:suggestion
csharp_style_unused_value_assignment_preference = discard_variable:suggestion # has the following severity in root editorconfig: error

# "Null" checking preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/null-checking-preferences#c-null-checking-preferences
csharp_style_conditional_delegate_call = true:suggestion # has the following severity in root editorconfig: error
csharp_style_throw_expression = true:suggestion # has the following severity in root editorconfig: error

# Code-block preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/code-block-preferences
csharp_prefer_braces = true:suggestion # has the following severity in root editorconfig: error
csharp_prefer_simple_using_statement = true:suggestion

# 'using' directive preferences. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/ide0065
csharp_using_directive_placement = inside_namespace:suggestion # has the following severity in root editorconfig: error

# Modifier preferences
csharp_prefer_static_local_function = true:suggestion # has the following severity in root editorconfig: error
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async:suggestion # has the following severity in root editorconfig: error



# C# formatting rules. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#c-formatting-rules
dotnet_sort_system_directives_first = true:suggestion # has the following severity in root editorconfig: error
dotnet_separate_import_directive_groups = false:suggestion # has the following severity in root editorconfig: error
s
# New Line Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#new-line-options
csharp_new_line_before_catch = true:suggestion # has the following severity in root editorconfig: error
csharp_new_line_before_else = false:suggestion # has the following severity in root editorconfig: error
csharp_new_line_before_finally = true:suggestion # has the following severity in root editorconfig: error
csharp_new_line_before_members_in_anonymous_types = true:suggestion # has the following severity in root editorconfig: error
csharp_new_line_before_members_in_object_initializers = true:suggestion # has the following severity in root editorconfig: error
csharp_new_line_between_query_expression_clauses = false:suggestion # has the following severity in root editorconfig: error
csharp_new_line_before_open_brace = methods, properties, control_blocks, object_collection_array_initializers, types

# Indentation Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#indentation-options
csharp_indent_block_contents = true:suggestion # has the following severity in root editorconfig: error
csharp_indent_braces = false:suggestion # has the following severity in root editorconfig: error
csharp_indent_case_contents = true:suggestion # has the following severity in root editorconfig: error
csharp_indent_case_contents_when_block = false:suggestion # has the following severity in root editorconfig: error
csharp_indent_labels = no_change:suggestion # has the following severity in root editorconfig: error
csharp_indent_switch_labels = true:suggestion # has the following severity in root editorconfig: error

# Spacing Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#spacing-options
csharp_space_after_cast = false:suggestion # has the following severity in root editorconfig: error
csharp_space_after_colon_in_inheritance_clause = true:suggestion # has the following severity in root editorconfig: error
csharp_space_after_comma = true:suggestion # has the following severity in root editorconfig: error
csharp_space_after_dot = false:suggestion # has the following severity in root editorconfig: error
csharp_space_after_keywords_in_control_flow_statements = true:suggestion # has the following severity in root editorconfig: error
csharp_space_after_semicolon_in_for_statement = true:suggestion # has the following severity in root editorconfig: error
csharp_space_around_binary_operators = before_and_after:suggestion # has the following severity in root editorconfig: error
csharp_space_around_declaration_statements = do_not_ignore:suggestion # has the following severity in root editorconfig: error
csharp_space_before_colon_in_inheritance_clause = true:suggestion # has the following severity in root editorconfig: error
csharp_space_before_comma = false:suggestion # has the following severity in root editorconfig: error
csharp_space_before_dot = false:suggestion # has the following severity in root editorconfig: error
csharp_space_before_semicolon_in_for_statement = false:suggestion # has the following severity in root editorconfig: error
csharp_space_before_open_square_brackets = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_empty_square_brackets = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_method_declaration_name_and_open_parenthesis = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_method_declaration_parameter_list_parentheses = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_method_call_name_and_opening_parenthesis = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_method_call_parameter_list_parentheses = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_method_call_empty_parameter_list_parentheses = false:suggestion # has the following severity in root editorconfig: error
csharp_space_between_parentheses = expressions:suggestion # has the following severity in root editorconfig: error
csharp_space_between_square_brackets = false:suggestion # has the following severity in root editorconfig: error

# Wrap Options. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/formatting-rules#wrap-options
csharp_preserve_single_line_blocks = true:suggestion # has the following severity in root editorconfig: error
csharp_preserve_single_line_statements = false:suggestion # has the following severity in root editorconfig: error

# Modifier preferences
csharp_prefer_static_local_function = true:suggestion # has the following severity in root editorconfig: error

# Parameter preferences
dotnet_code_quality_unused_parameters = all:suggestion # has the following severity in root editorconfig: error

# Naming rules. See https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/style-rules/naming-rules


# Naming Symbols
# constant_fields - Define constant fields
dotnet_naming_symbols.constant_fields.applicable_kinds = field
dotnet_naming_symbols.constant_fields.required_modifiers = const
# non_private_readonly_fields - Define public, internal and protected readonly fields
dotnet_naming_symbols.non_private_readonly_fields.applicable_accessibilities = public, internal, protected
dotnet_naming_symbols.non_private_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.non_private_readonly_fields.required_modifiers = readonly
# static_readonly_fields - Define static and readonly fields
dotnet_naming_symbols.static_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.static_readonly_fields.required_modifiers = static, readonly
# private_readonly_fields - Define private readonly fields
dotnet_naming_symbols.private_readonly_fields.applicable_accessibilities = private
dotnet_naming_symbols.private_readonly_fields.applicable_kinds = field
dotnet_naming_symbols.private_readonly_fields.required_modifiers = readonly
# public_internal_fields - Define public and internal fields
dotnet_naming_symbols.public_internal_fields.applicable_accessibilities = public, internal
dotnet_naming_symbols.public_internal_fields.applicable_kinds = field
# private_protected_fields - Define private and protected fields
dotnet_naming_symbols.private_protected_fields.applicable_accessibilities = private, protected
dotnet_naming_symbols.private_protected_fields.applicable_kinds = field
# public_symbols - Define any public symbol
dotnet_naming_symbols.public_symbols.applicable_accessibilities = public, internal, protected, protected_internal
dotnet_naming_symbols.public_symbols.applicable_kinds = method, property, event, delegate
# parameters - Defines any parameter
dotnet_naming_symbols.parameters.applicable_kinds = parameter
# non_interface_types - Defines class, struct, enum and delegate types
dotnet_naming_symbols.non_interface_types.applicable_kinds = class, struct, enum, delegate
# interface_types - Defines interfaces
dotnet_naming_symbols.interface_types.applicable_kinds = interface

# Naming Styles
# camel_case - Define the camelCase style
dotnet_naming_style.camel_case.capitalization = camel_case
# pascal_case - Define the Pascal_case style
dotnet_naming_style.pascal_case.capitalization = pascal_case
# first_upper - The first character must start with an upper-case character
dotnet_naming_style.first_upper.capitalization = first_word_upper
# prefix_interface_interface_with_i - Interfaces must be PascalCase and the first character of an interface must be an 'I'
dotnet_naming_style.prefix_interface_interface_with_i.capitalization = pascal_case
dotnet_naming_style.prefix_interface_interface_with_i.required_prefix = I

# Naming Rules
# Constant fields must be PascalCase
dotnet_naming_rule.constant_fields_must_be_pascal_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.constant_fields_must_be_pascal_case.symbols = constant_fields
dotnet_naming_rule.constant_fields_must_be_pascal_case.style = pascal_case
# Public, internal and protected readonly fields must be PascalCase
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.symbols = non_private_readonly_fields
dotnet_naming_rule.non_private_readonly_fields_must_be_pascal_case.style = pascal_case
# Static readonly fields must be PascalCase
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.symbols = static_readonly_fields
dotnet_naming_rule.static_readonly_fields_must_be_pascal_case.style = pascal_case
# Private readonly fields must be camelCase
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.symbols = private_readonly_fields
dotnet_naming_rule.private_readonly_fields_must_be_camel_case.style = camel_case
# Public and internal fields must be PascalCase
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.symbols = public_internal_fields
dotnet_naming_rule.public_internal_fields_must_be_pascal_case.style = pascal_case
# Private and protected fields must be camelCase
dotnet_naming_rule.private_protected_fields_must_be_camel_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.private_protected_fields_must_be_camel_case.symbols = private_protected_fields
dotnet_naming_rule.private_protected_fields_must_be_camel_case.style = camel_case

# Public members must be capitalized
dotnet_naming_rule.public_members_must_be_capitalized.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.public_members_must_be_capitalized.symbols = public_symbols
dotnet_naming_rule.public_members_must_be_capitalized.style = first_upper
# Parameters must be camelCase
dotnet_naming_rule.parameters_must_be_camel_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.parameters_must_be_camel_case.symbols = parameters
dotnet_naming_rule.parameters_must_be_camel_case.style = camel_case
# Class, struct, enum and delegates must be PascalCase
dotnet_naming_rule.non_interface_types_must_be_pascal_case.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.non_interface_types_must_be_pascal_case.symbols = non_interface_types
dotnet_naming_rule.non_interface_types_must_be_pascal_case.style = pascal_case
# Interfaces must be PascalCase and start with an 'I'
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.severity = suggestion # has the following severity in root editorconfig: error
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.symbols = interface_types
dotnet_naming_rule.interface_types_must_be_prefixed_with_i.style = prefix_interface_interface_with_i

# Async methods should have "Async" suffix
dotnet_naming_rule.async_methods_end_in_async.symbols = any_async_methods
dotnet_naming_rule.async_methods_end_in_async.style = end_in_async
dotnet_naming_rule.async_methods_end_in_async.severity = suggestion

dotnet_naming_symbols.any_async_methods.applicable_kinds = method
dotnet_naming_symbols.any_async_methods.applicable_accessibilities = *
dotnet_naming_symbols.any_async_methods.required_modifiers = async

dotnet_naming_style.end_in_async.required_prefix =
dotnet_naming_style.end_in_async.required_suffix = Async
dotnet_naming_style.end_in_async.capitalization = pascal_case
dotnet_naming_style.end_in_async.word_separator =

# IDE0005: Using directive is unnecessary.
dotnet_diagnostic.IDE0005.severity = suggestion # has the following severity in root editorconfig: warning

# IDE0010: Add missing cases to switch statement
dotnet_diagnostic.IDE0010.severity = suggestion # has the following severity in root editorconfig: error

# IDE0016: Use throw expression
dotnet_diagnostic.IDE0016.severity = suggestion # has the following severity in root editorconfig: error

# IDE0050: Convert anonymous type to tuple
dotnet_diagnostic.IDE0050.severity = none

# IDE0055: Fix formatting
dotnet_diagnostic.IDE0055.severity = suggestion # has the following severity in root editorconfig: error

# IDE0064: Make struct fields writable
dotnet_diagnostic.IDE0064.severity = suggestion # has the following severity in root editorconfig: error

# IDE0070: Use 'System.HashCode.Combine'
dotnet_diagnostic.IDE0070.severity = suggestion # has the following severity in root editorconfig: error

# IDE0072: Add missing cases to switch expression
dotnet_diagnostic.IDE0072.severity = suggestion # has the following severity in root editorconfig: error

# IDE0001: Simplify Names
dotnet_diagnostic.IDE0001.severity = suggestion # has the following severity in root editorconfig: warning

# IDE0082: Convert typeof to nameof
dotnet_diagnostic.IDE0082.severity = suggestion # has the following severity in root editorconfig: error

# IDE1006: Naming rule violation
dotnet_diagnostic.IDE1006.severity = suggestion # has the following severity in root editorconfig: error


# CA1002: Do not expose generic lists
dotnet_diagnostic.CA1002.severity = suggestion # has the following severity in root editorconfig: error

# CA1008: Enums should have zero value
dotnet_diagnostic.CA1008.severity = none

# CA1031: Do not catch general exception types
dotnet_diagnostic.CA1031.severity = suggestion

# CA1034: Nested types should not be visible
dotnet_diagnostic.CA1034.severity = suggestion # has the following severity in root editorconfig: error

# CA1051: Do not declare visible instance fields
dotnet_diagnostic.CA1051.severity = suggestion # has the following severity in root editorconfig: error

# CA1052: Static holder types should be Static or NotInheritable
dotnet_diagnostic.CA1052.severity = suggestion # has the following severity in root editorconfig: error

# CA1054: URI-like parameters should not be strings
dotnet_diagnostic.CA1054.severity = suggestion

# CA1062: Validate arguments of public methods
dotnet_diagnostic.CA1062.severity = suggestion

# CA1304: Specify CultureInfo
dotnet_diagnostic.CA1304.severity = suggestion # has the following severity in root editorconfig: error

# CA1305: Specify IFormatProvider
dotnet_diagnostic.CA1305.severity = suggestion # has the following severity in root editorconfig: error

# CA1307: Specify StringComparison for clarity
dotnet_diagnostic.CA1307.severity = suggestion # has the following severity in root editorconfig: error

# CA1308: Normalize strings to uppercase
dotnet_diagnostic.CA1308.severity = suggestion # has the following severity in root editorconfig: error

# CA1309: Use ordinal string comparison
dotnet_diagnostic.CA1309.severity = suggestion # has the following severity in root editorconfig: error

# CA1310: Specify StringComparison for correctness
dotnet_diagnostic.CA1310.severity = suggestion # has the following severity in root editorconfig: error

# CA1507: Use nameof to express symbol names
dotnet_diagnostic.CA1507.severity = suggestion # has the following severity in root editorconfig: error

# CA1508 Remove or refactor the condition(s) to avoid dead code
dotnet_diagnostic.CA1508.severity = suggestion # has the following severity in root editorconfig: error

# CA1716: Identifiers should not match keywords
dotnet_diagnostic.CA1716.severity = suggestion # has the following severity in root editorconfig: warning

# CA1717: Only FlagsAttribute enums should have plural names
dotnet_diagnostic.CA1717.severity = suggestion # has the following severity in root editorconfig: error

# CA1724: Type Names Should Not Match Namespaces
dotnet_diagnostic.CA1724.severity = suggestion # has the following severity in root editorconfig: error

# CA1725: Parameter names should match base declaration
dotnet_diagnostic.CA1725.severity = suggestion # has the following severity in root editorconfig: error

# CA1801: Review unused parameters
dotnet_diagnostic.CA1801.severity = suggestion # has the following severity in root editorconfig: error

# CA1802: Use literals where appropriate
dotnet_diagnostic.CA1802.severity = suggestion # has the following severity in root editorconfig: error

# CA1806: Do not ignore method results
dotnet_diagnostic.CA1806.severity = suggestion # has the following severity in root editorconfig: error

# CA1812: Avoid uninstantiated internal classes
dotnet_diagnostic.CA1812.severity = suggestion # has the following severity in root editorconfig: error

# CA1813: Avoid unsealed attributes
dotnet_diagnostic.CA1813.severity = suggestion # has the following severity in root editorconfig: error

# CA1815: Override equals and operator equals on value types
dotnet_diagnostic.CA1815.severity = suggestion # has the following severity in root editorconfig: error

# CA1819: Properties should not return arrays
dotnet_diagnostic.CA1819.severity = suggestion

# CA1820: Test for empty strings using string length
dotnet_diagnostic.CA1820.severity = suggestion # has the following severity in root editorconfig: error

# CA1822: Member does not access instance data and can be marked as static
dotnet_diagnostic.CA1822.severity = suggestion # has the following severity in root editorconfig: error

# CA1829: Use Length/Count property instead of Count() when available
dotnet_diagnostic.CA1829.severity = suggestion # has the following severity in root editorconfig: error

# CA2000: Call System.IDisposable.Dispose on object created by 'MethodName' before all references to it are out of scope
dotnet_diagnostic.CA2000.severity = suggestion # has the following severity in root editorconfig: error

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = suggestion # has the following severity in root editorconfig: error
# See https://devblogs.microsoft.com/dotnet/configureawait-faq/

# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = suggestion # has the following severity in root editorconfig: error

# CA2227: Collection properties should be read only
dotnet_diagnostic.CA2227.severity = suggestion # has the following severity in root editorconfig: error

# CA2234: Pass system uri objects instead of strings
dotnet_diagnostic.CA2234.severity = suggestion # has the following severity in root editorconfig: warning

# CA2237: Mark ISerializable types with serializable
dotnet_diagnostic.CA2237.severity = suggestion # has the following severity in root editorconfig: error

# CA5401: Symmetric encryption uses non-default initialization vector, which could be potentially repeatable
dotnet_diagnostic.CA5401.severity = suggestion # has the following severity in root editorconfig: error

# CS0114: Member hides inherited member; missing override keyword
dotnet_diagnostic.CS0114.severity = suggestion # has the following severity in root editorconfig: error

# CS0168: Variable is declared but never used
dotnet_diagnostic.CS0168.severity = suggestion # has the following severity in root editorconfig: warning

# CS0618: Type or member is obsolete
dotnet_diagnostic.CS0618.severity = suggestion # has the following severity in root editorconfig: error

# CS1998: Async method lacks 'await' operators and will run synchronously
dotnet_diagnostic.CS1998.severity = suggestion # has the following severity in root editorconfig: error





# StyleCop rules

# SA0001 XML comment analysis is disabled due to project configuration.
dotnet_diagnostic.SA0001.severity = none

# SA1003: Symbols should be spaced correctly
dotnet_diagnostic.SA1003.severity = suggestion # has the following severity in root editorconfig: error

# SA1005: Single line comments should begin with single space
dotnet_diagnostic.SA1005.severity = suggestion # has the following severity in root editorconfig: error

# SA1009: Closing parenthesis should be spaced correctly
dotnet_diagnostic.SA1009.severity = suggestion # has the following severity in root editorconfig: error

# SA1013: Closing braces should be spaced correctly
dotnet_diagnostic.SA1013.severity = suggestion # has the following severity in root editorconfig: error

# SA1025: Code should not contain multiple whitespace in a row
dotnet_diagnostic.SA1025.severity = suggestion # has the following severity in root editorconfig: error

# SA1028 'public' members should come before 'private' members.
dotnet_diagnostic.SA1028.severity = suggestion # has the following severity in root editorconfig: error

# SA1101: Prefix local calls with this - this is handled by dotnet_style_qualification_for_*
dotnet_diagnostic.SA1101.severity = silent

# SA1106: Code should not contain empty statements
dotnet_diagnostic.SA1106.severity = suggestion # has the following severity in root editorconfig: error

# SA1108: Block statements should not contain embedded comments
dotnet_diagnostic.SA1108.severity = suggestion

# SA1116: Split parameters should start on line after declaration
dotnet_diagnostic.SA1116.severity = suggestion # has the following severity in root editorconfig: error

# SA1117: Parameters should be on same line or separate lines
dotnet_diagnostic.SA1117.severity = suggestion # has the following severity in root editorconfig: error

# SA1118: Parameter should not span multiple lines
dotnet_diagnostic.SA1118.severity = suggestion # has the following severity in root editorconfig: warning

# SA1120: Comments should contain text
dotnet_diagnostic.SA1120.severity = suggestion # has the following severity in root editorconfig: error

# SA1122: Use string.Empty for empty strings
dotnet_diagnostic.SA1122.severity = suggestion # has the following severity in root editorconfig: warning

# SA1128: Put constructor initializers on their own line
dotnet_diagnostic.SA1128.severity = suggestion # has the following severity in root editorconfig: error

# SA1129: Do not use default value type constructor
dotnet_diagnostic.SA1129.severity = suggestion # has the following severity in root editorconfig: error

# SA1131: When a comparison is made between a variable and a literal, the variable should be placed on the left-hand-side to maximize readability.
dotnet_diagnostic.SA1131.severity = suggestion # has the following severity in root editorconfig: error

# SA1133 Each attribute should be placed in its own set of square brackets.
dotnet_diagnostic.SA1133.severity = suggestion # has the following severity in root editorconfig: error

# SA1135: Using directives should be qualified
dotnet_diagnostic.SA1135.severity = suggestion # has the following severity in root editorconfig: error

# SA1136: Enum values should be on separate lines
dotnet_diagnostic.SA1136.severity = suggestion # has the following severity in root editorconfig: error

# SA1137: Elements should have the same indentation
dotnet_diagnostic.SA1137.severity = suggestion # has the following severity in root editorconfig: warning

# SA1200: Using directives should be placed correctly
dotnet_diagnostic.SA1200.severity = suggestion # has the following severity in root editorconfig: error

# SA1201: Elements must appear in the correct order
dotnet_diagnostic.SA1201.severity = suggestion # has the following severity in root editorconfig: error

# SA1202 'public' members should come before 'private' members.
dotnet_diagnostic.SA1202.severity = suggestion # has the following severity in root editorconfig: error

# SA1203: Constants should appear before fields
dotnet_diagnostic.SA1203.severity = suggestion # has the following severity in root editorconfig: error

# SA1204 Static members should appear before non-static members.
dotnet_diagnostic.SA1204.severity = suggestion # has the following severity in root editorconfig: error

# SA1208 Using directive for 'System.*' should appear before directive for 'NonSystem.*'
dotnet_diagnostic.SA1208.severity = suggestion # has the following severity in root editorconfig: error

# SA1210: Using directives should be ordered alphabetically by namespace
dotnet_diagnostic.SA1210.severity = suggestion # has the following severity in root editorconfig: warning

# SA1212: Property accessors should follow order
dotnet_diagnostic.SA1212.severity = suggestion # has the following severity in root editorconfig: error

# SA1214: Readonly fields should appear before non-readonly fields
dotnet_diagnostic.SA1214.severity = suggestion # has the following severity in root editorconfig: warning

# SA1300 Element 'inRiver' should begin with an uppercase letter.
dotnet_diagnostic.SA1300.severity = suggestion

# SA1303: Const field names should begin with upper-case letter
dotnet_diagnostic.SA1303.severity = suggestion # has the following severity in root editorconfig: error

# SA1306: Field names should begin with lower-case letter
dotnet_diagnostic.SA1306.severity = suggestion # has the following severity in root editorconfig: error

# SA1307: Accessible fields should begin with upper-case letter
dotnet_diagnostic.SA1307.severity = suggestion # has the following severity in root editorconfig: error

# SA1309 Field should not begin with an underscore.
dotnet_diagnostic.SA1309.severity = suggestion # has the following severity in root editorconfig: error

# SA1312: Variable names should begin with lower-case letter
dotnet_diagnostic.SA1312.severity = suggestion # has the following severity in root editorconfig: error

# SA1401: Fields should be private
dotnet_diagnostic.SA1401.severity = suggestion # has the following severity in root editorconfig: error

# SA1402 File may only contain a single type.
dotnet_diagnostic.SA1402.severity = suggestion # has the following severity in root editorconfig: error

# SA1405:Debug.Assert should provide message text
dotnet_diagnostic.SA1405.severity = suggestion # has the following severity in root editorconfig: error

# SA1411 Attribute constructor should not use unnecessary parenthesis.
dotnet_diagnostic.SA1411.severity = suggestion # has the following severity in root editorconfig: error

# SA1413 Use trailing comma in multi-line initializers.
dotnet_diagnostic.SA1413.severity = none

# SA1500 Braces for multi-line statements should not share line.
dotnet_diagnostic.SA1500.severity = none

# SA1505 An opening brace should not be followed by a blank line.
dotnet_diagnostic.SA1505.severity = suggestion # has the following severity in root editorconfig: error

# SA1507 Code should not contain multiple blank lines in a row.
dotnet_diagnostic.SA1507.severity = suggestion # has the following severity in root editorconfig: error

# SA1508 A closing brace should not be preceded by a blank line.
dotnet_diagnostic.SA1508.severity = suggestion # has the following severity in root editorconfig: error

# SA1509:Opening braces should not be preceded by blank line
dotnet_diagnostic.SA1509.severity = suggestion # has the following severity in root editorconfig: error

# SA1513 Closing brace should be followed by blank line.
dotnet_diagnostic.SA1513.severity = suggestion # has the following severity in root editorconfig: error

# SA1515: Single-line comment should be preceded by blank line
dotnet_diagnostic.SA1515.severity = suggestion # has the following severity in root editorconfig: error

# SA1516 Elements should be separated by blank line.
dotnet_diagnostic.SA1516.severity = suggestion # has the following severity in root editorconfig: error

# SA1517:Code should not contain blank lines at start of file
dotnet_diagnostic.SA1517.severity = suggestion # has the following severity in root editorconfig: error

# SA1518:Use line endings correctly at end of file
dotnet_diagnostic.SA1518.severity = suggestion # has the following severity in root editorconfig: warning

# SA1629 Documentation text should end with a period
dotnet_diagnostic.SA1629.severity = suggestion # has the following severity in root editorconfig: error

# SA1600: Elements should be documented
dotnet_diagnostic.SA1600.severity = none

# SA1602: Enumeration items should be documented
dotnet_diagnostic.SA1602.severity = suggestion

# SA1633 The file header is missing or not located at the top of the file
dotnet_diagnostic.SA1633.severity = none

# SA1649: File name should match first type name
dotnet_diagnostic.SA1649.severity = suggestion # has the following severity in root editorconfig: error

# CA2100: Review SQL queries for security vulnerabilities
dotnet_diagnostic.CA2100.severity = suggestion # has the following severity in root editorconfig: warning



# The below items are not yet configured in root editorconfig. They are warnings or errors by default.

# SA1510: Chained statement blocks should not be preceded by blank line
dotnet_diagnostic.SA1510.severity = suggestion

# SA1503: Braces should not be omitted
dotnet_diagnostic.SA1503.severity = suggestion

# SA1124: Do not use regions
dotnet_diagnostic.SA1124.severity = suggestion

# CA2208: Instantiate argument exceptions correctly
dotnet_diagnostic.CA2208.severity = suggestion

# SA1008: Opening parenthesis should be spaced correctly
dotnet_diagnostic.SA1008.severity = suggestion

# CA3075: Insecure DTD processing in XML
dotnet_diagnostic.CA3075.severity = suggestion

# CA1056: URI-like properties should not be strings
dotnet_diagnostic.CA1056.severity = suggestion

# CA1715: Identifiers should have correct prefix
dotnet_diagnostic.CA1715.severity = suggestion

# SA1313: Parameter names should begin with lower-case letter
dotnet_diagnostic.SA1313.severity = suggestion

# SA1012: Opening braces should be spaced correctly
dotnet_diagnostic.SA1012.severity = suggestion

# SA1001: Commas should be spaced correctly
dotnet_diagnostic.SA1001.severity = suggestion

# SA1121: Use built-in type alias
dotnet_diagnostic.SA1121.severity = suggestion

# SA1520: Use braces consistently
dotnet_diagnostic.SA1520.severity = suggestion

# SA1512: Single-line comments should not be followed by blank line
dotnet_diagnostic.SA1512.severity = suggestion

# SA1119: Statement should not use unnecessary parenthesis
dotnet_diagnostic.SA1119.severity = suggestion

# SA1004: Documentation lines should begin with single space
dotnet_diagnostic.SA1004.severity = suggestion

# SA1611: Element parameters should be documented
dotnet_diagnostic.SA1611.severity = suggestion

# CA1834: Consider using 'StringBuilder.Append(char)' when applicable
dotnet_diagnostic.CA1834.severity = suggestion

# SA1209: Using alias directives should be placed after other using directives
dotnet_diagnostic.SA1209.severity = suggestion

# CA1043: Use Integral Or String Argument For Indexers
dotnet_diagnostic.CA1043.severity = suggestion

# CA2200: Rethrow to preserve stack details
dotnet_diagnostic.CA2200.severity = suggestion

# SA1400: Access modifier should be declared
dotnet_diagnostic.SA1400.severity = suggestion

# SA1310: Field names should not contain underscore
dotnet_diagnostic.SA1310.severity = suggestion

# CA1805: Do not initialize unnecessarily
dotnet_diagnostic.CA1805.severity = suggestion

# SA1604: Element documentation should have summary
dotnet_diagnostic.SA1604.severity = suggestion

# SA1010: Opening square brackets should be spaced correctly
dotnet_diagnostic.SA1010.severity = suggestion

# CA1714: Flags enums should have plural names
dotnet_diagnostic.CA1714.severity = suggestion

# SA1024: Colons Should Be Spaced Correctly
dotnet_diagnostic.SA1024.severity = suggestion

# CA1720: Identifier contains type name
dotnet_diagnostic.CA1720.severity = suggestion

# CA1707: Identifiers should not contain underscores
dotnet_diagnostic.CA1707.severity = suggestion

# CA1032: Implement standard exception constructors
dotnet_diagnostic.CA1032.severity = suggestion

# SA1111: Closing parenthesis should be on line of last parameter
dotnet_diagnostic.SA1111.severity = suggestion

# SA1502: Element should not be on a single line
dotnet_diagnostic.SA1502.severity = suggestion

# CA2002: Do not lock on objects with weak identity
dotnet_diagnostic.CA2002.severity = suggestion

# IDE0058: Expression value is never used
csharp_style_unused_value_expression_statement_preference = discard_variable:suggestion

# SA1519: Braces should not be omitted from multi-line child statement
dotnet_diagnostic.SA1519.severity = suggestion

# SA1113: Comma should be on the same line as previous parameter
dotnet_diagnostic.SA1113.severity = suggestion

# SA1115: Parameter should follow comma
dotnet_diagnostic.SA1115.severity = suggestion

# SA1107: Code should not contain multiple statements on one line
dotnet_diagnostic.SA1107.severity = suggestion

# CS0472: The result of the expression is always the same since a value of this type is never equal to 'null'
dotnet_diagnostic.CS0472.severity = suggestion

# SA1100: Do not prefix calls with base unless local implementation exists
dotnet_diagnostic.SA1100.severity = suggestion

# SA1000: Keywords should be spaced correctly
dotnet_diagnostic.SA1000.severity = suggestion

# CA1055: URI-like return values should not be strings
dotnet_diagnostic.CA1055.severity = suggestion

# SA1027: Use tabs correctly
dotnet_diagnostic.SA1027.severity = suggestion

# CA2211: Non-constant fields should not be visible
dotnet_diagnostic.CA2211.severity = suggestion

# SA1211: Using alias directives should be ordered alphabetically by alias name
dotnet_diagnostic.SA1211.severity = suggestion

# SA1311: Static readonly fields should begin with upper-case letter
dotnet_diagnostic.SA1311.severity = suggestion

# CA1010: Generic interface should also be implemented
dotnet_diagnostic.CA1010.severity = suggestion

# SA1615: Element return value should be documented
dotnet_diagnostic.SA1615.severity = suggestion

# SA1606: Element documentation should have summary text
dotnet_diagnostic.SA1606.severity = suggestion

# SA1614: Element parameter documentation should have text
dotnet_diagnostic.SA1614.severity = suggestion

# CA1064: Exceptions should be public
dotnet_diagnostic.CA1064.severity = suggestion

# CS0436: Type conflicts with imported type
dotnet_diagnostic.CS0436.severity = suggestion

# CS0162: Unreachable code detected
dotnet_diagnostic.CS0162.severity = suggestion

# SA1011: Closing square brackets should be spaced correctly
dotnet_diagnostic.SA1011.severity = suggestion

# CA1044: Properties should not be write only
dotnet_diagnostic.CA1044.severity = suggestion

# SA1002: Semicolons should be spaced correctly
dotnet_diagnostic.SA1002.severity = suggestion

# CA1721: Property names should not match get methods
dotnet_diagnostic.CA1721.severity = suggestion

# SA1127: Generic type constraints should be on their own line
dotnet_diagnostic.SA1127.severity = suggestion

# SA1205: Partial elements should declare an access modifier
dotnet_diagnostic.SA1205.severity = suggestion

# CA5379: Rfc2898DeriveBytes might be using a weak hash algorithm. Use SHA256, SHA384, or SHA512 to create a strong key from a password.
dotnet_diagnostic.CA5379.severity = suggestion

# CA5373: Call to obsolete key derivation function
dotnet_diagnostic.CA5373.severity = suggestion

# CA1823: Unused field 'InternalDateTimeFormat'
dotnet_diagnostic.CA1823.severity = suggestion

# CA5351: GetMD5Hash uses a broken cryptographic algorithm MD5
dotnet_diagnostic.CA5351.severity = suggestion

# SA1110: Opening parenthesis or bracket should be on declaration line.
dotnet_diagnostic.SA1110.severity = suggestion

# CA1836: Prefer 'IsEmpty' over 'Count' to determine whether the object contains or not any items
dotnet_diagnostic.CA1836.severity = suggestion

# CA1810: Initialize all static fields when those fields are declared and remove the explicit static constructor
dotnet_diagnostic.CA1810.severity = suggestion

# CA1000: Do not declare static members on generic types
dotnet_diagnostic.CA1000.severity = suggestion

# CA1825: Avoid unnecessary zero-length array allocations.  Use Array.Empty<>() instead.
dotnet_diagnostic.CA1825.severity = suggestion

# SA1501: Statement should not be on a single line
dotnet_diagnostic.SA1501.severity = suggestion

# SA1002: Semicolons should be followed by a space.
dotnet_diagnostic.SA1002.severity = suggestion

# SA1408: Conditional expressions should declare precedence
dotnet_diagnostic.SA1408.severity = suggestion

# SA1123: Region should not be located within a code element.
dotnet_diagnostic.SA1123.severity = suggestion

# SA1015: Closing generic bracket should be followed by a space.
dotnet_diagnostic.SA1015.severity = suggestion

# CA1065: creates an exception of type Exception, an exception type that should not be raised in a property. If this exception instance might be raised, use a different exception type, convert this property into a method, or change this property's logic so that it no longer raises an exception.
dotnet_diagnostic.CA1065.severity = suggestion

# SA1014: Opening generic brackets should not be preceded by a space.
dotnet_diagnostic.SA1014.severity = suggestion

# SA1216: Using static directives should be placed at the correct location.
dotnet_diagnostic.SA1216.severity = suggestion

# SA1206: The 'public' modifier should appear before 'static'
dotnet_diagnostic.SA1206.severity = suggestion

# CA1710: Rename it to end in 'Exception'
dotnet_diagnostic.CA1710.severity = suggestion

# CA1001: Type owns disposable field(s) but is not disposable
dotnet_diagnostic.CA1001.severity = suggestion

# CA1827: Count() is used where Any() could be used instead to improve performance
dotnet_diagnostic.CA1827.severity = suggestion

# SA1130: Use lambda syntax
dotnet_diagnostic.SA1130.severity = suggestion

# CA1063: Modify so that it calls Dispose(true), then calls GC.SuppressFinalize on the current object instance ('this'), and then returns
dotnet_diagnostic.CA1063.severity = suggestion

# CA1816: Change to call GC.SuppressFinalize(object). This will prevent derived types that introduce a finalizer from needing to re-implement 'IDisposable' to call it.
dotnet_diagnostic.CA1816.severity = suggestion

# CS0414: The field is assigned but its value is never used
dotnet_diagnostic.CS0414.severity = suggestion

# CA1018: Specify AttributeUsage
dotnet_diagnostic.CA1018.severity = suggestion

# SA1314: Type parameter names should begin with T
dotnet_diagnostic.SA1314.severity = suggestion

# CA2214: Do not call overridable methods in constructors
dotnet_diagnostic.CA2214.severity = suggestion

# SA1022: Positive sign should be preceded by a space.
dotnet_diagnostic.SA1022.severity = suggestion

# SA1407: Arithmetic expressions should declare precedence
dotnet_diagnostic.SA1407.severity = suggestion

