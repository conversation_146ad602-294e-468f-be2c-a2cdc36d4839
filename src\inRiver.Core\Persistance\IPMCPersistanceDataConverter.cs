namespace inRiver.Core.Persistance
{
    using System;
    using inRiver.Core.Objects;
    using inRiver.iPMC.Persistance;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Converters;
    using Newtonsoft.Json.Linq;

    public static class IPMCPersistanceDataConverter
    {
        public static readonly JsonConverter JsonDateTimeConverter = new IsoDateTimeConverter()
        {
            DateTimeFormat = PersistanceEntity.InternalDateTimeFormat
        };

        public static readonly JsonConverter[] JsonConverters = new JsonConverter[]
        {
            JsonDateTimeConverter,
            new ObjectToStringJsonConverter(),
            new ToRemoteFieldJsonConverter(),
            new ToIPMCFieldJsonConverter()
        };

        public static T ConvertTo<T>(object inputObject)
        {
            var outputObject = default(T);

            if (inputObject != null)
            {
                var jsonString = JsonConvert.SerializeObject(inputObject, JsonConverters);
                outputObject = JsonConvert.DeserializeObject<T>(jsonString, JsonConverters);
            }

            return outputObject;
        }
    }

    /**
     * To convert object into string.
     * Use case:  LocaleString object (in Field.Data) to a string field (in DTOField.Data)
     */
    public class ObjectToStringJsonConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(string);
        }

        public override bool CanWrite
        {
            get { return false; }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
                return (object)null;

            var jtoken = JToken.Load(reader);
            return jtoken.ToString();
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }
    }

    public class ToIPMCFieldJsonConverter : AbstractFieldCreatorJsonConverter<Field, LocaleString>
    {
        public override string GetDataType(Field fieldInstance)
        {
            return fieldInstance.DataType;
        }

        public override object GetData(Field fieldInstance)
        {
            return fieldInstance.Data;
        }

        public override bool GetExpressionSupport(Field fieldInstance)
        {
            var expressionSupport = fieldInstance.FieldType?.ExpressionSupport;
            return expressionSupport ?? false;
        }

        public override void AssignData(Field fieldInstance, object stronglyTypedData)
        {
            fieldInstance.Data = stronglyTypedData;
        }
    }

    public class ToRemoteFieldJsonConverter : AbstractFieldCreatorJsonConverter<Remoting.Objects.Field, Remoting.Objects.LocaleString>
    {
        public override string GetDataType(Remoting.Objects.Field fieldInstance)
        {
            return fieldInstance.FieldType?.DataType;
        }

        public override object GetData(Remoting.Objects.Field fieldInstance)
        {
            return fieldInstance.Data;
        }

        public override bool GetExpressionSupport(Remoting.Objects.Field fieldInstance)
        {
            var expressionSupport = fieldInstance.FieldType?.ExpressionSupport;
            return expressionSupport ?? false;
        }

        public override void AssignData(Remoting.Objects.Field fieldInstance, object stronglyTypedData)
        {
            fieldInstance.Data = stronglyTypedData;
        }
    }

    public abstract class AbstractFieldCreatorJsonConverter<T, LOCALE_STRING> : JsonConverter
    {
        public abstract string GetDataType(T fieldInstance);

        public abstract object GetData(T fieldInstance);

        public abstract bool GetExpressionSupport(T fieldInstance);

        public abstract void AssignData(T fieldInstance, object stronglyTypedData);

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
                return (object)null;

            var jToken = JToken.Load(reader);

            if (jToken == null)
            {
                return null;
            }

            var field = JsonConvert.DeserializeObject<T>(jToken.ToString(), IPMCPersistanceDataConverter.JsonDateTimeConverter);

            var dataType = this.GetDataType(field);
            var fieldData = this.GetData(field);
            var expressionSupport = this.GetExpressionSupport(field);
            var stronglyTypedFieldData = ConvertToStronglyTypedData(dataType, fieldData, expressionSupport);


            AssignData(field, stronglyTypedFieldData);

            return field;
        }

        private object ConvertToStronglyTypedData(string dataType, object originalData, bool expressionSupport)
        {
            var result = originalData;

            if (originalData == null)
                return result;

            if (Remoting.Util.Utility.StringIsInriverExpression(expressionSupport, originalData as string))
            {
                return result;
            }

            if (dataType == DataType.Boolean)
            {
                result = JsonConvert.DeserializeObject<bool>(originalData.ToString().ToLower());
            }
            else if (dataType == DataType.DateTime)
            {
                result = DateTime.Parse(originalData.ToString());
            }
            else if (dataType == DataType.Double)
            {
                result = JsonConvert.DeserializeObject<double>(originalData.ToString());
            }
            else if (dataType == DataType.LocaleString)
            {
                result = JsonConvert.DeserializeObject<LOCALE_STRING>(originalData.ToString());
            }
            else if (dataType == DataType.Integer)
            {
                result = JsonConvert.DeserializeObject<int>(originalData.ToString());
            }
            else if (dataType == DataType.File)
            {
                result = JsonConvert.DeserializeObject<int>(originalData.ToString());
            }

            return result;
        }

        public override bool CanConvert(Type objectType)
        {
            return (objectType == typeof(T));
        }

        public override bool CanWrite
        {
            get { return false; }
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }
    }
}
