namespace inRiver.Server.UnitTests.ExcelImport
{
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Server.Helpers;
    using Xunit;

    public class ExcelImportRepositoryTests
    {
        private static readonly List<FileImportColumnModel> Columns = new List<FileImportColumnModel>();

        [Theory]
        [InlineData("{'number': 1.1}", "En", "1.1")]
        [InlineData("{'number': 1.1}", "Sv", "1,1")]
        [InlineData("{'number': 1.1}", "", "1.1")]
        [InlineData("{'number': 1.1}", null, "1.1")]
        [InlineData("{'integer_number': 1}", "En", "1")]
        [InlineData("{'integer_number': 1}", "Sv", "1")]
        [InlineData("{'integer_number': 1}", "", "1")]
        [InlineData("{'integer_number': 1}", null, "1")]
        [InlineData("{'number_with_zeros': 0.01}", "En", "0.01")]
        [InlineData("{'number_with_zeros': 0.01}", "Sv", "0,01")]
        [InlineData("{'number_with_zeros': 0.01}", "", "0.01")]
        [InlineData("{'number_with_zeros': 0.01}", null, "0.01")]
        public void When_ValueIsInterpretedAsDecimalNumber_Then_DecimalSeparatorIsTakenFromCultureInModel(string json, string language, string expectedResult)
        {
            // Arrange
            var configurationModel = new FileImportConfigurationModel()
            {
                CurrentLanguage = language,
                Columns = Columns
            };

            // Act
            var columnValuePairs = ExcelImportHelper.CreateColumnValuePairs(json, configurationModel);

            // Assert
            Assert.Equal(expectedResult, columnValuePairs.First().Value);
        }

        [Theory]
        [InlineData("{'comma_string':'1,1'}", "En", "1,1")]
        [InlineData("{'comma_string':'1,1'}", "Sv", "1,1")]
        [InlineData("{'comma_string':'1,1'}", "", "1,1")]
        [InlineData("{'comma_string':'1,1'}", null, "1,1")]
        [InlineData("{'dot_string':'1.1'}", "En", "1.1")]
        [InlineData("{'dot_string':'1.1'}", "Sv", "1.1")]
        [InlineData("{'dot_string':'1.1'}", "", "1.1")]
        [InlineData("{'dot_string':'1.1'}", null, "1.1")]
        [InlineData("{'comma_string_with_zeros':'0,01'}", "En", "0,01")]
        [InlineData("{'comma_string_with_zeros':'0,01'}", "Sv", "0,01")]
        [InlineData("{'comma_string_with_zeros':'0,01'}", "", "0,01")]
        [InlineData("{'comma_string_with_zeros':'0,01'}", null, "0,01")]
        [InlineData("{'dot_string_with_zeros':'0.01'}", "En", "0.01")]
        [InlineData("{'dot_string_with_zeros':'0.01'}", "Sv", "0.01")]
        [InlineData("{'dot_string_with_zeros':'0.01'}", "", "0.01")]
        [InlineData("{'dot_string_with_zeros':'0.01'}", null, "0.01")]
        [InlineData("{'any_string': '0123a.0'}", "En", "0123a.0")]
        [InlineData("{'any_string': '0123a.0'}", "Sv", "0123a.0")]
        [InlineData("{'any_string': '0123a.0'}", "", "0123a.0")]
        [InlineData("{'any_string': '0123a.0'}", null, "0123a.0")]
        [InlineData("{'dot_starting_string':'.1'}", "En", ".1")]
        [InlineData("{'dot_starting_string':'.1'}", "Sv", ".1")]
        [InlineData("{'spaced_number_string':' 1'}", "En", " 1")]
        [InlineData("{'spaced_number_string':' 1'}", "Sv", " 1")]
        [InlineData("{'negative_number_string':'-1'}", "En", "-1")]
        [InlineData("{'negative_number_string':'-1'}", "Sv", "-1")]
        [InlineData("{'zero_starting_string':'01'}", "En", "01")]
        [InlineData("{'zero_starting_string':'01'}", "Sv", "01")]
        [InlineData("{'zero_string':'0'}", "En", "0")]
        [InlineData("{'zero_string':'0'}", "Sv", "0")]
        [InlineData("{'zero_after_decimal_string':'1.0'}", "En", "1.0")]
        [InlineData("{'zero_after_decimal_string':'1.0'}", "Sv", "1.0")]
        [InlineData("{'double_zero_after_decimal_string':'1.00'}", "En", "1.00")]
        [InlineData("{'double_zero_after_decimal_string':'1.00'}", "Sv", "1.00")]
        public void When_ValueIsNotInterpretedAsDecimalNumber_Then_LeaveValueUnchanged(string json, string language, string expectedResult)
        {
            // Arrange
            var configurationModel = new FileImportConfigurationModel()
            {
                CurrentLanguage = language,
                Columns = Columns
            };

            // Act
            var columnValuePairs = ExcelImportHelper.CreateColumnValuePairs(json, configurationModel);

            // Assert
            Assert.Equal(expectedResult, columnValuePairs.First().Value);
        }
    }
}
