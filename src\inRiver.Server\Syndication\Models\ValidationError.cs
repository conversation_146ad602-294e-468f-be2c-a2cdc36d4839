namespace inRiver.Server.Syndication.Models
{
    using inRiver.Server.Syndication.Constants;

    public abstract class ValidationError
    {
        public string FieldId { get; }

        public string ErrorType { get; }

        public int? FormatFieldId { get; }

        protected ValidationError(ValidationErrorTypes type, string fieldId, int? formatFieldId)
        {
            this.ErrorType = type.Value;
            this.FieldId = fieldId;
            this.FormatFieldId = formatFieldId;
        }
    }
}
