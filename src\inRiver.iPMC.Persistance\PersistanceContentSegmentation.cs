namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Data;
    using System.Data.SqlClient;
    using System.Threading.Tasks;
    using inRiver.Log;

    public class PersistanceContentSegmentation : BasePersistance, IPersistanceContentSegmentation
    {
        public PersistanceContentSegmentation(
            String connectionString, 
            ICommonLogging logInstance,
            IContentSegmentPermissionProvider contentSegmentProvider)

            : base(connectionString, logInstance, contentSegmentProvider)
        {
        }

        public Segment GetSegment(int id)
        {
            Segment contentSegmentation = null;
            using (var conn = new SqlConnection(ConnectionString))
            {
                try
                {
                    var cmd = conn.CreateCommand();
                    cmd.CommandText = "SELECT ID, SegmentName, SegmentDescription FROM [dbo].[ContentSegmentation] WHERE [ID] = @Id;";
                    cmd.Parameters.AddWithValue("@Id", id);
                    conn.Open();

                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            contentSegmentation = new Segment
                            {
                                Id = reader.GetInt32(0),
                                Name = reader.GetString(1),
                                Description = reader.GetString(2)
                            };
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (conn.State != ConnectionState.Closed)
                    {
                        conn.Close();
                    }

                    LogInstance.Error($"An unexpected error occured when getting ContentSegmentation {id}", ex, string.Empty, string.Empty);
                }
            }
            return contentSegmentation;
        }

        public async Task<Segment> GetSegmentAsync(int contentSegmentationId)
        {
            Segment contentSegmentation = null;
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    await using var command = connection.CreateCommand();
                    command.CommandText = "SELECT ID, SegmentName, SegmentDescription FROM [dbo].[ContentSegmentation] WHERE [ID] = @Id;";
                    command.Parameters.AddWithValue("@Id", contentSegmentationId);

                    await connection.OpenAsync();

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            contentSegmentation = new Segment
                            {
                                Id = reader.GetInt32(0),
                                Name = reader.GetString(1),
                                Description = reader.GetString(2)
                            };
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogInstance.Error($"An unexpected error occured when getting ContentSegmentation {contentSegmentationId}", ex, string.Empty, string.Empty);
                }
            }

            return contentSegmentation;
        }
    }
}
