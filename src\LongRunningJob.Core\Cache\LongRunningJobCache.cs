namespace LongRunningJob.Core.Cache
{
    using System;
    using inRiver.Core.Redis;
    using Inriver.StackEssentials.Config;
    using StackExchange.Redis;

    public class LongRunningJobCache : RedisCache
    {
        private const string JobIsCancelledCacheKeySuffix = "JobIsCancelled";

        public LongRunningJobCache()
            : base(Connection)
        {
        }

#pragma warning disable CS0618 // Cannot use DI as this class is used by LRJ Actor where we don't have dependency injection
        private static ConnectionMultiplexer Connection => new Lazy<ConnectionMultiplexer>(()
            => ConnectionMultiplexer.Connect(StackConfig.Instance.RedisConnectionString.UnScramble())).Value;
#pragma warning restore CS0618

        public void SetJobCancelledState(string jobActorId)
            => this.Add(GetJobIsCancelledCacheKey(jobActorId), "Cancelled");

        public void DeleteJobCancelledState(string jobActorId)
            => this.Remove(GetJobIsCancelledCacheKey(jobActorId));

        public bool JobIsCancelled(string jobActorId)
        {
            string isCancelled = this.Get(GetJobIsCancelledCacheKey(jobActorId));
            return !string.IsNullOrWhiteSpace(isCancelled);
        }

        private static string GetJobIsCancelledCacheKey(string jobActorId) => $"{jobActorId}-{JobIsCancelledCacheKeySuffix}";
    }
}
