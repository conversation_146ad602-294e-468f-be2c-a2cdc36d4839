namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Api.Data.Client;
    using inRiver.Core.Util;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Util;
    using inRiver.Server.DataAccess;
    using Newtonsoft.Json;

    /// <summary>
    /// Documentation.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public override DtoField GetField(int entityId, string fieldTypeId)
            => this.GetFieldsInternal(entityId, new List<string> { fieldTypeId }).FirstOrDefault();

        public override List<DtoField> GetFieldsForEntity(DtoEntity entity)
            => this.GetFieldsInternal(entity.Id, null);

        public override List<DtoField> GetFields(int entityId, List<string> fieldTypeIds)
        {
            if (fieldTypeIds != null)
            {
                return this.GetFieldsInternal(entityId, fieldTypeIds);
            }

            return null;
        }

        public override object GetFieldValue(int entityId, string fieldTypeId)
        {
            var data = this.GetFieldsInternal(entityId, new List<string>() { fieldTypeId }).FirstOrDefault();

            if (data != null)
            {
                return FieldDataStringToObject(data.Data, data.DataType);
            }

            return null;
        }

        public override Task<object> GetFieldValueAsync(int entityId, string fieldTypeId)
        {
            var data = this.GetFieldsInternal(entityId, new List<string>() { fieldTypeId }).FirstOrDefault();

            return data != null
                ? Task.FromResult(FieldDataStringToObject(data.Data, data.DataType))
                : null;
        }

        public override List<Field> GetFullFieldsForEntity(Entity entity)
        {
            var allFieldTypesForEntityType = this.GetFieldTypesForEntityType(entity.EntityType.Id);
            var dtoFields = this.GetFieldsInternal(entity.Id, allFieldTypesForEntityType.Select(x => x.Id).ToList());
            return dtoFields.Select(x => DtoFieldToField(x, allFieldTypesForEntityType)).ToList();
        }

        public override List<DtoField> GetAllFieldsByFieldType(string fieldTypeId)
            => this.GetFieldsInternal(null, new List<string>() { fieldTypeId });

        public override void AddFields(List<Field> fields)
            => this.UpdateFields(fields);

        public override void UpdateFields(List<Field> fields)
        {
            var dtoFields = fields.ConvertAll(new Converter<Field, DtoField>(FieldToDtoField));
            InRiverDataApiClient.UpsertFields(
                this.GetAuthInfo(),
                dtoFields);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }

        public override List<string> GetAllFieldValuesForField(string fieldTypeId)
            => InRiverDataApiClient.GetFieldvaluesForFieldType(this.GetAuthInfo(), fieldTypeId, false);

        public override List<string> GetAllFieldValuesForFieldCaseSensitive(string fieldTypeId)
            => InRiverDataApiClient.GetFieldvaluesForFieldType(this.GetAuthInfo(), fieldTypeId, true);

        public override List<DtoTaskCategory> GetTaskCategoriesAssignedToUser(string username, int maxAmount)
            => InRiverDataApiClient.GetTasksForUser(this.GetAuthInfo(), username, string.Empty, maxAmount, false);

        public override List<DtoTaskCategory> GetTaskCategoriesCreatedByUser(string username, int maxAmount)
            => InRiverDataApiClient.GetTasksForUser(this.GetAuthInfo(), username, string.Empty, maxAmount, true);

        public override List<DtoTaskCategory> GetTasksByUserAndGroup(string username, string groupId, int maxAmount)
            => InRiverDataApiClient.GetTasksForUser(this.GetAuthInfo(), username, groupId, maxAmount, false);

        private static DtoField FieldToDtoField(Field f)
        {
            return f == null ?
                null :
                new DtoField
                {
                    EntityId = f.EntityId,
                    FieldTypeId = f.FieldType.Id,
                    DataType = f.FieldType.DataType,
                    Revision = f.Revision,
                    LastModified = f.LastModified.ToString("s", CultureInfo.InvariantCulture),
                    Data = FieldDataToString(f)
                };
        }

        private static Field DtoFieldToField(DtoField f, IList<FieldType> fieldTypes)
        {
            return f == null ?
                null :
                new Field
                {
                    EntityId = f.EntityId,
                    FieldType = fieldTypes.First(x => x.Id == f.FieldTypeId),
                    Revision = f.Revision,
                    LastModified = f.LastModified != null ? DateTime.Parse(f.LastModified, CultureInfo.InvariantCulture) : default,
                    Data = FieldDataStringToObject(f.Data, fieldTypes.First(x => x.Id == f.FieldTypeId).DataType)
                };
        }

        private static string FieldDataToString(Field f)
        {
            if (f.Data == null || f.IsEmpty())
            {
                return null;
            }
            else if (Utility.StringIsInriverExpression(f.FieldType.ExpressionSupport, f.Data as string))
            {
                return f.Data as string;
            }

            switch (f.FieldType.DataType)
            {
                case "LocaleString":
                    return JsonConvert.SerializeObject(f.Data);
                case "DateTime":
                    return Convert.ToDateTime(f.Data).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                case "Double":
                    return ((double)f.Data).ToString(CultureInfo.InvariantCulture);
                case "Boolean":
                case "CVL":
                case "Integer":
                case "String":
                case "Xml":
                default:
                    return f.Data.ToString();
            }
        }

        private static object FieldDataStringToObject(string data, string dataType)
        {
            if (data == null)
            {
                return null;
            }

            switch (dataType)
            {
                case "CVL":
                case "String":
                case "Xml":
                    return data;
                case "DateTime":
                    return Convert.ToDateTime(data, CultureInfo.InvariantCulture);
                case "LocaleString":
                    return JsonConvert.DeserializeObject<LocaleString>(data);
                case "Boolean":
                    _ = bool.TryParse(data, out var b);
                    return b;
                case "Double":
                    _ = double.TryParse(data, out var d);
                    return d;
                case "Integer":
                case "File":
                    _ = int.TryParse(data, out var i);
                    return i;
                default:
                    return data;
            }
        }

        private List<DtoField> GetFieldsInternal(int? entityId, IList<string> fieldTypeIdsToFetch = null)
        {
            var batchSize = 1000;
            if (fieldTypeIdsToFetch == null || fieldTypeIdsToFetch.Count < batchSize)
            {
                return InRiverDataApiClient.GetFields(this.GetAuthInfo(), entityId, fieldTypeIdsToFetch);
            }

            var chunks = new List<List<string>>();
            var chunkCount = fieldTypeIdsToFetch.Count / batchSize;
            if (fieldTypeIdsToFetch.Count % batchSize > 0)
            {
                chunkCount++;
            }

            for (var i = 0; i < chunkCount; i++)
            {
                chunks.Add(fieldTypeIdsToFetch.Skip(i * batchSize).Take(batchSize).ToList());
            }

            var fieldBag = new ConcurrentBag<DtoField>();
            var result = Parallel.ForEach(
                chunks,
                new ParallelOptions { MaxDegreeOfParallelism = 5 },
                chunk => {
                    var cosmosData = InRiverDataApiClient.GetFields(this.GetAuthInfo(), entityId, chunk);
                    cosmosData.ForEach(e => fieldBag.Add(e));
                });

            var sortedResult = fieldTypeIdsToFetch.Join(
              fieldBag,
              fieldType => fieldType,
              bagField => bagField.FieldTypeId,
              (fieldType, bagField) => bagField);
            return sortedResult.ToList();
        }
    }
}
