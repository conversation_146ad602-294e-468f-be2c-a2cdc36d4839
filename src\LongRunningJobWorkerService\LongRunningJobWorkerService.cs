namespace LongRunningJobWorkerService
{
    using System;
    using System.Collections.Generic;
    using System.Fabric;
    using System.Fabric.Description;
    using System.Threading;
    using System.Threading.Tasks;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Models;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.DataContracts;
    using Microsoft.ServiceFabric.Services.Communication.Runtime;
    using Microsoft.ServiceFabric.Services.Runtime;
    using Serilog;
    using Serilog.Context;

    /// <summary>
    /// An instance of this class is created for each service instance by the Service Fabric runtime.
    /// </summary>
    internal sealed class LongRunningJobWorkerService : StatelessService
    {
        private readonly ILongRunningJobRepository longRunningJobRepository;
        private readonly TelemetryClient telemetryClient;
        private readonly IJobRunner jobRunner;
        private readonly JobContext jobContext;

        public LongRunningJobWorkerService(StatelessServiceContext context, ILongRunningJobRepository longRunningJobRepository, TelemetryClient telemetryClient, IJob<PERSON>un<PERSON> jobRunner, JobContext jobContext)
            : base(context)
        {
            this.longRunningJobRepository = longRunningJobRepository;
            this.telemetryClient = telemetryClient;
            this.jobRunner = jobRunner;
            this.jobContext = jobContext;
        }

        /// <summary>
        /// Optional override to create listeners (e.g., TCP, HTTP) for this service replica to handle client or user requests.
        /// </summary>
        /// <returns>A collection of listeners.</returns>
        protected override IEnumerable<ServiceInstanceListener> CreateServiceInstanceListeners() => new ServiceInstanceListener[0];

        /// <summary>
        /// This is the main entry point for your service instance.
        /// </summary>
        /// <param name="cancellationToken">Canceled when Service Fabric needs to shut down this service instance.</param>
        protected override async Task RunAsync(CancellationToken cancellationToken)
        {
            cancellationToken.Register(() => throw new OperationCanceledException());
            try
            {
                var job = await this.longRunningJobRepository.GetAsync(this.jobContext.JobId);
                using (LogContext.PushProperty("environment", $"{this.jobContext.CustomerSafename}/{this.jobContext.EnvironmentSafename}"))
                using (LogContext.PushProperty("jobId", this.jobContext.JobId))
                using (LogContext.PushProperty("jobType", job.JobType))
                using (var operation = this.telemetryClient.StartOperation<RequestTelemetry>(job.JobType))
                {
                    var isSuccessful = await this.jobRunner.RunAsync(job, cancellationToken);
                    operation.Telemetry.Success = isSuccessful;
                    if (!isSuccessful)
                    {
                        // See https://stackoverflow.com/a/62521792/1755724
                        // If we don't manually set a response code for unsuccessful request telemetry the Application Insights server seems to reject the item
                        operation.Telemetry.ResponseCode = "500";
                    }
                }
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                Log.Error(ex, "Error occurred before job was able to start");
            }
            finally
            {
                await this.telemetryClient.FlushAsync(cancellationToken);
                await this.DeleteSelfAsync(cancellationToken);
            }
        }

        // Shamelessly copied from https://stackoverflow.com/a/52884620/1755724
        private async Task DeleteSelfAsync(CancellationToken cancellationToken)
        {
            using (var client = new FabricClient())
            {
                await client.ServiceManager.DeleteServiceAsync(new DeleteServiceDescription(this.Context.ServiceName), TimeSpan.FromMinutes(1), cancellationToken);
            }
        }
    }
}
