namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;
    using System.Data;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Remoting.Query;

    public interface IPersistanceEntity
    {
        Entity AddEntity(Entity entity);

        List<Entity> AddEntities(List<Entity> entities);

        Entity GetEntity(int entityId);

        Task<object> GetFieldValueAsync(int entityId, string fieldTypeId);

        Task<Entity> GetEntityAsync(int entityId);

        Entity GetFullEntity(int id, EntityType entityType, bool includePendingDelete = false, bool ignoreSegmentCheck = false);

        int? GetEntityIdByUniqueValue(string fieldTypeId, string value);

        void ReCalculateDisplayValuesForEntity(int entityId, List<Field> updatedFields);

        void ReCalculateEntityMainPicture(int entityId, string entityTypeId);

        List<Entity> GetEntities(List<int> entityIds);

        Task<IEnumerable<Entity>> GetEntitiesAsync(IEnumerable<int> entityIds);

        Task<List<Entity>> GetEntitiesAsync(List<int> entityIds);

        List<Entity> GetEntitiesWithData(List<int> entityIds, CancellationToken cancellationToken);

        Task<List<Entity>> GetEntitiesWithDataAsync(List<int> entityIds, CancellationToken cancellationToken);

        DataTable GetEntitiesAsDataTable(List<int> entityIds, string entityType, List<FieldType> fields, ContentSegmentationEnum segmentationOption, CancellationToken cancellationToken);

        List<Field> GetFieldsForEntity(Entity entity, string fieldTypeId = null);

        public List<Field> GetFields(int entityId, List<string> fieldTypeIds);

        bool DeleteEntity(int id, string entityTypeId = null);

        void UpdateEntityFields(Entity entity);

        bool DoesEntityExist(FieldType fieldType, object value);

        List<int> Search(Criteria criteria, Join? joinOperator = null);

        List<int> SearchEntitiesByCriterions(Join joinOperator, List<Criteria> criteria);

        List<int> SearchEntity(SystemQuery generalCriteria, List<Criteria> fieldLevelCriteria, Join? joinOperator = null);

        bool SetSegmentForEntities(List<int> entityIds, int segmentId);

        IDictionary<int, IList<SyndicationRelatedEntityFieldValue>> GetRelatedEntityFields(
            string entityName,
            string field,
            string[] linkTypeIds,
            int[] entityLinkPosition,
            string language);
    }
}
