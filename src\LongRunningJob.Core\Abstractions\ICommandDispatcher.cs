namespace LongRunningJob.Core.Abstractions
{
    using System.Threading;
    using System.Threading.Tasks;

    public interface ICommandDispatcher<TResult>
        where TResult : IResult
    {
        ICommandDispatcher<TResult> RegisterHandler<TCommand>(ICommandHandler<TCommand, TResult> handler)
            where TCommand : ICommand;

        Task<TResult> DispatchAsync<TCommand>(TCommand command, CancellationToken cancellationToken)
            where TCommand : ICommand;
    }
}
