namespace inRiver.Validation
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Text.RegularExpressions;
    using System.Xml;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Util;
    using Newtonsoft.Json.Linq;

    public static class FieldDataValidation
    {
        public static bool ValueMatchesRegex(string regexPattern, string value)
        {
            if (!regexPattern.StartsWith("^"))
            {
                regexPattern = $"^{regexPattern}";
            }

            if (!regexPattern.EndsWith("$"))
            {
                regexPattern = $"{regexPattern}$";
            }

            var regex = new Regex(regexPattern, RegexOptions.None, TimeSpan.FromSeconds(2));
            return regex.IsMatch(value);
        }

        public static bool ValueMatchesDataType(string dataType, string value, bool expressionSupportEnabled)
        {
            if (value == null)
            {
                return true;
            }

            if (Utility.StringIsInriverExpression(expressionSupportEnabled, value) && Utility.DataTypeHasExpressionSupport(dataType))
            {
                return true;
            }

            if (dataType == DataType.String || dataType == DataType.String.ToLower())
            {
                return true;
            }

            if (dataType == DataType.CVL)
            {
                return true;
            }

            if (dataType == DataType.LocaleString)
            {
                return true;
            }

            if (dataType == DataType.Double)
            {
                return ValidateValueIsDouble(value);
            }

            if (dataType == DataType.Integer)
            {
                return ValidateValueIsInteger(value);
            }

            if (dataType == DataType.Boolean)
            {
                return ValidateValueIsBoolean(value);
            }

            if (dataType == DataType.DateTime)
            {
                return ValidateValueIsDateTime(value);
            }

            if (dataType == DataType.Xml)
            {
                return ValidateValueIsXml(value);
            }

            if (dataType == DataType.File)
            {
                return ValidateValueIsInteger(value);
            }

            return false;
        }

        public static bool VerifyCvlData(FieldType fieldType, string[] values, IReadOnlyDictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary)
        {
            if (values == null)
            {
                return true;
            }

            if (values.Count() > 1 && !fieldType.Multivalue)
            {
                return false;
            }

            if (values.GroupBy(x => x).Any(y => y.Count() > 1))
            {
                return false;
            }

            if (cvlValueDictionaryDictionary == null || !cvlValueDictionaryDictionary.ContainsKey(fieldType.CVLId))
            {
                return false;
            }
            
            var cvlValueDictionary = new Dictionary<string, CVLValue>(cvlValueDictionaryDictionary[fieldType.CVLId], StringComparer.OrdinalIgnoreCase);

            return values.Select(value => value.Trim())
                .Select(value => cvlValueDictionary.Any(keyValuePair => keyValuePair.Key.Equals(value, StringComparison.CurrentCultureIgnoreCase)) ? cvlValueDictionary[value] : null)
                .All(cvl => cvl != null);
        }

        public static bool VerifyCvlData(FieldType fieldType, string value, IReadOnlyDictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary)
        {
            if (value == null)
            {
                return true;
            }

            if (cvlValueDictionaryDictionary == null || !cvlValueDictionaryDictionary.ContainsKey(fieldType.CVLId))
            {
                return false;
            }

            var cvlValueDictionary = cvlValueDictionaryDictionary[fieldType.CVLId];

            return cvlValueDictionary.Any(keyValuePair => keyValuePair.Key.Equals(value, StringComparison.CurrentCultureIgnoreCase));
        }

        public static bool ValueMatchesDataType(string dataType, string value)
        {
            if (value == null)
            {
                return true;
            }

            if (dataType == DataType.String || dataType == DataType.String.ToLower())
            {
                return true;
            }

            if (dataType == DataType.CVL)
            {
                return true;
            }

            if (dataType == DataType.LocaleString)
            {
                return true;
            }

            if (dataType == DataType.Double)
            {
                return ValidateValueIsDouble(value);
            }

            if (dataType == DataType.Integer)
            {
                return ValidateValueIsInteger(value);
            }

            if (dataType == DataType.Boolean)
            {
                return ValidateValueIsBoolean(value);
            }

            if (dataType == DataType.DateTime)
            {
                return ValidateValueIsDateTime(value);
            }

            if (dataType == DataType.Xml)
            {
                return ValidateValueIsXml(value);
            }

            if (dataType == DataType.File)
            {
                return ValidateValueIsInteger(value);
            }

            return false;
        }

        public static bool VerifyCvlRelationships(FieldType fieldType, string value, IReadOnlyDictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary, Field parentField)
        {

            if (cvlValueDictionaryDictionary.TryGetValue(fieldType.CVLId, out var cvlValueDictionary) &&
                cvlValueDictionary.TryGetValue(value, out var cvlValue))
            {

                if (string.IsNullOrEmpty(cvlValue.ParentKey))
                {
                    return true;
                }

                var parentDataString = parentField?.Data?.ToString() ?? string.Empty;

                if (parentField.FieldType.Multivalue)
                {
                    var parentCVLValues = parentDataString.Split(";");
                    return parentCVLValues.Any(parentValue => string.Equals(parentValue, cvlValue.ParentKey));
                }
                else
                {
                    return string.Equals(parentDataString, cvlValue.ParentKey);
                }
            }

            return true;
        }

        public static bool VerifyCvlRelationships(FieldType fieldType, string[] values,
        IReadOnlyDictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary, Field parentField)
        {
            var parentDataString = parentField?.Data?.ToString() ?? string.Empty;

            if (parentField.FieldType.Multivalue)
            {
                var parentCVLKeys = parentDataString.Split(";");
                return values.All(value =>
                    cvlValueDictionaryDictionary.TryGetValue(fieldType.CVLId, out var cvlValueDictionary) &&
                    cvlValueDictionary.TryGetValue(value, out var cvlValue) &&
                    (string.IsNullOrEmpty(cvlValue.ParentKey) || parentCVLKeys.Any(parentKey => string.Equals(parentKey, cvlValue.ParentKey))));
            }
            else
            {
                return values.All(value =>
                    cvlValueDictionaryDictionary.TryGetValue(fieldType.CVLId, out var cvlValueDictionary) &&
                    cvlValueDictionary.TryGetValue(value, out var cvlValue) &&
                    (string.IsNullOrEmpty(cvlValue.ParentKey) || string.Equals(parentDataString, cvlValue.ParentKey)));
            }
        }


        private static bool ValidateValueIsXml(string data)
        {
            if (string.IsNullOrEmpty(data))
            {
                return true;
            }

            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(data);
            }
            catch
            {
                return false;
            }

            return true;
        }

        private static bool ValidateValueIsDateTime(string data)
        {
            if (string.IsNullOrEmpty(data))
            {
                return true;
            }

            Double dateDouble;
            bool isDouble = Double.TryParse(data, out dateDouble);
            if (isDouble && dateDouble > 0)
            {
                try
                {
                    DateTime dateTimeObj = DateTime.FromOADate(dateDouble);
                    return true;
                }
                catch (Exception e)
                {
                    return false;
                }
            }
            else if (!isDouble)
            {
                return DateTime.TryParse(data, out DateTime value);
            }

            return false;
        }

        private static bool ValidateValueIsBoolean(string data)
        {
            if (string.IsNullOrEmpty(data))
            {
                return true;
            }

            if (bool.TryParse(data, out bool value))
            {
                return true;
            }

            return data == "0" || data == "1";
        }

        private static bool ValidateValueIsInteger(string data)
        {
            if (string.IsNullOrEmpty(data))
            {
                return true;
            }

            return int.TryParse(data, out int value);
        }

        private static bool ValidateValueIsDouble(string data)
        {
            if (string.IsNullOrEmpty(data))
            {
                return true;
            }

            return double.TryParse(data.Replace(",", "."), NumberStyles.Any, CultureInfo.InvariantCulture, out double value);
        }
    }
}
