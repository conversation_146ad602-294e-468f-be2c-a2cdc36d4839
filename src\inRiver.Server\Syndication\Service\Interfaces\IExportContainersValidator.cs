namespace inRiver.Server.Syndication.Service.Interfaces
{
    using System.Collections.Generic;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Models;

    public interface IExportContainersValidator
    {
        ValidationResult Validate(IList<MapField> formatFileFields, IList<ExportContainer> exportContainers, bool isDynamicFormatFile);

        ValidationProgress GetUpdatedProgress(int batchSize, int currentBatch, int totalEntities, int currentEntities);

        ValidationProgress GetProgressWithError();
    }
}
