namespace LongRunningJob.Core.UnitTests
{
    using System.Threading;
    using System.Threading.Tasks;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Server.Repository;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.CommandHandlers;
    using LongRunningJob.Core.Commands;
    using LongRunningJob.Core.Models;
    using Xunit;

    public class ExcelExportHandlerTests
    {
        [Fact]
        public async Task HandleAsync_JobWithoutErrors_ShouldReturnFinishedState()
        {
            const bool hasErrors = false;
            var job = new LongRunningJob();
            var jobModel = new ExcelExportJobModel { ExcelExportModel = new ExcelExportModel() };
            var jobModelRepositoryFake = A.Fake<IJobModelRepository>();
            A.<PERSON>(() => jobModelRepositoryFake.GetJobModelAsync<ExcelExportJobModel>(A<int>.Ignored)).Returns(jobModel);
            var excelExportRepositoryFake = A.Fake<IExcelExportRepository>();
            A.CallTo(() => excelExportRepositoryFake.BatchExportExcelAsync(A<ExcelExportModel>.Ignored, 100, CancellationToken.None)).Returns(hasErrors);
            var excelExportRepositoryFactoryFake = A.Fake<IExcelExportRepositoryFactory>();
            A.CallTo(() => excelExportRepositoryFactoryFake.CreateAsync()).Returns(excelExportRepositoryFake);
            var environmentContextAccessorFake = A.Fake<IEnvironmentContextAccessor>();
            A.CallTo(() => environmentContextAccessorFake.EnvironmentContext).Returns(new EnvironmentContext(1, "cs", "es", "db", "st"));
            var environmentSettingsRepository = A.Fake<IEnvironmentSettingsRepository>();
            A.CallTo(() => environmentSettingsRepository.GetEnvironmentSettingAsync(A<string>.Ignored, A<string>.Ignored)).Returns(new EnvironmentSetting());

            var excelExportHandler = new ExcelExportHandler(
                jobModelRepositoryFake,
                excelExportRepositoryFactoryFake,
                A.Dummy<ILongRunningJobRepository>(),
                environmentSettingsRepository);

            var result = await excelExportHandler.HandleAsync(new ExcelExportCommand(job), CancellationToken.None);

            result.JobState.Should().Be(LongRunningJobsStatus.Finished);
        }

        [Fact]
        public async Task HandleAsync_JobWithErrors_ShouldReturnFinishedWithErrorsState()
        {
            const bool hasErrors = true;
            var job = new LongRunningJob();
            var jobModel = new ExcelExportJobModel { ExcelExportModel = new ExcelExportModel() };
            var jobModelRepositoryFake = A.Fake<IJobModelRepository>();
            A.CallTo(() => jobModelRepositoryFake.GetJobModelAsync<ExcelExportJobModel>(A<int>.Ignored)).Returns(jobModel);
            var excelExportRepositoryFake = A.Fake<IExcelExportRepository>();
            A.CallTo(() => excelExportRepositoryFake.BatchExportExcelAsync(A<ExcelExportModel>.Ignored, 100, CancellationToken.None)).Returns(hasErrors);
            var excelExportRepositoryFactoryFake = A.Fake<IExcelExportRepositoryFactory>();
            A.CallTo(() => excelExportRepositoryFactoryFake.CreateAsync()).Returns(excelExportRepositoryFake);
            var environmentContextAccessorFake = A.Fake<IEnvironmentContextAccessor>();
            A.CallTo(() => environmentContextAccessorFake.EnvironmentContext).Returns(new EnvironmentContext(1, "cs", "es", "db", "st"));
            var environmentSettingsRepository = A.Fake<IEnvironmentSettingsRepository>();
            A.CallTo(() => environmentSettingsRepository.GetEnvironmentSettingAsync(A<string>.Ignored, A<string>.Ignored)).Returns(new EnvironmentSetting());

            var excelExportHandler = new ExcelExportHandler(
                jobModelRepositoryFake,
                excelExportRepositoryFactoryFake,
                A.Dummy<ILongRunningJobRepository>(),
                environmentSettingsRepository);

            var result = await excelExportHandler.HandleAsync(new ExcelExportCommand(job), CancellationToken.None);

            result.JobState.Should().Be(LongRunningJobsStatus.FinishedWithErrors);
        }

        [Fact]
        public async Task HandleAsync_ShouldSetJobStateToRunning()
        {
            var job = new LongRunningJob();
            var jobModel = new ExcelExportJobModel { ExcelExportModel = new ExcelExportModel() };
            var jobModelRepositoryFake = A.Fake<IJobModelRepository>();
            A.CallTo(() => jobModelRepositoryFake.GetJobModelAsync<ExcelExportJobModel>(A<int>.Ignored)).Returns(jobModel);
            var longRunningJobRepositoryFake = A.Fake<ILongRunningJobRepository>();
            var excelExportRepositoryFactoryFake = A.Fake<IExcelExportRepositoryFactory>();
            A.CallTo(() => excelExportRepositoryFactoryFake.CreateAsync()).Returns(A.Dummy<IExcelExportRepository>());
            var environmentContextAccessorFake = A.Fake<IEnvironmentContextAccessor>();
            A.CallTo(() => environmentContextAccessorFake.EnvironmentContext).Returns(new EnvironmentContext(1, "cs", "es", "db", "st"));
            var environmentSettingsRepository = A.Fake<IEnvironmentSettingsRepository>();
            A.CallTo(() => environmentSettingsRepository.GetEnvironmentSettingAsync(A<string>.Ignored, A<string>.Ignored)).Returns(new EnvironmentSetting());

            var excelExportHandler = new ExcelExportHandler(
                jobModelRepositoryFake,
                excelExportRepositoryFactoryFake,
                longRunningJobRepositoryFake,
                environmentSettingsRepository);

            await excelExportHandler.HandleAsync(new ExcelExportCommand(job), CancellationToken.None);

            A.CallTo(() => longRunningJobRepositoryFake.UpdateStateAsync(A<int>.Ignored, LongRunningJobsStatus.Running, A<string>.Ignored)).MustHaveHappened();
        }
    }
}
