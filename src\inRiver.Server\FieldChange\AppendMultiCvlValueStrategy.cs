namespace inRiver.Server.FieldChange
{
    using System.Linq;
    using inRiver.Server.Repository;

    public class AppendMultiCvlValueStrategy : BaseMultiCvlValueStrategy
    {
        public AppendMultiCvlValueStrategy(IFieldRepository fieldRepository)
            : base(fieldRepository)
        {
        }

        public override string Calculate(string newValue, int entityId, string fieldTypeId)
        {
            var oldFieldValues = this.GetOldValues(entityId, fieldTypeId);
            var newFieldValues = this.ParseNewValues(newValue);
            var calculatedFieldValues = oldFieldValues.Concat(newFieldValues)
                .OrderBy(value => value)
                .Distinct();
            return this.ConvertToString(calculatedFieldValues);
        }
    }
}
