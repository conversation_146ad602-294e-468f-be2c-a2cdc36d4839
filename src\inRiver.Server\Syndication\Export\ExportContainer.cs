namespace inRiver.Server.Syndication.Export
{
    using System.Collections.Generic;
    using Newtonsoft.Json;

    public class ExportContainer
    {
        public int Id { get; set; }

        [JsonIgnore]
        public List<int> RelatedIds { get; set; }

        [JsonIgnore]
        public string Source { get; set; }

        public List<ExportField> Fields { get; set; }

        public List<ResourceExportField> ResourceExportFields { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether ResourceExportFields should be serialized
        /// This property is set to true in GetExportContainerFromEntity() to serialize ResourceExportFields when saving export containers to blobs in SyndicationRepository.GetExportContainers().
        /// After it is serialized, the property value is reset to default false value as it is ignored.
        /// When the next time export containers are being serialized in SyndicationRepository.GetExportResultAsync, ResourceExportFields is no longer serialized.
        /// </summary>
        [JsonIgnore]
        public bool ShouldSerializeResources { get; set; }

        // used by Newtonsoft (https://www.newtonsoft.com/json/help/html/conditionalproperties.htm)
        public bool ShouldSerializeResourceExportFields() => this.ShouldSerializeResources;
    }
}
