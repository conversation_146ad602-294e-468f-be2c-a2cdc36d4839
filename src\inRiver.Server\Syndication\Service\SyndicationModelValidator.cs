namespace inRiver.Server.Syndication.Service
{
    using System;
    using DocumentFormat.OpenXml;
    using inRiver.Configuration.Core.Repository;
    using inRiver.Core.Models.inRiver;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Service.Interfaces;

    public class SyndicationModelValidator : ISyndicationModelValidator
    {
        private const string EvaluateEnabledSettingsKey = "EVALUATE_ENABLED";

        private readonly IEnvironmentSettingsRepository environmentSettingsRepository;

        public SyndicationModelValidator(IEnvironmentSettingsRepository environmentSettingsRepository)
        {
            this.environmentSettingsRepository = environmentSettingsRepository;
        }

        public void Validate(RequestContext requestContext, SyndicationModel syndicationModel)
        {
            if (syndicationModel.RunDsaSyndication)
            {
                if (!syndicationModel.DsaMappingId.HasValue)
                {
                    throw new SyndicateException($"{nameof(syndicationModel.DsaMappingId)} can't be empty when running syndication in DSA mode");
                }

                EnvironmentSetting isEvaluateEnabledSetting = null;
                try
                {
                    isEvaluateEnabledSetting = this.environmentSettingsRepository.GetEnvironmentSetting(EvaluateEnabledSettingsKey, requestContext.EnvironmentId);
                }
                catch (Exception)
                {
                }

                if (isEvaluateEnabledSetting == null || !bool.TryParse(isEvaluateEnabledSetting.Value, out var isEvaluateEnabled) || !isEvaluateEnabled)
                {
                    throw new EvaluateIsDisabledException("Unable to run syndication in DSA mode when Evaluate is disabled");
                }
            }
        }
    }
}
