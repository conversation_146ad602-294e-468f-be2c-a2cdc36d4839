namespace inRiver.Server.Syndication.Script
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using inRiver.Core.Util;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Mapping;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    public class TransformationManager
    {
        private readonly RequestContext context;

        public TransformationManager(string json, RequestContext context)
        {
            this.TransformationJson = JsonConvert.DeserializeObject<TransformationJson>(json);
            this.context = context;
        }

        private TransformationJson TransformationJson { get; set; }

        public string[] Args => this.TransformationJson.Transformation.SelectMany(x => x.Function.Args).ToArray();

        public string[] Values => this.TransformationJson.Transformation.SelectMany(x => x.Function.Values).ToArray();

        public IList<object> GetValues(InRiverEntity entity, bool enableSKU, SkuInjector skuInjector, string skuId)
        {
            var values = new List<object>();

            this.AddToValueList(entity, enableSKU, skuInjector, skuId, this.Values, values);
            return values;
        }

        private void AddToValueList(InRiverEntity entity, bool enableSKU, SkuInjector skuInjector, string skuId, string[] data, List<object> values)
        {
            foreach (var value in data)
            {
                if (value == null)
                {
                    continue;
                }

                InRiverField found = entity.Fields.SingleOrDefault(x => x.FieldType.FieldTypeId == value);
                if (found != null)
                {
                    //Check if x.FieldType.DataType is localestring and if so change how value is added
                    if (found.FieldType.DataType == inRiver.Remoting.Objects.DataType.LocaleString)
                    {
                        if (data.Length == 3)
                        {
                            values.Add(GetValueFromFieldData(found, data.ElementAt(2)));
                            break;
                        }
                        else
                        {
                            this.context?.Log(Remoting.Log.LogLevel.Error, $"Sanity check failed: Expected a data structure with 3 elements, recieved: {data} (AddToValueList() in TransformationManager in iPMC.Common)");
                        }
                    }
                    values.Add(found?.Data);
                    continue;
                }

                var foundEntity = entity.RelatedEntities?.Where(ent => ent.Fields.Any(f => f.FieldType.FieldTypeId == value))
                    .FirstOrDefault();
                found = (foundEntity != null)
                    ? foundEntity.Fields.SingleOrDefault(x => x.FieldType.FieldTypeId == value)
                    : null;

                if (found != null)
                {
                    if (found.FieldType.DataType == inRiver.Remoting.Objects.DataType.LocaleString && found.Data != null)
                    {
                        LocaleString ls = (LocaleString)found.Data;
                        values.Add(ls[new CultureInfo(this.Values.ElementAt(2))]);
                        return;
                    }
                    else
                    {
                        values.Add(found.Data);
                    }

                    continue;
                }

                if (enableSKU)
                {
                    var valueFromSKU = GetValueFromSku(skuInjector, skuId, value);
                    if (!string.IsNullOrEmpty(valueFromSKU))
                    {
                        values.Add(valueFromSKU);
                    }

                    continue;
                }

                // constant
                values.Add(value);
            }
        }

        public static object GetTransformedMainValue(object mainValue)
        {
            if (mainValue is object[] valueArray)
            {
                // This is required to correctly handle Image Fields that are of type JArray.
                if (valueArray.All(value => value.GetType() == typeof(JArray)))
                {
                    return valueArray;
                }

                var valueString = string.Join(",", valueArray.Select(value => $"\"{value}\""));
                return "[" + valueString + "]";
            }

            return mainValue;
        }

        public static object GetLocaleStringMainValue(object mainValue, string language)
        {
            if (string.IsNullOrEmpty(language))
            {
                return null;
            }

            if (mainValue is object[] fieldDataArray)
            {
                var value = string.Empty;
                for (var i = 0; i < fieldDataArray.Length; i++)
                {
                    value += $"{(i > 0 ? ",\"" : "\"")}{((Dictionary<string, string>)fieldDataArray[i])[language]}\"";
                }

                return fieldDataArray.Length > 1 ? "[" + value + "]" : value;
            }

            return ((Dictionary<string, string>)mainValue)?[language];
        }

        public static object GetLocaleStringValue(
            InRiverField field,
            string fieldTypeId,
            string language,
            bool enableSKU,
            SkuInjector skuInjector,
            string skuId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return null;
            }

            if (field != null)
            {
                return GetValueFromFieldData(field, language);
            }

            if (enableSKU)
            {
                var valueFromSku = GetValueFromSku(skuInjector, skuId, fieldTypeId);
                if (!string.IsNullOrEmpty(valueFromSku))
                {
                    return valueFromSku;
                }
            }

            return null;
        }

        internal static bool IsFieldLocaleString(InRiverEntity entity, FoundField field)
        {
            if (field == null)
            {
                return false;
            }

            var foundInRiverField = GetInRiverField(entity, field.FieldTypeId);
            return IsFieldOfSpecifiedDataType(foundInRiverField, DataType.LocaleString);
        }

        internal static bool IsFieldOfSpecifiedDataType(InRiverField field, string dataType)
            => field?.FieldType.DataType.Equals(dataType, StringComparison.OrdinalIgnoreCase) ?? false;

        internal static InRiverField GetInRiverField(InRiverEntity entity, string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId) || entity == null)
            {
                return null;
            }

            var fieldsDictionary = entity.Fields
                .GroupBy(f => f.FieldType.FieldTypeId)
                .ToDictionary(x => x.Key, x => x.ToList());

            if (fieldsDictionary.TryGetValue(fieldTypeId, out var foundInRiverFieldTypes))
            {
                return foundInRiverFieldTypes.FirstOrDefault();
            }

            if (entity.RelatedEntities != null)
            {
                var relatedFieldsDictionary = entity.RelatedEntities.SelectMany(relatedEntity => relatedEntity.Fields)
                    .GroupBy(f => f.FieldType.FieldTypeId)
                    .ToDictionary(x => x.Key, x => x.ToList());

                if (relatedFieldsDictionary.TryGetValue(fieldTypeId, out var relatedInRiverFieldTypes))
                {
                    return relatedInRiverFieldTypes.FirstOrDefault();
                }
            }

            return null;
        }

        private static object GetValueFromFieldData(InRiverField field, string language)
        {
            if (field == null)
            {
                return null;
            }

            if (field.FieldType.DataType == DataType.LocaleString && field.Data != null && !string.IsNullOrEmpty(language))
            {
                var ls = (LocaleString)field.Data;
                return ls[new CultureInfo(language)];
            }

            return field.Data;
        }

        private static string GetValueFromSku(SkuInjector skuInjector, string skuId, string fieldName)
        {
            if (string.IsNullOrEmpty(skuId) || skuInjector == null)
            {
                return string.Empty;
            }

            return skuInjector.GetValueForSkuOutputFieldName(skuId, fieldName);
        }
    }
}
