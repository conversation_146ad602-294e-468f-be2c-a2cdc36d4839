﻿#region Generated Code
namespace inRiver.Core.Models.inRiver
{
#endregion
    
    public class Link
        : IIdentifierAsIntInterface
    {
        public int Id { get; set; }

        public string LinkTypeId { get; set; }

        public Entity Source { get; set; }

        public Entity Target { get; set; }

        public Entity LinkEntity { get; set; }

        public int Index { get; set; }

        public bool Inactive { get; set; }
    }
}
