namespace inRiver.Configuration.Core.Persistance
{
    using System.Data.SqlClient;
    using System.Linq;
    using Dapper;
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver;
    using inRiver.Log;

    public class EnvironmentSettingsPersistance : BaseConfigurationCorePersistance, IEnvironmentSettingsPersistance
    {
        public EnvironmentSettingsPersistance(
            string configConnectionString,
            string readonlyConfigConnectionString,
            ICommonLogging systemLogInstance,
            ApiCaller caller)
            : base(configConnectionString, readonlyConfigConnectionString, systemLogInstance, caller)
        {
        }

        public EnvironmentSetting GetEnvironmentSetting(string key, int? environmentId = null)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                var settings = connection.Query<EnvironmentSetting>("SELECT [EnvironmentId], [Name], [Value] FROM EnvironmentSettings WHERE Name = @name", new { name = key }).ToList();

                var environmentSpecificSetting = settings.SingleOrDefault(s => s.EnvironmentId == environmentId);
                return environmentSpecificSetting ?? settings.SingleOrDefault(s => !s.EnvironmentId.HasValue);
            }
        }
    }
}
