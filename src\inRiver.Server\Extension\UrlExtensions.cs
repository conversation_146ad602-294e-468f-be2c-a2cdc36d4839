namespace inRiver.Server.Extension
{
    using System;
    using System.Text.RegularExpressions;

    public static class UrlExtensions
    {
        private const string HttpAndHttpsProtocolPattern = @"^https?://";

        public static string CreateUrl(this string baseUrl, string requestPath)
        {
            if (string.IsNullOrEmpty(baseUrl))
            {
                throw new ArgumentNullException(nameof(baseUrl), "Base url is not specified.");
            }

            var path = string.Empty;
            if (string.IsNullOrEmpty(requestPath))
            {
                return $"{baseUrl}{path}";
            }

            var delimiter = !baseUrl.EndsWith('/') && !requestPath.StartsWith('/') ? "/" : string.Empty;
            path = $"{delimiter}{requestPath}";

            return $"{baseUrl}{path}";
        }

        public static void ValidateUrl(this string url, bool mustContainPathAndQuery)
        {
            if (!Regex.IsMatch(url, HttpAndHttpsProtocolPattern, RegexOptions.IgnoreCase))
            {
                url = $"{Uri.UriSchemeHttps}://{url}";
            }

            var isValidUrl = Uri.TryCreate(url, UriKind.Absolute, out var uri);
            if (!isValidUrl)
            {
                throw new ArgumentException("Invalid URL format.", nameof(url));
            }

            if (!uri.HasPathAndQuery())
            {
                throw new ArgumentException("Request path can't be empty.", nameof(url));
            }
        }

        private static bool HasPathAndQuery(this Uri uri)
            => !string.IsNullOrEmpty(uri.PathAndQuery) && uri.PathAndQuery != "/";
    }
}
