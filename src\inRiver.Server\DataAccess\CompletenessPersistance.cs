namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Data.SqlTypes;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Query;
    using inRiver.Server.Completeness;
    using inRiver.Server.Error;
    using inRiver.Server.Util;
    using Serilog;

    // ReSharper disable once InconsistentNaming
    [SuppressMessage("StyleCop.CSharp.NamingRules", "SA1300:ElementMustBeginWithUpperCaseLetter", Justification = "Reviewed. Suppression is OK here.")]
    public partial class inRiverPersistance
    {
        #region Completeness Definition

        public CompletenessDefinition GetCompletenessDefinition(int id)
        {
            List<CultureInfo> serverLanguages = this.GetAllLanguages();

            CompletenessDefinition definition = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Id], [Name], [EntityTypeId] FROM [CompletenessDefinition] WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            definition = new CompletenessDefinition();

                            SqlXml xml = reader.GetSqlXml(1);

                            definition.Id = reader.GetInt32(0);
                            definition.Name = Utilities.XmlToLocaleString(xml.Value, serverLanguages);
                            definition.EntityTypeId = reader.GetString(2);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Completeness Definition " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Completeness Definition " + id, ex);
                }
            }

            if (definition == null)
            {
                return null;
            }

            List<CompletenessGroup> groups = this.GetAllCompletenessGroupForDefinition(definition.Id);
            definition.GroupIds = new List<int>();
            foreach (CompletenessGroup group in groups)
            {
                definition.GroupIds.Add(group.Id);
            }

            return definition;
        }

        public int? GetCompletenessDefinitionIdForEntityType(string entityTypeId)
        {

            int? definitionId = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Id] FROM [CompletenessDefinition] WHERE EntityTypeId = @EntityTypeId";

                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            definitionId = reader.GetInt32(0);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Completeness Definition id for entity type " + entityTypeId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Completeness Definition id for entity type " + entityTypeId, ex);
                }
            }

            return definitionId;
        }

        #endregion

        #region Completeness Business Rules

        public List<CompletenessBusinessRule> GetAllCompletenessBusinessRules()
        {
            List<CultureInfo> serverLanguages = this.GetAllLanguages();
            List<CompletenessBusinessRule> rules = new List<CompletenessBusinessRule>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = @"SELECT r.Id, r.Name, r.Type, gr.Weight, gr.SortOrder, gr.CompletenessGroupId 
                                            FROM CompletenessGroupRules AS gr INNER JOIN CompletenessRule AS r ON gr.CompletenessRuleId = r.Id;";
                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            SqlXml xml = reader.GetSqlXml(1);
                            CompletenessBusinessRule rule = new CompletenessBusinessRule();
                            rule.Id = reader.GetInt32(0);
                            rule.Name = Utilities.XmlToLocaleString(xml.Value, serverLanguages);
                            rule.Type = reader.GetString(2);
                            rule.Weight = reader.GetInt32(3);
                            rule.SortOrder = reader.GetInt32(4);
                            rule.GroupIds = new List<int> { reader.GetInt32(5) };

                            rules.Add(rule);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Completeness Business Rules");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Completeness Business Rules", ex);
                }
            }

            foreach (CompletenessBusinessRule rule in rules)
            {
                rule.RuleSettings = this.GetAllCompletenessRuleSettingsForBusinessRule(rule.Id);
            }

            return rules;
        }

        public List<CompletenessBusinessRule> GetCompletenessBusinessRulesForGroup(int groupId)
        {
            List<CultureInfo> serverLanguages = this.GetAllLanguages();
            List<CompletenessBusinessRule> rules = new List<CompletenessBusinessRule>();
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = @"SELECT r.Id, r.Name, r.Type, gr.Weight, gr.SortOrder, gr.CompletenessGroupId 
                                            FROM CompletenessGroupRules AS gr INNER JOIN CompletenessRule AS r ON gr.CompletenessRuleId = r.Id 
                                            WHERE (gr.CompletenessGroupId = @GroupId);";
                    command.Parameters.AddWithValue("@GroupId", groupId);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            SqlXml xml = reader.GetSqlXml(1);
                            rules.Add(
                                new CompletenessBusinessRule
                                {
                                    Id = reader.GetInt32(0),
                                    Name = Utilities.XmlToLocaleString(xml.Value, serverLanguages),
                                    Type = reader.GetString(2),
                                    Weight = reader.GetInt32(3),
                                    SortOrder = reader.GetInt32(4),
                                    GroupIds = new List<int> { groupId },
                                    RuleSettings = this.GetAllCompletenessRuleSettingsForBusinessRule(reader.GetInt32(0))
                                });
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting completeness rules for group " + groupId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting completeness rules for group " + groupId, ex);
                }

                return rules;
            }
        }

        #endregion

        #region Completeness Groups

        public List<CompletenessGroup> GetAllCompletenessGroupForDefinition(int defintionId)
        {
            List<CultureInfo> serverLanguages = this.GetAllLanguages();
            List<CompletenessGroup> groups = new List<CompletenessGroup>();
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Id], [CompletenessDefinitionId], [Name], [Weight], [SortOrder] FROM [CompletenessGroup] WHERE CompletenessDefinitionId = @CompletenessDefinitionId";
                    command.Parameters.AddWithValue("@CompletenessDefinitionId", defintionId);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            CompletenessGroup group = new CompletenessGroup();

                            group.Id = reader.GetInt32(0);
                            group.CompletenessDefinitionId = reader.GetInt32(1);

                            SqlXml xml = reader.GetSqlXml(2);
                            group.Name = Utilities.XmlToLocaleString(xml.Value, serverLanguages);

                            group.Weight = reader.GetInt32(3);
                            group.SortOrder = reader.GetInt32(4);

                            group.RuleIds = new List<int>();
                            var rules = this.GetCompletenessBusinessRulesForGroup(group.Id);
                            foreach (CompletenessBusinessRule rule in rules)
                            {
                                group.RuleIds.Add(rule.Id);
                            }

                            groups.Add(group);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Completeness Groups");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Completeness Groups", ex);
                }
            }

            return groups;
        }

        public List<ShallowCompletenessGroup> GetShallowCompletenessGroupsForDefinition(int defintionId)
        {
            List<ShallowCompletenessGroup> groups = new List<ShallowCompletenessGroup>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText =
                        "SELECT [GroupId], [GroupWeight], [RuleId], [RuleWeight], [Type], [SettingsKey], [SettingsValue], [SettingsId] FROM [ViewCompletenessSettings] WHERE CompletenessDefinitionId = @CompletenessDefinitionId";
                    command.Parameters.AddWithValue("@CompletenessDefinitionId", defintionId);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            int groupId = reader.GetInt32(0);

                            ShallowCompletenessGroup group = groups.FirstOrDefault(cg => cg.GroupId == groupId);

                            if (group == null)
                            {
                                group = new ShallowCompletenessGroup();
                                group.GroupId = groupId;
                                group.Weight = reader.GetInt32(1);
                                group.Rules = new List<ShallowCompletenessRule>();

                                groups.Add(group);
                            }

                            int ruleId = reader.GetInt32(2);

                            ShallowCompletenessRule rule = group.Rules.FirstOrDefault(cr => cr.RuleId == ruleId);

                            if (rule == null)
                            {
                                rule = new ShallowCompletenessRule();
                                rule.RuleId = ruleId;
                                rule.Weight = reader.GetInt32(3);
                                rule.Type = reader.GetString(4);

                                rule.Settings = new List<CompletenessRuleSetting>();

                                group.Rules.Add(rule);
                            }

                            CompletenessRuleSetting setting = new CompletenessRuleSetting();

                            setting.Key = reader.GetString(5);
                            setting.Value = reader.IsDBNull(6) ? null : reader.GetString(6);
                            setting.BusinessRuleId = ruleId;
                            setting.Type = rule.Type;
                            setting.Id = reader.GetInt32(7);

                            rule.Settings.Add(setting);
                        }

                        reader.Close();

                        connection.Close();
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Completeness Groups");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when getting all Completeness Groups",
                        ex);
                }
            }

            return groups;
        }

        public bool GroupForEntityCompleted(int entityId, int completenessGroupId)
        {
            bool complete = false;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Complete] FROM [CompletenessEntityState] WHERE [EntityId] = @EntityId AND GroupId = @GroupId AND RuleId IS NULL";
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@GroupId", completenessGroupId);
                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            complete = reader.GetBoolean(0);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    string message = $"An unexpected error occurred when checking if group {completenessGroupId} is complete for entity {entityId}";

                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }

            return complete;
        }

        public async Task<bool> GroupForEntityCompletedAsync(int entityId, int completenessGroupId)
        {
            var complete = false;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    await using var command = connection.CreateCommand();
                    command.CommandText = "SELECT [Complete] FROM [CompletenessEntityState] WHERE [EntityId] = @EntityId AND GroupId = @GroupId AND RuleId IS NULL";
                    _ = command.Parameters.AddWithValue("@EntityId", entityId);
                    _ = command.Parameters.AddWithValue("@GroupId", completenessGroupId);

                    await connection.OpenAsync();

                    using var reader = await command.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        complete = reader.GetBoolean(0);
                    }
                }
                catch (Exception ex)
                {
                    var message = $"An unexpected error occurred when checking if group {completenessGroupId} is complete for entity {entityId}";

                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }

            return complete;
        }

        #endregion

        #region Completeness Rule Settings

        public List<CompletenessRuleSetting> GetAllCompletenessRuleSettingsForBusinessRule(int ruleId)
        {
            List<CompletenessRuleSetting> settings = new List<CompletenessRuleSetting>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT [Id], [SettingsKey], [SettingsValue], [Type], [CompletenessRuleId] FROM [CompletenessRuleSettings] WHERE CompletenessRuleId = @CompletenessRuleId";
                    command.Parameters.AddWithValue("@CompletenessRuleId", ruleId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            CompletenessRuleSetting setting = new CompletenessRuleSetting
                            {
                                Id = reader.GetInt32(0),
                                Key = reader.GetString(1)
                            };

                            if (!reader.IsDBNull(2))
                            {
                                setting.Value = reader.GetString(2);
                            }

                            if (!reader.IsDBNull(3))
                            {
                                setting.Type = reader.GetString(3);
                            }

                            if (!reader.IsDBNull(4))
                            {
                                setting.BusinessRuleId = reader.GetInt32(4);
                            }

                            settings.Add(setting);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Completeness Business Rule settings for rule " + ruleId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Completeness Business Rule settings for rule " + ruleId, ex);
                }
            }

            return settings;
        }

        public List<int> SearchCompleteness(CompletenessQuery query)
        {
            if (query.ChannelId.HasValue)
            {
                List<int> includedIn = this.GetEntitiesInChannelForDefinition(query.ChannelId.Value, query.CompletenessDefinitionId);

                List<int> searchResult = this.GetCompletessResultForQuery(query);

                return searchResult.Intersect(includedIn).ToList();
            }

            return this.GetCompletessResultForQuery(query);
        }

        #endregion

        #region Completeness Action

        public List<CompletenessAction> GetCompletenessActionsByDefinitionAndRule(int definitionId, int ruleId, string actiontrigger)
        {
            List<CompletenessAction> actions = new List<CompletenessAction>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Id],[CompletenessDefinitionId],[CompletenessRuleId],[ActionTrigger],[TaskName],[TaskDescription],[TaskAssignedTo],[TaskCreatedBy],[CompletenessGroupId],[ActionType] "
                                          + "FROM [dbo].[CompletenessAction] WHERE CompletenessDefinitionId = @CompletenessDefinitionId";

                    command.Parameters.AddWithValue("@CompletenessDefinitionId", definitionId);

                    if (ruleId > -1)
                    {
                        command.CommandText += " AND CompletenessRuleId = @CompletenessRuleId";
                        command.Parameters.AddWithValue("@CompletenessRuleId", ruleId);
                    }

                    if (!string.IsNullOrEmpty(actiontrigger))
                    {
                        command.CommandText += " AND ActionTrigger = @ActionTrigger";
                        command.Parameters.AddWithValue("@ActionTrigger", actiontrigger);
                    }

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            CompletenessAction action = new CompletenessAction();

                            action.Id = reader.GetInt32(0);
                            action.CompletenessDefinitionId = reader.GetInt32(1);
                            if (!reader.IsDBNull(2))
                            {
                                action.CompletenessRuleId = reader.GetInt32(2);
                            }

                            action.ActionTrigger = reader.GetString(3);
                            if (!reader.IsDBNull(4))
                            {
                                action.TaskName = reader.GetString(4);
                            }

                            if (!reader.IsDBNull(5))
                            {
                                action.TaskDescription = reader.GetString(5);
                            }

                            action.TaskAssignedTo = reader.GetString(6);
                            action.TaskCreatedBy = reader.GetString(7);
                            if (!reader.IsDBNull(8))
                            {
                                action.CompletenessGroupId = reader.GetInt32(8);
                            }

                            if (!reader.IsDBNull(9))
                            {
                                action.ActionType = reader.GetString(9);
                            }

                            actions.Add(action);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Completeness Actions for rule");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Completeness Actions for rule", ex);
                }
            }

            return actions;
        }

        public List<CompletenessAction> GetCompletenessActionsByDefinitionAndGroup(int definitionId, int groupId, string actiontrigger)
        {
            List<CompletenessAction> actions = new List<CompletenessAction>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Id],[CompletenessDefinitionId],[CompletenessRuleId],[ActionTrigger],[TaskName],[TaskDescription],[TaskAssignedTo],[TaskCreatedBy],[CompletenessGroupId],[ActionType] "
                                          + "FROM [dbo].[CompletenessAction] WHERE CompletenessDefinitionId = @CompletenessDefinitionId AND CompletenessGroupId = @CompletenessGroupId AND ActionTrigger = @ActionTrigger";

                    command.Parameters.AddWithValue("@CompletenessDefinitionId", definitionId);
                    command.Parameters.AddWithValue("@CompletenessGroupId", groupId);
                    if (string.IsNullOrEmpty(actiontrigger))
                    {
                        command.Parameters.AddWithValue("@ActionTrigger", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@ActionTrigger", actiontrigger);
                    }

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            CompletenessAction action = new CompletenessAction();

                            action.Id = reader.GetInt32(0);
                            action.CompletenessDefinitionId = reader.GetInt32(1);
                            if (!reader.IsDBNull(2))
                            {
                                action.CompletenessRuleId = reader.GetInt32(2);
                            }

                            action.ActionTrigger = reader.GetString(3);
                            if (!reader.IsDBNull(4))
                            {
                                action.TaskName = reader.GetString(4);
                            }

                            if (!reader.IsDBNull(5))
                            {
                                action.TaskDescription = reader.GetString(5);
                            }

                            action.TaskAssignedTo = reader.GetString(6);
                            action.TaskCreatedBy = reader.GetString(7);
                            if (!reader.IsDBNull(8))
                            {
                                action.CompletenessGroupId = reader.GetInt32(8);
                            }

                            if (!reader.IsDBNull(9))
                            {
                                action.ActionType = reader.GetString(9);
                            }

                            actions.Add(action);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Completeness Actions for group");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Completeness Actions for group", ex);
                }
            }

            return actions;
        }

        #endregion

        #region Completeness State

        public CompletenessDetail GetEntityCompletenessDetailFromRuleId(int ruleId)
        {
            var serverLanguages = this.GetAllLanguages();

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT [GroupId], [GroupName] ,[GroupSortOrder], [Complete], [RuleId] ,[RuleName] ,[RuleSortOrder]" +
                        "  FROM [ViewCompletenessDetails] WHERE RuleId = @RuleId";

                    command.Parameters.AddWithValue("@RuleId", ruleId);

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            LocaleString ruleName = null;
                            int? ruleSortOrder = null;

                            var groupId = reader.GetInt32(0);

                            var groupNameXml = reader.GetSqlXml(1);
                            var groupName = Utilities.XmlToLocaleString(groupNameXml.Value, serverLanguages);

                            var groupSortOrder = reader.GetInt32(2);
                            var complete = reader.GetBoolean(3);

                            if (!reader.IsDBNull(4))
                            {
                                ruleId = reader.GetInt32(4);
                            }

                            if (!reader.IsDBNull(5))
                            {
                                var ruleNameXml = reader.GetSqlXml(5);
                                ruleName = Utilities.XmlToLocaleString(ruleNameXml.Value, serverLanguages);
                            }

                            if (!reader.IsDBNull(6))
                            {
                                ruleSortOrder = reader.GetInt32(6);
                            }

                            var detail = new CompletenessDetail
                            {
                                GroupId = groupId,
                                GroupName = groupName,
                                SortOrder = groupSortOrder,
                                Rules = new List<CompletenessRuleDetail>()
                            };

                            var ruleDetail = new CompletenessRuleDetail
                            {
                                RuleName = ruleName,
                                RuleId = ruleId,
                                SortOrder = ruleSortOrder.Value,
                                Complete = complete
                            };

                            detail.Rules.Add(ruleDetail);
                            return detail;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An unexpected error occurred when getting entity Completeness details");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting entity Completeness details", ex);
                }
            }

            return null;
        }

        public List<CompletenessDetail> GetShallowEntityCompletenessDetails(int entityId)
        {
            List<CompletenessDetail> completenessDetails = new List<CompletenessDetail>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [GroupId], [Complete], [RuleId] FROM [CompletenessEntityState] WHERE EntityId = @EntityId";

                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            int? ruleId = null;

                            int groupId = reader.GetInt32(0);
                            bool complete = reader.GetBoolean(1);

                            if (!reader.IsDBNull(2))
                            {
                                ruleId = reader.GetInt32(2);
                            }

                            CompletenessDetail detail = completenessDetails.FirstOrDefault(cd => cd.GroupId == groupId);

                            if (detail == null)
                            {
                                detail = new CompletenessDetail();
                                detail.GroupId = groupId;
                                detail.Rules = new List<CompletenessRuleDetail>();

                                completenessDetails.Add(detail);
                            }

                            if (ruleId.HasValue)
                            {
                                CompletenessRuleDetail ruleDetail = new CompletenessRuleDetail();
                                ruleDetail.RuleId = ruleId.Value;
                                ruleDetail.Complete = complete;

                                detail.Rules.Add(ruleDetail);
                            }
                            else
                            {
                                detail.Complete = complete;
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    const string Message = "An unexpected error occurred when getting shallow entity completeness details";
                    Log.Error(ex, Message);
                    throw ErrorUtility.GetDataAccessException(Message, ex);
                }
            }

            return completenessDetails;
        }

        public List<DtoEntity> GetRelatedCompletenessEntities(int entityId)
        {
            List<DtoEntity> entities = new List<DtoEntity>();

            string types = this.GetRelatedTypesForCompleteness();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT DISTINCT [Id], EntityTypeId, Completeness FROM [ViewRelatedCompleteness] WHERE TargetEntityId = @TargetEntityId AND " +
                        $"[Type] IN ({types})";
                    command.Parameters.AddWithValue("@TargetEntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            int id = reader.GetInt32(0);
                            string entityTypeId = reader.GetString(1);
                            int? completeness = null;

                            if (!reader.IsDBNull(2))
                            {
                                completeness = reader.GetInt32(2);
                            }

                            entities.Add(new DtoEntity { Id = id, EntityTypeId = entityTypeId, Completeness = completeness });
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    var message = $"An unexpected error occurred when getting related completeness fom entity {entityId}.";
                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }

            return entities;
        }

        public void DeleteCompletenessStateForDefinition(int definitionId, CancellationToken cancellationToken)
        {
            if (IsOnlyDefinitionWithCompletenessState(definitionId))
            {
                TruncateCompletenessEntityState();
                return;
            }

            using (SqlConnection connection = new SqlConnection(ConnectionString))
            {
                try
                {
                    const int chunkSize = 5000;

                    var command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = $"DELETE TOP ({chunkSize}) FROM CompletenessEntityState WHERE DefinitionId = @Id ";
                    command.Parameters.AddWithValue("@Id", definitionId);

                    connection.Open();

                    int affectedRows;

                    do
                    {
                        affectedRows = command.ExecuteNonQuery();
                    } while (affectedRows > 0);

                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when deleting Completeness state for definition " + definitionId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when deleting Completeness state for definition " + definitionId, ex);
                }
            }
        }

        public void SetEntityCompletenessState(int entityId, int completenessDefinitionId, int groupId, int? ruleId, bool complete)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try

                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SetEntityCompletenessState";
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@DefinitionId", completenessDefinitionId);
                    command.Parameters.AddWithValue("@GroupId", groupId);

                    if (ruleId.HasValue)
                    {
                        command.Parameters.AddWithValue("@RuleId", ruleId);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@RuleId", DBNull.Value);
                    }

                    command.Parameters.AddWithValue("@Complete", complete);

                    command.CommandTimeout = 9000;

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, $"An unexpected error occurred when setting completeness state for entity {entityId}");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when setting completeness state for entity {entityId}", ex);
                }
            }
        }

        public async Task SetEntityCompletenessStateAsync(int entityId, int completenessDefinitionId, int groupId, int? ruleId, bool complete)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    await using var command = connection.CreateCommand();

                    command.CommandText = "SetEntityCompletenessState";
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@DefinitionId", completenessDefinitionId);
                    command.Parameters.AddWithValue("@GroupId", groupId);

                    if (ruleId.HasValue)
                    {
                        command.Parameters.AddWithValue("@RuleId", ruleId);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@RuleId", DBNull.Value);
                    }

                    command.Parameters.AddWithValue("@Complete", complete);

                    command.CommandTimeout = 9000;

                    await connection.OpenAsync();
                    await command.ExecuteNonQueryAsync();
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occurred when setting completeness state for entity {entityId}");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when setting completeness state for entity {entityId}", ex);
                }
            }
        }

        #endregion

        #region Private Methods

        private string GetRelatedTypesForCompleteness()
        {
            List<string> types = new List<string>
                                     {
                                         "inRiver.Server.Completeness.Criteria.RelationsCompleteCritera",
                                         "inRiver.Server.Completeness.Criteria.RelationsGroupCompleteCriteria"
                                     };

            string completenessTypesToRecalcRelation = this.GetServerSetting("COMPLETENESS_CUSTOM_CRITERIA_RELATION_TYPES");

            if (!string.IsNullOrEmpty(completenessTypesToRecalcRelation))
            {
                string[] additionalTypes = completenessTypesToRecalcRelation.Split(
                    new[] { ';' },
                    StringSplitOptions.RemoveEmptyEntries);
                types.AddRange(additionalTypes.Select(additionalType => additionalType.Trim()));
            }

            StringBuilder sb = new StringBuilder();

            foreach (string type in types)
            {
                sb.Append($"'{type}',");
            }

            sb.Remove(sb.Length - 1, 1);

            return sb.ToString();
        }

        private List<int> GetEntitiesInChannelForDefinition(int channelId, int definitionId)
        {
            List<int> entities = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT DISTINCT EntityId FROM ChannelStructure INNER JOIN CompletenessDefinition ON ChannelStructure.EntityTypeId = CompletenessDefinition.EntityTypeId " +
                                        "WHERE(CompletenessDefinition.Id = @DefinitionId) AND ChannelId = @ChannelId";
                    command.Parameters.AddWithValue("@ChannelId", channelId);
                    command.Parameters.AddWithValue("@DefinitionId", definitionId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            entities.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    string message = $"An unexpected error occurred when getting entities for channel {channelId} and definition {definitionId}";

                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }

            return entities;
        }

        private List<int> GetCompletessResultForQuery(CompletenessQuery query)
        {
            List<int> ids = new List<int>();
            StringBuilder queryCommand = new StringBuilder();

            var joinCount = 0;
            if (query.Groups != null && query.Rules != null)
            {
                joinCount = query.Groups.Count + query.Rules.Count;
            }

            if (query.Groups != null && query.Rules == null)
            {
                joinCount = query.Groups.Count;
            }

            if (query.Groups == null && query.Rules != null)
            {
                joinCount = query.Rules.Count;
            }

            if (joinCount == 0)
            {
                return new List<int>();
            }

            if (joinCount == 1)
            {
                queryCommand.Append("SELECT EntityId FROM CompletenessEntityState WHERE ");

                if (query.Groups != null && query.Groups.Any())
                {
                    queryCommand.Append($"GroupId = {query.Groups[0].CompletenessGroupId} AND RuleId IS NULL AND Complete = {Convert.ToInt32(query.Groups[0].IsComplete)}");
                }
                else
                {
                    queryCommand.Append($"RuleId = {query.Rules[0].CompletenessRuleId} AND Complete = {Convert.ToInt32(query.Rules[0].IsComplete)}");
                }
            }
            else
            {
                for (int i = 0; i < query.Groups.Count; i++)
                {
                    if (queryCommand.Length == 0)
                    {
                        queryCommand.Append($"SELECT [{i}].EntityId FROM CompletenessEntityState [{i}] ");
                    }
                    else
                    {
                        queryCommand.Append($" INNER JOIN CompletenessEntityState [{i}] ON [{i - 1}].EntityId = [{i}].EntityId");
                    }
                }

                for (int i = query.Groups.Count; i < query.Rules.Count + query.Groups.Count; i++)
                {
                    if (queryCommand.Length == 0)
                    {
                        queryCommand.Append($"SELECT [{i}].EntityId FROM CompletenessEntityState [{i}] ");
                    }
                    else
                    {
                        queryCommand.Append($" INNER JOIN CompletenessEntityState [{i}] ON [{i - 1}].EntityId = [{i}].EntityId");
                    }
                }

                queryCommand.Append(" WHERE ");

                for (int i = 0; i < query.Groups.Count; i++)
                {
                    if (i > 0)
                    {
                        queryCommand.Append(" AND ");
                    }
                    queryCommand.Append($"[{i}].GroupId = {query.Groups[i].CompletenessGroupId} AND [{i}].RuleId IS NULL AND [{i}].Complete = {Convert.ToInt32(query.Groups[i].IsComplete)}");
                }

                for (int i = query.Groups.Count; i < query.Rules.Count + query.Groups.Count; i++)
                {
                    if (i > query.Groups.Count || query.Groups.Count > 0)
                    {
                        queryCommand.Append(" AND ");
                    }

                    int decrease = query.Groups.Count;

                    queryCommand.Append($"[{i}].RuleId = {query.Rules[i - decrease].CompletenessRuleId} AND [{i}].Complete = {Convert.ToInt32(query.Rules[i - decrease].IsComplete)}");
                }
            }

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = queryCommand.ToString();

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when doing completeness search");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when doing completeness search", ex);
                }
            }

            return ids;
        }

        private bool IsOnlyDefinitionWithCompletenessState(int definitionId)
        {
            using (var connection = new SqlConnection(ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT COUNT(*) FROM CompletenessDefinition WHERE Id <> @Id";
                    command.Parameters.AddWithValue("@Id", definitionId);

                    connection.Open();

                    var count = (int)command.ExecuteScalar();

                    return count == 0;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occurred when checking CompletenessDefinition for definition {definitionId} in {context.CustomerSafeName}/{context.EnvironmentSafeName}");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when checking CompletenessDefinition for definition " + definitionId, ex);
                }
            }
        }

        private void TruncateCompletenessEntityState()
        {
            using (var connection = new SqlConnection(ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    command.CommandText = "TRUNCATE TABLE CompletenessEntityState";

                    connection.Open();

                    command.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occurred when truncating CompletenessEntityState table in {context.CustomerSafeName}/{context.EnvironmentSafeName}");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when truncating CompletenessEntityState table", ex);
                }
            }
        }

        #endregion
    }
}
