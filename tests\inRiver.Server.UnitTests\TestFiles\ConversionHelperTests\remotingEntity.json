{"ChangeSet": 6, "Completeness": 0, "CreatedBy": "<EMAIL>", "DateCreated": "2023-03-28 09:01:00", "DisplayDescription": null, "DisplayName": {"Data": {"stringMap": {"ar-SA": "", "bg-BG": "", "ca-ES": "", "cs-CZ": "", "da-DK": "", "de": "asg", "de-DE": "", "el-GR": "", "en": "as", "es-MX": "", "es-US": "", "it": "", "pl-PL": "", "sv-SE": "", "zh-TW": ""}}, "EntityId": 89567, "FieldType": {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductDescription", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": null, "TrackChanges": true, "Unique": false, "Units": null}, "LastModified": "2023-06-15 21:37:00", "Revision": 0}, "EntityType": {"FieldSets": null, "FieldTypes": [{"CategoryId": "FileInformation", "CVLId": null, "DataType": "Boolean", "DefaultValue": "False", "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Approved", "Index": 3, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "Approved"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ChildCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ChildCVL", "Index": 26, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "ChildCVL"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ChildCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ChildCVL2", "Index": 27, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "ChildCVL2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Color", "Index": 2, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"stringMap": {"en": "Color"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "CustomCvl", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CustomCVLField", "Index": 28, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ChildTestCVL", "DataType": "CVL", "DefaultValue": "spring_jacket", "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL1", "Index": 10, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVL1Child"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "Color", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL10Locale3", "Index": 19, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVL10Locale3"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ChildChildTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL2ReadOnly", "Index": 11, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVL2ChildChild"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": "first", "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL3Mandatory", "Index": 12, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"stringMap": {"en": "CVL3Mandatory"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": "second", "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL4Multivalue", "Index": 13, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"stringMap": {"en": "CVL4Multivalue"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": "Sand", "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL5", "Index": 14, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVL5"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL6Trackable", "Index": 15, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVL6Trackable"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL7", "Index": 16, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVL7"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL8LocaleString", "Index": 17, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVL8LocaleString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "CVL9Locale2", "Index": 18, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"ar-SA": "CVL9Locale2", "bg-BG": "CVL9Locale2", "ca-ES": "CVL9Locale2", "de": "CVL9Locale2", "en": "CVL9Locale2", "es-MX": "CVL9Locale2", "es-US": "CVL9Locale2", "it": "CVL9Locale2", "sv-SE": "CVL9Locale2", "zh-TW": "CVL9Locale2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "DecimalNumber", "Index": 44, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "DecimalNumber"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "DoubleNumberId", "Index": 32, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"da-DK": "DoubleNumber", "de": "DoubleNumber", "en": "DoubleNumber", "sv-SE": "DoubleNumber"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "FieldForSKU", "Index": 6, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MainCategory", "Index": 21, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"stringMap": {"en": "MainCategory", "sv-SE": "MainCategory"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "MainMenuCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MainMenu", "Index": 20, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"stringMap": {"en": "MainMenu", "sv-SE": "MainMenu"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "MultiCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "MultiCVL", "Index": 43, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"stringMap": {"en": "MultiCVL"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Number2", "Index": 30, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "Number 2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "MultivalueCVLTest", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ParentCVL", "Index": 25, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "ParentCVL"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ParentTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCustomCVLTest", "Index": 9, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "CVLParent"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLLocaleString", "Index": 38, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "ProductLocaleString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": "Countries", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLLocaleStringMultivalue", "Index": 39, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"stringMap": {"en": "ProductCVLLocaleStringMultivalue"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLString", "Index": 37, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "ProductCVLString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductCVLStringMultivalue", "Index": 46, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": true, "Name": {"stringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductDescription", "Index": 1, "IsDisplayDescription": false, "IsDisplayName": true, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "Description"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductIncludeTest", "Index": 40, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": null, "DataType": "Boolean", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductIncludeTest2", "Index": 8, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "Product Include Test 2"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "LocaleString", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductLocaleString", "Index": 35, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "ProductLocaleString"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ProductName", "Index": 0, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"de": "Name", "de-DE": "Name", "en": "Name", "pl-PL": "Nazwa"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": true, "Hidden": false, "Id": "ProductNumber", "Index": 29, "IsDisplayDescription": true, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"ar-SA": "Product Number", "bg-BG": "Product Number", "ca-ES": "Product Number", "cs-CZ": "Product Number", "da-DK": "Product Number", "de": "Product Number", "de-DE": "Product Number", "el-GR": "Product Number", "en": "Product Number", "es-MX": "Product Number", "es-US": "Product Number", "it": "Product Number", "pl-PL": "Product Number", "sv-SE": "Product Number", "zh-TW": "Product Number"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true, "Units": null}, {"CategoryId": "Details", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "ProductXmlSKU", "Index": 5, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "Brand", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "Series", "Index": 23, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"stringMap": {"en": "Series", "sv-SE": "Series"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SimpleDouble", "Index": 33, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "SimpleDouble"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": "ReadOnlyTestCVL", "DataType": "CVL", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "SubCategory", "Index": 22, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "SubCategory", "sv-SE": "SubCategory"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, {"CategoryId": "Details", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "UniqueField", "Index": 4, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": false, "Multivalue": false, "Name": {"stringMap": {"en": "Unique Field"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": true, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "Integer", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "uniqueFieldIntegerImportError", "Index": 42, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "uniqueFieldIntegerImportError"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": true, "Units": null}, {"CategoryId": "Authoring", "CVLId": null, "DataType": "String", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "uniqueFieldStringImportError", "Index": 41, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"en": "uniqueFieldStringImportError"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": false, "Unique": true, "Units": null}], "FieldViews": null, "Id": "Product", "IsLinkEntityType": false, "LinkTypes": [], "Name": {"stringMap": {"en": "Product"}}}, "Fields": [{"Data": 4.0, "EntityId": 89567, "FieldType": {"CategoryId": "Authoring", "CVLId": null, "DataType": "Double", "DefaultValue": null, "Description": {"stringMap": {}}, "EntityTypeId": "Product", "ExcludeFromDefaultView": false, "Hidden": false, "Id": "DoubleNumberId", "Index": 32, "IsDisplayDescription": false, "IsDisplayName": false, "Mandatory": true, "Multivalue": false, "Name": {"stringMap": {"da-DK": "DoubleNumber", "de": "DoubleNumber", "en": "DoubleNumber", "sv-SE": "DoubleNumber"}}, "ReadOnly": false, "Settings": {}, "TrackChanges": true, "Unique": false, "Units": null}, "LastModified": "0001-01-01 00:00:00", "Revision": 0}], "FieldSetId": null, "Id": 89567, "LastModified": "2023-06-15 21:37:00", "Links": [], "LoadLevel": 2, "Locked": null, "MainPictureId": null, "MainPictureUrl": null, "ModifiedBy": "<EMAIL>", "Segment": {"Description": null, "Id": 0, "Name": null}, "Version": 1}