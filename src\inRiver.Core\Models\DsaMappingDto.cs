namespace inRiver.Core.Models
{
    using System;

    public class DsaMappingDto
    {
        public int Id { get; set; }
        public object Data { get; set; }
        public string EnvironmentGid { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
