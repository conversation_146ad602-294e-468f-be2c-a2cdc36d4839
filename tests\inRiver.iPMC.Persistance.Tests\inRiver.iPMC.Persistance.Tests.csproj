<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
  <PackageReference Include="inRiver.Log" version="2.1.1" />
  <PackageReference Include="inRiver.Remoting.iPMC" Version="8.19.2" />
  <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" version="5.0.3">
    <PrivateAssets>all</PrivateAssets>
    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
  </PackageReference>
  <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.0.0" />
  <PackageReference Include="Newtonsoft.Json" version="12.0.2" />
  <PackageReference Include="StyleCop.Analyzers" version="1.2.0-beta.376">
    <PrivateAssets>all</PrivateAssets>
    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
  </PackageReference>
  <PackageReference Include="xunit" version="2.4.1" />
  <PackageReference Include="xunit.abstractions" version="2.0.3" />
  <PackageReference Include="xunit.analyzers" version="0.10.0" />
  <PackageReference Include="xunit.assert" version="2.4.1" />
  <PackageReference Include="xunit.core" version="2.4.1" />
  <PackageReference Include="xunit.extensibility.core" version="2.4.1" />
  <PackageReference Include="xunit.extensibility.execution" version="2.4.1" />
  <PackageReference Include="xunit.runner.visualstudio" version="2.4.1" />
  <PackageReference Include="xunit.runner.console" version="2.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\inRiver.iPMC.Persistance\inRiver.iPMC.Persistance.csproj" />
  </ItemGroup>

</Project>
