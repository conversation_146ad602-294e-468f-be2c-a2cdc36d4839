namespace inRiver.Server.Managers
{
    using System;
    using System.Collections.Concurrent;
    using System.Net.Http.Headers;
    using inRiver.Core.Http;

    public static class HttpClientManager
    {
        private static readonly ConcurrentDictionary<string, IHttpClient> httpClients = new ConcurrentDictionary<string, IHttpClient>();

        private static readonly int DEFAULT_TIMEOUT_SECONDS = 300;

        public static IHttpClient GetJsonAcceptHttpClient(string key, string serviceUrl, int timeoutInSeconds)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                throw new Exception("key must be set.");
            }

            if (string.IsNullOrWhiteSpace(serviceUrl))
            {
                throw new Exception("serviceUrl must be set, use RawHttpClient if an empty base address was intended.");
            }

            return httpClients.GetOrAdd(key, url =>
            {
                var httpClient = new inRiverHttpClient
                {
                    BaseAddress = new Uri(serviceUrl),
                    Timeout = TimeSpan.FromSeconds(timeoutInSeconds)
                };
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                return httpClient;
            });
        }

        public static IHttpClient GetExistingOrDefaultHttpClient(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                throw new ArgumentException("key must be set.");
            }

            return httpClients.GetOrAdd(key, url => new inRiverHttpClient());
        }

        public static IHttpClient GetJsonAcceptHttpClient(string serviceUrl)
        {
            return GetJsonAcceptHttpClient(serviceUrl, serviceUrl, DEFAULT_TIMEOUT_SECONDS);
        }
    }
}
