namespace inRiver.Server.Repository
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Security;
    using inRiver.Server.Completeness;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.EventPublishing;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;
    using Polly;
    using Serilog;

    public class CompletenessRepository
    {
        private readonly IDataPersistance dataContext;

        private readonly RequestContext context;

        private static readonly Dictionary<int, Task<int?>> ActiveSetEntityCompletenessTasks =
            new Dictionary<int, Task<int?>>();

        private static readonly Dictionary<int, Dictionary<int, Task<int?>>> QueuedSetEntityCompletenessTasks =
            new Dictionary<int, Dictionary<int, Task<int?>>>();

        public CompletenessRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;

            this.context = context;
        }

        #region Completeness Definition

        public int? GetCompletenessDefinitionIdForEntityType(string entityTypeId)
        {
            return this.dataContext.GetCompletenessDefinitionIdForEntityType(entityTypeId);
        }

        #endregion

        #region Completeness Group

        #endregion

        #region Completeness Rule

        #endregion

        #region Completeness Rule Settings

        #endregion

        #region Completeness Actions

        public List<CompletenessAction> GetCompletenessActionsByDefAndRule(int definitionid, int ruleid, string actiontrigger)
        {
            return this.dataContext.GetCompletenessActionsByDefinitionAndRule(definitionid, ruleid, actiontrigger);
        }

        public List<CompletenessAction> GetCompletenessActionsByDefAndGroup(int definitionid, int groupId, string actiontrigger)
        {
            return this.dataContext.GetCompletenessActionsByDefinitionAndGroup(definitionid, groupId, actiontrigger);
        }

        #endregion

        #region Criteria

        #endregion

        #region Calculations

        private int? GetEntityCompletenessValue(DtoEntity entity, List<Task> notificationTasks)
        {
            if (entity == null)
            {
                return null;
            }

            var definitionId = this.dataContext.GetCompletenessDefinitionIdForEntityType(entity.EntityTypeId);

            if (!definitionId.HasValue)
            {
                return null;
            }

            var entityCompleteness = 0;

            var groupInfo = this.dataContext.GetShallowCompletenessGroupsForDefinition(definitionId.Value);

            var groupDetails = this.dataContext.GetShallowEntityCompletenessDetails(entity.Id);

            foreach (var group in groupInfo)
            {
                var groupCompleteness = 0;

                var groupDetail = groupDetails.FirstOrDefault(gd => gd.GroupId == group.GroupId);

                foreach (var rule in group.Rules)
                {
                    CompletenessRuleDetail ruleDetail = null;

                    if (groupDetail != null)
                    {
                        ruleDetail = groupDetail.Rules.FirstOrDefault(rd => rd.RuleId == rule.RuleId);
                    }

                    var settings = rule.Settings ?? new List<CompletenessRuleSetting>();

                    var ruleCompleteness = new ExtensionManager(this.context).GetCriteriaCompletenessPercentage(entity.Id, rule.Type, settings);

                    if (!ruleCompleteness.HasValue)
                    {
                        continue;
                    }

                    if (ruleCompleteness > 100)
                    {
                        ruleCompleteness = 100;
                    }

                    if (ruleCompleteness < 0)
                    {
                        ruleCompleteness = 0;
                    }

                    var completenessChanged = true;
                    if (ruleDetail != null)
                    {
                        completenessChanged = (ruleCompleteness == 100 && !ruleDetail.Complete) || (ruleCompleteness < 100 && ruleDetail.Complete);
                    }

                    if (completenessChanged)
                    {
                        var ruleComplete = ruleCompleteness == 100 && (ruleDetail == null || !ruleDetail.Complete);

                        if (!ruleComplete)
                        {
                            this.context.DataPersistance.SetEntityCompletenessState(entity.Id, definitionId.Value, group.GroupId, rule.RuleId, ruleComplete);
                            notificationTasks.Add(
                                new Task(
                                    () =>
                                        EventPublisher.NotifyCompletenessRuleIncomplete(
                                            this.context,
                                            entity.Id,
                                            definitionId.Value,
                                            group.GroupId,
                                            rule.RuleId)));
                        }
                        else
                        {
                            this.dataContext.SetEntityCompletenessState(entity.Id, definitionId.Value, group.GroupId, rule.RuleId, ruleComplete);
                            notificationTasks.Add(
                                new Task(
                                    () =>
                                        EventPublisher.NotifyCompletenessRuleComplete(
                                            this.context,
                                            entity.Id,
                                            definitionId.Value,
                                            group.GroupId,
                                            rule.RuleId)));
                        }
                    }

                    groupCompleteness = groupCompleteness + (int)Math.Round(rule.Weight * (ruleCompleteness.Value / 100m));

                }

                if (groupDetail != null)
                {
                    if (groupDetail.Complete && groupCompleteness != 100)
                    {
                        this.dataContext.SetEntityCompletenessState(entity.Id, definitionId.Value, group.GroupId, null, false);

                        notificationTasks.Add(
                            new Task(
                                () =>
                                    EventPublisher.NotifyCompletenessGroupIncomplete(
                                        this.context,
                                        entity.Id,
                                        definitionId.Value,
                                        group.GroupId)));
                    }

                    if (!groupDetail.Complete && groupCompleteness == 100)
                    {
                        this.dataContext.SetEntityCompletenessState(entity.Id, definitionId.Value, group.GroupId, null, true);

                        notificationTasks.Add(
                            new Task(
                                () =>
                                    EventPublisher.NotifyCompletenessGroupComplete(
                                        this.context,
                                        entity.Id,
                                        definitionId.Value,
                                        group.GroupId)));
                    }
                }
                else
                {
                    this.dataContext.SetEntityCompletenessState(entity.Id, definitionId.Value, group.GroupId, null, groupCompleteness == 100);
                }

                entityCompleteness = entityCompleteness + (int)Math.Round(group.Weight * (groupCompleteness / 100m));
            }

            return entityCompleteness;
        }

        private async Task<int> SetEntityGroupCompletenessAsync(int entityId, CompletenessGroup group, IEnumerable<CompletenessBusinessRule> rules)
        {
            var completeness = 0;

            foreach (var rule in rules)
            {
                var settings = rule.RuleSettings ?? new List<CompletenessRuleSetting>();

                var completePercentage = await new ExtensionManager(this.context).GetCriteriaCompletenessPercentageAsync(entityId, rule.Type, settings);

                if (!completePercentage.HasValue)
                {
                    continue;
                }

                if (completePercentage > 100)
                {
                    completePercentage = 100;
                }

                if (completePercentage < 0)
                {
                    completePercentage = 0;
                }

                await this.dataContext.SetEntityCompletenessStateAsync(entityId, group.CompletenessDefinitionId, group.Id, rule.Id, completePercentage == 100);

                completeness += (int)Math.Round(rule.Weight * (completePercentage.Value / 100m));
            }

            await this.dataContext.SetEntityCompletenessStateAsync(entityId, group.CompletenessDefinitionId, group.Id, ruleId: null, completeness == 100);

            return completeness;
        }

        public async Task<RecalculateCompletenessResult> ReCalculateCompletenessForDefinitionAsync(int definitionId, CancellationToken cancellationToken)
        {
            if (!this.context.UserHasPermission(UserPermission.Administrator))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to recalculate completeness definitions in inRiver");
            }

            var definition = this.dataContext.GetCompletenessDefinition(definitionId);

            if (definition == null)
            {
                return new RecalculateCompletenessResult();
            }

            this.dataContext.DeleteCompletenessStateForDefinition(definitionId, cancellationToken);

            var entityIds = this.dataContext.GetAllEntityIdsForEntityType(definition.EntityTypeId, cancellationToken);
            var groups = this.dataContext.GetAllCompletenessGroupForDefinition(definition.Id);
            var allRules = this.dataContext.GetAllCompletenessBusinessRules();

            var failedEntityIds = new List<int>();
            var batchEntityIds = SplitIntoBatches(entityIds, 10);
            foreach (var batchIds in batchEntityIds)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var recalculateCompletenessForEntityTasks = batchIds.Select<int, Task<int?>>(async entityId => {
                    try
                    {
                        await Policy
                            .Handle<Exception>()
                            .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(300))
                            .ExecuteAsync(async () => await this.ReCalculateCompletenessForEntityAsync(entityId, groups, allRules));
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error when executing ReCalculateCompletenessForEntityAsync for entity: {entityId}", entityId);
                        return entityId;
                    }

                    return null;
                });

                var resultIds = await Task.WhenAll(recalculateCompletenessForEntityTasks);
                failedEntityIds.AddRange(resultIds.Where(id => id.HasValue).Select(id => id.Value));
            }

            return new RecalculateCompletenessResult
            {
                FailedToProcessEntityIds = failedEntityIds,
            };
        }

        private static IEnumerable<IEnumerable<T>> SplitIntoBatches<T>(IEnumerable<T> source, int batchSize)
            => source
                .Select((x, i) => new { Index = i, Value = x })
                .GroupBy(x => x.Index / batchSize)
                .Select(x => x.Select(v => v.Value));

        private async Task ReCalculateCompletenessForEntityAsync(int entityId, IEnumerable<CompletenessGroup> groups, IEnumerable<CompletenessBusinessRule> businessRules)
        {
            var completeness = 0;

            foreach (var group in groups)
            {
                if (!group.RuleIds.Any())
                {
                    continue;
                }

                var rules = businessRules.ToList().FindAll(r => r.GroupIds.Contains(group.Id));

                var completePercentage = await this.SetEntityGroupCompletenessAsync(entityId, group, rules);

                if (completePercentage > 100)
                {
                    completePercentage = 100;
                }

                if (completePercentage < 0)
                {
                    completePercentage = 0;
                }

                completeness += (int)Math.Round(group.Weight * (completePercentage / 100m));
            }

            _ = await this.dataContext.SetEntityCompletenessAsync(entityId, completeness);
        }

        public DtoEntity SetEntityCompleteness(DtoEntity entity)
        {
            // Fetch the task and check if null
            var setEntityCompletenessTask = this.GetSetEntityCompletenessTask(entity.Id);
            if (setEntityCompletenessTask != null)
            {
                // In case we have a task that can provide the completeness value we execute it
                entity.Completeness = setEntityCompletenessTask.Result;
            }

            return entity;
        }

        #endregion

        #region Search Completeness

        #endregion

        #region Private methods

        private Task<int?> GetSetEntityCompletenessTask(int entityId)
        {
            lock (ActiveSetEntityCompletenessTasks)
            {
                Task<int?> task;
                if (!ActiveSetEntityCompletenessTasks.ContainsKey(this.context.EnvironmentId))
                {
                    Log.Information($"Starting active SetEntityCompletenessTasks for environment {this.context.EnvironmentId} and entity: {entityId}");
                    task = Task.Run(() => this.SetEntityCompleteness(entityId));
                    ActiveSetEntityCompletenessTasks.Add(this.context.EnvironmentId, task);
                    return task;
                }
                else
                {
                    if (QueuedSetEntityCompletenessTasks.TryGetValue(this.context.EnvironmentId, out var queuedTasks))
                    {
                        if (queuedTasks.TryGetValue(entityId, out task))
                        {
                            Log.Information($"Task in environment queue already exists, ignoring. Environment {this.context.EnvironmentId} and entity: {entityId}");
                            return null;
                        }
                    }
                    else
                    {
                        queuedTasks = new Dictionary<int, Task<int?>>();
                        QueuedSetEntityCompletenessTasks.Add(this.context.EnvironmentId, queuedTasks);
                    }

                    Log.Information($"Enqueueing SetEntityCompletenessTasks for environment {this.context.EnvironmentId} and entity: {entityId}");
                    task = new Task<int?>(() => this.SetEntityCompleteness(entityId));
                    queuedTasks.Add(entityId, task);
                }
            }

            return null;
        }

        private int? SetEntityCompleteness(int entityId)
        {
            Log.Information($"Starting SetEntityCompleteness for environment {this.context.EnvironmentId} and entity: {entityId}");
            int? newCompletenessValue;

            try
            {
                var entity = this.dataContext.GetEntity(entityId);
                var originalCompleteness = entity?.Completeness;
                var notificationTasks = new List<Task>();

                newCompletenessValue = this.GetEntityCompletenessValue(entity, notificationTasks);

                if (CompletenessHasBeenModified(originalCompleteness, newCompletenessValue))
                {

                    this.context.DataPersistance.SetEntityCompleteness(entityId, newCompletenessValue);

                    if (newCompletenessValue == 100 || originalCompleteness == 100)
                    {
                        notificationTasks.Add(
                            originalCompleteness == 100
                                ? new Task(() => EventPublisher.NotifyEntityInComplete(this.context, entityId))
                                : new Task(() => EventPublisher.NotifyEntityComplete(this.context, entityId)));

                        foreach (
                            var relatedEntity in this.context.DataPersistance.GetRelatedCompletenessEntities(entityId))
                        {
                            this.GetSetEntityCompletenessTask(relatedEntity.Id);
                        }
                    }
                }

                // Fire and forget
                notificationTasks.ForEach(task => task.Start());
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Error when executing SetEntityCompleteness for environment {this.context.EnvironmentId} and entity: {entityId}");
                throw;
            }
            finally
            {
                lock (ActiveSetEntityCompletenessTasks)
                {
                    if (QueuedSetEntityCompletenessTasks.TryGetValue(this.context.EnvironmentId, out var queuedTasks) && queuedTasks.Any())
                    {
                        var queuePair = queuedTasks.FirstOrDefault();

                        Log.Information($"Found queued task for environment {this.context.EnvironmentId} and entity: {queuePair.Key}");

                        queuePair.Value?.Start();
                        queuedTasks.Remove(queuePair.Key);
                        ActiveSetEntityCompletenessTasks[this.context.EnvironmentId] = queuePair.Value;
                    }
                    else
                    {
                        Log.Information($"Queue empty for environment {this.context.EnvironmentId}, removing ActiveSetEntityCompletenessTasks");

                        ActiveSetEntityCompletenessTasks.Remove(this.context.EnvironmentId);
                    }
                }
            }

            return newCompletenessValue;
        }

        private static bool CompletenessHasBeenModified(int? originalCompleteness, int? newCompletenessValue)
        {
            if (originalCompleteness.Equals(newCompletenessValue))
            {
                return false;
            }

            return true;
        }

        #endregion
    }
}
