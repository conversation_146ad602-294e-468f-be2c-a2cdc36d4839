namespace inRiver.Server.Service
{
    using System;
    using Microsoft.Extensions.DependencyInjection;

    public static class StaticServiceProvider
    {
        private static IServiceProvider serviceProvider;

        public static void Configure(IServiceProvider serviceProvider) => StaticServiceProvider.serviceProvider =
            serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        public static T GetRequiredService<T>() => serviceProvider.GetRequiredService(typeof(T)) is T service
            ? service
            : throw new InvalidOperationException($"Service of type {typeof(T)} not found.");
    }
}
