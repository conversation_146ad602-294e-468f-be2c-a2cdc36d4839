namespace inRiver.Server.UnitTests.ExcelImport
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Repository;
    using inRiver.Server.Request;
    using Xunit;

    public class EntityValidationTests
    {
        public EntityValidationTests()
        {
            this.User = new User()
            {
                Id = 1,
                Username = "test",
                Roles = new List<Role> {
                    new Role
                    {
                        Id = 1,
                        Name = "Administrator",
                    }
                },
                CultureInfo = new CultureInfo("en-US")
            };

            this.EnvironmentContextData = new EnvironmentContextData();

            this.RequestContext = new RequestContext(this.EnvironmentContextData)
            {
                Username = "test",
                Roles = new List<string> { "Administrator" }
            };

            this.ExcelImportRepository = new ExcelImportRepository(this.RequestContext);

            this.ItemIdFieldType = new FieldType("ItemId", "Item", DataType.String, "General");
            this.ItemEntityType = new EntityType()
            {
                Id = "Item",
                FieldSets = null,
                FieldTypes = new List<FieldType>
                {
                    this.ItemIdFieldType
                }
            };

            this.EntityId = 9;
            this.ItemEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = "123",
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 1
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.ItemIdImportColumn = new FileImportColumnModel()
            {
                ColumnName = "ItemId",
                FieldTypeId = "ItemId",
                FieldTypeName = null
            };

            this.FileImportConfiguration = new FileImportConfigurationModel()
            {
                ClearEmptyValues = false,
                Columns = new List<FileImportColumnModel>
                {
                    this.ItemIdImportColumn
                }
            };

            this.CvlValueDictionaryDictionary = new Dictionary<string, Dictionary<string, CVLValue>>();
            this.CvlDictionary = new Dictionary<string, CVL>();
        }

        public ExcelImportRepository ExcelImportRepository { get; set; }

        public FileImportColumnModel ItemIdImportColumn { get; set; }

        public FileImportConfigurationModel FileImportConfiguration { get; set; }

        public int EntityId { get; set; }

        public Entity ItemEntity { get; set; }

        public RequestContext RequestContext { get; set; }

        public EnvironmentContextData EnvironmentContextData { get; set; }

        public EntityType ItemEntityType { get; set; }

        public FieldType ItemIdFieldType { get; set; }

        public User User { get; set; }

        public Dictionary<string, CVL> CvlDictionary { get; }

        public Dictionary<string, Dictionary<string, CVLValue>> CvlValueDictionaryDictionary { get; }

        [Fact]
        public void When_ValueIsString_Then_EntityIsValid()
        {
            var data = "string value";
            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = data,
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);
            Assert.Contains(importEntity.Fields, field =>
                string.Equals(field.Data?.ToString(), data, StringComparison.OrdinalIgnoreCase)); // data is not changed/removed
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        public void When_ValueIsNullOrEmpty_And_FieldIsMandatory_Then_EntityIsValid_And_FieldIsRemoved(string data)
        {
            this.ItemIdFieldType.Mandatory = true;

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = data,
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);
            Assert.Empty(importEntity.Fields); // the mandatory field is removed so that it will not be updated
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        public void When_ValueIsNullOrEmpty_And_FieldIsMandatory_And_ClearEmptyValue_Then_EntityIsNotValid(string data)
        {
            this.ItemIdFieldType.Mandatory = true;

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = data,
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.ClearEmptyValues = true;

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "The validation should fail!");
            Assert.True(validationError.GeneralError.Contains("mandatory", StringComparison.OrdinalIgnoreCase));
            Assert.True(validationError.GeneralError.Contains("ItemId", StringComparison.OrdinalIgnoreCase));
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        public void When_ValueIsNullOrEmpty_And_ClearEmptyValue_Then_EntityIsValid_And_FieldValueIsNull(string data)
        {
            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = data,
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.ClearEmptyValues = true;

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);
            var field = importEntity.Fields.Find(f => string.Equals(f.FieldType.Id, "itemId", StringComparison.OrdinalIgnoreCase));
            Assert.NotNull(field);
            Assert.Null(field.Data);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("some string")]
        public void When_ColumnName_ItemId_FieldIsReadOnly_Then_EntityIsValid_And_FieldIsIgnored(string data)
        {
            this.ItemIdFieldType.ReadOnly = true;
            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = data,
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);
            Assert.Contains(importEntity.Fields, field =>
                string.Equals(field.Data?.ToString(), data, StringComparison.OrdinalIgnoreCase)); // data is not changed/removed
        }

        [Fact]
        public void When_FieldDataTypeIsLocaleString_RegexSettingDoNotMatch_EntityIsNotValid()
        {
            this.ItemIdFieldType.DataType = DataType.LocaleString;
            this.ItemIdFieldType.Settings = new Dictionary<string, string>()
            {
                { "RegExp", "^[a-zA-Z0-9]*$" }
            };
            var cultures = new List<CultureInfo>
            {
                new CultureInfo("en")
            };
            var data = new LocaleString(cultures)
            {
                [new CultureInfo("en")] = "$123&&&"
            };

            this.ItemIdImportColumn.LanguageCode = "en";

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = data,
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, validationError.GeneralError);
            Assert.True(validationError.GeneralError.Contains("ItemId-Value does not match regular expression", StringComparison.OrdinalIgnoreCase));
        }

        [Fact]
        public void When_FieldDataTypeIsLocaleString_RegexSettingMatch_EntityIsValid()
        {
            this.ItemIdFieldType.DataType = DataType.LocaleString;
            this.ItemIdFieldType.Settings = new Dictionary<string, string>()
            {
                { "RegExp", "^[a-zA-Z0-9]*$" }
            };
            var cultures = new List<CultureInfo>
            {
                new CultureInfo("en")
            };
            var data = new LocaleString(cultures)
            {
                [new CultureInfo("en")] = "Test123"
            };

            this.ItemIdImportColumn.LanguageCode = "en";

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = data,
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);

            var field = importEntity.Fields.Find(f => string.Equals(f.FieldType.Id, "itemId", StringComparison.OrdinalIgnoreCase));
            Assert.NotNull(field);
            var fieldData = field.Data as LocaleString;
            Assert.NotNull(fieldData);
            Assert.Equal("Test123", fieldData[new CultureInfo("en")]);
        }

        [Fact]
        public void When_FieldDataTypeIString_RegexSettingDoNotMatch_EntityIsNotValid()
        {
            this.ItemIdFieldType.Settings = new Dictionary<string, string>()
            {
                { "RegExp", "^[a-zA-Z0-9]*$" }
            };

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = "$$$$",
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, validationError.GeneralError);
            Assert.True(validationError.GeneralError.Contains("ItemId-Value does not match regular expression", StringComparison.OrdinalIgnoreCase));
        }

        [Fact]
        public void When_FieldDataTypeIString_RegexSettingMatch_EntityIsValid()
        {
            this.ItemIdFieldType.Settings = new Dictionary<string, string>()
            {
                { "RegExp", "^[a-zA-Z0-9]*$" }
            };

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = "1234abczAZD",
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);
        }

        [Fact]
        public void When_ColumnName_Equals_Fieldset_New_FieldSetId_EntityIsValid()
        {
            this.ItemEntityType.FieldSets = new List<FieldSet>
            {
                new FieldSet
                {
                    Id = "TestFieldSet",
                    EntityTypeId = "Item"
                },
                new FieldSet
                {
                    Id = "NewFieldset",
                    EntityTypeId = "Item"
                }
            };
            this.ItemEntity.FieldSetId = "TestFieldSet";

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>(),
                EntityType = this.ItemEntityType,
                FieldSetId = "NewFieldset"
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = FileImportFieldModel.SysFieldSetFieldType
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);
        }

        [Fact]
        public void When_ColumnName_Equals_Fieldset_Current_FieldSetId_EntityIsValid()
        {
            this.ItemEntityType.FieldSets = new List<FieldSet>
            {
                new FieldSet
                {
                    Id = "TestFieldSet",
                    EntityTypeId = "Item"
                },
            };
            this.ItemEntity.FieldSetId = "TestFieldSet";

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>(),
                EntityType = this.ItemEntityType,
                FieldSetId = "TestFieldSet"
            };

            this.FileImportConfiguration.ClearEmptyValues = true;
            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = FileImportFieldModel.SysFieldSetFieldType
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, validationError.GeneralError);
        }

        [Fact]
        public void When_ColumnName_Equals_Fieldset_EntityTypeNoFieldSets_EntityIsNotValid()
        {
            this.ItemEntityType.FieldSets = new List<FieldSet>();
            this.ItemEntity.FieldSetId = "TestFieldSet";

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>(),
                EntityType = this.ItemEntityType,
                FieldSetId = "NewTestFieldSet"
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = FileImportFieldModel.SysFieldSetFieldType
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "The entity should be invalid because the Item entity type has no field sets.");
            Assert.True(validationError.ColumnErrors.ContainsKey(FileImportFieldModel.SysFieldSetFieldType));
            Assert.True(validationError.ColumnErrors[FileImportFieldModel.SysFieldSetFieldType].Contains("Fieldset NewTestFieldSet does not exist", StringComparison.OrdinalIgnoreCase));
        }

        [Fact]
        public void When_ColumnName_Equals_Fieldset_FieldSetDoNotBelongToEntity_EntityIsNotValid()
        {
            this.ItemEntityType.FieldSets = new List<FieldSet>
            {
                new FieldSet
                {
                    Id = "TestFieldSet",
                    EntityTypeId = "Item"
                },
                new FieldSet
                {
                    Id = "NewTestFieldSet",
                    EntityTypeId = "Product" // this is unlikely, but just in case
                },
            };
            this.ItemEntity.FieldSetId = "TestFieldSet";

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>(),
                EntityType = this.ItemEntityType,
                FieldSetId = "NewTestFieldSet"
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = FileImportFieldModel.SysFieldSetFieldType
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "The entity should be invalid because the new fieldset does not belong to the entity type.");
            Assert.Contains(validationError.ColumnErrors.Keys, k => k == FileImportFieldModel.SysFieldSetFieldType);
            Assert.True(
                validationError.ColumnErrors[FileImportFieldModel.SysFieldSetFieldType]
                    .Contains("Fieldset NewTestFieldSet does not belong to selected entity type", StringComparison.OrdinalIgnoreCase),
                "Wrong error received: " + validationError.ColumnErrors[FileImportFieldModel.SysFieldSetFieldType]);
        }

        [Fact]
        public void When_ColumnName_Equals_ItemId_NoFieldType_EntityIsValid()
        {
            this.ItemEntityType.FieldTypes.Clear();

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = this.ItemIdFieldType,
                        Data = "Item-001",
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, "Column that is a field type of the entity type should be ignored");
            Assert.True(string.IsNullOrWhiteSpace(validationError.GeneralError));
            Assert.Empty(validationError.ColumnErrors);
        }

        [Fact]
        public void When_FieldDataType_Is_DateTime_With_Invalid_Value_EntityIsNotValid()
        {
            this.ItemEntityType.FieldTypes.Clear();
            var itemDescFieldType = new FieldType("ItemDate", "Item", DataType.DateTime, "General");
            this.ItemEntityType.FieldTypes.Add(itemDescFieldType);

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = itemDescFieldType,
                        Data = "Dog",
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = "ItemDate",
                FieldTypeId = "ItemDate"
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "Entity is invalid when a Field of data type DateTime has an invalid value.");
            Assert.True(validationError.GeneralError.Contains("Value does not match date format", StringComparison.OrdinalIgnoreCase));
            Assert.NotEmpty(validationError.ColumnErrors);
            Assert.True(validationError.ColumnErrors.ContainsKey("ItemDate"), "ItemDate column error is missing.");
            Assert.True(validationError.ColumnErrors["ItemDate"].Contains("Value does not match date format", StringComparison.OrdinalIgnoreCase), "Wrong message for invalid date time");
        }

        [Theory]
        [InlineData(nameof(DataType.Integer), "not integer")]
        [InlineData(nameof(DataType.Double), "not double")]
        [InlineData(nameof(DataType.Boolean), "not boolean")]
        [InlineData(nameof(DataType.File), "not boolean")]
        [InlineData(nameof(DataType.Xml), "not xml")]
        public void When_FieldDataType_DoesNotMatch_Value_EntityIsNotValid(string dataType, object data)
        {
            this.ItemEntityType.FieldTypes.Clear();
            var itemDescFieldType = new FieldType("ItemField", "Item", dataType, "General");
            this.ItemEntityType.FieldTypes.Add(itemDescFieldType);

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = itemDescFieldType,
                        Data = data,
                        EntityId = this.EntityId
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = "ItemField",
                FieldTypeId = "ItemField"
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "Entity is invalid when a Field of data does not match the data type.");
            Assert.True(validationError.GeneralError.Contains("Value does not match type for field", StringComparison.OrdinalIgnoreCase));
            Assert.NotEmpty(validationError.ColumnErrors);
            Assert.True(validationError.ColumnErrors.ContainsKey("ItemField"), "ItemField column error is missing.");
            Assert.True(validationError.ColumnErrors["ItemField"].Contains("Value does not match type for field", StringComparison.OrdinalIgnoreCase), "Wrong message for invalid value.");
        }

        [Fact]
        public void When_FieldDataType_CVL_MultiValue_HasEmptyValue_EntityIsNotValid()
        {
            this.ItemEntityType.FieldTypes.Clear();
            var itemDescFieldType = new FieldType("ItemField", "Item", DataType.CVL, "General")
            {
                Multivalue = true,
                CVLId = "AnimalType"
            };
            this.ItemEntityType.FieldTypes.Add(itemDescFieldType);
            this.CvlDictionary.Add(
                "AnimalType",
                new CVL
                {
                    Id = "AnimalType",
                    DataType = "String",
                    CustomValueList = false
                });
            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = itemDescFieldType,
                        Data = "dog;cat;;",
                        EntityId = this.EntityId
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = "ItemField",
                FieldTypeId = "ItemField"
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "Entity is invalid when a there is a blank value.");
            Assert.True(validationError.GeneralError.Contains("Invalid value supplied", StringComparison.OrdinalIgnoreCase));
            Assert.NotEmpty(validationError.ColumnErrors);
            Assert.True(validationError.ColumnErrors.ContainsKey("ItemField"), "ItemField column error is missing.");
            Assert.True(validationError.ColumnErrors["ItemField"].Contains("Invalid value supplied", StringComparison.OrdinalIgnoreCase), "Wrong message for invalid value.");
        }

        [Fact]
        public void When_FieldDataType_CVL_MultiValue_HasValidValue_EntityIsValid()
        {
            this.ItemEntityType.FieldTypes.Clear();
            var itemDescFieldType = new FieldType("ItemField", "Item", DataType.CVL, "General")
            {
                Multivalue = true,
                CVLId = "AnimalType"
            };
            this.ItemEntityType.FieldTypes.Add(itemDescFieldType);
            this.CvlDictionary.Add(
                "AnimalType",
                new CVL
                {
                    Id = "AnimalType",
                    DataType = "String",
                    CustomValueList = false
                });
            this.CvlValueDictionaryDictionary.Add(
                "AnimalType",
                new Dictionary<string, CVLValue>()
                {
                    { "dog", new CVLValue { Value = "Dog", Key = "dog" } },
                    { "cat", new CVLValue { Value = "Cat", Key = "cat" } }
                });

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = itemDescFieldType,
                        Data = "dog;cat",
                        EntityId = this.EntityId
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = "ItemField",
                FieldTypeId = "ItemField"
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, "Entity is valid when the values are in the CVL keys");
            Assert.True(string.IsNullOrWhiteSpace(validationError.GeneralError));
            Assert.Empty(validationError.ColumnErrors);
        }

        [Fact]
        public void When_FieldDataType_CVL_MultiValue_HasInvalidValue_EntityIsNotValid()
        {
            this.ItemEntityType.FieldTypes.Clear();
            var itemDescFieldType = new FieldType("ItemField", "Item", DataType.CVL, "General")
            {
                Multivalue = true,
                CVLId = "AnimalType"
            };
            this.ItemEntityType.FieldTypes.Add(itemDescFieldType);
            this.CvlDictionary.Add(
                "AnimalType",
                new CVL
                {
                    Id = "AnimalType",
                    DataType = "String",
                    CustomValueList = false
                });
            this.CvlValueDictionaryDictionary.Add(
                "AnimalType",
                new Dictionary<string, CVLValue>()
                {
                    { "dog", new CVLValue { Value = "Dog", Key = "dog" } },
                    { "cat", new CVLValue { Value = "Cat", Key = "cat" } }
                });

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = itemDescFieldType,
                        Data = "dog;cat;cow", // cow is not a valid value
                        EntityId = this.EntityId
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = "ItemField",
                FieldTypeId = "ItemField"
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "Entity is invalid when a there is a data not in the CVL keys.");
            Assert.True(validationError.GeneralError.Contains("Invalid value supplied", StringComparison.OrdinalIgnoreCase));
            Assert.NotEmpty(validationError.ColumnErrors);
            Assert.True(validationError.ColumnErrors.ContainsKey("ItemField"), "ItemField column error is missing.");
            Assert.True(validationError.ColumnErrors["ItemField"].Contains("Invalid value supplied", StringComparison.OrdinalIgnoreCase), "Wrong message for invalid value.");
        }

        [Fact]
        public void When_FieldDataType_CVL_HasValidValue_EntityIsValid()
        {
            this.ItemEntityType.FieldTypes.Clear();
            var itemDescFieldType = new FieldType("ItemField", "Item", DataType.CVL, "General")
            {
                CVLId = "AnimalType"
            };
            this.ItemEntityType.FieldTypes.Add(itemDescFieldType);
            this.CvlDictionary.Add(
                "AnimalType",
                new CVL
                {
                    Id = "AnimalType",
                    DataType = "String",
                    CustomValueList = false
                });
            this.CvlValueDictionaryDictionary.Add(
                "AnimalType",
                new Dictionary<string, CVLValue>()
                {
                    { "dog", new CVLValue { Value = "Dog", Key = "dog" } },
                    { "cat", new CVLValue { Value = "Cat", Key = "cat" } }
                });

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = itemDescFieldType,
                        Data = "dog",
                        EntityId = this.EntityId
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = "ItemField",
                FieldTypeId = "ItemField"
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, "Entity is valid when the value is in the CVL keys");
            Assert.True(string.IsNullOrWhiteSpace(validationError.GeneralError));
            Assert.Empty(validationError.ColumnErrors);
        }

        [Fact]
        public void When_FieldDataType_CVL_InvalidValue_EntityIsNotValid()
        {
            this.ItemEntityType.FieldTypes.Clear();
            var itemDescFieldType = new FieldType("ItemField", "Item", DataType.CVL, "General")
            {
                CVLId = "AnimalType"
            };
            this.ItemEntityType.FieldTypes.Add(itemDescFieldType);
            this.CvlDictionary.Add(
                "AnimalType",
                new CVL
                {
                    Id = "AnimalType",
                    DataType = "String",
                    CustomValueList = false
                });
            this.CvlValueDictionaryDictionary.Add(
                "AnimalType",
                new Dictionary<string, CVLValue>()
                {
                    { "dog", new CVLValue { Value = "Dog", Key = "dog" } },
                    { "cat", new CVLValue { Value = "Cat", Key = "cat" } }
                });

            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = itemDescFieldType,
                        Data = "cow", // cow is not a valid value
                        EntityId = this.EntityId
                    }
                },
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.Columns.Clear();
            this.FileImportConfiguration.Columns.Add(new FileImportColumnModel
            {
                ColumnName = "ItemField",
                FieldTypeId = "ItemField"
            });

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.False(result, "Entity is invalid when the data is not in the CVL keys.");
            Assert.True(validationError.GeneralError.Contains("Invalid value supplied", StringComparison.OrdinalIgnoreCase));
            Assert.NotEmpty(validationError.ColumnErrors);
            Assert.True(validationError.ColumnErrors.ContainsKey("ItemField"), "ItemField column error is missing.");
            Assert.True(validationError.ColumnErrors["ItemField"].Contains("Invalid value supplied", StringComparison.OrdinalIgnoreCase), "Wrong message for invalid value.");
        }

        [Fact]
        public void When_ColumnName_Equals_ItemId_ImportEntity_NoItemIdField_EntityIsValid()
        {
            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>()
                {
                    new Field()
                    {
                        FieldType = new FieldType("ItemDesc", "Item", DataType.String, "General"),
                        Data = "Item-001",
                        EntityId = this.EntityId,
                        LastModified = new DateTime(2020, 1, 1, 12, 0, 12, DateTimeKind.Utc),
                        Revision = 2
                    }
                },
                EntityType = this.ItemEntityType
            };

            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, "Column that is not found in the import entity should be ignored");
            Assert.True(string.IsNullOrWhiteSpace(validationError.GeneralError));
            Assert.Empty(validationError.ColumnErrors);
        }

        [Fact]
        public void When_ColumnName_Equals_ItemId_ImportEntity_NoItemIdField_ClearEmptyValues_EntityIsValid()
        {
            var importEntity = new Entity
            {
                Id = this.EntityId,
                Fields = new List<Field>(),
                EntityType = this.ItemEntityType
            };

            this.FileImportConfiguration.ClearEmptyValues = true;
            var (result, validationError) = this.IsEntityValid(ref importEntity);

            Assert.True(result, "Not adding the Field ItemId in the import entity is allowed.");
            Assert.True(string.IsNullOrWhiteSpace(validationError.GeneralError));
            Assert.Empty(validationError.ColumnErrors);
            Assert.NotEmpty(importEntity.Fields);
            var field = importEntity.Fields.Find(f => string.Equals(f.FieldType.Id, "ItemId", StringComparison.OrdinalIgnoreCase));
            Assert.NotNull(field);
            Assert.Null(field.Data);
        }

        private (bool Result, ImportError ValidationError) IsEntityValid(ref Entity importEntity)
        {
            var existingEntity = this.ItemEntity;
            var validationError = new ImportError();

            var result = this.ExcelImportRepository.IsEntityValid(
                ref existingEntity,
                ref importEntity,
                null,
                this.User,
                this.ItemEntityType,
                ref validationError,
                this.FileImportConfiguration,
                null,
                null,
                null,
                this.CvlDictionary,
                this.CvlValueDictionaryDictionary,
                null,
                null,
                null);
            return (result, validationError);
        }
    }
}
