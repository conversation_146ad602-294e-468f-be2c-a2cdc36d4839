namespace inRiver.Core.Services
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models;
    using Microsoft.Extensions.Caching.Memory;
    using Newtonsoft.Json;

    public class AccessTokenRetriever : IAccessTokenRetriever
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IDictionary<string, Auth0Options> _auth0OptionsDictionary;
        private IMemoryCache _memoryCache;

        public AccessTokenRetriever(IHttpClientFactory clientFactory, IDictionary<string, Auth0Options> auth0OptionsDictionary,
            IMemoryCache memoryCache)
        {
            this._httpClientFactory = clientFactory;
            this._auth0OptionsDictionary = auth0OptionsDictionary;
            this._memoryCache = memoryCache;
        }

        public async Task<string> GetAccessTokenAsync(string clientName, CancellationToken cancellationToken)
        {
            return this._memoryCache.TryGetValue("AccessToken" + clientName, out var accessToken)
                ? accessToken.ToString() ??
                  throw new InvalidOperationException("Invalid access token received from cache.")
                : await this.GetAccessTokenFromAuth0Async(clientName, cancellationToken).ConfigureAwait(false);
        }

        private async Task<string> GetAccessTokenFromAuth0Async(string clientName, CancellationToken cancellationToken)
        {
            if (!this._auth0OptionsDictionary.TryGetValue(clientName, out var auth0Options))
            {
                throw new InvalidOperationException($"No valid Auth0 options found for client with name {clientName}.");
            }

            var httpClient = this._httpClientFactory.CreateClient(clientName);

            var response = await httpClient.PostAsJsonAsync(
                "oauth/token",
                new {
                    client_id = auth0Options.ClientId,
                    client_secret = auth0Options.ClientSecret,
                    audience = auth0Options.Audience,
                    grant_type = "client_credentials",
                },
                cancellationToken).ConfigureAwait(false);

            _ = response.EnsureSuccessStatusCode();

            var auth0ResponseContent = await response.Content.ReadAsStringAsync();
            var auth0Response = JsonConvert.DeserializeObject<Auth0Response>(auth0ResponseContent);

            return this.SetMemoryCache(auth0Response, clientName);
        }

        private string SetMemoryCache(Auth0Response auth0Response, string clientName)
        {
            var accessToken = auth0Response?.AccessToken ??
                              throw new ArgumentNullException(nameof(auth0Response),
                                  "No valid access token received from Auth0.");

            using (var cacheEntry = this._memoryCache.CreateEntry("AccessToken" + clientName))
            {
                cacheEntry.SetValue(accessToken);
                cacheEntry.AbsoluteExpiration =
                    new DateTimeOffset(DateTime.UtcNow).AddSeconds(auth0Response.ExpiresIn).AddMinutes(-1);
            }

            return accessToken;
        }

        private sealed class Auth0Response
        {
            public Auth0Response(string accessToken, int expiresIn)
            {
                this.AccessToken = accessToken;
                this.ExpiresIn = expiresIn;
            }

            [JsonProperty("access_token")] public string AccessToken { get; }

            [JsonProperty("expires_in")] public int ExpiresIn { get; }
        }
    }
}
