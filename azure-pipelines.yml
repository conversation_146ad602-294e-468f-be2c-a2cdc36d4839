name: $(Build.SourceBranchName).$(Date:MM.dd)$(Rev:.r)

parameters:
- name: 'agent_pool'
  type: 'string'
  default: 'inriver_products_default_v1'
  displayName: 'Build Agent Pool'

- name: 'agent_version'
  type: 'string'
  default: 'Any'
  displayName: 'Build Agent Version'

- name: 'configuration'
  values:
  - 'Debug'
  - 'Release'
  type: 'string'
  default: 'Release'
  displayName: 'Build Configuration'

trigger:
- main
- release/*

resources:
  repositories:
    - repository: templates
      type: git
      name: SRE/sre_pipeline_templates
      ref: 'refs/heads/main'

jobs:
- job:
  displayName: 'Build LongRunningJob'

  variables:
    solution: 'inRiver.ServiceFabric.LongRunningJob.sln'
    sf_proj: 'src/LongRunningJob/LongRunningJob.sfproj'
    artifact_name: 'drop'

  pool:
    name: ${{ parameters.agent_pool }}
    ${{ if ne(parameters.agent_version, 'Any') }}:
      demands:
      - BUILDAGENT_VERSION -equals ${{ parameters.agent_version }}


  workspace:
    clean: all

  steps:
  - template: build/tasks/dotnet_standard_setup.yml@templates

  - template: build/tasks/build_sf_application.yml@templates
    parameters:
      solution: $(solution)
      configuration: ${{ parameters.configuration }}

  - template: build/tasks/package_sf_application.yml@templates
    parameters:
      sf_proj: $(sf_proj)
      artifact_name: $(artifact_name)
      configuration: ${{ parameters.configuration }}

  - template: 'build/tasks/run_unit_tests.yml@templates'

  - task: PublishBuildArtifacts@1
    displayName: 'Publish artifact'
    inputs:
      ArtifactName: $(artifact_name)
      PathtoPublish: '$(build.artifactstagingdirectory)'
    condition: succeeded()
