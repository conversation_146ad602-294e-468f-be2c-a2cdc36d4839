namespace inRiver.Core.Models.inRiver.ExcelExport
{
    using System.Collections.Generic;
    using iPMC.Persistance;

    public class ExcelExportModel
    {
        public string Name { get; set; }

        public List<string> Languages { get; set; }

        public ContentSegmentationEnum SegmentationOption { get; set; }

        public List<int> EntityIds { get; set; }

        public List<ExcelExportEntityTypeModel> EntityTypeModels { get; set; }

        public string UserEmail { get; set; }

        public string Username { get; set; }

        public string CurrentLanguage { get; set; }

        public string CVLExportSetting { get; set; }

        private bool includeAvailableCVLKeys;
        public bool IncludeAvailableCVLKeys
        {
            get => includeAvailableCVLKeys;
            set {
                includeAvailableCVLKeys = value;
                if (includeAvailableCVLKeys)
                {
                    AvailableCVLKeysSetting = "IncludeAvailableCVLKeys";
                }
            }
        }

        public string AvailableCVLKeysSetting { get; set; }

        public bool IncludeExpressions { get; set; }
    }
}
