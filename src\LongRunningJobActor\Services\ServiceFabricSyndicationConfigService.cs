namespace LongRunningJobActor.Services
{
    using Abstraction;
    using Code;

    public class ServiceFabricSyndicationConfigService : ISyndicationConfigService
    {
        public string TokenServiceAddress() => Util.TokenServiceAddress();

        public bool GetRequiredHttps() => Util.GetRequiredHttps();

        public string SMTPSendUser() => Util.SMTPSendUser();

        public string SMTPSendUserName() => Util.SMTPSendUserName();
    }
}
