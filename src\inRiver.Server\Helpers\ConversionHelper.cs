namespace inRiver.Server.Helpers
{
    using System;
    using AutoMapper;
    using inRiver.Core.Persistance;
    using inRiver.iPMC.Persistance;
    using Newtonsoft.Json;
    using Remoting.Dto;
    using RemotingCriteria = Remoting.Query.Criteria;
    using RemotingEntity = Remoting.Objects.Entity;
    using RemotingEntityType = Remoting.Objects.EntityType;
    using RemotingField = Remoting.Objects.Field;
    using RemotingFieldSet = Remoting.Objects.FieldSet;
    using RemotingFieldType = Remoting.Objects.FieldType;
    using RemotingLink = Remoting.Objects.Link;
    using RemotingLinkType = Remoting.Objects.LinkType;
    using RemotingLocaleString = Remoting.Objects.LocaleString;
    using RemotingSegment = Remoting.Objects.Segment;
    using RemotingSystemQuery = Remoting.Query.SystemQuery;

    public static class ConversionHelper
    {
        private static readonly MapperConfiguration mapperConfig;

        static ConversionHelper() => mapperConfig = new MapperConfiguration(CreateMapping);

        public static T Map<T>(object input)
        {
            var mapper = mapperConfig.CreateMapper();
            return mapper.Map<T>(input);
        }

        private static void CreateMapping(IMapperConfigurationExpression cfg)
        {
            cfg.AllowNullCollections = true;

            // Persistance -> Remoting
            cfg.CreateMap<EntityType, RemotingEntityType>();
            cfg.CreateMap<FieldType, RemotingFieldType>();
            cfg.CreateMap<LinkType, RemotingLinkType>();
            cfg.CreateMap<FieldSet, RemotingFieldSet>();

            cfg.CreateMap<Entity, RemotingEntity>();
            cfg.CreateMap<Field, RemotingField>()
                .ForMember(f => f.Data, opt => {
                    opt.PreCondition(src => src.Data != null);
                    opt.MapFrom(src => Convert(src.Data, src.FieldType) ?? src.Data);
                });
            cfg.CreateMap<Link, RemotingLink>();
            cfg.CreateMap<Segment, RemotingSegment>();
            cfg.CreateMap<LocaleString, RemotingLocaleString>()
                .ConvertUsing(ls => Convert(ls));

            // Remoting -> Persistance
            cfg.CreateMap<RemotingEntityType, EntityType>();
            cfg.CreateMap<RemotingFieldType, FieldType>();
            cfg.CreateMap<RemotingLinkType, LinkType>();
            cfg.CreateMap<RemotingFieldSet, FieldSet>();

            cfg.CreateMap<RemotingEntity, Entity>();
            cfg.CreateMap<RemotingField, Field>()
                .ForMember(f => f.Data, opt => {
                    opt.PreCondition(src => src.Data != null);
                    opt.MapFrom(src => Convert(src.Data as RemotingLocaleString) ?? src.Data);
                });
            cfg.CreateMap<RemotingLink, Link>();
            cfg.CreateMap<RemotingSegment, Segment>();
            cfg.CreateMap<RemotingLocaleString, LocaleString>()
                .ConvertUsing(ls => Convert(ls));

            cfg.CreateMap<RemotingSystemQuery, SystemQuery>();
            cfg.CreateMap<RemotingCriteria, Criteria>();

            // Persistance -> Dto
            cfg.CreateMap<Entity, DtoEntity>();
            cfg.CreateMap<Field, DtoField>()
                .ForMember(f => f.Data, opt => {
                    opt.PreCondition(src => src.Data != null);
                    opt.MapFrom(src => ConvertDataToDto(src.Data));
                });
            cfg.CreateMap<Link, DtoLink>();
            cfg.CreateMap<Segment, DtoSegment>();

            // System
            cfg.CreateMap<DateTime, string>()
                .ConvertUsing(dt => dt.ToString(PersistanceEntity.InternalDateTimeFormat));
        }

        private static object Convert(object data, FieldType fieldType)
        {
            if (data is null || fieldType?.DataType != "LocaleString")
            {
                return data;
            }

            if (data is string)
            {
                return JsonConvert.DeserializeObject<RemotingLocaleString>(data as string, IPMCPersistanceDataConverter.JsonConverters);
            }

            var localeString = data as LocaleString;

            if (localeString != null)
            {
                return Convert(localeString);
            }

            return data;
        }

        private static string ConvertDataToDto(object data)
        {
            if (data is LocaleString)
            {
                return JsonConvert.SerializeObject(data, IPMCPersistanceDataConverter.JsonConverters);
            }

            if (data is DateTime)
            {
                return ((DateTime)data).ToString(PersistanceEntity.InternalDateTimeFormat);
            }

            return data.ToString();
        }

        private static LocaleString Convert(RemotingLocaleString localeString)
        {
            if (localeString == null)
            {
                return null;
            }

            var result = new LocaleString();

            foreach (var cultureInfo in localeString.Languages)
            {
                result.StringMap.Add(cultureInfo, localeString[cultureInfo]);
            }

            return result;
        }

        private static RemotingLocaleString Convert(LocaleString ls)
        {
            if (ls == null)
            {
                return null;
            }

            var result = new RemotingLocaleString();

            foreach (var cultureInfo in ls.Languages)
            {
                result[cultureInfo] = ls[cultureInfo];
            }

            return result;
        }
    }
}
