﻿namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Core.Models.inRiver;

    /// <summary>
    /// EntityType related operations
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        public override EntityType AddEntityType(EntityType entityType)
        {
            var result = this._origInRiverPersistance.AddEntityType(entityType);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override EntityType UpdateEntityType(EntityType entityType)
        {
            var result = this._origInRiverPersistance.UpdateEntityType(entityType);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool DeleteEntityType(string id)
        {
            var result = this._origInRiverPersistance.DeleteEntityType(id);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool DeleteAllEntityTypes()
        {
            var result = this._origInRiverPersistance.DeleteAllEntityTypes();
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }
    }
}
