namespace inRiver.Server.Request
{
    public class EnvironmentContextData
    {
        public string ConnectionString { get; set; }

        public string LogConnectionString { get; set; }

        public string LogTable { get; set; }

        public string CustomerSafeName { get; set; }

        public string EnvironmentSafeName { get; set; }

        public string EnvironmentLocation { get; set; }

        public int CustomerId { get; set; }

        public int EnvironmentId { get; set; }

        public string AssetServiceUrl { get; set; }

        public string AssetServiceInternalUrl { get; set; }

        public string EnvironmentFullName { get; set; }

        public string JobServiceUrl { get; set; }

        public int EntityModel { get; set; }

        public string StorageAccountConnectionString { get; set; }
    }
}
