namespace LongRunningJob.Core.Repositories
{
    using System.Diagnostics.CodeAnalysis;
    using System.IO;
    using System.Text;
    using System.Text.Json;
    using System.Threading.Tasks;
    using Azure.Storage.Blobs;
    using Azure.Storage.Blobs.Models;
    using LongRunningJob.Core.Abstractions;

    public class JobModelBlobRepository : IJobModelRepository
    {
        private readonly IEnvironmentContextAccessor environmentContextAccessor;

        public JobModelBlobRepository(IEnvironmentContextAccessor environmentContextAccessor)
        {
            this.environmentContextAccessor = environmentContextAccessor;
        }

        public async Task<T> GetJobModelAsync<T>(int jobId)
        {
            var container = this.GetBlobContainerClient();

            var pathAndFileName = this.GetBlobPathAndFileName(jobId);

            var blobClient = container.GetBlobClient(pathAndFileName);

            BlobDownloadResult downloadResult = await blobClient.DownloadContentAsync();

            return downloadResult.Content.ToObjectFromJson<T>();
        }

        public async Task SaveJobModelAsync<T>(T jobModel, int jobId)
        {
            var container = this.GetBlobContainerClient();

            await container.CreateIfNotExistsAsync();

            var pathAndFileName = this.GetBlobPathAndFileName(jobId);

            var blobClient = container.GetBlobClient(pathAndFileName);

            var json = JsonSerializer.Serialize(jobModel);

            await using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(json)))
            {
                await blobClient.UploadAsync(stream);
            }
        }

        [SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Containers have lower case names.")]
        private string GetBlobPathAndFileName(int jobId) => $"{this.environmentContextAccessor.EnvironmentContext.EnvironmentSafename}/jobmodels/{jobId}.json".ToLowerInvariant();

        [SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Containers have lower case names.")]
        private BlobContainerClient GetBlobContainerClient() =>
            new BlobContainerClient(this.environmentContextAccessor.EnvironmentContext.StorageAccountConnectionString, this.environmentContextAccessor.EnvironmentContext.CustomerSafename.ToLowerInvariant());
    }
}
