﻿namespace inRiver.Core.Extension.Augmenta
{
    using System;
    using System.Linq;
    using inRiver.Core.Models.Augmenta;
    using inRiver.Remoting.Objects;

    internal static class EntityExtensions
    {
        public static EntityInputModel ToEntityInputModel(this Entity entity)
            => new EntityInputModel
            {
                Id = entity.Id,
                EntityTypeId = entity.EntityType.Id,
                FieldSetId = entity.FieldSetId,
                Fields = entity.Fields is null ? Array.Empty<FieldInputModel>() : entity.Fields.Where(x => x != null).Select(field => field.ToFieldInputModel()).ToArray(),
                SegmentId = entity.Segment.Id,
            };
    }
}