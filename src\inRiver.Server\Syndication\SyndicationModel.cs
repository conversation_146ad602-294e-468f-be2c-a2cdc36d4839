namespace inRiver.Server.Syndication
{
    using System.Collections.Generic;
    using System.Text.Json.Serialization;
    using inRiver.Server.Syndication.Enums;

    public class SyndicationModel
    {
        public int Id { get; set; }

        public int? FileFormatId { get; set; }

        public int? DynamicFormatId { get; set; }

        public string Name { get; set; }

        public string WorkareaId { get; set; }

        public IEnumerable<int> EntityIds { get; set; } = new List<int>();

        public string WorkareaName { get; set; }

        public int MappingId { get; set; }

        public string MappingName { get; set; }

        public string ImageUrl { get; set; }

        public string ExtensionId { get; set; }

        public string ExtensionDisplayName { get; set; }

        public string OutputFormat { get; set; }

        public string LinkEntityTypeId { get; set; }

        public bool EnableSKU { get; set; }

        public bool IsResourceExportEnabled { get; set; }

        public bool DisableResourceExportLimitPreCheck { get; set; }

        public int? ChannelId { get; set; }

        public bool RunPreview { get; set; } = false;

        public bool RunDsaSyndication { get; set; } = false;

        public int? DsaMappingId { get; set; }

        /// <summary>
        /// Will get mapped to the 0 value of the enum, "Extension", if the property is not set in the request.
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public SyndicationMappingSource MappingSource { get; set; }

        /// <summary>
        /// Will get mapped to the 0 value of the enum, "Extension", if the property is not set in the request.
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public SyndicationOutputDestination OutputDestination { get; set; }

        /// <summary>
        /// Will get mapped to the 0 value of the enum, "None", if the property is not set in the request.
        /// </summary>
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public SyndicationIdentifierType IdentifierType { get; set; }
    }
}
