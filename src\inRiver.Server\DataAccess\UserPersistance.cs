namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Text;
    using inRiver.iPMC.Persistance;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Error;
    using Serilog;
    using Segment = Remoting.Objects.Segment;

    public partial class inRiverPersistance
    {
        #region User

        public User GetUserByUsername(string username)
        {
            User user = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, Username, FirstName, LastName, Email, AuthenticationType, RestAPIkey, GlobalId FROM [User] WHERE Username = @Username";

                    command.Parameters.AddWithValue("@Username", username);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            user = new User();

                            var index = 0;
                            user.Id = reader.GetInt32(index);

                            if (!reader.IsDBNull(++index))
                            {
                                user.Username = reader.GetString(index);
                            }

                            if (!reader.IsDBNull(++index))
                            {
                                user.FirstName = reader.GetString(index);
                            }

                            if (!reader.IsDBNull(++index))
                            {
                                user.LastName = reader.GetString(index);
                            }

                            if (!reader.IsDBNull(++index))
                            {
                                user.Email = reader.GetString(index);
                            }

                            user.AuthenticationType = reader.GetString(++index);

                            if (!reader.IsDBNull(++index))
                            {
                                user.RestAPIkey = reader.GetString(index);
                            }

                            if (!Convert.IsDBNull(reader["GlobalId"]))
                            {
                                user.GlobalId = (Guid)reader["GlobalId"];
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting User by username");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting User by username", ex);
                }
            }

            // to load additional attributes of the user if 'user' is not null
            loadRolesAndPermissionsIntoUser(user);

            return user;
        }

        private List<SegmentRole> GetSegmentRolesByUserId(int userId)
        {
            List<SegmentRole> retVal = new List<SegmentRole>();

            PersistanceUserRoleContentSegmentation persistanceUserRole =
                new PersistanceUserRoleContentSegmentation(context.ConnectionString, context.Logging, null);

            List<UserRoleSegment> list = persistanceUserRole.GetUserRoleContentSegmentationByUserId(userId);

            if (list != null)
            {
                foreach (var item in list)
                {
                    SegmentRole segmentRole = new SegmentRole();
                    segmentRole.Role = GetRole(item.RoleId);
                    segmentRole.Segment = GetSegment(item.SegmentId);

                    retVal.Add(segmentRole);
                }
            }

            return retVal;
        }

        private Segment GetSegment(int id)
        {
            PersistanceContentSegmentation persistanceContentSegmentation =
                new PersistanceContentSegmentation(context.ConnectionString, context.Logging, null);

            iPMC.Persistance.Segment item = persistanceContentSegmentation.GetSegment(id);

            return new Segment()
            {
                Id = item.Id,
                Name = item.Name,
                Description = item.Description
            };
        }

        public Dictionary<string, string> GetAllUserSettings(int id)
        {
            Dictionary<string, string> settings = new Dictionary<string, string>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Name, Value FROM [UserSettings] WHERE UserId = @UserId";

                    command.Parameters.AddWithValue("@UserId", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string key = reader.GetString(0);

                            string value = string.Empty;

                            if (!reader.IsDBNull(1))
                            {
                                value = reader.GetString(1);
                            }

                            if (settings.ContainsKey(key))
                            {
                                Log.Warning("The user with id " + id + " has a duplicate setting " + key);
                            }
                            else
                            {
                                settings.Add(key, value);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all user settings");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all user settings", ex);
                }
            }

            return settings;
        }

        public List<Role> GetRolesForUser(string username)
        {
            List<Role> roles = new List<Role>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT DISTINCT Id, Name, Description FROM [ViewUserRoles] WHERE Username = @UserName";

                    command.Parameters.AddWithValue("@UserName", username);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            while (reader.Read())
                            {
                                Role role = new Role();

                                role.Id = reader.GetInt32(0);
                                role.Name = reader.GetString(1);

                                if (!reader.IsDBNull(2))
                                {
                                    role.Description = reader.GetString(2);
                                }

                                roles.Add(role);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting roles for user");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting roles for user", ex);
                }
            }

            // TODO: Add to view
            foreach (Role role in roles)
            {
                role.Permissions = this.GetPermissionsForRole(role.Id);
            }

            return roles;
        }

        public List<Permission> GetPermissionsForUser(string username)
        {
            List<Permission> permissions = new List<Permission>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT DISTINCT PermissionId, PermissionName, PermissionDescription FROM [ViewUserPermissions] WHERE Username = @Username AND PermissionId IS NOT NULL";
                    command.Parameters.AddWithValue("@Username", username);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            while (reader.Read())
                            {
                                Permission permission = new Permission();

                                permission.Id = reader.GetInt32(0);
                                permission.Name = reader.GetString(1);

                                if (!reader.IsDBNull(2))
                                {
                                    permission.Description = reader.GetString(2);
                                }

                                permissions.Add(permission);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting permissions for user");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting permissions for user", ex);
                }
            }

            return permissions;
        }

        public User GetShallowUser(string username)
        {
            User user = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, FirstName, LastName, Email, AuthenticationType, RestAPIkey, GlobalId FROM [User] WHERE Username = @Username";

                    command.Parameters.AddWithValue("@Username", username);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            user = new User();

                            user.Username = username;
                            user.Id = reader.GetInt32(0);

                            if (!reader.IsDBNull(1))
                            {
                                user.FirstName = reader.GetString(1);
                            }

                            if (!reader.IsDBNull(2))
                            {
                                user.LastName = reader.GetString(2);
                            }

                            if (!reader.IsDBNull(3))
                            {
                                user.Email = reader.GetString(3);
                            }

                            user.AuthenticationType = reader.GetString(4);

                            if (!Convert.IsDBNull(reader["RestAPIkey"]))
                            {
                                user.RestAPIkey = (string)reader["RestAPIkey"];
                            }

                            if (!Convert.IsDBNull(reader["GlobalId"]))
                            {
                                user.GlobalId = (Guid)reader["GlobalId"];
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting shallow user");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting shallow user", ex);
                }
            }

            return user;
        }

        public List<User> GetAllShallowUsers()
        {
            List<User> users = new List<User>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, Username, FirstName, LastName, Email, AuthenticationType FROM [User] WHERE IsSupplierEditing IS NULL";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            User user = new User();

                            user.Id = reader.GetInt32(0);
                            user.Username = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                user.FirstName = reader.GetString(2);
                            }

                            if (!reader.IsDBNull(3))
                            {
                                user.LastName = reader.GetString(3);
                            }

                            if (!reader.IsDBNull(4))
                            {
                                user.Email = reader.GetString(4);
                            }

                            user.AuthenticationType = reader.GetString(5);

                            users.Add(user);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    const string Message = "An unexpected error occurred when getting all shallow users";
                    Log.Error(ex, Message);
                    throw ErrorUtility.GetDataAccessException(Message, ex);
                }
            }

            return users;
        }

        #endregion

        #region Role

        public Role GetRole(int id)
        {
            Role role = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Name, Description FROM [Role] WHERE Id = @Id";

                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            role = new Role();

                            role.Id = id;
                            role.Name = reader.GetString(0);

                            if (!reader.IsDBNull(1))
                            {
                                role.Description = reader.GetString(1);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Role");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Role", ex);
                }
            }

            // TODO: Add view
            if (role != null)
            {
                role.Permissions = this.GetPermissionsForRole(role.Id);
            }

            return role;
        }

        public List<Role> GetAllRoles()
        {
            List<Role> roles = new List<Role>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id, Name, Description FROM [Role]";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Role role = new Role();

                            role.Id = reader.GetInt32(0);
                            role.Name = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                role.Description = reader.GetString(2);
                            }

                            roles.Add(role);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Roles");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Roles", ex);
                }
            }

            var permissions = this.GetAllPermissionsForRoles();

            foreach (Role role in roles)
            {
                if (!permissions.ContainsKey(role.Id))
                {
                    role.Permissions = new List<Permission>();
                    continue;
                }

                role.Permissions = permissions[role.Id];
            }

            return roles;
        }

        #endregion

        #region Permission

        #endregion

        #region Restricted Field Permission

        #endregion

        #region Private Methods

        private List<RestrictedFieldPermission> GetRestrictedPermissionsForRoles(List<Role> roles)
        {
            List<RestrictedFieldPermission> permissions = new List<RestrictedFieldPermission>();

            if (roles.Count == 0)
            {
                return new List<RestrictedFieldPermission>();
            }

            StringBuilder stringBuilder = new StringBuilder("(");

            foreach (Role role in roles)
            {
                stringBuilder.Append(role.Id + ",");
            }

            string rolesString = stringBuilder.ToString(0, stringBuilder.Length - 1) + ")";

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Id], [EntityTypeId], [FieldTypeId], [CategoryId], [RestrictionType], [RoleId] FROM [RestrictedFieldPermission] WHERE RoleId IN " + rolesString;

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            RestrictedFieldPermission permission = new RestrictedFieldPermission();

                            permission.Id = reader.GetInt32(0);
                            permission.EntityTypeId = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                permission.FieldTypeId = reader.GetString(2);
                            }

                            if (!reader.IsDBNull(3))
                            {
                                permission.CategoryId = reader.GetString(3);
                            }

                            permission.RestrictionType = reader.GetString(4);
                            permission.RoleId = reader.GetInt32(5);

                            permissions.Add(permission);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting Restricted Field Permissions for roles");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting Restricted Field Permissions for roles", ex);
                }
            }

            return permissions;
        }

        private List<Permission> GetPermissionsForRole(int roleId)
        {
            List<Permission> permissions = new List<Permission>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, Name, Description FROM [ViewRolePermissions] WHERE RoleId = @RoleId";
                    command.Parameters.AddWithValue("@RoleId", roleId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Permission permission = new Permission();

                            permission.Id = reader.GetInt32(0);
                            permission.Name = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                permission.Description = reader.GetString(2);
                            }

                            permissions.Add(permission);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Permissions for role");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Permissions for role", ex);
                }
            }

            return permissions;
        }

        private Dictionary<int, List<Permission>> GetAllPermissionsForRoles()
        {
            Dictionary<int, List<Permission>> rolePermissions = new Dictionary<int, List<Permission>>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, Name, Description, RoleId FROM [ViewRolePermissions]";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Permission permission = new Permission();

                            permission.Id = reader.GetInt32(0);
                            permission.Name = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                permission.Description = reader.GetString(2);
                            }

                            int roleId = reader.GetInt32(3);

                            if (!rolePermissions.ContainsKey(roleId))
                            {
                                rolePermissions.Add(roleId, new List<Permission>());
                            }

                            rolePermissions[roleId].Add(permission);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all Permissions for roles");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all Permissions for roles", ex);
                }
            }

            return rolePermissions;
        }

        // to load additional attributes of the user if 'user' is not null
        // Roles, Permissions, Settings, RestrictedFieldPermissions, RoleSegments
        private void loadRolesAndPermissionsIntoUser(User user)
        {
            if (user != null)
            {
                user.Roles = this.GetRolesForUser(user.Username);
                user.Permissions = this.GetPermissionsForUser(user.Username);
                user.Settings = this.GetAllUserSettings(user.Id);
                user.RestrictedFieldPermissions = this.GetRestrictedPermissionsForRoles(user.Roles);

                user.SegmentRoles = this.GetSegmentRolesByUserId(user.Id);
            }

        }

        #endregion
    }
}
