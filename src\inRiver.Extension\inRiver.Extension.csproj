<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net461</TargetFramework>
    <Platforms>AnyCPU;x64</Platforms>
    <Configurations>Debug;Release;Development-LocalEuw;Development-LocalUse;Development-DevSql2016</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="inRiver.Log" version="2.0.1" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Core.EnvironmentSettings\inRiver.Core.EnvironmentSettings.csproj" />
  </ItemGroup>

</Project>
