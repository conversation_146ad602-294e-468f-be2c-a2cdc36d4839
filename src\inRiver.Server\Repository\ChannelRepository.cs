namespace inRiver.Server.Repository
{
    using System.Collections.Generic;
    using System.Threading;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.EventPublishing;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;
    using inRiver.Server.Util;

    public class ChannelRepository
    {
        private readonly IDataPersistance dataContext;

        private readonly RequestContext context;

        public ChannelRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;

            this.context = context;
        }

        public void ReloadChannel(int id, CancellationToken cancellationToken)
        {
            if (!this.context.UserHasPermission(UserPermission.PublishChannel))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to update channels in inRiver");
            }

            DtoEntity channel = this.dataContext.GetEntity(id);

            if (channel == null)
            {
                return;
            }

            if (channel.EntityTypeId != "Channel" && channel.EntityTypeId != "Publication")
            {
                return;
            }

            this.dataContext.ReloadChannel(id, cancellationToken);
        }

        public List<int> GetDigitalChannelsForEntity(int entityId)
        {
            return this.dataContext.GetDigitalChannelsForEntity(entityId);
        }

        public void UpdateFieldsetForEntity(int entityId, string fieldSetId)
        {
            foreach (int channelId in this.GetDigitalChannelsForEntity(entityId))
            {
                EventPublisher.NotifyChannelEntityFieldsetUpdate(this.context, channelId, entityId, fieldSetId);
            }
        }

        public void SynchronizeChannel(int channelId, CancellationToken cancellationToken)
        {
            if (!this.context.UserHasPermission(UserPermission.PublishChannel))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to update channels in inRiver");
            }

            EventPublisher.NotifyChannelSynchronize(this.context, channelId);

            this.dataContext.SynchronizeChannel(channelId, cancellationToken);
        }

        #region Async updates

        public void UpdateEntity(int entityId, List<Field> fields)
        {
            new ChannelQueueWorker(this.context).Enqueue(new ChannelQueueMessage { Method = "UpdateEntity", EntityId = entityId, Fields = fields });
        }

        public void AddLink(DtoLink link)
        {
            new ChannelQueueWorker(this.context).Enqueue(new ChannelQueueMessage { Method = "AddLink", Data = link });
        }

        public void DeleteLink(DtoLink link)
        {
            new ChannelQueueWorker(this.context).Enqueue(new ChannelQueueMessage { Method = "DeleteLink", Data = link });
        }

        #endregion

        #region Connector Events

        #endregion

        #region Channel Structure

        #endregion
    }
}
