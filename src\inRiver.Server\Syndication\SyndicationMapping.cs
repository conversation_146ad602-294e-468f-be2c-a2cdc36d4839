namespace inRiver.Server.Syndication
{
    using System.Collections.Generic;
    using inRiver.Server.Syndication.Models;

    public class SyndicationMapping
    {
        public int FormatId { get; set; }

        public string WorkareaEntityTypeId { get; set; }

        public string FirstRelatedEntityTypeId { get; set; }

        public int MappingId { get; set; }

        public string MappingName { get; set; }

        public string FirstLinkEntityTypeId { get; set; }

        public List<SyndicationMappingField> SyndicationMappingFields { get; set; }

        public IList<SyndicationMappingResourceField> SyndicationMappingResourceFields { get; set; }
            = new List<SyndicationMappingResourceField>();

        public string SecondRelatedEntityTypeId { get; set; }

        public string SecondLinkEntityTypeId { get; set; }

        public string OutputEntityTypeId { get; set; }

        public bool EnableSKU { get; set; }

        public string DefaultLanguage { get; set; }

        public bool IsResourceExportConfigured { get; set; }
    }
}
