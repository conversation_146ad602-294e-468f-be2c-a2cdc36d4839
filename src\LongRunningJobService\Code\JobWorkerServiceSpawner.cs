namespace LongRunningJobService.Code
{
    using System;
    using System.Fabric;
    using System.Fabric.Description;
    using System.Threading.Tasks;
    using global::LongRunningJobService.Abstractions;
    using global::LongRunningJobService.Config;
    using LongRunningJob.Core.Abstractions;
    using Microsoft.Extensions.Options;

    public class JobWorkerServiceSpawner : IJobWorkerServiceSpawner
    {
        private readonly IEnvironmentContextAccessor environmentContextAccessor;
        private readonly ServiceFabricSettings serviceFabricSettings;

        public JobWorkerServiceSpawner(IEnvironmentContextAccessor environmentContextAccessor, IOptions<ServiceFabricSettings> serviceFabricSettings)
        {
            this.environmentContextAccessor = environmentContextAccessor;
            this.serviceFabricSettings = serviceFabricSettings.Value;
        }

        public async Task SpawnAsync(int jobId)
        {
            var serviceUri = GetServiceUri(this.environmentContextAccessor.EnvironmentContext.CustomerSafename, this.environmentContextAccessor.EnvironmentContext.EnvironmentSafename, jobId);

            using (var client = new FabricClient())
            {
                var serviceDescription = new StatelessServiceDescription()
                {
                    ApplicationName = new Uri(FabricRuntime.GetActivationContext().ApplicationName),
                    ServiceName = serviceUri,
                    ServiceTypeName = "LongRunningJobWorkerServiceType",
                    PartitionSchemeDescription = new UniformInt64RangePartitionSchemeDescription(),
                    ServicePackageActivationMode = ServicePackageActivationMode.ExclusiveProcess,
                    InstanceCount = 1,
                    PlacementConstraints = this.serviceFabricSettings.LongRunningJobWorkerServicePlacementConstraints,
                };

                await client.ServiceManager.CreateServiceAsync(serviceDescription);
            }
        }

        private static Uri GetServiceUri(string customerSafename, string environmentSafename, int jobId)
        {
            if (string.IsNullOrEmpty(customerSafename))
            {
                throw new ArgumentException($"{nameof(customerSafename)} must be specified.");
            }

            if (string.IsNullOrEmpty(environmentSafename))
            {
                throw new ArgumentException($"{nameof(environmentSafename)} must be specified.");
            }

            var builder = new UriBuilder(GetApplicationUri());
            builder.Path += $"/LRJWorkerService/{customerSafename.ToLower()}/{environmentSafename.ToLower()}/{jobId}";
            return builder.Uri;
        }

        private static Uri GetApplicationUri()
        {
            string applicationName;

            using (var context = FabricRuntime.GetActivationContext())
            {
                applicationName = context.ApplicationName;
            }

            return new Uri(applicationName);
        }
    }
}
