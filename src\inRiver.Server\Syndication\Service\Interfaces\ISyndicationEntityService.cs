namespace inRiver.Server.Syndication.Service.Interfaces
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using inRiver.Remoting.Dto;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;

    public interface ISyndicationEntityService
    {
        IEnumerable<int> GetWorkAreaEntityIds(string workAreaId);

        Task<IEnumerable<DtoEntity>> GetEntitiesAsync(IEnumerable<int> ids);

        IList<InRiverEntity> ConvertExportContainersToEntityModels(IEnumerable<ExportContainer> exportContainers);
    }
}
