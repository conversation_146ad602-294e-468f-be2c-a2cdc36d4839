namespace inRiver.Server.EventPublishing
{
    using System;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Text;
    using System.Threading.Tasks;

    public static class MessagingServiceClient
    {
        public static string BaseUrlForLogging { get; private set; }

        private static readonly object InitLock = new object();

        private static HttpClient httpClient => lazyHttpClient?.Value ?? throw new InvalidOperationException($"{nameof(MessagingServiceClient)} is not initialized.");
        private static Lazy<HttpClient> lazyHttpClient;

        public static void Init(string messagingServiceBaseUrl)
        {
            if (!Uri.TryCreate(messagingServiceBaseUrl, UriKind.Absolute, out _))
            {
                throw new ArgumentException($"Invalid {nameof(messagingServiceBaseUrl)}: {messagingServiceBaseUrl}");
            }

            if (lazyHttpClient == null)
            {
                lock (InitLock)
                {
                    if (lazyHttpClient == null)
                    {
                        ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                        BaseUrlForLogging = messagingServiceBaseUrl;
                        lazyHttpClient = new Lazy<HttpClient>(() => CreateHttpClient(messagingServiceBaseUrl));
                    }
                }
            }
        }

        public static async Task PublishEventAsync(string jsonPayload, string customerSafeName, string environmentSafeName, string username)
        {
            var url = $@"api/event/{customerSafeName}/{environmentSafeName}";

            using (var request = new HttpRequestMessage(HttpMethod.Post, url))
            {
                request.Headers.TryAddWithoutValidation("X-Username", username);
                request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                using (var response = await httpClient.SendAsync(request))
                {
                    response.EnsureSuccessStatusCode();
                }
            }
        }

        public static HttpResponseMessage PublishEvent(string jsonPayload, string customerSafeName, string environmentSafeName, string username)
        {
            var url = $@"api/event/{customerSafeName}/{environmentSafeName}";

            using (var request = new HttpRequestMessage(HttpMethod.Post, url))
            {
                request.Headers.TryAddWithoutValidation("X-Username", username);
                request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                return httpClient.SendAsync(request).ConfigureAwait(true).GetAwaiter().GetResult();
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "<Pending>")]
        private static HttpClient CreateHttpClient(string messagingServiceBaseUrl)
        {
            var handler = new HttpClientHandler() { UseCookies = false };
            var httpClient = new HttpClient(handler)
            {
                BaseAddress = new Uri(messagingServiceBaseUrl),
                Timeout = TimeSpan.FromMinutes(5)
            };

            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            httpClient.DefaultRequestHeaders.UserAgent.Add(new ProductInfoHeaderValue("Inriver", "1.0"));

            return httpClient;
        }
    }
}
