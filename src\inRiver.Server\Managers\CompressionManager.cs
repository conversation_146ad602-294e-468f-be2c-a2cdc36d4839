namespace inRiver.Server.Managers
{
    using System;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Access;
    using inRiver.Server.Configuration;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Export;
    using Serilog;

    public class CompressionManager : ICompressionManager
    {
        private readonly TimeSpan compressionServiceTimeout = TimeSpan.FromHours(4);

        private readonly RequestContext requestContext;

        public CompressionManager(RequestContext requestContext)
        {
            this.requestContext = requestContext ?? throw new ArgumentNullException(nameof(requestContext));
        }

        public async Task<string> CallResourceExportAsync(ResourceExportModel resourceData, CancellationToken cancellationToken)
        {
            using var httpClient = StaticHttpClientFactory.CreateHttpClient(
                StaticConfigurationProvider.CompressionServiceUrl,
                this.compressionServiceTimeout);

            var response = await httpClient.PostAsJsonAsync(
                $"api/{this.requestContext.CustomerSafeName}/{this.requestContext.EnvironmentSafeName}/compressions?Timeout={this.compressionServiceTimeout.TotalSeconds}",
                resourceData,
                cancellationToken);

            var content = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                return content;
            }

            Log.Error("Compression Service failed. Status code: {StatusCode}. Content: {Content}", response.StatusCode, content);
            return string.Empty;
        }

        public async Task DeleteFileAsync(string fileName)
        {
            using var httpClient = StaticHttpClientFactory.CreateHttpClient(
                StaticConfigurationProvider.CompressionServiceUrl,
                this.compressionServiceTimeout);

            _ = await httpClient.DeleteAsync(
                $"api/{this.requestContext.CustomerSafeName}/{this.requestContext.EnvironmentSafeName}/compressions/{fileName}");
        }
    }
}
