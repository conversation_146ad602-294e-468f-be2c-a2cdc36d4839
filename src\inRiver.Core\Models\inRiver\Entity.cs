#region Generated Code

using inRiver.Core.Objects;

namespace inRiver.Core.Models.inRiver
{
#endregion

    using System;
    using System.Collections.Generic;

    public class Entity
        : IIdentifierAsIntInterface
    {
        public int Id { get; set; }

        public int Version { get; set; }

        public int ChangeSet { get; set; }

        public string EntityTypeId { get; set; }

        public string Locked { get; set; }

        public List<Field> Fields { get; set; }

        public List<Link> Links { get; set; }

        public string DateCreated { get; set; }

        public string CreatedBy { get; set; }

        public string ModifiedBy { get; set; }

        public string FieldSetId { get; set; }

        public string LastModified { get; set; }

        public LoadLevel LoadLevel { get; set; }

        public int? MainPictureId { get; set; }

        public string MainPictureUrl { get; set; }

        public Field DisplayName { get; set; }

        public Field DisplayDescription { get; set; }

        public int? Completeness { get; set; }

        public int SegmentId { get; set; }

        public List<Link> OutboundLinks
        {
            get
            {
                if (this.Links == null)
                {
                    return new List<Link>();
                }

                if (this.Links.Count == 0)
                {
                    return new List<Link>();
                }

                return this.Links.FindAll(l => l.Source.Id == this.Id);
            }
        }

        public List<Link> InboundLinks
        {
            get
            {
                if (this.Links == null)
                {
                    return new List<Link>();
                }

                if (this.Links.Count == 0)
                {
                    return new List<Link>();
                }

                return this.Links.FindAll(l => l.Target.Id == this.Id);
            }
        }

        public static Entity CreateEntity(EntityType entityType)
        {
            Entity entity = new Entity();
            entity.EntityTypeId = entityType.Id;
            entity.Fields = new List<Field>();

            foreach (FieldType fieldType in entityType.FieldTypes)
            {
                Field field = new Field();
                field.FieldTypeId = fieldType.Id;
                field.Revision = 0;

                if (!string.IsNullOrEmpty(fieldType.DefaultValue))
                {
                    field.Data = GetDefaultValueForDataType(fieldType).ToString();
                }

                entity.Fields.Add(field);
            }

            return entity;
        }

        public override bool Equals(object obj)
        {
            var entity = obj as Entity;
            if (entity == null)
            {
                return false;
            }

            return this.Id == ((Entity)obj).Id;
        }

        public override int GetHashCode()
        {
            return this.Id;
        }

        private static object GetDefaultValueForDataType(FieldType fieldType)
        {
            switch (fieldType.DataType)
            {
                case "Boolean":
                    bool resultBoolean;
                    bool.TryParse(fieldType.DefaultValue, out resultBoolean);

                    return resultBoolean;

                case "Double":
                    double resultDouble;
                    double.TryParse(fieldType.DefaultValue, out resultDouble);

                    return resultDouble;

                case "Integer":
                    int resultInteger;
                    int.TryParse(fieldType.DefaultValue, out resultInteger);

                    return resultInteger;

                case "DateTime":
                    DateTime resultDateTime;
                    DateTime.TryParse(fieldType.DefaultValue, out resultDateTime);

                    return resultDateTime;

                case "CVL":
                    return fieldType.DefaultValue;

                case "String":
                    if (string.IsNullOrEmpty(fieldType.DefaultValue))
                    {
                        return string.Empty;
                    }

                    if (fieldType.DefaultValue.ToLower() == "guid")
                    {
                        return Guid.NewGuid().ToString();
                    }

                    return fieldType.DefaultValue;
            }

            return null;
        }
    }
}
