namespace inRiver.Server.Request
{
    using System.Data.SqlClient;
    using System.Threading.Tasks;

    internal static class StaticEnvironmentContextStore
    {
        internal static async Task<EnvironmentContextData> GetContextDataForEnvironmentAsync(string customerSafeName, string environmentSafeName, string configurationConnectionString)
        {
            if (string.IsNullOrEmpty(customerSafeName) || string.IsNullOrEmpty(environmentSafeName))
            {
                return null;
            }

            EnvironmentContextData contextData;

            using (var connection = new SqlConnection(configurationConnectionString))
            {
                var command = connection.CreateCommand();

                command.CommandText = "SELECT Customer.Id AS 'CustomerId', Customer.Safename AS 'CustomerSafename', Environment.Id AS 'EnvironmentId', " +
                                      "Environment.Safename AS 'EnvironmentSafename', Environment.DbConnectionString, Environment.LogName, Environment.DbConnectionStringLog, " +
                                      "Environment.AssetServiceUrl, Environment.AssetServiceInternalUrl, Environment.FullName, JobServiceUrl, Environment.EntityModel, Environment.EnvironmentLocation " +
                                      "FROM Customer INNER JOIN CustomerEnvironment ON Customer.Id = CustomerEnvironment.CustomerId INNER JOIN Environment ON CustomerEnvironment.EnvironmentId = dbo.Environment.Id " +
                                      "WHERE Customer.Safename = @CustomerSafename AND Environment.Safename = @EnvironmentSafename";

                _ = command.Parameters.AddWithValue("@CustomerSafename", customerSafeName);
                _ = command.Parameters.AddWithValue("@EnvironmentSafename", environmentSafeName);
                await connection.OpenAsync();

                using (var reader = await command.ExecuteReaderAsync())
                {
                    if (!reader.HasRows)
                    {
                        return null;
                    }

                    _ = await reader.ReadAsync();

                    contextData = new EnvironmentContextData
                    {
                        CustomerId = reader.GetInt32(reader.GetOrdinal("CustomerId")),
                        CustomerSafeName = reader.GetString(reader.GetOrdinal("CustomerSafename")),
                        EnvironmentId = reader.GetInt32(reader.GetOrdinal("EnvironmentId")),
                        EnvironmentSafeName = reader.GetString(reader.GetOrdinal("EnvironmentSafename")),
                        ConnectionString = reader.GetString(reader.GetOrdinal("DbConnectionString")),
                        LogTable = reader.GetString(reader.GetOrdinal("LogName")),
                        LogConnectionString = reader.GetString(reader.GetOrdinal("DbConnectionStringLog")),
                        AssetServiceUrl = reader.GetString(reader.GetOrdinal("AssetServiceUrl")),
                        AssetServiceInternalUrl = reader.GetString(reader.GetOrdinal("AssetServiceInternalUrl")),
                        EnvironmentFullName = reader.GetString(reader.GetOrdinal("FullName")),
                        JobServiceUrl = reader.GetString(reader.GetOrdinal("JobServiceUrl")),
                        EntityModel = reader.GetInt32(reader.GetOrdinal("EntityModel")),
                        EnvironmentLocation = reader.GetString(reader.GetOrdinal("EnvironmentLocation")),
                    };
                }
            }

            return contextData;
        }
    }
}
