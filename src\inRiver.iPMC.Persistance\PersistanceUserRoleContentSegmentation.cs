namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using inRiver.Log;

    public class PersistanceUserRoleContentSegmentation : BasePersistance, IPersistanceUserRoleContentSegmentation
    {
        public PersistanceUserRoleContentSegmentation(
            string connectionString, 
            ICommonLogging logInstance,
            IContentSegmentPermissionProvider contentSegmentProvider) 
            
            : base(connectionString, logInstance, contentSegmentProvider)
        { }

        public List<UserRoleSegment> GetUserRoleContentSegmentationByUserId(int userId)
        {
            var userRoleContentSegmentations = new List<UserRoleSegment>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                try
                {
                    var cmd = connection.CreateCommand();
                    cmd.CommandText = "SELECT UserId, RoleId, ContentSegmentationId " +
                                      "FROM [dbo].[UserRoles] " +
                                      "WHERE UserId = @UserId;";

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    connection.Open();

                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var userRoleContentSegmentation = new UserRoleSegment
                            {
                                UserId = reader.GetInt32(0),
                                RoleId = reader.GetInt32(1),
                                SegmentId = reader.GetInt32(2)
                            };

                            userRoleContentSegmentations.Add(userRoleContentSegmentation);
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    LogInstance.Error($"An unexpected error occured when getting UserRoleContentSegmentations for user {userId}", ex, string.Empty, string.Empty);
                }
            }
            return userRoleContentSegmentations;
        }
    }
}
