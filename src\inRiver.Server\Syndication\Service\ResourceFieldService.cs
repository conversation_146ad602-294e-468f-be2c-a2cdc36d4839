namespace inRiver.Server.Syndication.Service
{
    using inRiver.Server.Syndication.Constants;
    using inRiver.Server.Syndication.Service.Interfaces;

    public class ResourceFieldService : IResourceFieldService
    {
        public bool IsResourceFieldType(string fieldType, string resourceFieldType)
            => !string.IsNullOrEmpty(fieldType) && fieldType.EndsWith(resourceFieldType);

        public string GetImageUrlTypeFromImageFieldType(string fieldType)
            => fieldType.Replace(ResourceFieldTypes.ImageUrl, ResourceFieldTypes.Image);
    }
}
