﻿namespace inRiver.Core.Extension.Augmenta
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Text.Json;
    using inRiver.Core.Models.Augmenta;
    using inRiver.Remoting.Objects;
    using static System.StringComparison;

    internal static class FieldExtensions
    {
        public static FieldInputModel ToFieldInputModel(this Field field) =>
            new FieldInputModel
            {
                FieldTypeId = field.FieldType.Id,
                Data = field.Data is LocaleString localeString
                    ? localeString.ToDictionary(localeString.Languages).Where(x => !string.IsNullOrEmpty(x.Value))
                        .ToDictionary(x => x.Key, x => x.Value)
                    : field.Data,
            };

        public static Field UpdateFieldData(this Field field, FieldOutputModel fieldOutputModel, string dataType)
        {
            field.Data = GetFieldData(fieldOutputModel, dataType);
            return field;
        }

        private static object GetFieldData(FieldOutputModel fieldOutputModel, string dataType)
        {
            var data = fieldOutputModel?.Data;

            if (!(data is JsonElement jsonElement))
            {
                return null;
            }

            switch (dataType.ToUpperInvariant())
            {
                case "STRING":
                case "XML":
                case "CVL":
                    return jsonElement.ToString();

                case "INTEGER":
                case "FILE":
                    {
                        if (!jsonElement.TryGetInt32(value: out var valueInteger))
                        {
                            return null;
                        }

                        return valueInteger;
                    }

                case "DOUBLE":
                    {
                        if (!jsonElement.TryGetDouble(value: out var valueDouble))
                        {
                            return null;
                        }

                        return valueDouble;
                    }

                case "DATETIME":
                    {
                        if (!jsonElement.TryGetDateTime(out var valueDateTime))
                        {
                            return null;
                        }

                        return valueDateTime;
                    }

                case "BOOLEAN":
                    {
                        if (jsonElement.ValueKind == JsonValueKind.False)
                        {
                            return false;
                        }

                        if (jsonElement.ValueKind == JsonValueKind.True)
                        {
                            return true;
                        }

                        return null;
                    }

                case "LOCALESTRING":
                    {
                        var localeString = new LocaleString();
                        foreach (var property in jsonElement.EnumerateObject())
                        {
                            localeString[new CultureInfo(property.Name)] = property.Value.ToString();
                        }

                        return localeString;
                    }

                default:
                    return null;
            }
        }
    }
}
