namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Diagnostics;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.ServiceModel;
    using System.Threading;
    using System.Threading.Tasks;
    using Dapper;
    using Error;
    using inRiver.Remoting.Objects;
    using inRiver.Server.EventPublishing;
    using Remoting.Dto;
    using Remoting.Log;

    // ReSharper disable once InconsistentNaming
    [SuppressMessage("StyleCop.CSharp.NamingRules", "SA1300:ElementMustBeginWithUpperCaseLetter", Justification = "Reviewed. Suppression is OK here.")]
    public partial class inRiverPersistance
    {

        public void ReloadChannel(int channelId) => this.ReloadChannel(channelId, CancellationToken.None);

        public void ReloadChannel(int channelId, CancellationToken cancellationToken)
        {
            var channel = this.context.DataPersistance.GetEntity(channelId);
            if (channel == null)
            {
                Serilog.Log.Warning($"Triggered re-building for non-existing channel/publication with id {channelId} in {this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}");
                return;
            }

            var displayField = this.context.DataPersistance.GetField(channelId, channel.DisplayName?.FieldTypeId);

            this.context.Log(
                LogLevel.Information,
                displayField != null ? $"Started re-building channel with id {channelId} ({displayField.Data})" : $"Started re-building channel with id {channelId}");
            Serilog.Log.Information($"Rebuild channel: Start for id {channelId} in {this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}");
            var stopwatch = Stopwatch.StartNew();

            this.UpdateChannelFilter(cancellationToken);
            this.UpdateEnhancedChannelFilter(cancellationToken);
            Serilog.Log.Information($"Rebuild channel: ChannelFilter update for id {channelId} in {this.context.CustomerSafeName}/{this.context.EnvironmentSafeName} took {stopwatch.ElapsedMilliseconds} ms");

            cancellationToken.ThrowIfCancellationRequested();
            this.context.DataPersistance.CreateChannelStructure(channelId, channel.EntityTypeId, cancellationToken);
            Serilog.Log.Information($"Rebuild channel: Create structure for id {channelId} in {this.context.CustomerSafeName}/{this.context.EnvironmentSafeName} took {stopwatch.ElapsedMilliseconds} ms");
            stopwatch.Stop();
            this.context.Log(
                LogLevel.Information,
                $"Finished re-building channel with id {channelId}. Took {stopwatch.ElapsedMilliseconds} ms.");
            Serilog.Log.Information($"Rebuild channel: Finished for id {channelId} in {this.context.CustomerSafeName}/{this.context.EnvironmentSafeName} took {stopwatch.ElapsedMilliseconds} ms");
        }

        public void SynchronizeChannel(int channelId, CancellationToken cancellationToken)
        {
            Stopwatch stopwatch = Stopwatch.StartNew();

            DtoEntity channel = context.DataPersistance.GetEntity(channelId);

            DtoField displayField = context.DataPersistance.GetField(channelId, channel.DisplayName.FieldTypeId);

            if (displayField != null)
            {
                context.Log(LogLevel.Information, $"Started checking channel with id {channelId} ({displayField.Data})");
            }
            else
            {
                context.Log(LogLevel.Information, $"Started checking channel with id {channelId}");
            }

            string entityPath = channelId.ToString(CultureInfo.InvariantCulture);

            string fullPath = channel.EntityTypeId + "_" + channelId;

            if (!this.CheckChannelStructureElementExists(fullPath))
            {
                context.DataPersistance.ReloadChannel(channelId);

                return;
            }

            var entityPaths = this.GetAllEntityPathsForChannel(channelId, cancellationToken);

            foreach (var link in this.GetOutboundLinksForEntity(channel.Id, cancellationToken))
            {
                this.SynchronizeChannelEntity(channelId, channel.EntityTypeId, link, entityPath, fullPath, null, entityPaths, cancellationToken);
            }

            this.RemoveChannelStructureElementsWithoutLinks(channelId, channel.EntityTypeId, cancellationToken);
            cancellationToken.ThrowIfCancellationRequested();

            stopwatch.Stop();

            context.Log(LogLevel.Information, $"Finished checking channel with id {channelId}. Took {stopwatch.ElapsedMilliseconds} ms");
        }

        public List<int> GetDigitalChannelsForEntity(int entityId)
        {
            List<int> channels = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT DISTINCT ChannelId FROM [ChannelStructure] WHERE (EntityId = @EntityId OR LinkEntityId = @EntityId) AND ChannelType = 'Channel'";
                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            channels.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when getting digitals channels for entity " + entityId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting digitals channels for entity " + entityId, ex);
                }
            }

            return channels;
        }

        public List<int> GetAllEntityIdsForChannel(int channelId)
        {
            List<int> entityIds = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT DISTINCT EntityId FROM [ChannelStructure] WHERE ChannelId = @ChannelId";

                    command.Parameters.AddWithValue("@ChannelId", channelId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            entityIds.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when getting all channel entity ids.");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when getting all channel entity ids.", ex);
                }
            }

            return entityIds;
        }

        public async Task<IList<Models.ChannelStructure>> GetChannelStructureByEntityTypeIdsForChannelAsync(int channelId, IList<string> entityTypeIds)
        {
            var structures = new List<Models.ChannelStructure>();
            try
            {
                await using var connection = new SqlConnection(this.ConnectionString);
                var command = connection.CreateCommand();
                var entityTypeIdsList = entityTypeIds.Select((type, index) => $"@EntityTypeId{index}").ToList();
                var inClause = string.Join(",", entityTypeIdsList);
                command.CommandText = $"SELECT [EntityId], [EntityTypeId], [FullPath] FROM [ChannelStructure] WHERE [ChannelId] = @ChannelId AND [EntityTypeId] in ({inClause})";
                command.Parameters.AddWithValue("@ChannelId", channelId);
                for (var i = 0; i < entityTypeIds.Count; i++)
                {
                    command.Parameters.AddWithValue($"@EntityTypeId{i}", entityTypeIds[i]);
                }

                await connection.OpenAsync();
                await using var reader = await command.ExecuteReaderAsync();
                if (!reader.HasRows)
                {
                    return structures;
                }

                var entityIdIndex = reader.GetOrdinal("EntityId");
                var fullPathIndex = reader.GetOrdinal("FullPath");
                var entityTypeIdIndex = reader.GetOrdinal("EntityTypeId");

                while (reader.Read())
                {
                    var channelStructure = new Models.ChannelStructure
                    {
                        EntityId = reader.GetInt32(entityIdIndex),
                        FullPath = reader.GetString(fullPathIndex),
                        EntityTypeId = reader.GetString(entityTypeIdIndex)

                    };
                    structures.Add(channelStructure);
                }
            }
            catch (Exception ex)
            {
                const string errorMessage = "An unexpected error occurred when getting channel structure by entity type ids";
                Serilog.Log.Error(ex, errorMessage);
                throw ErrorUtility.GetDataAccessException(errorMessage, ex);
            }

            return structures;
        }

        public List<int> GetAllEntityIdsNotInChannel(int channelId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    string query = @"
                        WITH etble AS (
                            SELECT Id
                            FROM Entity
                            WHERE EntityTypeId <> 'Channel'
                        )
                        SELECT DISTINCT e.Id
                        FROM etble e
                        LEFT JOIN ChannelStructure cs ON e.Id = cs.EntityId AND cs.ChannelId = @ChannelId
                        WHERE cs.EntityId IS NULL;
                    ";

                    connection.Open();
                    var entityIds = connection.Query<int>(query, new { ChannelId = channelId }).ToList();
                    return entityIds;
                }
                catch (Exception ex)
                {
                    Serilog.Log.Error(ex, "An unexpected error occurred when getting all channel entity ids.");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when getting all channel entity ids.", ex);
                }
            }
        }

        #region Synchronize Channel

        private void RemoveChannelStructureElementsWithoutLinks(int channelId, string channelType, CancellationToken cancellationToken)
        {
            List<int> idsToRemove = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT ChannelStructure.Id, ChannelStructure.EntityId, ChannelStructure.ParentId, ChannelStructure.LinkTypeId, ChannelStructure.LinkEntityId " +
                                            "FROM ChannelStructure LEFT OUTER JOIN Link ON " +
                                            "ChannelStructure.EntityId = Link.TargetEntityId AND ChannelStructure.ParentId = Link.SourceEntityId AND ChannelStructure.LinkTypeId = Link.LinkTypeId AND " +
                                            "(ChannelStructure.LinkEntityId IS NULL AND Link.LinkEntityId IS NULL OR ChannelStructure.LinkEntityId = Link.LinkEntityId) " +
                                            "WHERE ChannelStructure.ChannelId = @ChannelId AND Link.Id IS NULL AND ChannelStructure.EntityId <> ChannelStructure.ChannelId";

                    command.Parameters.AddWithValue("@ChannelId", channelId);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            if (cancellationToken.IsCancellationRequested)
                            {
                                break;
                            }

                            idsToRemove.Add(reader.GetInt32(0));

                            if (channelType != "Channel")
                            {
                                continue;
                            }

                            int target = reader.GetInt32(1);
                            int source = reader.GetInt32(2);
                            string linkTypeId = reader.GetString(3);

                            if (!reader.IsDBNull(4))
                            {
                                int? linkEntityId = reader.GetInt32(4);

                                EventPublisher.NotifyChannelLinkDeleted(
                                    this.context,
                                    channelId,
                                    new DtoLink()
                                    {
                                        Source = new DtoEntity { Id = source },
                                        Target = new DtoEntity { Id = target },
                                        LinkTypeId = linkTypeId,
                                        LinkEntity = new DtoEntity { Id = linkEntityId.Value }
                                    });
                            }
                            else
                            {
                                EventPublisher.NotifyChannelLinkDeleted(
                                    this.context,
                                    channelId,
                                    new DtoLink
                                    {
                                        Source = new DtoEntity { Id = source },
                                        Target = new DtoEntity { Id = target },
                                        LinkTypeId = linkTypeId
                                    });
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (FaultException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when checking channel structure element");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when checking channel structure element",
                        ex);
                }
            }

            foreach (int id in idsToRemove)
            {
                DeleteChannelStructureElement(id);
            }
        }

        private Dictionary<int, List<string>> GetAllEntityPathsForChannel(int channelId, CancellationToken cancellationToken)
        {
            Dictionary<int, List<string>> allEntityPaths = new Dictionary<int, List<string>>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = "SELECT EntityId, FullPath FROM ChannelStructure WHERE [ChannelId] = @ChannelId";

                    command.Parameters.AddWithValue("@ChannelId", channelId);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            int entityId = reader.GetInt32(0);
                            string path = reader.GetString(1);

                            if (!allEntityPaths.ContainsKey(entityId))
                            {
                                allEntityPaths.Add(entityId, new List<string>());
                            }

                            if (!allEntityPaths[entityId].Contains(path))
                            {
                                allEntityPaths[entityId].Add(path);
                            }
                            else
                            {
                                allEntityPaths[entityId].Add(path);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (FaultException)
                {
                    throw;
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when getting all channel structure elements");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all channel structure elements", ex);
                }
            }

            return allEntityPaths;
        }

        private void SynchronizeChannelEntity(
            int channelId,
            string channelType,
            DtoLink link,
            string parentEntityPath,
            string parentFullPath,
            DtoEntity linkEntity,
            Dictionary<int, List<string>> entityPaths,
            CancellationToken cancellationToken)
        {
            if (this.ShouldStopTraversal(channelId, link))
            {
                return;
            }

            cancellationToken.ThrowIfCancellationRequested();

            string entityPath = parentEntityPath + "/" + link.Target.Id;

            string fullPath;

            if (link.Source.EntityTypeId == "Channel" || link.Source.EntityTypeId == "Publication")
            {
                fullPath = string.Format("{0}/{1}_{2}", parentFullPath, link.Target.EntityTypeId, link.Target.Id);
            }
            else
            {
                if (linkEntity != null)
                {
                    fullPath = string.Format("{0}/Link_{1}_{2}/{3}_{4}", parentFullPath, link.LinkTypeId, linkEntity.Id, link.Target.EntityTypeId, link.Target.Id);
                }
                else
                {
                    fullPath = string.Format("{0}/Link_{1}/{2}_{3}", parentFullPath, link.LinkTypeId, link.Target.EntityTypeId, link.Target.Id);
                }
            }

            if (this.HasChannelFilter(channelId))
            {
                if (this.context.DataPersistance.IsExcludedByChannelFilter(channelId, link.Target.Id, link.LinkTypeId))
                {
                    if (entityPaths.ContainsKey(link.Target.Id) && entityPaths[link.Target.Id].Contains(fullPath))
                    {
                        if (channelType == "Channel")
                        {
                            EventPublisher.NotifyChannelLinkDeleted(this.context, channelId, link);
                        }

                        foreach (int id in GetChannelStructureIdsByPath(fullPath))
                        {
                            DeleteChannelStructureElement(id);
                        }
                    }

                    return;
                }
            }

            cancellationToken.ThrowIfCancellationRequested();
            if (!entityPaths.ContainsKey(link.Target.Id) || !entityPaths[link.Target.Id].Contains(fullPath))
            {
                context.Log(LogLevel.Information, "Channel structure entity with path " + fullPath + " is missing. Adding updated structure.");
                if (linkEntity != null)
                {
                    SaveChannelStructureElement(channelId, channelType, link.Target.Id, link.Target.EntityTypeId, link.Source.Id, link.LinkTypeId, link.Index, entityPath, fullPath, linkEntity.Id);
                }
                else
                {
                    SaveChannelStructureElement(channelId, channelType, link.Target.Id, link.Target.EntityTypeId, link.Source.Id, link.LinkTypeId, link.Index, entityPath, fullPath, null);
                }

                if (channelType == "Channel")
                {
                    EventPublisher.NotifyChannelLinkAdded(this.context, channelId, link);
                }
            }

            cancellationToken.ThrowIfCancellationRequested();
            RemoveDuplicates(entityPaths, link.Target.Id);

            if (link.LinkEntity?.Id != null)
            {
                return;
            }

            if (this.EntityTypeAlreadyExistsInParent(parentFullPath, link.Target.EntityTypeId))
            {
                return;
            }

            cancellationToken.ThrowIfCancellationRequested();
            List<DtoLink> childLinks = this.GetOutboundLinksForEntity(link.Target.Id, cancellationToken);

            foreach (DtoLink childLink in childLinks)
            {
                cancellationToken.ThrowIfCancellationRequested();
                if (this.CircularReference(fullPath, childLink.Target.EntityTypeId + "_" + childLink.Target.Id))
                {
                    continue;
                }

                this.SynchronizeChannelEntity(channelId, channelType, childLink, entityPath, fullPath, childLink.LinkEntity, entityPaths, cancellationToken);
            }
        }

        private void RemoveDuplicates(Dictionary<int, List<string>> entityPaths, int key)
        {
            if (!entityPaths.ContainsKey(key))
            {
                return;
            }

            var duplicates = entityPaths[key].GroupBy(x => x)
                        .Where(group => group.Count() > 1)
                        .Select(group => group.Key);

            IEnumerable<string> duplicatePaths = duplicates as IList<string> ?? duplicates.ToList();

            if (!duplicatePaths.Any())
            {
                return;
            }

            foreach (string duplicatePath in duplicatePaths)
            {
                List<int> ids = this.GetChannelStructureIdsByPath(duplicatePath);

                if (ids.Count <= 1)
                {
                    continue;
                }

                context.Log(LogLevel.Information, "Found " + ids.Count + " duplicates for path " + duplicatePath);

                for (int i = 1; i < ids.Count; i++)
                {
                    DeleteChannelStructureElement(ids[i]);
                }
            }
        }

        private void DeleteChannelStructureElement(int id)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "DELETE FROM ChannelStructure WITH (ROWLOCK) WHERE Id = @Id";

                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (FaultException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when deleting channel structure element");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when deleting channel structure element",
                        ex);
                }
            }
        }

        private List<int> GetChannelStructureIdsByPath(string fullPath)
        {
            List<int> ids = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id FROM ChannelStructure WHERE [FullPath] = @FullPath";

                    command.Parameters.AddWithValue("@FullPath", fullPath);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (FaultException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when checking channel structure element");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when checking channel structure element",
                        ex);
                }
            }

            return ids;
        }

        private bool CheckChannelStructureElementExists(string fullPath)
        {
            bool exists = false;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id FROM ChannelStructure WHERE [FullPath] = @FullPath";

                    command.Parameters.AddWithValue("@FullPath", fullPath);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            exists = true;
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (FaultException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when checking channel structure element");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when checking channel structure element", ex);
                }
            }

            return exists;
        }

        #endregion

        private bool EntityTypeAlreadyExistsInParent(string parentFullPath, string entityType)
        {
            if (entityType.Equals("Publication") || entityType.Equals("Channel"))
            {
                return false;
            }

            if (entityType.Equals("Section") || entityType.Equals("ChannelNode"))
            {
                return false;
            }

            foreach (string part in parentFullPath.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries))
            {
                if (part.StartsWith(entityType + "_"))
                {
                    return true;
                }
            }

            return false;
        }

        private bool CircularReference(string parentFullPath, string entityPath)
        {
            foreach (string part in parentFullPath.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries))
            {
                if (part.Equals(entityPath))
                {
                    return true;
                }
            }

            return false;
        }

        // TODO: Remove this when we improve channel filters to include link types
        private bool ShouldStopTraversal(int channelId, DtoLink link)
        {
            if (link == null)
            {
                return true;
            }

            if (link.Inactive)
            {
                return true;
            }

            string sourceEntityTypeId = link.Source.EntityTypeId;

            if (this.StopAssortmentTraversal(sourceEntityTypeId))
            {
                return true;
            }

            if (this.StopSpecificationTraversal(link))
            {
                return true;
            }

            if (this.context.DataPersistance.IsExcludedByChannelFilter(channelId, link.Target.Id, link.LinkTypeId))
            {
                return true;
            }

            return false;
        }

        public bool HasChannelFilter(int channelId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var result = connection.QueryFirstOrDefault(
                        @"SELECT TOP(1) Id FROM ChannelFilter WHERE ChannelId = @channelId 
                          UNION all
                          SELECT TOP(1) Id FROM ChannelFilterEnhanced WHERE ChannelId = @channelId",
                        new { channelId });
                    return result != null;
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, $"An unexpected error occurred in HasChannelFilter channelId: {channelId}. ({context.CustomerSafeName}/{context.EnvironmentSafeName})");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred in HasChannelFilter channelId: {channelId}", ex);
                }
            }
        }

        public bool IsExcludedByChannelFilter(int channelId, int entityId, string linkTypeId)
        {
            bool isExcluded = true;
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    using (SqlCommand command =
                        new SqlCommand("SELECT dbo.IsExcludedByChannelFilterEnhanced(@channelId, @entityId, @linkTypeId)", connection))
                    {
                        command.CommandType = CommandType.Text;

                        command.Parameters.AddWithValue("@channelId", channelId);
                        command.Parameters.AddWithValue("@entityId", entityId);
                        command.Parameters.AddWithValue("@linkTypeId", linkTypeId);

                        connection.Open();
                        isExcluded = (bool)command.ExecuteScalar();
                        connection.Close();
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, $"An unexpected error occurred in IsExcludedByChannelFilter channelId: {channelId} entityId: {entityId}. ({context.CustomerSafeName}/{context.EnvironmentSafeName})");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred in IsExcludedByChannelFilter channelId: {channelId} entityId: {entityId}", ex);
                }
            }

            return isExcluded;
        }

        private bool StopSpecificationTraversal(DtoLink link)
        {
            if (link.Source.EntityTypeId != "Specification")
            {
                return false;
            }

            if (link.Target.EntityTypeId != "Specification")
            {
                return false;
            }

            string setting = this.GetServerSetting("EXCLUDE_SPECIFICATION_STRUCTURE_FROM_CHANNEL");

            if (string.IsNullOrWhiteSpace(setting))
            {
                return false;
            }

            bool result;

            if (!bool.TryParse(setting, out result))
            {
                return false;
            }

            return result;
        }

        private bool StopAssortmentTraversal(string entityTypeId)
        {
            if (entityTypeId != "Assortment")
            {
                return false;
            }

            string setting = this.GetServerSetting("EXCLUDE_ASSORTMENT_STRUCTURE_FROM_CHANNEL");

            if (string.IsNullOrWhiteSpace(setting))
            {
                return false;
            }

            bool result;

            if (!bool.TryParse(setting, out result))
            {
                return false;
            }

            return result;
        }

        private void SaveChannelStructureElement(int channelId, string channelType, int entityId, string entityTypeId, int parentId, string linkTypeId, int sortorder, string entityPath, string fullPath, int? linkEntityId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "INSERT INTO ChannelStructure ([ChannelId], [ChannelType], [EntityId], [EntityTypeId], [ParentId], [LinkTypeId], [SortOrder], [EntityPath], [FullPath], [LinkEntityId]) VALUES (@ChannelId, @ChannelType, @EntityId, @EntityTypeId, @ParentId, @LinkTypeId, @SortOrder, @EntityPath, @FullPath, @LinkEntityId)";

                    command.Parameters.AddWithValue("@ChannelId", channelId);
                    command.Parameters.AddWithValue("@ChannelType", channelType);
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                    command.Parameters.AddWithValue("@ParentId", parentId);

                    if (string.IsNullOrEmpty(linkTypeId))
                    {
                        command.Parameters.AddWithValue("@LinkTypeId", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);
                    }

                    command.Parameters.AddWithValue("@SortOrder", sortorder);
                    command.Parameters.AddWithValue("@EntityPath", entityPath);
                    command.Parameters.AddWithValue("@FullPath", fullPath);

                    if (linkEntityId.HasValue)
                    {
                        command.Parameters.AddWithValue("@LinkEntityId", linkEntityId);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@LinkEntityId", DBNull.Value);
                    }

                    connection.Open();
                    command.ExecuteNonQuery();

                    connection.Close();
                }
                catch (FaultException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when adding channel structure element");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when adding channel structure element", ex);
                }
            }
        }

        public void CreateChannelStructure(int channelId, string entityTypeId, CancellationToken cancellationToken)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = "exec CreateChannelStructureEnhanced @ChannelId, @EntityTypeId";
                    command.Parameters.AddWithValue("@ChannelId", channelId);
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                    command.CommandTimeout = Convert.ToInt32(72000);
                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, $"An unexpected error occurred when create the channel structure for id: {channelId} ({this.context.CustomerSafeName}/{this.context.EnvironmentSafeName})");
                    throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when create the channel structure for id: {channelId}", ex);
                }
            }
        }

        private void UpdateChannelFilter(CancellationToken cancellationToken)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = "exec UpdateChannelFilter";
                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Serilog.Log.Error(ex, "An unexpected error occurred when update the channel filter");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when update the channel filter", ex);
                }
            }
        }

        private void UpdateEnhancedChannelFilter(CancellationToken cancellationToken)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                using var command = connection.CreateCommand();
                _ = cancellationToken.Register(() => {
                    command.Cancel();
                    cancellationToken.ThrowIfCancellationRequested();
                });
                command.CommandText = "UpdateChannelFilterEnhanced";
                command.CommandType = CommandType.StoredProcedure;
                connection.Open();
                _ = command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "An unexpected error occurred when updating enhanced channel filter");
                throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating enhanced channel filter", ex);
            }
        }
    }
}
