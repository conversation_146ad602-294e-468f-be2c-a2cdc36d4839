namespace LongRunningJob.Core.Repositories
{
    using System.Threading.Tasks;
    using inRiver.Core.EnvironmentSettings;
    using inRiver.Core.Models.inRiver;
    using LongRunningJob.Core.Abstractions;

    public class EnvironmentSettingsRepository : IEnvironmentSettingsRepository
    {
        private readonly IEnvironmentContextAccessor environmentContextAccessor;
        private readonly IRequestContextFactory requestContextFactory;

        public EnvironmentSettingsRepository(IEnvironmentContextAccessor environmentContextAccessor, IRequestContextFactory requestContextFactory)
        {
            this.environmentContextAccessor = environmentContextAccessor;
            this.requestContextFactory = requestContextFactory;
        }

        public async Task<EnvironmentSetting> GetEnvironmentSettingAsync(string nameOfService, string key)
        {
            var requestContext = await this.requestContextFactory.CreateAsync(
                this.environmentContextAccessor.EnvironmentContext.CustomerSafename,
                this.environmentContextAccessor.EnvironmentContext.EnvironmentSafename);

            var environmentSettingsRepository = RepositoryFactory.GetEnvironmentSettingsRepository(nameOfService, requestContext.Username, requestContext);
            return environmentSettingsRepository.GetEnvironmentSetting(key, requestContext.EnvironmentId);
        }
    }
}
