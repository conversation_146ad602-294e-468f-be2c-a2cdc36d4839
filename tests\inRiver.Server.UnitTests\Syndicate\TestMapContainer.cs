namespace inRiver.Server.UnitTests.Syndicate
{
    using inRiver.Core.Http;
    using FakeItEasy;
    using inRiver.Log;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Service;
    using Xunit;
    using inRiver.Server.Syndication.Enums;
    using Microsoft.Extensions.DependencyInjection;
    using inRiver.Server.Service;

    public class TestMapContainer
    {
        private const int MappingId = 23;
        private const int MappingFormatId = 1;
        private const string SourceEntityTypeId = "2";
        private const string TargetEntityTypeId = "3";

        public TestMapContainer()
        {
            var services = new ServiceCollection();

            _ = services.AddHttpClient<IOutputAdapterHttpClient, OutputAdapterHttpClient>();

            StaticServiceProvider.Configure(services.BuildServiceProvider());
        }

        public static TestMapContainer Instance { get; } = new TestMapContainer();

        private static RequestContext context
        {
            get {
                var persistance = new Fake<IDataPersistance>();
                persistance.CallsTo(x => x.GetMappingDetails(MappingId))
                    .Returns(FakePersistance.GetSyndicationMapping(MappingId));
                persistance.CallsTo(x => x.GetMapFormat(MappingFormatId))
                    .Returns(FakePersistance.GetMapType());
                persistance.CallsTo(x => x.GetLinkTypes(SourceEntityTypeId, TargetEntityTypeId))
                    .Returns(FakePersistance.GetLinkTypes());

                return new RequestContext
                {
                    DataPersistance = persistance.FakedObject,
                    Logging = new Fake<ICommonLogging>().FakedObject
                };
            }
        }

        [Fact]
        public void TestMapFieldsHasCorrectMapFieldType()
        {
            var resourceExportService = A.Fake<ResourceExportService>();
            var mapManager = new MapManager(context.DataPersistance, MappingId, resourceExportService, SyndicationMappingSource.Extension, "devenv");
            mapManager.Load();

            var hasErrors = false;
            foreach (var field in mapManager.MapContainer.MapFields)
            {
                // MapFieldTypeId equals DefaultValue in FakePersistance
                if (field.MapFieldTypeId != field.MapFieldType.DefaultValue)
                {
                    hasErrors = true;
                    break;
                }
            }

            Assert.False(hasErrors, "TestLoadMapType failed");
        }
    }
}
