namespace inRiver.Server.UnitTests.Syndicate.DefaultFunctions
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using FakeItEasy;
    using inRiver.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Mapping;

    internal static class DefaultFunctionsData
    {
        internal static LocaleString LocaleStringValue => new LocaleString
        {
            [new CultureInfo("ar")] = "ar value",
            [new CultureInfo("en")] = "en value",
            [new CultureInfo("sv")] = "sv value",
            [new CultureInfo("fr")] = null,
            [new CultureInfo("pl")] = string.Empty
        };

        internal static List<CultureInfo> ServerLanguages => new List<CultureInfo>
        {
            new CultureInfo("ar"),
            new CultureInfo("en"),
            new CultureInfo("sv"),
            new CultureInfo("fr"),
            new CultureInfo("pl")
        };

        internal static Dictionary<string, string> LSDictionary =>
            LocaleStringValue.ToDictionary(ServerLanguages);

        internal static InRiverEntity Entity => new InRiverEntity
        {
            Fields = new List<InRiverField>
            {
                new InRiverField
                {
                    FieldType = new InRiverFieldType { FieldTypeId = "ProductNameLS", DataType = "LocaleString" },
                    Data = LocaleStringValue
                },
                new InRiverField
                {
                    FieldType = new InRiverFieldType { FieldTypeId = "ProductNameCVLSingle", DataType = "CVL" },
                    Data = "black"
                },
                new InRiverField
                {
                    FieldType = new InRiverFieldType { FieldTypeId = "ProductNameCVLMulti", DataType = "CVL" },
                    Data = "black;green"
                },
                new InRiverField
                {
                    FieldType = new InRiverFieldType { FieldTypeId = "ProductNameString", DataType = "String" },
                    Data = "Product name string"
                }
            },
            RelatedEntities = new List<InRiverEntity>
            {
                new InRiverEntity
                {
                    Fields = new List<InRiverField>
                    {
                        new InRiverField
                        {
                            FieldType = new InRiverFieldType { FieldTypeId = "ProductNameEmpty", DataType = "String" },
                            Data = null
                        },
                    }
                }
            }
        };

        internal static RequestContext RequestContext
        {
            get {
                var persistance = new Fake<IDataPersistance>();
                persistance.CallsTo(x => x.GetCVLValuesForCVL("stringCVL")).Returns(new List<CVLValue>
                {
                    new CVLValue { CVLId = "stringCVL", Key = "black", Value = "Black" },
                    new CVLValue { CVLId = "stringCVL", Key = "green", Value = "Green" },
                    new CVLValue { CVLId = "stringCVL", Key = "blue", Value = string.Empty },
                });
                persistance.CallsTo(x => x.GetCVLValuesForCVL("localeStringCVL")).Returns(new List<CVLValue>
                {
                    new CVLValue { CVLId = "localeStringCVL", Key = "black", Value = new LocaleString
                        {
                            [new CultureInfo("ar")] = "ar black",
                            [new CultureInfo("en")] = "en black",
                            [new CultureInfo("sv")] = "sv black",
                            [new CultureInfo("fr")] = null,
                            [new CultureInfo("pl")] = string.Empty
                        }
                    },
                    new CVLValue { CVLId = "localeStringCVL", Key = "green", Value = new LocaleString
                    {
                        [new CultureInfo("ar")] = "ar green",
                        [new CultureInfo("en")] = "en green",
                        [new CultureInfo("sv")] = null,
                        [new CultureInfo("fr")] = null,
                        [new CultureInfo("pl")] = string.Empty
                    } },
                });
                persistance.CallsTo(x => x.GetCvlIdForFieldType("ProductNameCVLMulti")).Returns("stringCVL");
                persistance.CallsTo(x => x.GetCvlIdForFieldType("ProductNameCVLSingle")).Returns(null);
                var requestContext = new RequestContext
                {
                    DataPersistance = persistance.FakedObject,
                    Logging = new Fake<ICommonLogging>().FakedObject
                };
                return requestContext;
            }
        }
    }
}
