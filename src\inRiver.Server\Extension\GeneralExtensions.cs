namespace inRiver.Server.Extension
{
    using System.Collections.Generic;
    using System.Linq;

    public static class GeneralExtensions
    {
        public static IEnumerable<IEnumerable<T>> Batch<T>(this IEnumerable<T> items, int maxItems)
        {
            return
                items.Select((item, inx) => new { item, inx })
                    .GroupBy(x => x.inx / maxItems)
                    .Select(g => g.Select(x => x.item));
        }

        public static IEnumerable<List<T>> SplitIntoChunks<T>(this IEnumerable<T> source, int chunkSize)
        {
            var chunk = new List<T>(chunkSize);
            foreach (var item in source)
            {
                if (chunk.Count == chunkSize)
                {
                    yield return chunk;
                    chunk = new List<T>(chunkSize);
                }

                chunk.Add(item);
            }

            yield return chunk;
        }

        public static string ToCleanSeparator(this string str, string separator = ";")
        {
            if (str.StartsWith(separator))
            {
                str = str.Substring(1);
            }
            if (str.EndsWith(separator))
            {
                str = str.Substring(0, str.Length - 1);
            }
            return str;
        }

        public static string[] ToRemoveEmptyOrNull(this string[] array)
        {
            return array.Where(x => !string.IsNullOrEmpty(x)).ToArray();
        }
    }
}
