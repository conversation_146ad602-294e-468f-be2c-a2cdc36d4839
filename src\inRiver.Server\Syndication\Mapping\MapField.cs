namespace inRiver.Server.Syndication.Mapping
{
    using System.Collections.Generic;

    public class MapField
    {
        public Source Source { get; set; }

        public string MapFieldTypeId { get; set; } // i.e google color

        public string MapPath { get; set; }

        public int? MappingFieldGroupId { get; set; }

        public string UnitType { get; set; }

        public string UnitCvl { get; set; }

        public string UnitDefaultValue { get; set; }

        public string UnitValue { get; set; }

        public int SortOrder { get; set; }

        /// <summary>
        /// Gets or sets the function name.
        /// </summary>
        public string Converter { get; set; }

        /// <summary>
        /// Gets or sets the function's script.
        /// </summary>
        public string Script { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether function is custom function which is created by customer or default function which is provided by inriver.
        /// </summary>
        public bool IsCustom { get; set; }

        /// <summary>
        /// Gets or sets the info of function name, parameters(args and values) for a mapped function.
        /// </summary>
        public string Args { get; set; }

        public MapFieldType MapFieldType { get; set; }

        public List<MapEnumeration> Enumerations { get; set; }

        public int MappingFormatFieldId { get; set; }
    }
}
