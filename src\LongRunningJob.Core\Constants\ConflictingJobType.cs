namespace LongRunningJob.Core.Constants
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    public static class ConflictingJobType
    {
        /// <summary>
        /// Represents Jobs that might conflict with each other.
        /// </summary>
        public static readonly Dictionary<string, string> ConflictingJobTypes = new Dictionary<string, string>
        {
            {LongRunningJobsJobType.SynchronizeChannel, LongRunningJobsJobType.CalculateCompleteness },
            {LongRunningJobsJobType.CalculateCompleteness, LongRunningJobsJobType.SynchronizeChannel },
        };
    }
}
