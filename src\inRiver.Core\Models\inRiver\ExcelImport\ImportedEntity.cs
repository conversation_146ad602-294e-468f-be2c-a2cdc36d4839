namespace inRiver.Core.Models.inRiver.ExcelImport
{
    using System.Collections.Generic;

    public class ImportedEntity
    {
        public string InRiverImportColumnId { get; set; }

        public Remoting.Objects.Entity Entity { get; set; }

        public bool IsNew { get; set; }

        public List<Remoting.Objects.Field> UpdatedFields { get; set; }

        public bool SegmentChanged { get; set; }

        public bool FieldSetChanged { get; set; }

        public int PreviousSegmentId { get; set; }
    }
}
