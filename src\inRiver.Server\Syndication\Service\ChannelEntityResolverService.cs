namespace inRiver.Server.Syndication.Service
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Server.Syndication.Exceptions;
    using Interfaces;
    using Server.Models;

    public class ChannelEntityResolverService : IChannelEntityResolverService
    {
        private readonly ISyndicationChannelService channelService;
        private readonly ISyndicationEntityService syndicationEntityService;

        public ChannelEntityResolverService(ISyndicationChannelService channelService, ISyndicationEntityService syndicationEntityService)
        {
            this.channelService = channelService;
            this.syndicationEntityService = syndicationEntityService;
        }

        public async Task<IList<int>> GetEntityIdsFromChannelAsync(int channelId, IList<int> entityIds, string mappingEntityType)
        {
            if (!entityIds.Any())
            {
                return this.GetUniqueEntityIdsFromChannelStructure(await this.channelService.GetChannelStructureByEntityTypeIds(channelId, new List<string> { mappingEntityType }));
            }

            var entities = await this.syndicationEntityService.GetEntitiesAsync(entityIds);
            var entityTypeId = entities.FirstOrDefault(x => x.EntityTypeId != null)?.EntityTypeId;
            if (string.IsNullOrEmpty(entityTypeId))
            {
                throw new SyndicateException("Could not resolve entity type of provided entity ids.");
            }

            if (mappingEntityType == entityTypeId)
            {
                return entityIds;
            }

            var targetEntityPathPart = entityIds.Distinct().Select(entityId => $"{entityTypeId}_{entityId}").ToList();
            var channelStructure = await this.channelService.GetChannelStructureByEntityTypeIds(channelId, new List<string> { mappingEntityType, entityTypeId });
            var isOutboundRelation = channelStructure
                .Where(x => x.EntityTypeId == mappingEntityType)
                .Any(x => x.FullPath.Contains($"{mappingEntityType}_") && x.FullPath.Contains($"{entityTypeId}_"));
            if (isOutboundRelation)
            {
                return this.GetUniqueEntityIdsFromChannelStructure(channelStructure
                    .Where(x => x.EntityTypeId == mappingEntityType)
                    .Where(x => targetEntityPathPart.Any(y => x.FullPath.Contains(y))).ToList());
            }

            var channelStructureForTargetEntities = channelStructure
                .Where(x => x.EntityTypeId == entityTypeId)
                .Where(x => targetEntityPathPart.Any(y => x.FullPath.EndsWith(y)))
                .ToList();

            return this.GetUniqueEntityIdsFromChannelStructure(channelStructure
                .Where(x => x.EntityTypeId == mappingEntityType)
                .Where(x => channelStructureForTargetEntities.Any(y => y.FullPath.StartsWith(x.FullPath)))
                .ToList());
        }

        private IList<int> GetUniqueEntityIdsFromChannelStructure(IList<ChannelStructure> channelStructure)
            => channelStructure.Select(x => x.EntityId).Distinct().ToList();
    }
}
