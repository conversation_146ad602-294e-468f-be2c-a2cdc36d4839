namespace LongRunningJob.Core.Models
{
    using System;

    public class EnvironmentContext
    {
        public EnvironmentContext(int environmentId, string customerSafename, string environmentSafename, string databaseConnectionString, string storageAccountConnectionString)
        {
            ThrowIfNullOrEmpty(customerSafename, nameof(customerSafename));
            ThrowIfNullOrEmpty(environmentSafename, nameof(environmentSafename));
            ThrowIfNullOrEmpty(databaseConnectionString, nameof(databaseConnectionString));
            ThrowIfNullOrEmpty(storageAccountConnectionString, nameof(storageAccountConnectionString));

            this.EnvironmentId = environmentId;
            this.CustomerSafename = customerSafename;
            this.EnvironmentSafename = environmentSafename;
            this.DatabaseConnectionString = databaseConnectionString;
            this.StorageAccountConnectionString = storageAccountConnectionString;
        }

        public int EnvironmentId { get; }

        public string CustomerSafename { get; }

        public string EnvironmentSafename { get; }

        public string DatabaseConnectionString { get; }

        public string StorageAccountConnectionString { get; }

        private static void ThrowIfNullOrEmpty(string value, string parameterName)
        {
            if (string.IsNullOrEmpty(value))
            {
                throw new ArgumentException($"{parameterName} can not be null or empty.");
            }
        }
    }
}
