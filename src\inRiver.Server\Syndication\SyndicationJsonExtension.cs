namespace inRiver.Server.Syndication
{
    using System;
    using System.IO;
    using System.IO.Compression;
    using inRiver.Server.Request;
    using Newtonsoft.Json;

    internal static class SyndicationJsonExtension
    {
        public static string ToJson<T>(this T input)
        {
            return JsonConvert.SerializeObject(input, Formatting.None);
        }

        public static string ToCompressedJson<T>(this T input, RequestContext context)
        {
            var zippedBytes = Array.Empty<byte>();
            try
            {

                using var compressedFileStream = new MemoryStream();
                using (var zipArchive = new ZipArchive(compressedFileStream, ZipArchiveMode.Create, leaveOpen: true))
                {
                    var zipEntry = zipArchive.CreateEntry("export.json", CompressionLevel.Optimal);
                    using var zipEntryStream = zipEntry.Open();
                    using var streamWriter = new StreamWriter(zipEntryStream);
                    using JsonWriter writer = new JsonTextWriter(streamWriter);
                    var serializer = new JsonSerializer
                    {
                        Formatting = Formatting.None
                    };
                    serializer.Serialize(writer, input, typeof(T));
                    writer.Flush();
                    streamWriter.Flush();
                }

                zippedBytes = compressedFileStream.ToArray();
                compressedFileStream.Flush();
                compressedFileStream.Position = 0;
            }
            catch (Exception ex)
            {
                context.Log(Remoting.Log.LogLevel.Error, $"Unexpected exception occured when generating compressed JSON of { typeof(T).Name }", ex);
            }

            return Convert.ToBase64String(zippedBytes);
        }
    }
}
