<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="inRiver.Log" version="2.1.1" />
    <PackageReference Include="inRiver.StackEssentials" version="6.0.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Sendgrid" version="9.1.0" />
    <PackageReference Include="StyleCop.Analyzers" version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Configuration.Core\inRiver.Configuration.Core.csproj" />
    <ProjectReference Include="..\inRiver.Server\inRiver.Server.csproj" />
  </ItemGroup>

</Project>
