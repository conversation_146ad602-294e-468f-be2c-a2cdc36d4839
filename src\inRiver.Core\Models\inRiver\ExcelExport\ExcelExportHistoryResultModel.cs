namespace inRiver.Core.Models.inRiver.ExcelExport
{
    using System;
    using System.ComponentModel;
    using Enum;

    public class ExcelExportHistoryResultModel
    {

        public string BatchName { get; set; }


        /// <summary>
        /// UploadedByUserId refers to xConnectUser Id.
        /// </summary>
        public int UploadedByUserId { get; set; }

        /// <summary>
        /// UploadedBy refers to xConnectUser Username.
        /// </summary>
        public string UploadedBy { get; set; }

        public DateTime UploadedDate { get; set; }

        public string ImportedBy { get; set; }

        public DateTime ImportedDate { get; set; }

        public int EntityId { get; set; }

        public ImportStatus EntityStatus { get; set; }

        [DefaultValue(false)]
        public bool IsSpecificationField { get; set; }

        public string DisplayName { get; set; }

        public string FieldTypeId { get; set; }

        public string FieldDataBefore { get; set; }

        public string FieldDataAfter { get; set; }

        public int? FieldApprovalStatus { get; set; }

        public string FieldApprovalStatusUsername { get; set; }

        public override string ToString()
        {
            if (!string.IsNullOrEmpty(DisplayName))
            {
                return DisplayName;
            }

            if (EntityId != 0)
            {
                return "[" + this.EntityId + "]";
            }

            return base.ToString();
        }
    }
}
