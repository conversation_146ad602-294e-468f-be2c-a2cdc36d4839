namespace inRiver.Server.Util
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Diagnostics.CodeAnalysis;
    using System.Net.Http;
    using System.Runtime.Serialization;
    using Configuration;
    using inRiver.Core.Access;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;
    using Newtonsoft.Json;
    using Serilog;

    public class ChannelQueueWorker
    {
        private readonly RequestContext context;

        public ChannelQueueWorker(RequestContext context)
        {
            this.context = context;
        }

        internal void Enqueue(ChannelQueueMessage message)
        {
            this.SaveChannelMessage(message);

            if (this.IsProcessing())
            {
                return;
            }

            this.SendToChannelService(this.context);
        }

        private void SendToChannelService(RequestContext requestContext)
        {
            try
            {
                using var httpClient = StaticHttpClientFactory.CreateHttpClient(StaticConfigurationProvider.ChannelServiceUrl);
                httpClient.PostAsJsonAsync($"api/channel/{requestContext.CustomerSafeName}/{requestContext.EnvironmentSafeName}", new ChannelQueueMessage());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An error occurred when notifying channel service", "ChannelQueueWorker", requestContext.Username);
            }
        }

        private bool IsProcessing()
        {
            bool isProcessing = false;

            using (SqlConnection connection = new SqlConnection(this.context.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT TOP(1) Id FROM ChannelMessage WHERE [Status] = 'Processing' AND Modified > DATEADD(minute,-1,GETUTCDATE())";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            isProcessing = true;
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when checking if channel service is processing", "ChannelQueueWorker", this.context.Username);

                    return false;
                }

                return isProcessing;
            }
        }

        private void SaveChannelMessage(ChannelQueueMessage message)
        {
            using (SqlConnection connection = new SqlConnection(this.context.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "INSERT INTO ChannelMessage(Method, Message, Status, Modified, Added) VALUES (@Method, @Message, @Status, GETUTCDATE(), GETUTCDATE())";

                    command.Parameters.AddWithValue("@Method", message.Method);
                    command.Parameters.AddWithValue("@Message", JsonConvert.SerializeObject(message));
                    command.Parameters.AddWithValue("@Status", "Queued");

                    connection.Open();
                    command.ExecuteNonQuery();

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when saving channel message", "ChannelQueueWorker", this.context.Username);
                }
            }
        }
    }

    [SuppressMessage("StyleCop.CSharp.MaintainabilityRules", "SA1402:FileMayOnlyContainASingleClass", Justification = "Reviewed. Suppression is OK here.")]
    [DataContract]
    public class ChannelQueueMessage
    {
        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int? EntityId { get; set; }

        [DataMember]
        public string EntityTypeId { get; set; }

        [DataMember]
        public List<Field> Fields { get; set; }

        [DataMember]
        public DtoLink Link { get; set; }

        [DataMember]
        public string Method { get; set; }

        [DataMember]
        public string Xml { get; set; }

        [DataMember]
        public object Data { get; set; }
    }
}
