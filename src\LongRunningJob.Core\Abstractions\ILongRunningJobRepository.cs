namespace LongRunningJob.Core.Abstractions
{
    using System.Threading.Tasks;
    using inRiver.Core.Models.inRiver;

    public interface ILongRunningJobRepository
    {
        Task<LongRunningJob> GetAsync(int jobId);

        Task UpdateStateAsync(int jobId, string state);

        Task UpdateStateAsync(int jobId, string state, string metadata);

        Task<int> InsertLongRunningJobAsync(LongRunningJob job);

        Task<bool> StartedJobExistsAsync(string jobType, string identifier);
    }
}
