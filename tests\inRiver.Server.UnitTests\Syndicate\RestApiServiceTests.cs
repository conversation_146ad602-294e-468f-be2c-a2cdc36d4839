namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using FakeItEasy;
    using FakeItEasy.Configuration;
    using FluentAssertions;
    using inRiver.Core.Constants;
    using inRiver.Core.Http;
    using inRiver.Core.Util;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Script.Api;
    using Xunit;

    public class RestApiServiceTests
    {
        private const string DefaultResponseContent = "Content";

        private const int CacheExpiryTimeMinutesDisabled = 0;

        private static readonly HttpResponseMessage SuccessResponseMessage = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(DefaultResponseContent, Encoding.UTF8, "application/json")
        };

        [Theory]
        [InlineData("Get")]
        [InlineData("Delete")]
        [InlineData("Post")]
        [InlineData("Put")]
        [InlineData("Patch")]
        public void Fetch_AliasIsInactivated_ShouldThrowException(string httpMethod)
        {
            // Arrange
            const string endpointAlias = "ForbiddenName";
            var httpClient = A.Fake<IHttpClient>();
            var restApiSettingsService = CreateRestApiSettingsService(@"[{""EndpointAlias"": ""ForbiddenName"", ""IsInactivated"": ""true""}]");
            var restApiResponseService = A.Fake<IRestApiResponseService>();
            var restApiHeadersService = A.Fake<IRestApiHeadersService>();
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService();
            var context = A.Fake<RequestContext>();
            var restApiService = new RestApiService(httpClient, restApiSettingsService, restApiResponseService, restApiHeadersService, restApiCacheService, context);

            // Act
            Action act = () => restApiService.Fetch(new HttpMethod(httpMethod), endpointAlias, CacheExpiryTimeMinutesDisabled);

            // Assert
            act.Should().Throw<SyndicateApiException>().WithMessage($"Rest api requests for alias '{endpointAlias}' not activated.");
        }

        [Theory]
        [InlineData("Get")]
        [InlineData("Delete")]
        [InlineData("Post")]
        [InlineData("Put")]
        [InlineData("Patch")]
        public void Fetch_SuccessStatusCode_HandleResponseShouldHappen(string httpMethod)
        {
            // Arrange
            var httpClient = A.Fake<IHttpClient>();
            var restApiSettingsService = CreateRestApiSettingsService(@"[{""EndpointAlias"": ""Name1"", ""Url"": ""http://api.com/v1"", ""IsInactivated"": ""false""}]");
            var (restApiResponseService, handleResponseCall) = CreateRestApiResponseServiceWithDefaultResponse(httpMethod);
            var restApiHeadersService = A.Fake<IRestApiHeadersService>();
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService();
            var context = A.Fake<RequestContext>();
            var restApiService = new RestApiService(httpClient, restApiSettingsService, restApiResponseService, restApiHeadersService, restApiCacheService, context);

            // Act
            _ = restApiService.Fetch(new HttpMethod(httpMethod), "Name1", CacheExpiryTimeMinutesDisabled);

            // Assert
            handleResponseCall.MustHaveHappenedOnceExactly();
        }

        [Theory]
        [InlineData("Get")]
        [InlineData("Delete")]
        [InlineData("Post")]
        [InlineData("Put")]
        [InlineData("Patch")]
        public void Fetch_HttpClientThrowsTimeoutException_ShouldThrowExceptionWithCorrectMessage(string httpMethod)
        {
            // Arrange
            var httpClient = A.Fake<IHttpClient>();
            var exception = new SyndicateApiException("Exception when calling inRiver API. Timeout exception.", new TaskCanceledException());
            _ = A.CallTo(() => httpClient.Send(A<HttpRequestMessage>.Ignored)).Throws(exception);
            var restApiSettingsService = CreateRestApiSettingsService(@"[{""EndpointAlias"": ""Name1"", ""Url"": ""http://api.com/v1"", ""IsInactivated"": ""false""}]");
            var (restApiResponseService, _) = CreateRestApiResponseServiceWithDefaultResponse(httpMethod);
            var restApiHeadersService = A.Fake<IRestApiHeadersService>();
            var restApiCacheService = RestApiHelper.CreateRestApiCacheService();
            var context = A.Fake<RequestContext>();
            var restApiService = new RestApiService(httpClient, restApiSettingsService, restApiResponseService, restApiHeadersService, restApiCacheService, context);

            // Act
            Action act = () => restApiService.Fetch(new HttpMethod(httpMethod), "Name1", CacheExpiryTimeMinutesDisabled);

            // Assert
            act.Should().Throw<SyndicateApiException>()
                .WithMessage("Exception when calling inRiver API. Timeout exception.");
        }

        private static (IRestApiResponseService service, IReturnValueArgumentValidationConfiguration<Task<string>> handleResponseCall) CreateRestApiResponseServiceWithDefaultResponse(string httpMethod)
        {
            var restApiResponseService = A.Fake<IRestApiResponseService>();
            var handleResponseCall = A.CallTo(
                () => restApiResponseService.HandleResponseAsync(A<HttpResponseMessage>.Ignored, new HttpMethod(httpMethod)));
            _ = handleResponseCall.Returns(DefaultResponseContent);

            return (restApiResponseService, handleResponseCall);
        }

        private static IRestApiSettingsService CreateRestApiSettingsService(string endpointAliases)
        {
            var context = new RequestContext();
            var dataPersistance = CreateDataPersistance("someEncryptedValue");
            context.DataPersistance = dataPersistance;
            var cryptoService = A.Fake<ICrypto>();
            _ = A.CallTo(() => cryptoService.DecryptStringAsync(A<string>.Ignored))
                .Returns(Task.FromResult(endpointAliases));
            var restApiSettingsService = A.Fake<RestApiSettingsService>(
                options => options.WithArgumentsForConstructor(() => new RestApiSettingsService(context, cryptoService)));

            return restApiSettingsService;
        }

        private static IDataPersistance CreateDataPersistance(string endpointAliases)
        {
            var dataPersistance = A.Fake<IDataPersistance>();
            _ = A.CallTo(() => dataPersistance.GetServerSetting(ServerConstants.ENCRYPTED_SYNDICATION_ENDPOINT_ALIASES))
                .Returns(endpointAliases);

            return dataPersistance;
        }
    }
}
