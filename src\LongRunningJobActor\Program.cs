namespace LongRunningJobActor
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using Code;
    using inRiver.Api.Data.Client;
    using inRiver.Configuration.Core.Service;
    using inRiver.Core.Http;
    using inRiver.Core.Models;
    using inRiver.Core.Services;
    using inRiver.Server.Configuration;
    using inRiver.Server.Service;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.Extensions.Caching.Memory;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;
    using Microsoft.ServiceFabric.Actors.Runtime;
    using Serilog;
    using Serilog.Core;
    using Serilog.Events;
    using Telemetry.Initializers;

    internal static class Program
    {
        /// <summary>
        /// This is the entry point of the service host process.
        /// </summary>
        private static void Main()
        {
            try
            {
                TelemetryConfiguration.Active.InstrumentationKey = Code.Util.GetInstrumentationKey();
                var telemetryClient = new TelemetryClient();

                TelemetryConfiguration.Active.TelemetryInitializers.Add(new CloudRoleNameTelemetryInitializer(Constants.CloudRoleName));

                var loggingLevelSwitch = new LoggingLevelSwitch(Enum.TryParse(Code.Util.GetLogLevel(), out LogEventLevel level) ? level : LogEventLevel.Information);

                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.ControlledBy(loggingLevelSwitch)
                    .WriteTo.ApplicationInsights(TelemetryConfiguration.Active, TelemetryConverter.Traces)
                    .Enrich.FromLogContext()
                    .CreateLogger();

                Log.Information("Starting {Name} application.", Constants.CloudRoleName);

                StaticConfigurationProvider.ChannelServiceUrl = Util.GetChannelServiceUrl();
                StaticConfigurationProvider.ConnectServiceUrl = Util.GetConnectServiceUrl();
                StaticConfigurationProvider.CompressionServiceUrl = Util.GetCompressionServiceUrl();

                var services = new ServiceCollection();

                var keyVaultBaseUrl = Util.KeyVaultBaseUrl;

                var namedAuth0Options = new Dictionary<string, Auth0Options>();
                var auth0Options = new Auth0Options
                {
                    BaseAddress = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-domain").GetAwaiter().GetResult(),
                    ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-id").GetAwaiter().GetResult(),
                    ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-ipmc-system-client-secret").GetAwaiter().GetResult(),
                    Audience = Util.GetOAuthAudience(),
                };
                namedAuth0Options.Add("Auth0Client", auth0Options);

                try
                {
                    var outputAdapterAuth0Options = new Auth0Options
                    {
                        BaseAddress = Util.GetAuth0Domain,
                        ClientId = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-id").GetAwaiter().GetResult(),
                        ClientSecret = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-token-client-secret").GetAwaiter().GetResult(),
                        Audience = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "auth0-output-adapter-audience").GetAwaiter().GetResult(),
                    };
                    namedAuth0Options.Add("OutputAdapterAuth0Client", outputAdapterAuth0Options);
                }
                catch
                {
                }
                services.AddSingleton<IDictionary<string, Auth0Options>>(namedAuth0Options);


                services.TryAddTransient<BearerTokenHandler>();

                services.AddTransient<IAccessTokenRetriever, AccessTokenRetriever>();

                _ = services.AddHttpClient<IAugmentaHttpClient, AugmentaHttpClient>(c =>
                        c.WithBaseAddress(Util.GetAugmentaBaseUrl())
                            .WithAcceptJsonHeader()
                            .WithTimeout(TimeSpan.FromMinutes(5)))
                    .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

                _ = services.AddHttpClient<IOutputAdapterHttpClient, OutputAdapterHttpClient>(c =>
                        c.WithBaseAddress(Util.GetOutputAdapterApiBaseAddress)
                            .WithAcceptJsonHeader()
                            .WithTimeout(TimeSpan.FromMinutes(5)))
                    .AddHttpMessageHandler(provider => provider.GetRequiredService<BearerTokenHandler>());

                _ = services.AddMemoryCache();

                _ = services.AddHttpClient("Auth0Client", (provider, client) => {
                    var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["Auth0Client"].BaseAddress;
                    client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
                });

                _ = services.AddHttpClient("OutputAdapterAuth0Client", (provider, client) => {
                    var baseAddress = provider.GetRequiredService<IDictionary<string, Auth0Options>>()["OutputAdapterAuth0Client"].BaseAddress;
                    client.BaseAddress = new Uri(baseAddress, UriKind.Absolute);
                });

                StaticServiceProvider.Configure(services.BuildServiceProvider());

                // This line registers an Actor Service to host your actor class with the Service Fabric runtime.
                // The contents of your ServiceManifest.xml and ApplicationManifest.xml files
                // are automatically populated when you build this project.
                // For more information, see https://aka.ms/servicefabricactorsplatform
                ActorRuntime.RegisterActorAsync<LongRunningJobActor>(
                    (context, actorType) => {
                        return new ActorService(context, actorType, 
                            (service, id) => new LongRunningJobActor(service, id, telemetryClient),
                            settings: new ActorServiceSettings
                            {
                                ActorGarbageCollectionSettings =
                                    new ActorGarbageCollectionSettings(idleTimeoutInSeconds: 10, scanIntervalInSeconds: 2)
                            }
                        );
                    }).GetAwaiter().GetResult();
                Thread.Sleep(Timeout.Infinite);
            }
            catch (Exception e)
            {
                ActorEventSource.Current.ActorHostInitializationFailed(e.ToString());
                var telemetryClient = new TelemetryClient();
                telemetryClient.Context.Cloud.RoleName = Constants.CloudRoleName;
                telemetryClient.TrackException(e);
                telemetryClient.Flush();
                Thread.Sleep(1000); // Recommended by AI documentation
                throw;
            }
        }
    }
}
