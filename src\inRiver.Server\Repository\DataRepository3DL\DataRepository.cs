namespace inRiver.Server.Repository
{
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Query;
    using inRiver.Remoting.Util;
    using inRiver.Server.DataAccess.ThirdDataLayer;
    using inRiver.Server.Error;
    using LogLevel = inRiver.Remoting.Log.LogLevel;

    public partial class DataRepository
    {
        public List<DtoEntity> Search(Query query, LoadLevel level)
        {
            if (query == null)
            {
                context.Log(LogLevel.Warning, "Query cannot be null");
                throw ErrorUtility.GetArgumentFault("Search", "critqueryeria", "Query cannot be null");
            }

            if (query.Criteria == null)
            {
                context.Log(LogLevel.Warning, "Criteria cannot be null");
                throw ErrorUtility.GetArgumentFault("Search", "query.Criteria", "Criteria cannot be null");
            }

            Stopwatch stopwatch = Stopwatch.StartNew();

            List<int> searchHits = null;
            if (this.context.EntityModel == 1)
            {
                searchHits = this.dataContext.SearchEntityBySystemQueryAndDataQuery(null, query);
            }
            else if (this.context.EntityModel == 2)
            {
                var threeDLPersistanceAdapter = this.dataContext as IPMCServer3DLPersistanceAdapter;
                searchHits = threeDLPersistanceAdapter.ExecuteDataQuery(query);
            }
            else
            {
                searchHits = this.SearchQuery(query);
            }

            stopwatch.Stop();
            context.Log(LogLevel.Verbose, "Search(Query) took " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            return this.GetDtoEntities(searchHits, level);
        }

        public List<DtoEntity> Search(Criteria criteria, LoadLevel level)
        {
            if (criteria == null)
            {
                context.Log(LogLevel.Warning, "Criteria cannot be null");
                throw ErrorUtility.GetArgumentFault("Search", "criteria", "Criteria cannot be null");
            }

            if (criteria.Value != null && string.IsNullOrWhiteSpace(criteria.Value.ToString()) &&
                !(criteria.Operator.Equals(Operator.IsFalse) || criteria.Operator.Equals(Operator.IsTrue) || criteria.Operator.Equals(Operator.IsNotNull) || criteria.Operator.Equals(Operator.IsNull)))
            {
                return new List<DtoEntity>();
            }

            if (!string.IsNullOrWhiteSpace(criteria.Language))
            {
                if (!this.dataContext.GetAllLanguages().Exists(c => c.Name == criteria.Language))
                {
                    context.Log(LogLevel.Warning, "Criteria language does not exist on server");
                    throw ErrorUtility.GetArgumentFault("Search", "Criteria.Language", "Criteria language does not exist on server");
                }
            }

            Stopwatch stopwatch = Stopwatch.StartNew();

            List<int> searchHits = null;
            if (this.context.EntityModel == 2)
            {
                var threeDLPersistanceAdapter = this.dataContext as IPMCServer3DLPersistanceAdapter;
                var query = new Query()
                {
                    Criteria = new List<Criteria>() { criteria }
                };
                searchHits = threeDLPersistanceAdapter.ExecuteDataQuery(query);
            }
            else
            {
                searchHits = Search(criteria);
            }

            stopwatch.Stop();
            context.Log(LogLevel.Verbose, "Search(Criteria) took " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            List<DtoEntity> entities = GetDtoEntities(searchHits, level);

            return entities.Distinct().ToList();
        }

        public List<int> Search(ComplexQuery query)
        {
            List<List<int>> results = new List<List<int>>();

            List<int> tempResult;

            // if new entity model, then try to consolidate the database query to improve performance
            if (this.context.EntityModel == 1 && query.DataQuery != null)
            {
                if (query.DataQuery.Criteria != null)
                {
                    foreach (var criterion in query.DataQuery.Criteria)
                    {
                        if (criterion.Interval != null && criterion.Interval.Value)
                        {
                            TimeIntervalQueryUtil.GetCriteriaValueFromDatetimeInterval(criterion, useUTC: false);
                        }
                    }
                }

                tempResult = this.dataContext.SearchEntityBySystemQueryAndDataQuery(query.SystemQuery, query.DataQuery);
                results.Add(tempResult);
            }
            else if (this.context.EntityModel == 2 && query.DataQuery != null)
            {
                if (query.SystemQuery != null)
                {
                    tempResult = this.dataContext.SystemSearch(query.SystemQuery);

                    results.Add(tempResult);
                }

                var threeDLPersistanceAdapter = this.dataContext as IPMCServer3DLPersistanceAdapter;
                results.Add(threeDLPersistanceAdapter.ExecuteDataQuery(query.DataQuery));
            }
            else
            {
                if (query.SystemQuery != null)
                {
                    tempResult = this.dataContext.SystemSearch(query.SystemQuery);

                    results.Add(tempResult);
                }

                if (query.DataQuery != null)
                {
                    tempResult = SearchQuery(query.DataQuery);

                    results.Add(tempResult);
                }
            }

            if (int.TryParse(query?.SystemQuery?.Publication, out var value))
            {
                results.Add(this.dataContext.GetAllEntityIdsForChannel(value));
            }

            if (query.SystemQuery != null && query.SystemQuery.Channel.HasValue)
            {
                tempResult = query.SystemQuery.ChannelOperator == Operator.Equal ?
                                this.dataContext.GetAllEntityIdsForChannel(query.SystemQuery.Channel.Value) :
                                this.dataContext.GetAllEntityIdsNotInChannel(query.SystemQuery.Channel.Value);

                results.Add(tempResult);
            } 
            else if (query.ChannelId.HasValue)
            {
                tempResult = this.dataContext.GetAllEntityIdsForChannel(query.ChannelId.Value);

                results.Add(tempResult);
            }

            if (!string.IsNullOrEmpty(query.EntityTypeId))
            {
                tempResult = this.dataContext.GetAllEntityIdsForEntityType(query.EntityTypeId);

                results.Add(tempResult);
            }

            if (query.LinkQuery != null)
            {
                tempResult = this.dataContext.LinkSearch(query.LinkQuery);

                results.Add(tempResult);
            }

            if (query.CompletenessQuery != null)
            {
                tempResult = this.dataContext.SearchCompleteness(query.CompletenessQuery);

                results.Add(tempResult);
            }

            if (query.SpecificationQuery != null)
            {
                tempResult = this.dataContext.SearchSpecification(query.SpecificationQuery);

                results.Add(tempResult);
            }

            List<int> result = results[0];

            for (int i = 1; i < results.Count; i++)
            {
                result = result.Intersect(results[i]).ToList();
            }

            return result;
        }

        private List<int> Search(Criteria criteria, Join? joinOperator = null)
        {
            List<int> searchHits = new List<int>();

            if (this.context.EntityModel != 2)
            {
                if ((this.FieldTypeIsMultivalue(criteria.FieldTypeId) || criteria.Operator == Operator.ContainsAny) ||
                    (this.FieldTypeIsCVL(criteria.FieldTypeId) && criteria.Operator == Operator.NotContainsAny))
                {
                    List<string> values = this.SplitCriteraValues((string)criteria.Value);

                    if (criteria.Operator == Operator.ContainsAny)
                    {
                        foreach (string value in values)
                        {
                            Criteria crit = new Criteria();
                            crit.FieldTypeId = criteria.FieldTypeId;
                            crit.Language = criteria.Language;
                            crit.Operator = Operator.Contains;
                            crit.Value = value;

                            List<int> ids = Search(crit);

                            searchHits = searchHits.Union(ids).ToList();
                        }

                        return searchHits;
                    }

                    if (criteria.Operator == Operator.ContainsAll)
                    {
                        bool first = true;
                        foreach (string value in values)
                        {
                            Criteria crit = new Criteria();
                            crit.FieldTypeId = criteria.FieldTypeId;
                            crit.Language = criteria.Language;
                            crit.Operator = Operator.Contains;
                            crit.Value = value;

                            List<int> ids = Search(crit);
                            if (first)
                            {
                                first = false;
                                searchHits = ids;
                            }
                            else
                            {
                                searchHits = searchHits.Intersect(ids).ToList();
                            }
                        }

                        return searchHits;
                    }

                    if (criteria.Operator == Operator.NotContainsAll)
                    {
                        foreach (string value in values)
                        {
                            Criteria crit = new Criteria();
                            crit.FieldTypeId = criteria.FieldTypeId;
                            crit.Language = criteria.Language;
                            crit.Operator = Operator.NotEqual;
                            crit.Value = value;

                            List<int> ids = Search(crit);

                            searchHits = searchHits.Union(ids).ToList();
                        }

                        return searchHits;
                    }

                    if (criteria.Operator == Operator.NotContainsAny)
                    {
                        bool first = true;
                        foreach (string value in values)
                        {
                            Criteria crit = new Criteria();
                            crit.FieldTypeId = criteria.FieldTypeId;
                            crit.Language = criteria.Language;
                            crit.Operator = Operator.NotEqual;
                            crit.Value = value;

                            List<int> ids = Search(crit);

                            if (first)
                            {
                                first = false;
                                searchHits = ids;
                            }
                            else
                            {
                                searchHits = searchHits.Intersect(ids).ToList();
                            }
                        }

                        return searchHits;
                    }
                }
            }

            if (criteria.Interval.HasValue && criteria.Interval.Value)
            {
                QueryUtil.GetCriteriaValueFromDatetimeInterval(criteria);
            }

            searchHits = this.dataContext.Search(criteria, joinOperator);

            return searchHits.Distinct().ToList();
        }
    }
}
