namespace inRiver.iPMC.Persistance
{
    public enum Operator
    {
        Equal = 0,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>,
        <PERSON>Than<PERSON>r<PERSON><PERSON><PERSON>,
        <PERSON><PERSON>han,
        IsNot<PERSON>ull,
        IsNull,
        <PERSON>Than,
        LessThanOr<PERSON>qual,
        NotEqual,
        IsTrue,
        IsFalse,
        Empty,
        NotEmpty,
        ContainsAll,
        ContainsAny,
        NotContainsAny,
        NotContainsAll,
        NotContains, // Only internally
        In,
        NotIn
    }

    public static class OperatorMethods
    {
        public static string GetSqlSymbol(this Operator o)
        {
            switch (o)
            {
                case Operator.Equal:
                    return "=";

                case Operator.BeginsWith:
                    return "like";

                case Operator.Contains:
                    return "like";

                case Operator.GreaterThanOrEqual:
                    return ">=";

                case Operator.GreaterThan:
                    return ">";

                case Operator.IsNotNull:
                    return "is not null";

                case Operator.IsNull:
                    return "is null";

                case Operator.LessThan:
                    return "<";

                case Operator.LessThanOrEqual:
                    return "<=";

                case Operator.NotEqual:
                    return "<>";

                case Operator.In:
                    return "IN";

                case Operator.NotIn:
                    return "NOT IN";

                default:
                    return null;
            }
        }
    }
}
