namespace inRiver.Server.UnitTests.Syndicate.DefaultFunctions
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using FluentAssertions;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Script;
    using inRiver.Server.Syndication.Script.DefaultFunctions;
    using Newtonsoft.Json;
    using Xunit;

    public class CoalesceFunctionTests
    {
        public static readonly object[][] CoalesceFunctionValues =
        {
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", null, null, null, null, null, false, null, "ar value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "en", null, "ProductNameLS", "en", null, false, null, "en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "en", null, "ProductNameCVLMulti", null, "stringCVL", false, null, "en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "en", null, "ProductNameCVLMulti", null, "localeStringCVL", false, null, "en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "en", null, "ProductNameString", null, null, false, null, "en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "en", null, null, null, null, true, "const value", "en value" },
            new object[] { DefaultFunctionsData.LSDictionary, "ProductNameLS", "pl", null, null, null, null, true, "const value", "const value" },
            new object[] { "black", "ProductNameCVLSingle", null, "stringCVL", null, null, null, false, null, "Black" },
            new object[] { "black", "ProductNameCVLSingle", null, "stringCVL", "ProductNameLS", "en", null, false, null, "Black" },
            new object[] { "black", "ProductNameCVLSingle", "en", "localeStringCVL", "ProductNameCVLMulti", null, "stringCVL", false, null, "en black" },
            new object[] { "black", "ProductNameCVLSingle", null, "localeStringCVL", "ProductNameCVLMulti", null, "stringCVL", false, null, "ar black" },
            new object[] { "black", "ProductNameCVLSingle", "sv", "localeStringCVL", "ProductNameCVLMulti", null, "localeStringCVL", false, null, "sv black" },
            new object[] { "black", "ProductNameCVLSingle", "fr", "localeStringCVL", "ProductNameString", null, null, false, null, "Product name string" },
            new object[] { "black;green", "ProductNameCVLMulti", "en", "localeStringCVL", null, null, null, true, "const value", "en black;en green" },
            new object[] { "Product name string", "ProductNameString", null, null, "ProductNameLS", "en", null, false, null, "Product name string" },
            new object[] { null, "ProductNameString", null, null, "ProductNameLS", "en", null, false, null, "en value" },
            new object[] { null, "ProductNameString", null, null, null, null, null, false, null, string.Empty },
            new object[] { string.Empty, "ProductNameString", null, null, "ProductNameLS", "en", null, false, null, "en value" },
            new object[] { null, "ProductNameString", null, null, "ProductNameCVLMulti", null, "stringCVL", false, null, "Black;Green" },
            new object[] { null, "ProductNameString", null, null, "ProductNameCVLMulti", "sv", "localeStringCVL", false, null, "sv black" },
            new object[] { null, "ProductNameString", "en", null, "ProductNameString", null, null, false, null, "Product name string" },
            new object[] { null, "ProductNameString", "en", null, null, null, null, true, "const value", "const value" },
            new object[] { null, "ProductNameString", "en", null, null, null, null, true, "[ ] \\ ^ $ . | ? * + ( )", "[ ] \\ ^ $ . | ? * + ( )" },
            new object[] { null, "ProductNameString", "en", null, null, null, null, true, string.Empty, string.Empty },
            new object[] { null, null, null, null, null, null, null, false, "const value", string.Empty },
            new object[] { null, null, null, null, null, null, null, false, null, string.Empty },
        };

        [Theory]
        [MemberData(nameof(CoalesceFunctionValues))]
        public void Coalesce_ShouldReturnCoalescedValue(
            object mainValue,
            string mainFieldTypeId,
            string language1,
            string cvl1,
            string fieldTypeId2,
            string language2,
            string cvl2,
            bool isConstant2,
            string constantValue2,
            string expectedResult)
        {
            // Arrange
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            ExportManager.CvlIdsDictionary = new ConcurrentDictionary<string, string>();
            var transformJson = new TransformationJson
            {
                Transformation = new List<Transformation>
                {
                    new Transformation
                    {
                        Function = new Function
                        {
                            Name = "Coalesce",
                            Args = Array.Empty<string>(),
                            Values = new[]
                            {
                                JsonConvert.SerializeObject(new CoalesceFunctionField
                                {
                                    Index = 1,
                                    ConstantValue = string.Empty,
                                    IsConstant = false,
                                    CvlId = cvl1,
                                    Language = language1
                                }),
                                JsonConvert.SerializeObject(new CoalesceFunctionField
                                {
                                    Index = 2,
                                    ConstantValue = constantValue2,
                                    IsConstant = isConstant2,
                                    FieldTypeId = fieldTypeId2,
                                    CvlId = cvl2,
                                    Language = language2
                                }),
                            }
                        }
                    }
                }
            };
            var transformationManager = new TransformationManager(JsonConvert.SerializeObject(transformJson), context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = mainFieldTypeId
            };

            var coalesceExecutor = new CoalesceFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: DefaultFunctionsData.RequestContext);

            // Act
            var result = coalesceExecutor.Execute();

            // Assert
            result.Should().Be(expectedResult);
        }

        [Fact]
        public void Coalesce_CVLIdNotSpecified_ShouldGetCvlIdByFieldTypeId()
        {
            // Arrange
            const string mainValue = "green;black";
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            ExportManager.CvlIdsDictionary = new ConcurrentDictionary<string, string>();
            var transformJson = new TransformationJson
            {
                Transformation = new List<Transformation>
                {
                    new Transformation
                    {
                        Function = new Function
                        {
                            Name = "Coalesce",
                            Args = Array.Empty<string>(),
                            Values = new[]
                            {
                                JsonConvert.SerializeObject(new CoalesceFunctionField
                                {
                                    Index = 1,
                                }),
                                JsonConvert.SerializeObject(new CoalesceFunctionField
                                {
                                    Index = 2,
                                    FieldTypeId = "ProductNameString"
                                })
                            }
                        }
                    }
                }
            };
            var transformationManager = new TransformationManager(JsonConvert.SerializeObject(transformJson), context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = "ProductNameCVLMulti"
            };

            var coalesceExecutor = new CoalesceFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: DefaultFunctionsData.RequestContext);

            // Act
            var result = coalesceExecutor.Execute();

            // Assert
            result.Should().Be("Green;Black");
        }

        [Fact]
        public void Coalesce_CVLIdNotFound_ShouldThrowException()
        {
            // Arrange
            const string mainValue = "black;green";
            ExportManager.CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            ExportManager.CvlIdsDictionary = new ConcurrentDictionary<string, string>();
            var transformJson = new TransformationJson
            {
                Transformation = new List<Transformation>
                {
                    new Transformation
                    {
                        Function = new Function
                        {
                            Name = "Coalesce",
                            Args = Array.Empty<string>(),
                            Values = new[]
                            {
                                JsonConvert.SerializeObject(new CoalesceFunctionField
                                {
                                    Index = 1,
                                }),
                                JsonConvert.SerializeObject(new CoalesceFunctionField
                                {
                                    Index = 2,
                                    FieldTypeId = "ProductNameString"
                                })
                            }
                        }
                    }
                }
            };
            var transformationManager = new TransformationManager(JsonConvert.SerializeObject(transformJson), context: null);
            var foundField = new FoundField
            {
                Data = mainValue,
                FieldTypeId = "ProductNameCVLSingle"
            };

            var coalesceExecutor = new CoalesceFunctionExecutor(
                transformationManager,
                foundField,
                DefaultFunctionsData.Entity,
                mainValue,
                enableSku: false,
                skuInjector: null,
                skuId: null,
                serverLanguages: DefaultFunctionsData.ServerLanguages,
                context: DefaultFunctionsData.RequestContext);

            // Act
            Action act = () => coalesceExecutor.Execute();

            // Assert
            act.Should().Throw<SyndicateException>()
                .WithMessage("CVL id is not specified for field of type CVL.");
        }
    }
}
