namespace inRiver.Server.UnitTests.Extensions
{
    using System;
    using FluentAssertions;
    using inRiver.Server.Extension;
    using Xunit;

    public class UrlExtensionsTests
    {
        [Theory]
        [InlineData("https://www.external.api?param1")]
        [InlineData("http://www.external.api?param1=1&param2=2")]
        [InlineData("https://www.external.api/path1")]
        [InlineData("http://www.external.api/path1/path2")]
        [InlineData("http://external.api/path1")]
        [InlineData("https://www.external.com:123/path")]
        [InlineData("http://www.external.com:123?param1=1")]
        [InlineData("www.external.co/path1/path2")]
        public void ValidateUrl_AllowedRequestsAndMustContainPathAndQueryIsTrue_ShouldNotThrowException(string url)
        {
            // Act
            Action act = () => url.ValidateUrl(mustContainPathAndQuery: true);

            // Assert
            act.Should().NotThrow<ArgumentException>();
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("http://external.api")]
        [InlineData("http://external.com")]
        [InlineData("http://www.external")]
        [InlineData("http://external")]
        [InlineData("http://www.external.com")]
        [InlineData("https://www.external.com")]
        [InlineData("https://www.external.com:123")]
        [InlineData("www.external")]
        [InlineData("external.api")]
        [InlineData("www.external.api")]
        [InlineData("www.external#.com")]
        [InlineData("ww.external.com")]
        [InlineData("www.e*/,?xternal.com")]
        [InlineData("www.external.com")]
        public void ValidateUrl_NotAllowedRequestsAndMustContainPathAndQueryIsTrue_ShouldThrowException(string url)
        {
            // Act
            Action act = () => url.ValidateUrl(mustContainPathAndQuery: true);

            // Assert
            act.Should().Throw<ArgumentException>();
        }

        [Fact]
        public void CreateUrl_BaseUrlAndPathSpecified_ShouldReturnFullUrl()
        {
            // Arrange
            const string baseUrl = "http://external.api/";
            const string requestPath = "path1/path2";

            // Act
            var createdUrl = baseUrl.CreateUrl(requestPath);

            // Assert
            createdUrl.Should().BeEquivalentTo($"{baseUrl}{requestPath}");
        }

        [Fact]
        public void CreateUrl_PathNotSpecified_ShouldReturnOnlyAliasUrl()
        {
            // Arrange
            const string baseUrl = "http://external.api/";

            // Act
            var createdUrl = baseUrl.CreateUrl(string.Empty);

            // Assert
            createdUrl.Should().BeEquivalentTo(baseUrl);
        }

        [Fact]
        public void CreateUrl_BaseUrlNotSpecified_ShouldReturnOnlyBaseUrl()
        {
            // Arrange
            const string requestPath = "path1/path2";

            // Act
            Action act = () => string.Empty.CreateUrl(requestPath);

            // Assert
            act.Should().Throw<ArgumentException>();
        }

        [Fact]
        public void CreateUrl_ParametersNotSpecified_ShouldReturnOnlyBaseUrl()
        {
            // Act
            Action act = () => string.Empty.CreateUrl(requestPath: string.Empty);

            // Assert
            act.Should().Throw<ArgumentException>();
        }

        [Fact]
        public void CreateUrl_DelimiterBetweenBaseUrlAndRequestPathNotSpecified_ShouldReturnFullPathWithDelimiter()
        {
            // Arrange
            const string baseUrl = "http://external.api";
            const string requestPath = "path1/path2";

            // Act
            var createdUrl = baseUrl.CreateUrl(requestPath);

            // Assert
            createdUrl.Should().BeEquivalentTo($"{baseUrl}/{requestPath}");
        }
    }
}
