namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Text;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Query;
    using inRiver.Server.Error;
    using Newtonsoft.Json;
    using Serilog;

    [SuppressMessage("StyleCop.CSharp.NamingRules", "SA1300:ElementMustBeginWithUpperCaseLetter", Justification = "Reviewed. Suppression is OK here.")]
    // ReSharper disable once InconsistentNaming
    public partial class inRiverPersistance
    {
        #region Languages

        public List<CultureInfo> GetAllLanguages()
        {
            List<CultureInfo> languages = new List<CultureInfo>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                SqlCommand command = connection.CreateCommand();
                command.CommandText = "SELECT LanguageCode FROM Language";

                try
                {
                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            try
                            {
                                var languageCode = reader.GetString(0);
                                var ci = new CultureInfo(languageCode);

                                languages.Add(ci);
                            }
                            catch (CultureNotFoundException)
                            {
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all languages");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all languages", ex);
                }
            }

            return languages;
        }

        public bool AddLanguage(CultureInfo cultureInfo)
            => throw new NotImplementedException();

        public bool DeleteAllLanguages()
            => throw new NotImplementedException();

        public bool DeleteLanguage(CultureInfo cultureInfo)
            => throw new NotImplementedException();

        #endregion

        #region Files

        #endregion

        #region Server

        public string GetServerSetting(string key)
        {
            string result = string.Empty;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Value FROM ServerSettings WHERE Name = @Name";
                    command.Parameters.AddWithValue("@Name", key);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            result = reader.GetString(0);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting server setting");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting server setting", ex);
                }
            }

            return result;
        }

        public Dictionary<string, string> GetServerSettings(List<string> keysList)
        {
            if (!keysList.Any())
            {
                return new Dictionary<string, string>();
            }
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                SqlCommand command = connection.CreateCommand();
                StringBuilder sb = new StringBuilder();
                int index = 1;
                foreach (string k in keysList)
                {
                    sb.Append((sb.Length <= 0 ? "" : ",") + "@Key" + index.ToString());
                    command.Parameters.AddWithValue("@Key" + index.ToString(), k);
                    index++;
                }

                Dictionary<string, string> result = new Dictionary<string, string>();

                try
                {
                    command.CommandText = $"SELECT [Name], [Value] FROM ServerSettings Where [Name] IN ({sb.ToString()})";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string name = reader.GetString(0);
                            string value = reader.GetString(1);

                            result.Add(name, value);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting specific server settings (GetServerSettings)");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting specific server settings", ex);
                }
                return result;
            }

        }

        #endregion

        #region Integration

        #endregion

        #region UI

        #endregion

        #region Personal Work Area Folder

        #endregion

        #region Shared Work Area Folder

        public WorkAreaFolder GetSharedWorkAreaFolder(Guid id, bool includeEntities)
        {
            // TODO: Get Query
            WorkAreaFolder folder = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Name, ParentId, IsQuery, [Index], Query FROM WorkAreaSharedFolder WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();

                            folder = new WorkAreaFolder();
                            folder.Id = id;

                            if (!reader.IsDBNull(0))
                            {
                                folder.Name = reader.GetString(0);
                            }

                            if (!reader.IsDBNull(1))
                            {
                                folder.ParentId = reader.GetGuid(1);
                            }

                            folder.IsQuery = reader.GetBoolean(2);

                            folder.Index = reader.GetInt32(3);

                            if (!reader.IsDBNull(4))
                            {
                                string queryJson = reader.GetString(4);
                                folder.Query = JsonConvert.DeserializeObject<ComplexQuery>(queryJson);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when shared work area folder with id " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting shared work area folder with id " + id, ex);
                }
            }

            if (folder != null && !folder.IsQuery && includeEntities)
            {
                folder.FolderEntities = GetEntitiesForFolder(id);
            }

            return folder;
        }

        public bool DeleteSharedWorkAreaFolder(Guid id)
        {
            List<Guid> childFolders = GetSharedWorkAreaFolderChildren(id);

            foreach (Guid guid in childFolders)
            {
                if (guid.Equals(id))
                {
                    continue;
                }

                DeleteSharedWorkAreaFolder(guid);
            }

            WorkAreaFolder folder = this.GetSharedWorkAreaFolder(id, false);

            if (folder == null)
            {
                return false;
            }

            this.DecreaseSharedWorkAreaFolderIndexByOneStep(folder.ParentId, folder.Index);

            int numberOfRowsAffected;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "DELETE FROM WorkAreaFolderEntities WHERE FolderId = @Id; DELETE FROM WorkAreaSharedFolder WHERE Id = @Id";

                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    numberOfRowsAffected = command.ExecuteNonQuery();

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when deleting shared work area folder with id " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when shared personal work area folder with id " + id, ex);
                }
            }

            return numberOfRowsAffected == 1;
        }

        #endregion

        #region Job

        #endregion

        #region Work Area Folder Entities

        #endregion

        #region Work Area Folder Entities

        public List<int> GetEntitiesForFolder(Guid id)
        {
            List<int> ids = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Distinct EntityId FROM WorkAreaFolderEntities WHERE FolderId = @FolderId";
                    command.Parameters.AddWithValue("@FolderId", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting entities for work area folder with id " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting entities for work area folder with id " + id, ex);
                }
            }

            return ids.ToList();
        }

        #endregion

        #region ConnectorEvent

        #endregion

        #region Notify Me When

        #endregion

        #region File Import Mapping

        #endregion

        #region Image Service Configuration

        #endregion

        #region Image Cache

        #endregion

        #region Planner View

        #endregion

        #region Connector State

        #endregion

        #region Html Templates

        #endregion

        #region Statistics

        #endregion

        #region Private Methods

        private void DecreaseSharedWorkAreaFolderIndexByOneStep(Guid? parentId, int index)
        {
            if (!parentId.HasValue)
            {
                return;
            }

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText =
                       "UPDATE WorkAreaSharedFolder SET [Index] = [Index] - 1 WHERE ParentId = @ParentId AND [Index] >= @Index";
                    command.Parameters.AddWithValue("@ParentId", parentId.Value);
                    command.Parameters.AddWithValue("@Index", index);

                    connection.Open();

                    command.ExecuteNonQuery();

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when updating shared work area folder indexes");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating shared work area folder indexes", ex);
                }
            }
        }

        private List<Guid> GetSharedWorkAreaFolderChildren(Guid parentId)
        {
            List<Guid> ids = new List<Guid>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT Id FROM WorkAreaSharedFolder WHERE ParentId = @ParentId";
                    command.Parameters.AddWithValue("@ParentId", parentId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetGuid(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting children for shared work area folder with id " + parentId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting children for shared work area folder with id " + parentId, ex);
                }
            }

            return ids;
        }

        #endregion
    }
}
