{
  "$schema": "https://raw.githubusercontent.com/DotNetAnalyzers/StyleCopAnalyzers/master/StyleCop.Analyzers/StyleCop.Analyzers/Settings/stylecop.schema.json",
  "settings": {
    "documentationRules": {
      "companyName": "inRiver"
    },
    "namingRules": {
      "allowedNamespaceComponentTerms": [ "inRiver" ] //https://github.com/DotNetAnalyzers/StyleCopAnalyzers/issues/2923#issuecomment-*********, Will be available when nuget version 1.2.0 is released, and we switch to C# 8.0 or later.
    }
  }
}
