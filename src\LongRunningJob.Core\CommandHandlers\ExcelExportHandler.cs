namespace LongRunningJob.Core.CommandHandlers
{
    using System;
    using System.Text.Json;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Server.Repository;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Commands;
    using LongRunningJob.Core.Models;

    public class ExcelExportHandler : ICommandHandler<ExcelExportCommand, Result>
    {
        private readonly IJobModelRepository jobModelRepository;
        private readonly IExcelExportRepositoryFactory excelExportRepositoryFactory;
        private readonly ILongRunningJobRepository longRunningJobRepository;
        private readonly IEnvironmentSettingsRepository environmentSettingsRepository;

        public ExcelExportHandler(
            IJobModelRepository jobModelRepository,
            IExcelExportRepositoryFactory excelExportRepositoryFactory,
            ILongRunningJobRepository longRunningJobRepository,
            IEnvironmentSettingsRepository environmentSettingsRepository)
        {
            this.jobModelRepository = jobModelRepository;
            this.excelExportRepositoryFactory = excelExportRepositoryFactory;
            this.longRunningJobRepository = longRunningJobRepository;
            this.environmentSettingsRepository = environmentSettingsRepository;
        }

        public async Task<Result> HandleAsync(ExcelExportCommand command, CancellationToken cancellationToken)
        {
            var jobModel = await this.jobModelRepository.GetJobModelAsync<ExcelExportJobModel>(command.Job.Id);

            var excelExportRepository = await this.excelExportRepositoryFactory.CreateAsync();

            var fileName = GetFileName(excelExportRepository, jobModel);
            jobModel.ExcelExportModel.Name = fileName;
            var metadata = JsonSerializer.Serialize(new { fileName });

            await this.longRunningJobRepository.UpdateStateAsync(command.Job.Id, LongRunningJobsStatus.Running, metadata);

            try
            {
                var excelExportBatchSize = await this.GetExcelExportBatchSizeAsync();
                var hasErrors = await excelExportRepository.BatchExportExcelAsync(jobModel.ExcelExportModel, excelExportBatchSize, cancellationToken);
                var jobState = hasErrors ? LongRunningJobsStatus.FinishedWithErrors : LongRunningJobsStatus.Finished;

                return new Result
                {
                    JobState = jobState
                };
            }
            catch (OperationCanceledException)
            {
                return new Result
                {
                    JobState = LongRunningJobsStatus.Cancelled
                };
            }
        }

        private static string GetFileName(IExcelExportRepository excelExportRepository, ExcelExportJobModel jobModel)
        {
            var entityType = excelExportRepository.GetExportMainEntityType(jobModel.ExcelExportModel.EntityTypeModels);
            var fileName = $"{jobModel.UserName}_{entityType.Id}_{DateTime.UtcNow:yyyy_MM_dd_HH_mm_ss}.xlsx";
            return fileName;
        }

        private async Task<int> GetExcelExportBatchSizeAsync()
        {
            var excelExportBatchSizeSetting = await this.environmentSettingsRepository.GetEnvironmentSettingAsync("LongRunningJob", "EXCEL_EXPORT_BATCH_SIZE");

            return excelExportBatchSizeSetting != null && int.TryParse(excelExportBatchSizeSetting.Value, out var excelExportBatchSize)
                ? excelExportBatchSize
                : ExcelExportRepository.DefaultExcelExportBatchSize;
        }
    }
}
