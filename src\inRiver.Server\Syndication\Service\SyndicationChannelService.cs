namespace inRiver.Server.Syndication.Service
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;
    using Core.Models.inRiver;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Models;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Newtonsoft.Json;

    public class SyndicationChannelService : ISyndicationChannelService
    {
        private readonly IDataPersistance dataContext;

        private readonly CultureInfo dataLanguage;

        public SyndicationChannelService(IRequestContext context)
        {
            this.dataContext = context.DataPersistance;
            this.dataLanguage = context.DataLanguage;
        }

        public async Task<IList<ChannelStructure>> GetChannelStructureByEntityTypeIds(int channelId, IList<string> entityTypeIds)
        {
            var channelStructure = await this.dataContext.GetChannelStructureByEntityTypeIdsForChannelAsync(channelId, entityTypeIds);

            return channelStructure.Any()
                ? channelStructure
                : throw new SyndicateException($"There is no {string.Join(",", entityTypeIds)} entities in the {channelId} channel.");
        }

        public async Task<string> GetChannelNameAsync(int channelId)
        {
            var channels = (await this.dataContext.GetEntitiesAsync(new List<int> { channelId })).ToList();

            var channel = channels.FirstOrDefault();
            if (channel == null)
            {
                throw new SyndicateException($"Couldn't find Channel by Id {channelId}");
            }

            if (channel.DisplayName?.Data == null)
            {
                return channelId.ToString();
            }

            if (channel.DisplayName.Data.Contains("stringMap"))
            {
                var nameLocaleString = JsonConvert.DeserializeObject<LocaleString>(channel.DisplayName.Data);

                return GetDisplayValue(nameLocaleString, channelId.ToString(), this.dataLanguage);
            }

            return channel.DisplayName.Data;
        }

        private static string GetDisplayValue(LocaleString localeString, string defaultValue, CultureInfo cultureInfo) =>
            string.IsNullOrEmpty(localeString?[cultureInfo])
                ? defaultValue
                : localeString[cultureInfo];
    }
}
