<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IsServiceFabricServiceProject>True</IsServiceFabricServiceProject>
    <ServerGarbageCollection>True</ServerGarbageCollection>
    <RuntimeIdentifier>win7-x64</RuntimeIdentifier>
    <TargetLatestRuntimePatch>False</TargetLatestRuntimePatch>
    <Platforms>x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="inRiver.StackEssentials" Version="6.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.19.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.ServiceFabric.AspNetCore.Configuration" Version="6.1.1653" />
    <PackageReference Include="Microsoft.ServiceFabric.Services" Version="6.1.1653" />
    <PackageReference Include="Scrutor" Version="3.3.0" />
    <PackageReference Include="Serilog" Version="2.8.0" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="3.1.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Core\inRiver.Core.csproj" />
    <ProjectReference Include="..\inRiver.Server\inRiver.Server.csproj" />
    <ProjectReference Include="..\LongRunningJob.Core\LongRunningJob.Core.csproj" />
  </ItemGroup>

</Project>
