namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Core.Persistance;
    using inRiver.iPMC.Persistance;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Query;
    using inRiver.Server.Completeness;
    using inRiver.Server.Error;
    using inRiver.Server.Helpers;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using Inriver.Expressions.Dto;
    using Serilog;
    using Syndication.Models;
    using ChannelStructure = Models.ChannelStructure;
    using Criteria = inRiver.iPMC.Persistance.Criteria;
    using Entity = inRiver.Remoting.Objects.Entity;
    using EntityType = inRiver.Remoting.Objects.EntityType;
    using Field = inRiver.Remoting.Objects.Field;
    using FieldSet = inRiver.Remoting.Objects.FieldSet;
    using FieldType = inRiver.Remoting.Objects.FieldType;
    using Link = inRiver.Remoting.Objects.Link;
    using LinkType = inRiver.Remoting.Objects.LinkType;
    using LoadLevel = inRiver.iPMC.Persistance.LoadLevel;
    using SystemQuery = inRiver.Remoting.Query.SystemQuery;

    class IPMCServerPersistanceAdapter : IDataPersistance
    {

        protected iPMC.Persistance.IPersistanceFieldType PersistentFieldType;
        protected iPMC.Persistance.IPersistanceFieldSet PersistanceFieldSet;
        protected iPMC.Persistance.IPersistanceEntityType PersistentEntityType;
        public iPMC.Persistance.IPersistanceEntity PersistentEntity;
        public iPMC.Persistance.IPersistanceContentSegmentation PersistanceContentSegmentation;
        public iPMC.Persistance.IPersistanceChannel PersistanceChannel;

        protected readonly IDataPersistance _origInRiverPersistance;

        protected readonly RequestContext _context;

        protected readonly IContentSegmentPermissionProvider _contentSegmentProvider;

        public IPMCServerPersistanceAdapter(RequestContext context, IDataPersistance origInRiverPersistance)
        {
            _origInRiverPersistance = origInRiverPersistance;

            _contentSegmentProvider = new ContentSegmentPermissionProvider(context);

            PersistentFieldType = new PersistanceFieldType(
                context.ConnectionString,
                context.Logging,
                _contentSegmentProvider);

            PersistanceFieldSet = new PersistanceFieldSet(
                context.ConnectionString,
                context.Logging,
                _contentSegmentProvider);

            PersistentEntityType = new PersistanceEntityType(
                context.ConnectionString,
                context.Logging,
                PersistentFieldType,
                PersistanceFieldSet,
                _contentSegmentProvider);

            PersistanceContentSegmentation = new PersistanceContentSegmentation(
                context.ConnectionString,
                context.Logging,
                _contentSegmentProvider);

            PersistentEntity = new PersistanceEntity(
                context.ConnectionString,
                context.Logging,
                PersistentEntityType,
                PersistentFieldType,
                PersistanceContentSegmentation,
                _contentSegmentProvider);

            PersistanceChannel = new PersistanceChannel(
                                            context.ConnectionString,
                                            context.Logging,
                                            _contentSegmentProvider);

            _context = context;
        }

        public static T ConvertTo<T>(object inputObject)
        {
            try
            {
                var result = ConversionHelper.Map<T>(inputObject);
                return result;
            }
            catch (Exception ex)
            {
                Log.Warning($"Failed to map from {inputObject.GetType()} to {typeof(T)}" + ex.Message);
            }

            return IPMCPersistanceDataConverter.ConvertTo<T>(inputObject);
        }

        #region Segmentation

        public bool SetSegmentForEntities(List<int> entityIds, int segmentId)
        {
            try
            {
                return PersistentEntity.SetSegmentForEntities(entityIds, segmentId);

            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when setting segment to " + segmentId);
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when setting segment to " + segmentId,
                    ex);
            }
        }

        public async Task<IEnumerable<Remoting.Objects.Segment>> GetAllSegmentsAsync()
        {
            try
            {
                return await _origInRiverPersistance.GetAllSegmentsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting all segments");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting all segments", ex);
            }
        }

        #endregion

        #region EntityType

        public List<EntityType> GetAllEntityTypes(bool loadFieldTypes = false, bool loadLinkTypes = false,
            bool loadFieldsets = false)
        {
            try
            {
                List<iPMC.Persistance.EntityType> entityTypes =
                    PersistentEntityType.GetAllEntityTypes(loadFieldTypes, loadLinkTypes, loadFieldsets);

                return ConvertTo<List<EntityType>>(entityTypes);
            }
            catch (Exception ex)
            {

                Log.Error(ex, "An unexpected error occured when getting all Entity Types");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting all Entity Types",
                    ex);
            }

        }

        public List<EntityType> GetAllEntityTypes()
        {
            return GetAllEntityTypes(true, true, true);
        }

        public EntityType GetEntityType(string id)
        {
            try
            {
                iPMC.Persistance.EntityType sourceEntity =
                    PersistentEntityType.GetEntityType(id, true, true, true);
                if (sourceEntity == null)
                {
                    return null;
                }
                EntityType returnEntity = ConvertTo<EntityType>(sourceEntity);

                List<CultureInfo> serverLanguages = this.GetAllLanguages();

                returnEntity.FieldViews = GetFieldViewsForEntityType(id, serverLanguages);
                return returnEntity;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting Entity Type");
                throw ErrorUtility.GetDataAccessException(
                    string.Format("An unexpected error occured when getting Entity Type ({0})", id), ex);
            }
        }

        #endregion

        #region Field Type

        public FieldType GetFieldType(string id)
        {
            try
            {
                return ConvertTo<FieldType>(PersistentFieldType.GetFieldType(id, true));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting Field Type");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting Field Type",
                    ex);
            }
        }

        public List<FieldType> GetAllFieldTypes()
        {
            try
            {
                return ConvertTo<List<FieldType>>(PersistentFieldType.GetAllFieldTypes(true));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting all Field Types");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting all Field Types",
                    ex);
            }
        }

        public List<FieldType> GetFieldTypesForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            try
            {
                return ConvertTo<List<FieldType>>(PersistentFieldType.GetFieldTypesForEntityType(entityTypeId, true));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting all Field Types for Entity Type " + entityTypeId);
                throw ErrorUtility.GetDataAccessException(
                    "An unexpected error occured when getting all Field Types for Entity Type " + entityTypeId,
                    ex);

            }
        }

        public Dictionary<string, string> GetFieldTypeSettings(string fieldTypeId)
        {
            try
            {
                return PersistentFieldType.GetFieldTypeSettings(fieldTypeId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting Field Types settings for Field Type " + fieldTypeId);
                throw ErrorUtility.GetDataAccessException(
                    "An unexpected error occured when getting Field Types settings for Field Type " + fieldTypeId,
                    ex);
            }
        }

        #endregion

        #region LinkType

        public LinkType GetLinkType(string id)
        {
            return _origInRiverPersistance.GetLinkType(id);
        }

        public LinkType GetLinkType(string id, List<CultureInfo> serverLanguages)
        {
            return _origInRiverPersistance.GetLinkType(id, serverLanguages);
        }

        public List<LinkType> GetAllLinkTypes()
        {
            return _origInRiverPersistance.GetAllLinkTypes();
        }

        public List<LinkType> GetLinkTypesForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            return _origInRiverPersistance.GetLinkTypesForEntityType(entityTypeId, languages);
        }

        public IEnumerable<LinkType> GetLinkTypes(string sourceEntityTypeId, string targetEntityTypeId)
        {
            return _origInRiverPersistance.GetLinkTypes(sourceEntityTypeId, targetEntityTypeId);
        }

        #endregion

        #region FieldSet

        public FieldSet GetFieldSet(string id)
        {
            try
            {
                return ConvertTo<FieldSet>(PersistanceFieldSet.GetFieldSet(id));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting Field Set");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting Field Set",
                    ex);
            }
        }

        public List<FieldSet> GetAllFieldSets()
        {
            try
            {
                return ConvertTo<List<FieldSet>>(PersistanceFieldSet.GetAllFieldSets(true));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting all Field Sets");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting all Field Sets",
                    ex);
            }
        }

        public List<FieldSet> GetFieldSetsForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            try
            {
                return ConvertTo<List<FieldSet>>(PersistanceFieldSet.GetFieldSetsForEntityType(entityTypeId, true));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting all Field Sets for Entity Type");
                throw ErrorUtility.GetDataAccessException(
                    "An unexpected error occured when getting all Field Sets for Entity Type", ex);
            }
        }

        public List<string> GetFieldTypesForFieldSet(string fieldSetId)
        {
            try
            {
                return PersistanceFieldSet.GetFieldTypesForFieldSet(fieldSetId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting Field Types for Field Set");
                throw ErrorUtility.GetDataAccessException(
                    "An unexpected error occured when getting Field Types for Field Set", ex);
            }
        }

        #endregion

        #region Entity

        // Note: once inserted successfully, some attributes within the input parameter 'entity'
        //   will be updated with the entityId, and some other administrative values.
        //   This is to maintain consistent behavior as what's been implemented in the legacy data layer
        public virtual DtoEntity AddEntity(Entity entity)
        {
            try
            {
                var input = ConvertTo<iPMC.Persistance.Entity>(entity);

                iPMC.Persistance.Entity output = PersistentEntity.AddEntity(input);

                if (output != null && entity != null)
                {
                    iPMC.Persistance.Entity newlyInserted = output;
                    MapEntityProperties(entity, newlyInserted);
                }

                return ConvertTo<DtoEntity>(output);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when adding entity");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when adding entity", ex);
            }
        }

        public DtoEntity AddEntityWithFields(Entity entity)
        {
            return this.AddEntity(entity);
        }

        // Note: once inserted successfully, the items within input parameter 'List<Entity> entities'
        //   will be updated with the entityId, and some other administrative values.
        //   This is to maintain consistent behavior as what's been implemented in the legacy data layer
        public virtual List<DtoEntity> AddEntities(List<Entity> entities)
        {
            try
            {
                var input = ConvertTo<List<iPMC.Persistance.Entity>>(entities);

                List<iPMC.Persistance.Entity> output = PersistentEntity.AddEntities(input);

                // Loop through the output list, and make update on the input list (input parameter entities)
                // In perfect situation length of output list and entities should be the same, but here 
                // we additional conditional checking to be sure we wont go out-of-bound trying to get items
                // from either lists.
                for (int i = 0; output != null && i < output.Count && entities != null && i < entities.Count; i++)
                {
                    iPMC.Persistance.Entity newlyInserted = output[i];

                    Entity entity = entities[i];
                    MapEntityProperties(entity, newlyInserted);
                }

                return ConvertTo<List<DtoEntity>>(output);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when adding entities");

                if (ex.Message.Contains("Duplicate Key"))
                {
                    string field = string.Empty;
                    int fieldIndex1 = ex.Message.IndexOf("index '");
                    int fieldIndex2 = ex.Message.IndexOf("'.");
                    if (fieldIndex1 > 0 && fieldIndex2 > 0)
                        field = ex.Message.Substring(fieldIndex1, fieldIndex2 - fieldIndex1);

                    string value = string.Empty;
                    int valueIndex1 = ex.Message.IndexOf("value is (");
                    int valueIndex2 = ex.Message.IndexOf(").", valueIndex1);
                    if (valueIndex2 > 0 && valueIndex1 > 0)
                        value = ex.Message.Substring(valueIndex1, valueIndex2 - valueIndex1);
                    throw ErrorUtility.GetDataAccessException(String.Format("A duplicate key was attempted to be added. Field:{0} Values:{1}", field, value), ex);
                }
                else
                {
                    throw ErrorUtility.GetDataAccessException("An unexpected error occured when adding entities",
                        ex);
                }
            }
        }

        public DtoEntity GetEntity(int id)
        {
            try
            {
                List<int> entityIds = new List<int>() { id };
                List<DtoEntity> entities = ConvertTo<List<DtoEntity>>(PersistentEntity.GetEntities(entityIds));
                return entities.Count > 0 ? entities[0] : null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting entity");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting entity", ex);
            }
        }

        public Entity GetFullEntity(int id, EntityType entityType, bool includeDisplayInformation = true,
            bool includePendingDelete = false, bool ignoreSegmentCheck = false)
        {
            try
            {
                iPMC.Persistance.EntityType ipmcEntityType = ConvertTo<iPMC.Persistance.EntityType>(entityType);

                List<int> entityIds = new List<int>() { id };

                var output = PersistentEntity.GetFullEntity(id, ipmcEntityType, includePendingDelete, ignoreSegmentCheck);

                return ConvertTo<Entity>(output);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting full entity");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting full entity",
                    ex);
            }

        }

        public virtual DtoEntity GetEntityWithData(int id)
        {
            try
            {
                iPMC.Persistance.Entity entity = PersistentEntity.GetEntity(id);

                if (entity != null)
                {
                    entity.LoadLevel = LoadLevel.DataOnly;
                    entity.Fields = PersistentEntity.GetFieldsForEntity(entity);
                    return ConvertTo<DtoEntity>(entity);
                }
                else
                {
                    throw new PersistanceException($"GetEntityWithData(id: {id}) - entity not found");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting entity with data");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting entity with data",
                    ex);
            }
        }

        // TODO: make it more efficient if possible
        public virtual List<DtoEntity> GetEntitiesWithData(List<int> entityIds, CancellationToken cancellationToken)
        {
            try
            {
                List<iPMC.Persistance.Entity> items = PersistentEntity.GetEntitiesWithData(entityIds, cancellationToken);
                List<DtoEntity> dtoEntities = new List<DtoEntity>();

                foreach (var entity in items)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    entity.LoadLevel = LoadLevel.DataOnly;
                    dtoEntities.Add(ConvertTo<DtoEntity>(entity));
                }

                return dtoEntities;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                Log.Error(ex, "An unexpected error occured when getting entities with data");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when  getting entities with data",
                    ex);
            }
        }

        public virtual async Task<List<DtoEntity>> GetEntitiesWithDataAsync(List<int> entityIds, CancellationToken cancellationToken)
        {
            try
            {
                List<iPMC.Persistance.Entity> items = await PersistentEntity.GetEntitiesWithDataAsync(entityIds, cancellationToken);
                List<DtoEntity> dtoEntities = new List<DtoEntity>();

                foreach (var entity in items)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    entity.LoadLevel = LoadLevel.DataOnly;
                    dtoEntities.Add(ConvertTo<DtoEntity>(entity));
                }

                return dtoEntities;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                Log.Error(ex, "An unexpected error occured when getting entities with data");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when  getting entities with data",
                    ex);
            }
        }

        public DataTable GetEntitiesAsTable(List<int> entityIds, string entityType, List<FieldType> fields, ContentSegmentationEnum segmentationOption, CancellationToken cancellationToken)
        {
            try
            {

                return PersistentEntity.GetEntitiesAsDataTable(entityIds, entityType, ConvertTo<List<iPMC.Persistance.FieldType>>(fields), segmentationOption, cancellationToken);
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                Log.Error(ex, "An unexpected error occured when getting entities");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting entities",
                    ex);
            }
        }

        public List<DtoEntity> GetEntities(List<int> ids)
        {
            try
            {

                return ConvertTo<List<DtoEntity>>(PersistentEntity.GetEntities(ids));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting entities");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting entities",
                    ex);
            }
        }

        public async Task<IEnumerable<DtoEntity>> GetEntitiesAsync(IEnumerable<int> ids)
        {
            try
            {
                return ConvertTo<IEnumerable<DtoEntity>>(await this.PersistentEntity.GetEntitiesAsync(ids));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting entities");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting entities",
                    ex);
            }
        }

        public async Task<List<DtoEntity>> GetEntitiesAsync(List<int> ids)
        {
            try
            {
                return ConvertTo<List<DtoEntity>>(await PersistentEntity.GetEntitiesAsync(ids));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when getting entities");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when getting entities",
                    ex);
            }
        }

        public virtual int? GetEntityIdByUniqueValue(string fieldTypeId, string value)
        {
            return PersistentEntity.GetEntityIdByUniqueValue(fieldTypeId, value);
        }

        public void ReCalculateDisplayValuesForEntity(int entityId, List<Field> updatedFields)
        {
            try
            {
                PersistentEntity.ReCalculateDisplayValuesForEntity(entityId,
                    ConvertTo<List<iPMC.Persistance.Field>>(updatedFields));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when recalculating display values for entity");
                throw ErrorUtility.GetDataAccessException(
                    "An unexpected error occured when recalculating display values for entity", ex);
            }
        }

        private static void MapEntityProperties(Entity entity, iPMC.Persistance.Entity newlyInserted)
        {
            entity.Id = newlyInserted.Id;
            entity.DateCreated = newlyInserted.DateCreated;
            entity.LastModified = newlyInserted.LastModified;
            entity.Version = newlyInserted.Version;
            entity.ChangeSet = newlyInserted.ChangeSet;
            entity.CreatedBy = newlyInserted.CreatedBy;
            entity.ModifiedBy = newlyInserted.ModifiedBy;

            if (entity.Fields != null)
            {
                foreach (var entityField in entity.Fields)
                {
                    entityField.Revision = 1;  // needed for FieldRevisionHistory generation
                }
            }
        }

        #region Using Old persistance model

        public void UpdateEntityChangeSet(int entityId, List<Field> updatedFields)
        {
            _origInRiverPersistance.UpdateEntityChangeSet(entityId, updatedFields);
        }

        public DtoEntity SetEntityFieldSet(int entityId, string fieldSetId)
        {
            return _origInRiverPersistance.SetEntityFieldSet(entityId, fieldSetId);
        }

        public List<int> GetAllEntityIdsForEntityType(string entityTypeId)
            => GetAllEntityIdsForEntityType(entityTypeId, CancellationToken.None);

        public List<int> GetAllEntityIdsForEntityType(string entityTypeId, CancellationToken cancellationToken)
        {
            return _origInRiverPersistance.GetAllEntityIdsForEntityType(entityTypeId, cancellationToken);
        }

        public bool SetEntityCompleteness(int entityId, int? completeness)
        {
            return _origInRiverPersistance.SetEntityCompleteness(entityId, completeness);
        }

        public async Task<bool> SetEntityCompletenessAsync(int entityId, int? completeness)
            => await this._origInRiverPersistance.SetEntityCompletenessAsync(entityId, completeness);

        public bool EntityExists(int entityId)
        {
            return _origInRiverPersistance.EntityExists(entityId);
        }

        #endregion old Persistance model

        #endregion

        #region Link

        public virtual DtoLink AddLink(Link link)
        {
            return _origInRiverPersistance.AddLink(link);
        }

        public bool LinkAlreadyExists(int sourceEntityId, int targetEntityId, int? linkEntityId, string linkTypeId)
        {
            return _origInRiverPersistance.LinkAlreadyExists(sourceEntityId, targetEntityId, linkEntityId, linkTypeId);
        }

        public List<DtoLink> GetLinksForEntitiesByLinkType(IList<int> entityIds, string linkTypeId) =>
            this._origInRiverPersistance.GetLinksForEntitiesByLinkType(entityIds, linkTypeId);

        public List<DtoLink> GetLinksForEntity(int entityId, CancellationToken cancellationToken)
        {
            return _origInRiverPersistance.GetLinksForEntity(entityId, cancellationToken);
        }

        public async Task<List<DtoLink>> GetLinksForEntityAsync(int entityId, CancellationToken cancellationToken)
        {
            return await _origInRiverPersistance.GetLinksForEntityAsync(entityId, cancellationToken);
        }

        public List<DtoLink> GetResourceLinksForEntity(int entityId)
        {
            return _origInRiverPersistance.GetResourceLinksForEntity(entityId);
        }

        public List<DtoLink> GetResourceLinksForEntities(IList<int> entityIds) =>
            this._origInRiverPersistance.GetResourceLinksForEntities(entityIds);

        public DtoLink GetLink(int id)
        {
            return _origInRiverPersistance.GetLink(id);
        }

        public List<DtoLink> GetOutboundLinksForEntity(int entityId, CancellationToken cancellationToken)
        {
            return _origInRiverPersistance.GetOutboundLinksForEntity(entityId, cancellationToken);
        }

        public List<DtoLink> GetInboundLinksForEntity(int entityId)
        {
            return _origInRiverPersistance.GetInboundLinksForEntity(entityId);
        }

        public int GetLinkCountForOutboundLinkType(string linkTypeId, int sourceEntityId)
        {
            return _origInRiverPersistance.GetLinkCountForOutboundLinkType(linkTypeId, sourceEntityId);
        }

        public async Task<int> GetLinkCountForOutboundLinkTypeAsync(string linkTypeId, int sourceEntityId)
            => await this._origInRiverPersistance.GetLinkCountForOutboundLinkTypeAsync(linkTypeId, sourceEntityId);

        public List<DtoLink> GetOutboundLinksForEntityAndLinkType(int entityId, string linkTypeId)
        {
            return _origInRiverPersistance.GetOutboundLinksForEntityAndLinkType(entityId, linkTypeId);
        }

        public async Task<IEnumerable<DtoLink>> GetOutboundLinksForEntityAndLinkTypeAsync(int entityId, string linkTypeId)
            => await this._origInRiverPersistance.GetOutboundLinksForEntityAndLinkTypeAsync(entityId, linkTypeId);

        public List<DtoLink> GetInboundLinksForEntityAndLinkType(int entityId, string linkTypeId)
        {
            return _origInRiverPersistance.GetInboundLinksForEntityAndLinkType(entityId, linkTypeId);
        }

        #endregion

        #region Channel

        #endregion

        public DtoLink GetValidationLink(Link link)
        {
            return _origInRiverPersistance.GetValidationLink(link);
        }

        public Dictionary<int, List<int>> GetInboundParentsForEntitiesAndLinkType(List<int> entityIds,
            string linkTypeId, CancellationToken cancellationToken)
        {
            return _origInRiverPersistance.GetInboundParentsForEntitiesAndLinkType(entityIds, linkTypeId, cancellationToken);
        }

        public Dictionary<int, List<int>> GetOutboundChildrenForEntitiesAndLinkType(List<int> entityIds,
            string linkTypeId, CancellationToken cancellationToken)
        {
            return _origInRiverPersistance.GetOutboundChildrenForEntitiesAndLinkType(entityIds, linkTypeId, cancellationToken);
        }

        #region Non-IPMC or Converted items

        #region Field

        public virtual void AddFields(List<Field> fields)
        {
            // do nothing, since adding a list of Fields is irrelevant.
            // it should be be handled by Add/Update Entity
            throw new NotImplementedException();
        }

        public IList<string> GetSearchHintFieldTypesByEntityId(string entityId) => this._origInRiverPersistance.GetSearchHintFieldTypesByEntityId(entityId);

        #endregion Field

        public void DeleteCompletenessStateForDefinition(int definitionId, CancellationToken cancellationToken)
        {
            _origInRiverPersistance.DeleteCompletenessStateForDefinition(definitionId, cancellationToken);
        }

        public bool DeleteSharedWorkAreaFolder(Guid id)
        {
            return _origInRiverPersistance.DeleteSharedWorkAreaFolder(id);
        }

        public virtual bool FieldValueAlreadyExistsForFieldType(string fieldTypeId, object value)
        {
            iPMC.Persistance.FieldType fieldType = PersistentFieldType.GetFieldType(fieldTypeId);
            return PersistentEntity.DoesEntityExist(fieldType, value);
        }

        public virtual bool IsExcludedByChannelFilter(int channelId, int entityId, string linkTypeId)
        {
            return PersistanceChannel.IsExcludedByChannelFilter(channelId, entityId, linkTypeId, _context.CustomerSafeName, _context.EnvironmentSafeName);
        }

        public List<CompletenessBusinessRule> GetAllCompletenessBusinessRules()
        {
            return _origInRiverPersistance.GetAllCompletenessBusinessRules();
        }

        public List<CompletenessGroup> GetAllCompletenessGroupForDefinition(int defintionId)
        {
            return _origInRiverPersistance.GetAllCompletenessGroupForDefinition(defintionId);
        }

        public List<CompletenessRuleSetting> GetAllCompletenessRuleSettingsForBusinessRule(int ruleId)
        {
            return _origInRiverPersistance.GetAllCompletenessRuleSettingsForBusinessRule(ruleId);
        }

        public List<int> GetAllEntityIdsForChannel(int channelId)
        {
            return _origInRiverPersistance.GetAllEntityIdsForChannel(channelId);
        }

        public async Task<IList<ChannelStructure>> GetChannelStructureByEntityTypeIdsForChannelAsync(int channelId, IList<string> entityTypeIds) =>
            await this._origInRiverPersistance.GetChannelStructureByEntityTypeIdsForChannelAsync(channelId, entityTypeIds);

        public List<int> GetAllEntityIdsNotInChannel(int channelId)
        {
            return _origInRiverPersistance.GetAllEntityIdsNotInChannel(channelId);
        }

        public List<CultureInfo> GetAllLanguages()
        {
            return _origInRiverPersistance.GetAllLanguages();
        }

        public virtual bool AddLanguage(CultureInfo cultureInfo)
            => throw new NotImplementedException();

        public virtual bool DeleteAllLanguages()
            => throw new NotImplementedException();

        public virtual bool DeleteLanguage(CultureInfo cultureInfo)
            => throw new NotImplementedException();

        public List<Remoting.Objects.Role> GetAllRoles()
        {
            return _origInRiverPersistance.GetAllRoles();
        }

        public List<Remoting.Objects.User> GetAllShallowUsers()
        {
            return _origInRiverPersistance.GetAllShallowUsers();
        }

        public Dictionary<string, string> GetAllUserSettings(int id)
        {
            return _origInRiverPersistance.GetAllUserSettings(id);
        }

        public Category GetCategory(string id)
        {
            return _origInRiverPersistance.GetCategory(id);
        }

        public List<CompletenessAction> GetCompletenessActionsByDefinitionAndGroup(int definitionId, int groupId,
            string actiontrigger)
        {
            return _origInRiverPersistance.GetCompletenessActionsByDefinitionAndGroup(definitionId, groupId,
                actiontrigger);
        }

        public List<CompletenessAction> GetCompletenessActionsByDefinitionAndRule(int definitionId, int ruleId,
            string actiontrigger)
        {
            return _origInRiverPersistance.GetCompletenessActionsByDefinitionAndRule(definitionId, ruleId,
                actiontrigger);
        }

        public List<CompletenessBusinessRule> GetCompletenessBusinessRulesForGroup(int groupId)
        {
            return _origInRiverPersistance.GetCompletenessBusinessRulesForGroup(groupId);
        }

        public CompletenessDefinition GetCompletenessDefinition(int id)
        {
            return _origInRiverPersistance.GetCompletenessDefinition(id);
        }

        public int? GetCompletenessDefinitionIdForEntityType(string entityTypeId)
        {
            return _origInRiverPersistance.GetCompletenessDefinitionIdForEntityType(entityTypeId);
        }

        public CVL GetCVL(string id)
        {
            return _origInRiverPersistance.GetCVL(id);
        }

        public IList<CVL> GetAllCVLs() => this._origInRiverPersistance.GetAllCVLs();

        public List<Core.Models.inRiver.CVLKey> GetCvlKeysByCvlId(string cvlId) => this._origInRiverPersistance.GetCvlKeysByCvlId(cvlId);

        public CVLValue GetCVLValueByKey(string key, string cvlId)
        {
            return _origInRiverPersistance.GetCVLValueByKey(key, cvlId);
        }

        public List<CVLValue> GetCVLValueByKeys(string[] keys, string cvlId, string selectedLanguage, List<CultureInfo> serverLanguages)
        {
            return _origInRiverPersistance.GetCVLValueByKeys(keys, cvlId, selectedLanguage, serverLanguages);
        }

        public List<CVLValue> GetExistingCVLKeysByKeyList(List<string> keys, CVL cvl)
        {
            return _origInRiverPersistance.GetExistingCVLKeysByKeyList(keys, cvl);
        }

        public string GetCvlIdForFieldType(string fieldTypeId) =>
            this._origInRiverPersistance.GetCvlIdForFieldType(fieldTypeId);

        public List<CVLValue> GetCVLValuesForCVL(string cvlId)
        {
            return _origInRiverPersistance.GetCVLValuesForCVL(cvlId);
        }

        public List<int> GetDigitalChannelsForEntity(int entityId)
        {
            return _origInRiverPersistance.GetDigitalChannelsForEntity(entityId);
        }

        public List<int> GetEntitiesForFolder(Guid id)
        {
            return _origInRiverPersistance.GetEntitiesForFolder(id);
        }

        public List<MapEnumeration> GetEnumerations(List<int> formatFieldIds, int mappingId)
        {
            return _origInRiverPersistance.GetEnumerations(formatFieldIds, mappingId);
        }

        public List<ExcelExportHistoryResultModel> GetExcelExportHistoryForEntity(int entityId)
        {
            return _origInRiverPersistance.GetExcelExportHistoryForEntity(entityId);
        }

        public List<ExcelExportHistoryResultModel> GetExcelExportHistoryForSupplier(int supplierId)
        {
            return _origInRiverPersistance.GetExcelExportHistoryForSupplier(supplierId);
        }

        public string GetExcelModel(string username, string batchId, bool configData)
        {
            return _origInRiverPersistance.GetExcelModel(username, batchId, configData);
        }

        public virtual DtoField GetField(int entityId, string fieldTypeId)
        {
            DtoField retVal = null;

            var persistanceEntity = PersistentEntity.GetEntity(entityId);

            if (persistanceEntity != null)
            {
                var output = PersistentEntity.GetFieldsForEntity(persistanceEntity, fieldTypeId).FirstOrDefault();
                retVal = ConvertTo<DtoField>(output);
            }

            return retVal;
        }

        public virtual List<DtoField> GetFieldsForEntity(DtoEntity entity)
        {
            var input = this.FromDtoJustBasicData(entity);
            var output = this.PersistentEntity.GetFieldsForEntity(input);

            return ConvertTo<List<DtoField>>(output);
        }

        private iPMC.Persistance.Entity FromDtoJustBasicData(DtoEntity entity)
        {
            if (entity == null)
            {
                return null;
            }

            // included only fields needed for further processing
            return new iPMC.Persistance.Entity()
            {
                EntityTypeId = entity.EntityTypeId,
                ChangeSet = entity.ChangeSet,
                Id = entity.Id,
                LastModified = DateTime.ParseExact(entity.LastModified, PersistanceEntity.InternalDateTimeFormat, null)
            };
        }

        public List<string> GetFieldTypesForFieldView(string fieldViewId)
        {
            return _origInRiverPersistance.GetFieldTypesForFieldView(fieldViewId);
        }

        public virtual object GetFieldValue(int entityId, string fieldTypeId)
        {
            object data = null;
            var persistanceEntity = PersistentEntity.GetEntity(entityId);
            if (persistanceEntity != null)
            {
                var field = PersistentEntity.GetFieldsForEntity(persistanceEntity, fieldTypeId).FirstOrDefault();

                var remotingField = ConvertTo<Field>(field);

                data = remotingField?.Data;
            }

            return data;
        }

        public virtual async Task<object> GetFieldValueAsync(int entityId, string fieldTypeId)
            => await this.PersistentEntity.GetFieldValueAsync(entityId, fieldTypeId);

        public List<FieldView> GetFieldViewsForEntityType(string entityTypeId, List<CultureInfo> languages = null)
        {
            return _origInRiverPersistance.GetFieldViewsForEntityType(entityTypeId, languages);
        }

        public Field GetFullField(int entityId, string entityTypeId, string fieldTypeId, DateTime entityCreated)
        {
            iPMC.Persistance.Entity entity = new iPMC.Persistance.Entity() { EntityTypeId = entityTypeId, Id = entityId };
            List<iPMC.Persistance.Field> output = PersistentEntity.GetFieldsForEntity(entity, fieldTypeId);
            return output.Count > 0 ? ConvertTo<Field>(output[0]) : null;
        }

        public virtual List<Field> GetFullFieldsForEntity(Entity entity)
        {
            var input = ConvertTo<iPMC.Persistance.Entity>(entity);
            List<iPMC.Persistance.Field> output = PersistentEntity.GetFieldsForEntity(input);

            return ConvertTo<List<Field>>(output);
        }

        public List<Link> GetFullLinksForEntity(int entityId, bool includePendingDelete = false, bool ignoreSegmentCheck = false)
        {
            return _origInRiverPersistance.GetFullLinksForEntity(entityId, includePendingDelete, ignoreSegmentCheck);
        }

        public Link GetFullLink(int id)
        {
            return _origInRiverPersistance.GetFullLink(id);
        }

        public MapFormat GetMapFormat(int id)
        {
            return _origInRiverPersistance.GetMapFormat(id);
        }

        public SyndicationMapping GetMappingDetails(int mappingId)
        {
            return _origInRiverPersistance.GetMappingDetails(mappingId);
        }

        public SyndicationMappingFunction GetMappingFunction(int id)
        {
            return _origInRiverPersistance.GetMappingFunction(id);
        }

        public List<Remoting.Objects.Permission> GetPermissionsForUser(string username)
        {
            return _origInRiverPersistance.GetPermissionsForUser(username);
        }

        public List<DtoEntity> GetRelatedCompletenessEntities(int id)
        {
            return _origInRiverPersistance.GetRelatedCompletenessEntities(id);
        }

        public Remoting.Objects.Role GetRole(int id)
        {
            return _origInRiverPersistance.GetRole(id);
        }

        public List<Remoting.Objects.Role> GetRolesForUser(string username)
        {
            return _origInRiverPersistance.GetRolesForUser(username);
        }

        public string GetServerSetting(string key)
        {
            return _origInRiverPersistance.GetServerSetting(key);
        }

        public Dictionary<string, string> GetServerSettings(List<string> keyList)
        {
            return _origInRiverPersistance.GetServerSettings(keyList);
        }

        public List<ShallowCompletenessGroup> GetShallowCompletenessGroupsForDefinition(int defintionId)
        {
            return _origInRiverPersistance.GetShallowCompletenessGroupsForDefinition(defintionId);
        }

        public List<CompletenessDetail> GetShallowEntityCompletenessDetails(int entityId)
        {
            return _origInRiverPersistance.GetShallowEntityCompletenessDetails(entityId);
        }

        public Remoting.Objects.User GetShallowUser(string username)
        {
            return _origInRiverPersistance.GetShallowUser(username);
        }

        public WorkAreaFolder GetSharedWorkAreaFolder(Guid id, bool includeEntities)
        {
            return _origInRiverPersistance.GetSharedWorkAreaFolder(id, includeEntities);
        }

        public List<string> GetSpecFieldTypeIdsFromImportedHistory(List<int> supplierIds)
        {
            return _origInRiverPersistance.GetSpecFieldTypeIdsFromImportedHistory(supplierIds);
        }

        public List<string> GetSpecFieldTypeIdsFromImportedHistoryByEntityId(int entityId)
        {
            return _origInRiverPersistance.GetSpecFieldTypeIdsFromImportedHistoryByEntityId(entityId);
        }

        public SpecificationField GetSpecificationField(int specificationEntityId, string specificationFieldTypeId)
        {
            return _origInRiverPersistance.GetSpecificationField(specificationEntityId, specificationFieldTypeId);
        }

        public List<SpecificationField> GetSpecificationFieldsForEntity(int entityId)
        {
            return _origInRiverPersistance.GetSpecificationFieldsForEntity(entityId);
        }

        public SpecificationFieldType GetSpecificationFieldType(string id)
        {
            return _origInRiverPersistance.GetSpecificationFieldType(id);
        }

        public List<SpecificationFieldType> GetSpecificationFieldTypes(List<string> ids)
        {
            return _origInRiverPersistance.GetSpecificationFieldTypes(ids);
        }

        public string GetStorageAccountConnectionString(int environmentId)
        {
            return _origInRiverPersistance.GetStorageAccountConnectionString(environmentId);
        }

        public IList<ResourceFile> GetResourceFiles(IList<int> resourceFileIds)
            => this._origInRiverPersistance.GetResourceFiles(resourceFileIds);

        public List<int> SearchEntityBySystemQueryAndDataQuery(SystemQuery systemQuery, inRiver.Remoting.Query.Query dataQuery)
        {
            iPMC.Persistance.SystemQuery generalEntityCriteria = ConvertTo<iPMC.Persistance.SystemQuery>(systemQuery);
            List<Criteria> fieldLevelCriteria = ConvertTo<List<Criteria>>(dataQuery.Criteria);

            return PersistentEntity.SearchEntity(generalEntityCriteria, fieldLevelCriteria, dataQuery.Join);
        }

        public Remoting.Objects.User GetUserByUsername(string username)
        {
            return _origInRiverPersistance.GetUserByUsername(username);
        }

        public bool GroupForEntityCompleted(int entityId, int completenessGroupId)
        {
            return _origInRiverPersistance.GroupForEntityCompleted(entityId, completenessGroupId);
        }

        public async Task<bool> GroupForEntityCompletedAsync(int entityId, int completenessGroupId)
            => await this._origInRiverPersistance.GroupForEntityCompletedAsync(entityId, completenessGroupId);

        public bool HasLinkRuleDefinition(int entityId, string linkTypeId)
        {
            return _origInRiverPersistance.HasLinkRuleDefinition(entityId, linkTypeId);
        }

        public virtual List<int> LinkSearch(inRiver.Remoting.Query.LinkQuery linkQuery)
        {
            return _origInRiverPersistance.LinkSearch(linkQuery);
        }

        public void LinkSpecification(DtoLink link)
        {
            _origInRiverPersistance.LinkSpecification(link);
        }

        public void UnlinkSpecification(DtoLink link)
        {
            _origInRiverPersistance.UnlinkSpecification(link);
        }

        public virtual void ReCalculateEntityMainPicture(int entityId, string entityTypeId)
        {
            PersistentEntity.ReCalculateEntityMainPicture(entityId, entityTypeId);
        }

        public virtual void ReloadChannel(int id)
        {
            _origInRiverPersistance.ReloadChannel(id, CancellationToken.None);
        }

        public virtual void ReloadChannel(int id, CancellationToken cancellationToken)
        {
            _origInRiverPersistance.ReloadChannel(id, cancellationToken);
        }

        public void SaveEntityFieldRevisionHistory(int entityId, List<Field> changedFields, bool entityIsNew,
            string userName = "system")
        {
            _origInRiverPersistance.SaveEntityFieldRevisionHistory(entityId, changedFields, entityIsNew, userName);
        }

        public void SaveEntityFieldRevisionHistorySynchronous(int entityId, List<Field> changedFields, bool entityIsNew)
        {
            _origInRiverPersistance.SaveEntityFieldRevisionHistorySynchronous(entityId, changedFields,
                entityIsNew);
        }

        public void SaveUpdatedEntityFieldRevisionHistory(int entityId, List<Field> changedFields,
            List<Field> persistedFields, string userName = "system") =>
            this._origInRiverPersistance.SaveUpdatedEntityFieldRevisionHistory(entityId, changedFields, persistedFields, userName);

        public List<int> SearchCompleteness(inRiver.Remoting.Query.CompletenessQuery query)
        {
            return _origInRiverPersistance.SearchCompleteness(query);
        }

        public List<int> SearchSpecification(inRiver.Remoting.Query.SpecificationQuery query)
        {
            return _origInRiverPersistance.SearchSpecification(query);
        }

        public void SetEntityCompletenessState(int entityId, int completenessDefinitionId, int groupId, int? ruleId,
            bool complete)
        {
            _origInRiverPersistance.SetEntityCompletenessState(entityId, completenessDefinitionId, groupId,
                ruleId, complete);
        }

        public Task SetEntityCompletenessStateAsync(int entityId, int completenessDefinitionId, int groupId, int? ruleId, bool complete)
            => this._origInRiverPersistance.SetEntityCompletenessStateAsync(entityId, completenessDefinitionId, groupId, ruleId, complete);

        public void SynchronizeChannel(int channelId, CancellationToken cancellationToken)
        {
            _origInRiverPersistance.SynchronizeChannel(channelId, cancellationToken);
        }

        public List<int> SystemSearch(SystemQuery systemQuery)
        {
            return _origInRiverPersistance.SystemSearch(systemQuery);
        }

        public virtual void UpdateFields(List<Field> fields)
        {
            // prerequisite: all fields must be from the same entity

            int entityId = fields[0].EntityId;
            string entityTypeId = fields[0].FieldType.EntityTypeId;

            iPMC.Persistance.Entity entity = new iPMC.Persistance.Entity()
            {
                Id = entityId,
                Fields = ConvertTo<List<iPMC.Persistance.Field>>(fields),
                EntityTypeId = entityTypeId
            };

            PersistentEntity.UpdateEntityFields(entity);
        }

        public virtual List<int> Search(Remoting.Query.Criteria criteria, Join? joinOperator = null)
        {
            var input = IPMCPersistanceDataConverter.ConvertTo<iPMC.Persistance.Criteria>(criteria);
            return PersistentEntity.Search(input, joinOperator);
        }

        public List<int> SearchForEntitiesBasedOnCriteria(Query query)
        {
            var input = IPMCPersistanceDataConverter.ConvertTo<List<iPMC.Persistance.Criteria>>(query.Criteria);
            return PersistentEntity.SearchEntitiesByCriterions(query.Join, input);
        }

        #endregion

        public void InsertRowIntoStagingTable(DataRow row)
        {
            _origInRiverPersistance.InsertRowIntoStagingTable(row);
        }

        public DataTable GetTableWithRowToProcessFromStagingTable(string batchId)
        {
            return _origInRiverPersistance.GetTableWithRowToProcessFromStagingTable(batchId);
        }

        public void SetImportStatus(string inRiverImportId, string status, string message)
        {
            _origInRiverPersistance.SetImportStatus(inRiverImportId, status, message);
        }

        public void SetPostProcessStatus(string inRiverImportId, string status, string message)
        {
            _origInRiverPersistance.SetPostProcessStatus(inRiverImportId, status, message);
        }

        public void SetStagingRow(DataRow row)
        {
            _origInRiverPersistance.SetStagingRow(row);
        }

        public List<int> GetSysIdsForImportJob(string jobId)
        {
            return _origInRiverPersistance.GetSysIdsForImportJob(jobId);
        }

        public void CreateChannelStructure(int channelId, string entityTypeId, CancellationToken cancellationToken)
        {
            PersistanceChannel.CreateChannelStructure(channelId, entityTypeId, _context.CustomerSafeName, _context.EnvironmentSafeName, cancellationToken);
        }

        public virtual IDictionary<int, IList<SyndicationRelatedEntityFieldValue>> GetRelatedEntityFields(
            string entityName,
            string field,
            string[] linkTypeIds,
            int[] entityLinkPosition,
            string language) => this.PersistentEntity.GetRelatedEntityFields(entityName, field, linkTypeIds, entityLinkPosition, language);

        public virtual List<DtoTaskCategory> GetTasksByUserAndGroup(string username, string groupId, int maxAmount)
            => throw new NotImplementedException();

        public virtual DtoEntity CreateNewVersion(int entityId)
            => throw new NotImplementedException();

        public virtual bool DeleteEntity(int id)
        {
            try
            {
                return this.PersistentEntity.DeleteEntity(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occured when deleting entity");
                throw ErrorUtility.GetDataAccessException("An unexpected error occured when deleting entity", ex);
            }
        }

        public virtual List<string> GetAllFieldValuesForField(string fieldTypeId)
            => throw new NotImplementedException();

        public virtual List<DtoField> GetFields(int entityId, List<string> fieldTypeIds)
        {
            var output = PersistentEntity.GetFields(entityId, fieldTypeIds);
            return ConvertTo<List<DtoField>>(output);
        }

        public virtual List<DtoField> GetAllFieldsByFieldType(string fieldTypeId)
            => throw new NotImplementedException();

        public virtual List<string> GetAllFieldValuesForFieldCaseSensitive(string fieldTypeId)
            => throw new NotImplementedException();

        public virtual List<DtoTaskCategory> GetTaskCategoriesAssignedToUser(string username, int maxAmount)
            => throw new NotImplementedException();

        public virtual List<DtoTaskCategory> GetTaskCategoriesCreatedByUser(string username, int maxAmount)
            => throw new NotImplementedException();

        public virtual void ReCalculateMainPictureForAllEntities()
            => throw new NotImplementedException();

        public virtual void ReCalculateDisplayValuesForAllEntities()
            => throw new NotImplementedException();

        public virtual List<Core.Models.inRiver.Queries.MultiValueSearchResult> MultiValueSearch(List<string> valuesList)
            => throw new NotImplementedException();

        public virtual void RecalculateMainpicturesForDeletedResource(object deletedResourceFileId)
            => throw new NotImplementedException();

        public virtual void DeleteStrayLinkEntites()
            => throw new NotImplementedException();

        public virtual (DtoLink, bool) AddLinkIfNotExists(Link link)
            => throw new NotImplementedException();

        public virtual DtoLink AddLinkAt(Link link, int index)
            => throw new NotImplementedException();

        public virtual List<DtoLink> AddLinksToNewTask(List<Link> links)
            => throw new NotImplementedException();

        public virtual bool DeleteLink(int linkId)
            => this._origInRiverPersistance.DeleteLink(linkId);

        public virtual bool DeleteLinksAndUpdateLinksSortOrder(int entityId, string[] linkTypeIdsToIgnore)
            => throw new NotImplementedException();

        public virtual DtoLink UpdateLinkSortOrder(int linkId, int index)
            => throw new NotImplementedException();

        public virtual void UpdateLinkSortOrderOnSourceEntity(int sourceEntityId, int index, string linkTypeId)
        {
            _origInRiverPersistance.UpdateLinkSortOrderOnSourceEntity(sourceEntityId, index, linkTypeId);
        }

        public virtual bool Inactivate(int id)
            => throw new NotImplementedException();

        public virtual bool Activate(int id)
            => throw new NotImplementedException();

        #region  Content Store

        public virtual void ExcludeFieldTypes(int contentStoreId, string fieldTypes) => this._origInRiverPersistance.ExcludeFieldTypes(contentStoreId, fieldTypes);
        public virtual void ExcludeEntityTypes(int contentStoreId, string entityTypes) => this._origInRiverPersistance.ExcludeEntityTypes(contentStoreId, entityTypes);
        public virtual void ExcludeLinkTypes(int contentStoreId, string linkTypes) => this._origInRiverPersistance.ExcludeLinkTypes(contentStoreId, linkTypes);

        public Dictionary<int, string> GetAllExcludeFieldTypes() => this._origInRiverPersistance.GetAllExcludeFieldTypes();

        public Dictionary<int, string> GetAllExcludeEntityTypes() => this._origInRiverPersistance.GetAllExcludeEntityTypes();

        public Dictionary<int, string> GetAllExcludeLinkTypes() => this._origInRiverPersistance.GetAllExcludeLinkTypes();

        #endregion

        public List<int> GetAllLinkIdsForEntityAndLinkType(int entityId, string linkTypeId, LinkDirection linkDirection) =>
            this._origInRiverPersistance.GetAllLinkIdsForEntityAndLinkType(entityId, linkTypeId, linkDirection);
        public List<FieldRevision> GetFieldRevisions(int entityId, string fieldTypeId, int maxNumberOfRevisions)
        {
            return this._origInRiverPersistance.GetFieldRevisions(entityId, fieldTypeId, maxNumberOfRevisions);
        }

        public bool EntityHasExpression(int entityId, string target, string targetType) => this._origInRiverPersistance.EntityHasExpression(entityId, target, targetType);
        public Dictionary<string, Dictionary<string, DtoExpression>> GetExpressionsForEntity(int entityId) => this._origInRiverPersistance.GetExpressionsForEntity(entityId);

        public List<DtoExpression> GetExpressionsForEntityTypesByTarget(List<string> entityTypeIds, string target) => this._origInRiverPersistance.GetExpressionsForEntityTypesByTarget(entityTypeIds, target);
        public int GetFirstLinkedEntity(int entityId, string direction, string linkTypeIds, bool inactive) => this._origInRiverPersistance.GetFirstLinkedEntity(entityId, direction, linkTypeIds, inactive);
        public void UpsertExpressions(List<DtoExpression> expressions) => this._origInRiverPersistance.UpsertExpressions(expressions);
        public void DeleteExpressions(List<int> ids) => this._origInRiverPersistance.DeleteExpressions(ids);
        public Dictionary<string, Dictionary<string, DtoExpression>> GetExpressionsForEntityType(string entityTypeId) => this._origInRiverPersistance.GetExpressionsForEntityType(entityTypeId);
        public DtoExpression GetExpressionForEntityByTarget(int entityId, string target) => this._origInRiverPersistance.GetExpressionForEntityByTarget(entityId, target);

        public virtual bool AddChannelLink(DtoLink link, int channelId)
            => throw new NotImplementedException();

        public virtual bool IsIncludedByChannelFilter(int channelId, int entityId, string linkTypeId)
            => throw new NotImplementedException();

        public List<int> GetEntityIdsForEntityTypeWithExpressions(string entityTypeId, string target, string targetType) => this._origInRiverPersistance.GetEntityIdsForEntityTypeWithExpressions(entityTypeId, target, targetType);

        public void DeleteExpressionsIfExists(List<int> entityIds, string target) => this._origInRiverPersistance.DeleteExpressionsIfExists(entityIds, target);

        public CompletenessDetail GetEntityCompletenessDetailFromRuleId(int ruleId) => this._origInRiverPersistance.GetEntityCompletenessDetailFromRuleId(ruleId);
    }
}
