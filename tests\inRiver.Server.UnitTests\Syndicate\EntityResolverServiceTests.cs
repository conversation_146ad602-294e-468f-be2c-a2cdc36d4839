namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using FakeItEasy;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Xunit;

    public class EntityResolverServiceTests
    {
        private readonly ISyndicationEntityService syndicationEntityService = A.Fake<ISyndicationEntityService>();

        private readonly IChannelEntityResolverService channelEntityResolverService = A.Fake<IChannelEntityResolverService>();

        private readonly IEntityResolverService entityResolverService;

        public EntityResolverServiceTests()
        {
            this.entityResolverService =
                new EntityResolverService(this.syndicationEntityService, this.channelEntityResolverService);
        }

        [Fact]
        public async Task GetEntityIds_WithEntityIds_ReturnsEntityIds()
        {
            // Arrange
            var syndicationModel = new SyndicationModel
            {
                WorkareaId = null,
                EntityIds = new List<int> { 1, 2, 3 }
            };

            // Act
            var result = await this.entityResolverService.GetEntityIdsAsync(syndicationModel, "Item");

            // Assert
            Assert.Equal(new List<int> { 1, 2, 3 }, result);
            A.CallTo(() => this.syndicationEntityService.GetWorkAreaEntityIds(A<string>._)).MustNotHaveHappened();
        }

        [Fact]
        public async Task GetEntityIds_WithValidWorkareaId_CallsGetWorkAreaEntityIds()
        {
            // Arrange
            A.CallTo(() => this.syndicationEntityService.GetWorkAreaEntityIds("validWorkarea"))
                .Returns(new List<int> { 101, 102, 103 });
            var syndicationModel = new SyndicationModel
            {
                WorkareaId = "validWorkarea",
                EntityIds = new List<int>()
            };

            // Act
            var result = await this.entityResolverService.GetEntityIdsAsync(syndicationModel, "Item");

            // Assert
            Assert.Equal(new List<int> { 101, 102, 103 }, result);
            A.CallTo(() => this.syndicationEntityService.GetWorkAreaEntityIds("validWorkarea")).MustHaveHappenedOnceExactly();
        }

        [Fact]
        public async Task GetEntityIds_WithNoWorkareaIdAndNoEntityIds_ThrowsSyndicateException()
        {
            // Arrange
            var syndicationModel = new SyndicationModel
            {
                WorkareaId = null,
                EntityIds = new List<int>()
            };

            // Act & Assert
            await Assert.ThrowsAsync<SyndicateException>(() => this.entityResolverService.GetEntityIdsAsync(syndicationModel, "Item"));
            A.CallTo(() => this.syndicationEntityService.GetWorkAreaEntityIds(A<string>._)).MustNotHaveHappened();
        }

        [Fact]
        public async Task GetEntityIds_WithInvalidWorkareaId_ReturnsEmptyList()
        {
            // Arrange
            A.CallTo(() => this.syndicationEntityService.GetWorkAreaEntityIds("invalidWorkarea")).Returns(new List<int>());
            var syndicationModel = new SyndicationModel
            {
                WorkareaId = "invalidWorkarea",
                EntityIds = new List<int>()
            };

            // Act
            var result = await this.entityResolverService.GetEntityIdsAsync(syndicationModel, "Item");

            // Assert
            Assert.Empty(result);
            A.CallTo(() => this.syndicationEntityService.GetWorkAreaEntityIds("invalidWorkarea")).MustHaveHappenedOnceExactly();
        }

        [Fact]
        public async Task GetEntityIds_ChannelIdProvided_ShouldCallChannelEntityResolverService()
        {
            // Arrange
            var syndicationModel = new SyndicationModel
            {
                ChannelId = 1,
                EntityIds = new List<int> { 1, 2, 3 }
            };
            const string mappingOutputEntityType = "Item";
            A.CallTo(() => this.channelEntityResolverService.GetEntityIdsFromChannelAsync(1, A<List<int>>._, mappingOutputEntityType))
                .Returns(new List<int> { 1, 2, 3 });

            // Act
            await this.entityResolverService.GetEntityIdsAsync(syndicationModel, mappingOutputEntityType);

            // Assert
            A.CallTo(() => this.channelEntityResolverService.GetEntityIdsFromChannelAsync(1, A<IList<int>>._, mappingOutputEntityType))
                .MustHaveHappenedOnceExactly();
        }

        [Fact]
        public async Task GetEntityIds_ChannelIsEmpty_ThrowsSyndicateException()
        {
            // Arrange
            var syndicationModel = new SyndicationModel
            {
                ChannelId = 1,
            };
            const string mappingOutputEntityType = "Item";
            A.CallTo(() => this.channelEntityResolverService.GetEntityIdsFromChannelAsync(1, Array.Empty<int>(), mappingOutputEntityType))
                .Returns(Array.Empty<int>());

            // Act & Assert
            await Assert.ThrowsAsync<SyndicateException>(() => this.entityResolverService.GetEntityIdsAsync(syndicationModel, mappingOutputEntityType));
        }

        [Fact]
        public async Task GetEntityIds_PreviousExportContainersAreProvided_ShouldReturnIdsFromPreviousExportContainers()
        {
            // Arrange
            var syndicationModel = new SyndicationModel
            {
                ChannelId = 1,
            };
            const string mappingOutputEntityType = "Item";
            var previousExportContainers = new List<ExportContainer>
            {
                new ExportContainer { Id = 5 },
                new ExportContainer { Id = 6 },
                new ExportContainer { Id = 7 }
            };

            // Act
            var result = await this.entityResolverService.GetEntityIdsAsync(syndicationModel, mappingOutputEntityType, previousExportContainers);

            // Assert
            Assert.Equal(new List<int> { 5, 6, 7 }, result);
        }
    }
}
