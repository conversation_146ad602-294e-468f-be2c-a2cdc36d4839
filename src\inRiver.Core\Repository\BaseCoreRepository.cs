namespace inRiver.Core.Repository
{
    using inRiver.Core.Models;
    using inRiver.Core.Persistance;
    using inRiver.Core.Persistance.Interfaces;

    public class BaseCoreRepository
    {
        internal IJobPersistance JobPersistance { private set; get; }

        public BaseCoreRepository(IinRiverPersistance persistance, ApiCaller apiCaller)
        {
            this.JobPersistance = (IJobPersistance)persistance;

            if (apiCaller != null)
            {
                this.ApiCaller = persistance.GetApiCaller();
            }
        }

        internal ApiCaller ApiCaller { get; }
    }
}
