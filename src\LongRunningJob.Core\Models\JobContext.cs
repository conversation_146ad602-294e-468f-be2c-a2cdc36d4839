namespace LongRunningJob.Core.Models
{
    using System;

    public class JobContext
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="JobContext"/> class by parsing the parts of the <paramref name="serviceFabricServiceName"/>.
        /// </summary>
        /// <param name="serviceFabricServiceName">A Service Fabric service name in the format fabric:/LRJWorkerService/LongRunningJob/{CustomerSafename}/{EnvironmentSafename}/{JobId}.</param>
        /// <exception cref="ArgumentException">Thrown when <paramref name="serviceFabricServiceName"/> has an incorrect format.</exception>
        public JobContext(Uri serviceFabricServiceName)
        {
            var serviceNameSegments = serviceFabricServiceName.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

            if (serviceNameSegments.Length != 5)
            {
                throw new ArgumentException($"{nameof(serviceFabricServiceName)} is malformed");
            }

            if (!int.TryParse(serviceNameSegments[4], out var jobId))
            {
                throw new ArgumentException("JobId must be an integer");
            }

            this.CustomerSafename = serviceNameSegments[2];
            this.EnvironmentSafename = serviceNameSegments[3];
            this.JobId = jobId;
        }

        public int JobId { get; }

        public string CustomerSafename { get; }

        public string EnvironmentSafename { get; }
    }
}
