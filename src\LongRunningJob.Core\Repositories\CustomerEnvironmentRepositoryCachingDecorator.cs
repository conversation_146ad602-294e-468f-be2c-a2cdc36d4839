namespace LongRunningJob.Core.Repositories
{
    using System.Threading.Tasks;
    using inRiver.Server.Request;
    using LongRunningJob.Core.Abstractions;
    using Microsoft.Extensions.Caching.Memory;

    public class CustomerEnvironmentRepositoryCachingDecorator : ICustomerEnvironmentRepository
    {
        private readonly ICustomerEnvironmentRepository customerEnvironmentRepository;
        private readonly IMemoryCache memoryCache;

        public CustomerEnvironmentRepositoryCachingDecorator(ICustomerEnvironmentRepository customerEnvironmentRepository, IMemoryCache memoryCache)
        {
            this.customerEnvironmentRepository = customerEnvironmentRepository;
            this.memoryCache = memoryCache;
        }

        public Task<EnvironmentContextData> GetAsync(string customerSafename, string environmentSafename)
        {
            var cacheKey = GetCacheKey(customerSafename, environmentSafename);
            return this.memoryCache.GetOrCreateAsync(cacheKey, (cacheEntry) => this.customerEnvironmentRepository.GetAsync(customerSafename, environmentSafename));
        }

        private static string GetCacheKey(string customerSafename, string environmentSafename) => $"CustomerEnvironment-{customerSafename}-{environmentSafename}";
    }
}
