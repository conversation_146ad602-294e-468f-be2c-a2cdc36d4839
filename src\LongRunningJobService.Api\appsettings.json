{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "StackConfigSecretName": "LocalStackConfig", "ConnectServiceUrl": "http://localhost:19081/Connect/ConnectGatewayService/", "CompressionServiceUrl": "http://localhost:19081/Resource/CompressionService/", "Augmenta": {"ApiBaseAddress": "http://localhost:5020/"}, "OAuth": {"Audience": "https://api.dev.inriver.io"}, "OutputAdapter": {"ApiBaseAddress": "https://localhost:7100/"}, "StackGroup": "dev", "Auth0DOmain": "https://inriverdev.eu.auth0.com"}