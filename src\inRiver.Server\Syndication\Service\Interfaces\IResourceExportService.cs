namespace inRiver.Server.Syndication.Service.Interfaces
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Script;

    public interface IResourceExportService
    {
        bool IsResourceExportAllowed { get; }

        void SetResourceExportAsConfigured();

        object[] GetScriptValues(TransformationManager transformationManager, InRiverEntity entity, object mainValue);

        ResourceExportModel GetResourceExportModel(string syndicationName, IList<ExportContainer> exportContainers);

        void ValidateResourceFiles(IList<Entity> allResourceEntities);

        Task HandleResourceExportAsync(ResourceExportModel resourceData, CancellationToken cancellationToken);
    }
}
