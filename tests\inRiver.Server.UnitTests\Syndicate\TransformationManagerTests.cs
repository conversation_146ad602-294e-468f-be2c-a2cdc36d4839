namespace inRiver.Server.UnitTests.Syndicate
{
    using System.Linq;
    using inRiver.Server.Syndication.Script;
    using Xunit;

    public class TransformationManagerTests
    {
        // {"transformations":[{"function":{"name":"concatenate","args":[" "],"values":["ItemDescription"]}}]}
        // {"transformations":[{"function":{"name":"toupper","args":[],"values":[]}}]}
        // {"transformations":[{"function":{"name":"constant","args":[],"values":["<PERSON><PERSON>"]}}]}
        [Fact]
        public void TestTransformConcatenate()
        {
            var inRiverItems = FakePersistance.CreateFakeEntities("Item").ToList();
            var entity = inRiverItems[0];

            var json = "{\"transformations\":[{\"function\":{ \"name\":\"concatenate\",\"args\":[\" \"],\"values\":[\"ItemDescription\"]}}]}";
            var manager = new TransformationManager(json, null);

            var list = manager.GetValues(entity, false, null, null);

            Assert.True(list.Count == 1, "TestConcatenate failed: wrong number of objects in list");
            Assert.True(list[0] as string == "Många fina färger", "TestConcatenate failed: wrong value");
        }

        [Fact]
        public void TestTransformConstant()
        {
            var inRiverItems = FakePersistance.CreateFakeEntities("Item").ToList();
            var entity = inRiverItems[0];

            var json = "{\"transformations\":[{\"function\":{ \"name\":\"constant\",\"args\":[],\"values\":[\"Mariah Carey\"]}}]}";
            var manager = new TransformationManager(json, null);

            var list = manager.GetValues(entity, false, null, null);

            Assert.True(list.Count == 1, "TestTransformConstant failed: wrong number of objects in list");
            Assert.True(list[0] as string == "Mariah Carey", "TestConcatenate failed: wrong value");
        }
    }
}
