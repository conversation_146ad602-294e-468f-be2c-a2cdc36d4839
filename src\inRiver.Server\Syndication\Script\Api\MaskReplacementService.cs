namespace inRiver.Server.Syndication.Script.Api
{
    using System.Globalization;
    using Newtonsoft.Json;

    public class MaskReplacementService : IMaskReplacementService
    {
        public const string EntitySystemIdMask = "{sys_id}";

        public (string RequestPath, object Body, bool MaskIsUsed) Replace(int inriverEntityId, string requestPath = null, object body = null)
        {
            var replacedUrlTuple = this.ReplaceSystemEntityId(requestPath, inriverEntityId);
            var replacedBodyTuple = this.ReplaceSystemEntityId(body, inriverEntityId);

            return (replacedUrlTuple.ReplacedString, replacedBodyTuple.ReplacedBody, replacedUrlTuple.MaskIsUsed || replacedBodyTuple.MaskIsUsed);
        }

        private (string ReplacedString, bool MaskIsUsed) ReplaceSystemEntityId(string sourceString, int currentEntitySystemId)
        {
            if (string.IsNullOrEmpty(sourceString))
            {
                return (null, false);
            }

            var maskIsUsed = sourceString.Contains(EntitySystemIdMask);
            var replacedString = sourceString.Replace(
                EntitySystemIdMask,
                currentEntitySystemId.ToString(CultureInfo.InvariantCulture),
                System.StringComparison.OrdinalIgnoreCase);

            return (replacedString, maskIsUsed);
        }

        private (object ReplacedBody, bool MaskIsUsed) ReplaceSystemEntityId(object body, int currentEntitySystemId)
        {
            if (body == null)
            {
                return (null, false);
            }

            var stringObject = JsonConvert.SerializeObject(body);
            var maskIsUsed = stringObject.Contains(EntitySystemIdMask);
            var replacedStringObject = stringObject
                .Replace(
                    $"\"{EntitySystemIdMask}\"",
                    currentEntitySystemId.ToString(CultureInfo.InvariantCulture),
                    System.StringComparison.OrdinalIgnoreCase)
                .Replace(
                    EntitySystemIdMask,
                    currentEntitySystemId.ToString(CultureInfo.InvariantCulture),
                    System.StringComparison.OrdinalIgnoreCase);

            return (JsonConvert.DeserializeObject<object>(replacedStringObject), maskIsUsed);
        }
    }
}
