namespace inRiver.Core.Http
{
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models;

    public interface IOutputAdapterHttpClient
    {
        Task<MappingDto> GetMapping(string environmentGid, int formatMappingId, CancellationToken cancellationToken);

        Task<DsaMappingDto> GetDsaMapping(string environmentGid, int dsaMappingId, CancellationToken cancellationToken);

        Task PutProducts(int longRunningJobId, string environmentSafeName, string customerSafeName, string environmentGid, int formatMappingId, string exportResultJson, CancellationToken cancellationToken);

        Task PutDsaProductsAsync(int longRunningJobId, string environmentSafeName, string customerSafeName, string username,
            int formatMappingId, int dsaMappingid, string exportResultJson, CancellationToken cancellationToken);
    }
}
