namespace inRiver.Server.Syndication.Service
{
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using Telemetry.Constants;
    using Core.Enum;
    using inRiver.Server.Syndication.Mapping;
    using Interfaces;

    public class SyndicationJobResultService : ISyndicationJobResultService
    {
        private const string NoValue = "-";

        private string status;

        private ActorMethodState actorMethodState;

        private int numberOfEntities;

        private int numberOfDefaultFunctions;

        private int numberOfCustomFunctions;

        public IDictionary<string, string> GetResultMetrics(Stopwatch stopWatch)
        {
            var executionTimeInMs = stopWatch.ElapsedMilliseconds;
            var metrics = new Dictionary<string, string> {
                { Dimension.Status, this.status },
                { Dimension.ExecutionTimeInMs, executionTimeInMs.ToString() },
            };

            var hasEntities = this.numberOfEntities != 0;
            var numberOfEntitiesValue = hasEntities ? this.numberOfEntities.ToString() : NoValue;
            _ = metrics.TryAdd(Dimension.NumberOfEntities, numberOfEntitiesValue);

            var executionTimeInMsPerEntityValue = hasEntities ? (executionTimeInMs / this.numberOfEntities).ToString() : NoValue;
            _ = metrics.TryAdd(Dimension.ExecutionTimeInMsPerEntity, executionTimeInMsPerEntityValue);

            _ = metrics.TryAdd(Dimension.NumberOfDefaultFunctions, this.numberOfDefaultFunctions != 0 ? this.numberOfDefaultFunctions.ToString() : NoValue);
            _ = metrics.TryAdd(Dimension.NumberOfCustomFunctions, this.numberOfCustomFunctions != 0 ? this.numberOfCustomFunctions.ToString() : NoValue);

            return metrics;
        }

        public void UpdateNumberOfFunctions(int batchNumberOfEntities, MapContainer mapContainer) {
            var mapFieldsWithFunctions = mapContainer.MapFields
                ?.Where(x => !string.IsNullOrEmpty(x.Script))
                .ToList() ?? new List<MapField>();
            var mapResourceFieldsWithFunctions = mapContainer.MapResourceFields
                ?.Where(x => !string.IsNullOrEmpty(x.Script))
                .ToList() ?? new List<MapResourceField>();

            this.numberOfDefaultFunctions += mapFieldsWithFunctions.Count(x => !x.IsCustom) * batchNumberOfEntities;
            this.numberOfCustomFunctions += (mapResourceFieldsWithFunctions.Count + mapFieldsWithFunctions.Count(x => x.IsCustom)) * batchNumberOfEntities;
        }

        public void SetNumberOfEntities(int newNumberOfEntities) => this.numberOfEntities = newNumberOfEntities;

        public void SetActorMethodStateAndJobStatus(ActorMethodState state, string newStatus)
        {
            this.actorMethodState = state;
            this.status = newStatus;
        }

        public ActorMethodState GetActorMethodState() => this.actorMethodState;

        public string GetStatusMetric() => this.status;

        public int GetNumberOfEntitiesMetric() => this.numberOfEntities;
    }
}
