namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Collections.Generic;
    using System.Data.SqlClient;
    using System.Linq;
    using inRiver.Log;

    public abstract class BasePersistance
    {
        public const string EntityTablePrefix = "Entity_iPMC_";

        public static int SqlBatchSize => 4000;

        protected string ConnectionString;
        protected ICommonLogging LogInstance;

        protected readonly IContentSegmentPermissionProvider contentSegmentProvider;

        protected BasePersistance(
            string connectionString, 
            ICommonLogging logInstance,
            IContentSegmentPermissionProvider contentSegmentProvider
        )
        {
            ConnectionString = connectionString;
            LogInstance = logInstance;
            
            this.contentSegmentProvider = contentSegmentProvider;
        }

        protected static string CreateTableName(string entityType)
        {
            return $"{EntityTablePrefix}{entityType}";
        }

        protected SqlParameter CreateParameterFromField(FieldType field, string parameterName, object value)
        {
            var sqlParameter = new SqlParameter() {ParameterName = parameterName, Value =value};

            try
            {
                sqlParameter.DbType = DataTypeHelper.GetDbType(field.DataType);
                return sqlParameter;
            }
            catch (Exception ex)
            {
                LogInstance.Error($"An unexpected error occured when getting SqlParameter for field {field.Id}", ex,
                    string.Empty, string.Empty);

                return null;
            }
        }

        public List<List<T>> SplitListIntoBatches<T>(List<T> collection)
        {
            var chunks = new List<List<T>>();
            var chunkCount = collection.Count() / SqlBatchSize;

            if (collection.Count % SqlBatchSize > 0)
            {
                chunkCount++;
            }

            for (var i = 0; i < chunkCount; i++)
            {
                chunks.Add(collection.Skip(i * SqlBatchSize).Take(SqlBatchSize).ToList());
            }

            return chunks;
        }

        protected string permittedSegmentIdsClause()
        {
            // permitted segmentIds
            List<int> segmentIds = contentSegmentProvider?.GetPermittedSegmentIds();

            string segmentIdsInClause = "";

            if (segmentIds != null && segmentIds.Any())
            {
                string segmentIdsStr = string.Join(",", segmentIds);
                segmentIdsInClause = $"ContentSegmentationId IN ({segmentIdsStr}) AND";
            }

            return segmentIdsInClause;
        }
    }
}
