namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Api.Data.Client;
    using inRiver.Core.Util;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;

    /// <summary>
    /// Contains Entity related operations.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public override DtoEntity AddEntity(Entity entity)
        {
            var dtoEntity = InRiverDataApiClient.AddEntity(this.GetAuthInfo(), EntityToDtoEntity(entity));
            MapEntityProperties(entity, dtoEntity);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return dtoEntity;
        }

        public override List<DtoEntity> AddEntities(List<Entity> entities)
        {
            var dtoEntities = entities.ConvertAll(new Converter<Entity, DtoEntity>(EntityToDtoEntity));
            var addedEntities = InRiverDataApiClient.AddEntities(this.GetAuthInfo(), dtoEntities);

            if (addedEntities.Count != entities.Count)
            {
                throw new IOException($"Unexpected api response in {nameof(AddEntities)}");
            }

            // Note: This bandaid fix is required because the client of AddEntities
            //       uses the input and expects it to be updated to contain information
            //       stored in the output.
            for (var i = 0; i < addedEntities.Count; i++)
            {
                MapEntityProperties(entities[i], addedEntities[i]);
            }
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return addedEntities;
        }

        public DtoEntity UpdateEntity(Entity entity)
        {
            var segmentIds = this._contentSegmentProvider.GetPermittedSegmentIds();
            var r = InRiverDataApiClient.UpdateEntity(this.GetAuthInfo(), EntityToDtoEntity(entity), segmentIds);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return r;
        }

        public override void ReCalculateEntityMainPicture(int entityId, string entityTypeId)
        {
            InRiverDataApiClient.ReCalculateEntityMainPicture(this.GetAuthInfo(), entityId, entityTypeId);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }

        public override void ReCalculateMainPictureForAllEntities()
        {
            InRiverDataApiClient.ReCalculateMainPictureForAllEntities(this.GetAuthInfo());
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }

        public override void ReCalculateDisplayValuesForAllEntities()
        {
            InRiverDataApiClient.ReCalculateDisplayValuesForAllEntities(this.GetAuthInfo());
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }

        public override void RecalculateMainpicturesForDeletedResource(object deletedResourceFileId)
        {
            if (!(deletedResourceFileId is int deletedId))
            {
                return;
            }

            InRiverDataApiClient.ReCalculateMainPicturesForDeletedResource(this.GetAuthInfo(), deletedId);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }

        public override bool DeleteEntity(int entityId)
        {
            InRiverDataApiClient.DeleteEntity(this.GetAuthInfo(), entityId);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return true;
        }

        public bool DeleteEntityFull(int entityId)
        {
            InRiverDataApiClient.DeleteEntityFull(this.GetAuthInfo(), entityId);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return true;
        }

        public override int? GetEntityIdByUniqueValue(string fieldTypeId, string value)
            => InRiverDataApiClient.GetEntityIdByUniqueValue(this.GetAuthInfo(), fieldTypeId, value);

        public override bool FieldValueAlreadyExistsForFieldType(string fieldTypeId, object value)
        {
            var entityId = this.GetEntityIdByUniqueValue(fieldTypeId, value.ToString());
            return entityId.HasValue;
        }

        public override DtoEntity GetEntityWithData(int id)
        {
            var result = GetEntitiesWithData(new List<int> { id }, CancellationToken.None).FirstOrDefault();
            if (result == null)
            {
                throw new iPMC.Persistance.PersistanceException($"GetEntityWithData(id: {id}) - entity not found");
            }

            return result;
        }

        public override async Task<List<DtoEntity>> GetEntitiesWithDataAsync(List<int> entityIds, CancellationToken cancellationToken)
            => this.GetEntitiesWithData(entityIds, cancellationToken);

        public override List<DtoEntity> GetEntitiesWithData(List<int> entityIds, CancellationToken cancellationToken)
        {
            var segmentIds = this._contentSegmentProvider.GetPermittedSegmentIds();
            var batchSize = 1000;
            if (entityIds.Count < batchSize)
            {
                return InRiverDataApiClient.GetEntitiesWithData(this.GetAuthInfo(), entityIds, segmentIds);
            }

            var chunks = new List<List<int>>();
            var chunkCount = entityIds.Count / batchSize;

            if (entityIds.Count % batchSize > 0)
            {
                chunkCount++;
            }

            for (var i = 0; i < chunkCount; i++)
            {
                chunks.Add(entityIds.Skip(i * batchSize).Take(batchSize).ToList());
            }

            var entityBag = new ConcurrentBag<DtoEntity>();
            var result = Parallel.ForEach(
                chunks,
                new ParallelOptions { MaxDegreeOfParallelism = 5 },
                chunk => {
                    var entityList = InRiverDataApiClient.GetEntitiesWithData(this.GetAuthInfo(), chunk, segmentIds);
                    entityList.ForEach(e => entityBag.Add(e));
                });

            var sortedResult = entityIds.Join(
              entityBag,
              entityId => entityId,
              bagEntity => bagEntity.Id,
              (entityId, bagEntity) => bagEntity);
            return sortedResult.ToList();
        }

        public override void DeleteStrayLinkEntites()
        {
            var segmentIds = this._contentSegmentProvider.GetPermittedSegmentIds();
            InRiverDataApiClient.DeleteStrayLinkEntites(this.GetAuthInfo(), segmentIds);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }

        public override DtoEntity CreateNewVersion(int entityId)
            => InRiverDataApiClient.CreateNewVersion(this.GetAuthInfo(), entityId);

        public DtoEntity SetEntityFieldSetFull(int entity, string fieldSetId)
        {
            var result = InRiverDataApiClient.UpdateEntityFieldSet(this.GetAuthInfo(), entity, fieldSetId);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return result;
        }

        public DtoEntity RemoveEntityFieldSetFull(int entity)
        {
            var result = InRiverDataApiClient.RemoveEntityFieldSet(this.GetAuthInfo(), entity);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return result;
        }

        protected static void MapEntityProperties(Entity entity, DtoEntity newlyInserted)
        {
            entity.Id = newlyInserted.Id;
            entity.DateCreated = DateTime.TryParse(newlyInserted.DateCreated, out var dateCreated) ? dateCreated : default;
            entity.LastModified = DateTime.TryParse(newlyInserted.LastModified, out var lastModified) ? lastModified : default;
            entity.Version = newlyInserted.Version;
            entity.ChangeSet = newlyInserted.ChangeSet;
            entity.CreatedBy = newlyInserted.CreatedBy;
            entity.ModifiedBy = newlyInserted.ModifiedBy;

            if (entity.Fields != null)
            {
                foreach (var entityField in entity.Fields)
                {
                    entityField.Revision = 1;  // needed for FieldRevisionHistory generation (????)
                }
            }
        }

        private static DtoEntity EntityToDtoEntity(Entity e)
        {
            return new DtoEntity
            {
                Id = e.Id,
                ChangeSet = e.ChangeSet,
                Completeness = e.Completeness,
                CreatedBy = e.CreatedBy,
                DateCreated = e.DateCreated.ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture),
                DisplayDescription = FieldToDtoField(e.DisplayDescription),
                DisplayName = FieldToDtoField(e.DisplayName),
                EntityTypeId = e.EntityType?.Id,
                Fields = e.Fields.ConvertAll(new Converter<Field, DtoField>(FieldToDtoField)),
                FieldSetId = e.FieldSetId,
                LastModified = e.LastModified.ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture),
                Links = e.Links.ConvertAll(new Converter<Link, DtoLink>(LinkToDtoLink)),
                LoadLevel = e.LoadLevel,
                Locked = e.Locked,
                MainPictureId = e.MainPictureId,
                MainPictureUrl = e.MainPictureUrl,
                ModifiedBy = e.ModifiedBy,
                Segment = SegmentToDtoSegment(e.Segment),
                Version = e.Version
            };
        }

        private static DtoLink LinkToDtoLink(Link link)
        {
            return link == null ?
                null :
                new DtoLink
                {
                    Id = link.Id,
                    Source = new DtoEntity { Id = link.Source.Id },
                    Target = new DtoEntity { Id = link.Target.Id },
                    LinkTypeId = link.LinkType.Id,
                    Index = link.Index,
                    Inactive = link.Inactive
                };
        }

        private static DtoSegment SegmentToDtoSegment(Segment segment)
        {
            return segment == null ?
                null :
                new DtoSegment
                {
                    Id = segment.Id,
                    Name = segment.Name,
                    Description = segment.Description
                };
        }
    }
}
