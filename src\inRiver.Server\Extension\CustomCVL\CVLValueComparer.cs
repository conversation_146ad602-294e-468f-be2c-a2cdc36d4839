namespace inRiver.Server.Extension.CustomCVL
{
    using System.Collections.Generic;
    using inRiver.Remoting.Objects;

    public class CVLValueComparer : IComparer<CVLValue>
    {
        public int Compare(CVLValue x, CVLValue y)
        {
            string value1 = x.Value as string;
            string value2 = y.Value as string;

            return string.Compare(value1, value2, System.StringComparison.Ordinal);
        }
    }
}
