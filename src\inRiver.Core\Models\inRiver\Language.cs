﻿namespace inRiver.Core.Models.inRiver
{
    using System.Globalization;

    public class Language
    {
        public Language()
        {
            this.Name = "en";
        }

        public Language(CultureInfo cultureInfo)
        {
            this.Name = cultureInfo.Name;
        }

        public string Name { get; set; }

        public string DisplayName
        {
            get
            {
                if (string.IsNullOrEmpty(this.Name))
                {
                    return string.Empty;
                }

                return new CultureInfo(this.Name).DisplayName;
            }
        }
    }
}
