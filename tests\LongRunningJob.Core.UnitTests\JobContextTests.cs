namespace LongRunningJob.Core.UnitTests
{
    using System;
    using FluentAssertions;
    using LongRunningJob.Core.Models;
    using Xunit;

    public class JobContextTests
    {
        [Fact]
        public void Constructor_CorrectlyFormedUri_ShouldCreateJobContext()
        {
            const string customerName = "customerName";
            const string environmentName = "environmentName";
            const int jobId = 123;
            var uri = new Uri($"fabric:/LongRunningJob/LRJWorkerService/{customerName}/{environmentName}/{jobId}");

            var jobContext = new JobContext(uri);

            jobContext.CustomerSafename.Should().Be(customerName);
            jobContext.EnvironmentSafename.Should().Be(environmentName);
            jobContext.JobId.Should().Be(jobId);
        }

        [Theory]
        [InlineData("fabric:/LongRunningJob/LRJWorkerService/customerName/environmentName/123/anotherSegment")]
        [InlineData("fabric:/LongRunningJob/LRJWorkerService/tooFewSegments")]
        public void Constructor_IncorrectNumberOfUriSegments_ShouldThrowException(string uriString)
        {
            var uri = new Uri(uriString);

            Action act = () => new JobContext(uri);

            act.Should().Throw<ArgumentException>().WithMessage("serviceFabricServiceName is malformed");
        }

        [Fact]
        public void Constructor_JobIdSegmentIsNotAnInteger_ShouldThrowException()
        {
            var uri = new Uri("fabric:/LongRunningJob/LRJWorkerService/customerName/environmentName/notAnInteger");

            Action act = () => new JobContext(uri);

            act.Should().Throw<ArgumentException>().WithMessage("JobId must be an integer");
        }
    }
}
