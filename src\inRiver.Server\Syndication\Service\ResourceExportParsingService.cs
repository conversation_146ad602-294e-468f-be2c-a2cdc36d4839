namespace inRiver.Server.Syndication.Service
{
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Newtonsoft.Json;

    public class ResourceExportParsingService : IResourceExportParsingService
    {
        public IList<ResourceExportResult> ParseResourcesData(IList<object> resourcesData)
        {
            var resourceExportResults = new List<ResourceExportResult>();
            foreach (var data in resourcesData)
            {
                var relatedEntityDeserializedResources = this.DeserializeObject<IList<IList<ResourceExportResult>>>(JsonConvert.SerializeObject(data));
                if (relatedEntityDeserializedResources != null)
                {
                    resourceExportResults.AddRange(relatedEntityDeserializedResources.SelectMany(x => x).ToList());
                    continue;
                }

                var deserializedResources = this.DeserializeObject<IList<ResourceExportResult>>(JsonConvert.SerializeObject(data));
                if (deserializedResources != null)
                {
                    resourceExportResults.AddRange(deserializedResources);
                    continue;
                }

                var singleDeserializedResource = this.DeserializeObject<ResourceExportResult>(JsonConvert.SerializeObject(data));
                if (singleDeserializedResource != null)
                {
                    resourceExportResults.Add(singleDeserializedResource);
                }
            }

            return this.FilterValidResourceModels(resourceExportResults);
        }

        private IList<ResourceExportResult> FilterValidResourceModels(IList<ResourceExportResult> resourcesData)
            => resourcesData.Where(data => !string.IsNullOrEmpty(data?.Url) && !string.IsNullOrEmpty(data.FileName)).ToList();

        private T DeserializeObject<T>(object data)
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(data.ToString());
            }
            catch
            {
                return default;
            }
        }
    }
}
