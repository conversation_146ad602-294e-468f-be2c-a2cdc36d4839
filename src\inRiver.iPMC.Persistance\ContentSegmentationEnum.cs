namespace inRiver.iPMC.Persistance
{
    using System;
    using System.ComponentModel;
    using System.Linq;

    public enum ContentSegmentationEnum
    {
        /// <summary>
        /// Enum used to fill UI with Descriptions *Keep the elements order* 
        /// </summary>
        None,
        [Description("sys_segmentID")]
        ID = 1,
        [Description("sys_segmentName")]
        SegmentName = 2,
        [Description("Segmentation Id & Segmentation Name")]
        SegmentationIdAndSegmentationName = 3,
        [Description("Segmentation Description")]
        SegmentDescription = 4
    }

    public static class EnumUtil
    {
        public static string GetDescription(this Enum c)
        {
            var aa = ((DescriptionAttribute[])((ContentSegmentationEnum)c).GetType().GetField(((ContentSegmentationEnum)c).ToString())
                .GetCustomAttributes(
                    typeof(DescriptionAttribute), false)).FirstOrDefault().Description;
            return aa;
        }
    }
}
