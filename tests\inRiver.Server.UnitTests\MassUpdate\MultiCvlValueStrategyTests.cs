namespace inRiver.Server.UnitTests.MassUpdate
{
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Server.FieldChange;
    using inRiver.Server.Repository;
    using Xunit;

    public class MultiCvlValueStrategyTests
    {
        private const int EntityId = 1;

        private const string FieldTypeId = "fieldTypeId";

        [Fact]
        public void Calculate_AppendStrategy_ReturnsNewValuesAppendedToOldValues()
        {
            // Arrange
            const string oldMultiValues = "value1;value2;value3";
            const string newMultiValues = "value4";
            var fieldRepository = A.Fake<IFieldRepository>();
            A.<PERSON>To(() => fieldRepository.GetFieldValue(EntityId, FieldTypeId))
                .Returns(oldMultiValues);
            var strategy = new AppendMultiCvlValueStrategy(fieldRepository);

            // Act
            var calculatedValue = strategy.Calculate(newMultiValues, EntityId, FieldTypeId);

            // Assert
            calculatedValue.Should().Be("value1;value2;value3;value4");
        }

        [Fact]
        public void Calculate_RemoveStrategy_ReturnsOldValuesExcludingNewValues()
        {
            // Arrange
            const string oldMultiValues = "value1;value2;value3";
            const string newMultiValues = "value2";
            var fieldRepository = A.Fake<IFieldRepository>();
            A.CallTo(() => fieldRepository.GetFieldValue(EntityId, FieldTypeId))
                .Returns(oldMultiValues);
            var strategy = new RemoveMultiCvlValueStrategy(fieldRepository);

            // Act
            var calculatedValue = strategy.Calculate(newMultiValues, EntityId, FieldTypeId);

            // Assert
            calculatedValue.Should().Be("value1;value3");
        }

        [Fact]
        public void Calculate_ReplaceStrategy_ReturnsOnlyNewValues()
        {
            // Arrange
            const string newMultiValues = "value1";
            var strategy = new ReplaceMultiCvlValueStrategy();

            // Act
            var calculatedValue = strategy.Calculate(newMultiValues, EntityId, FieldTypeId);

            // Assert
            calculatedValue.Should().Be("value1");
        }
    }
}
