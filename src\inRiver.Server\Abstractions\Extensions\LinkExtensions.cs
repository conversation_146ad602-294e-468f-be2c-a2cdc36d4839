﻿namespace inRiver.Server.Abstractions.Extensions
{
    using inRiver.Server.Abstractions.Models;
    using inRiver.Remoting.Objects;

    public static class LinkExtensions
    {
        public static LinkModel ToLinkModel(this Link link)
            => link == null
                ? null
                : new LinkModel
                {
                    Id = link.Id,
                    Index = link.Index,
                    Inactive = link.Inactive,
                    LinkTypeId = link.LinkType.Id,
                    SourceEntityId = link.Source.Id,
                    TargetEntityId = link.Target.Id,
                    LinkEntityId = link.LinkEntity?.Id,
                };
    }
}
