namespace inRiver.Server.UnitTests.Syndicate
{
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Service;
    using Xunit;

    public class ExportSerializationSettingsServiceTests
    {
        [Fact]
        public void ConfigureSettings_DsaMappingIdPresentAndRunDsaSyndicationTrueAndApplyDsaMappingFalse_ShouldSetSerializeMapFieldTypeTrue()
        {
            // Arrange
            var model = new SyndicationModel
            {
                DsaMappingId = 1,
                RunDsaSyndication = true
            };
            var applyDsaMapping = false;

            // Act
            ExportSerializationSettingsService.ConfigureSettings(model, applyDsaMapping);

            // Assert
            Assert.True(ExportSerializationSettings.SerializeMapFieldType);
        }

        [Fact]
        public void ConfigureSettings_DsaMappingIdNull_ShouldSetSerializeMapFieldTypeFalse()
        {
            // Arrange
            var model = new SyndicationModel
            {
                DsaMappingId = null,
                RunDsaSyndication = true
            };
            var applyDsaMapping = false;

            // Act
            ExportSerializationSettingsService.ConfigureSettings(model, applyDsaMapping);

            // Assert
            Assert.False(ExportSerializationSettings.SerializeMapFieldType);
        }

        [Fact]
        public void ConfigureSettings_RunDsaSyndicationFalse_ShouldSetSerializeMapFieldTypeFalse()
        {
            // Arrange
            var model = new SyndicationModel
            {
                DsaMappingId = 1,
                RunDsaSyndication = false
            };
            var applyDsaMapping = false;

            // Act
            ExportSerializationSettingsService.ConfigureSettings(model, applyDsaMapping);

            // Assert
            Assert.False(ExportSerializationSettings.SerializeMapFieldType);
        }

        [Fact]
        public void ConfigureSettings_ApplyDsaMappingTrue_ShouldSetSerializeMapFieldTypeFalse()
        {
            // Arrange
            var model = new SyndicationModel
            {
                DsaMappingId = 1,
                RunDsaSyndication = true
            };
            var applyDsaMapping = true;

            // Act
            ExportSerializationSettingsService.ConfigureSettings(model, applyDsaMapping);

            // Assert
            Assert.False(ExportSerializationSettings.SerializeMapFieldType);
        }
    }
}
