namespace inRiver.Server.Helpers
{
    using System;

    public class FileHelper : IFileHelper
    {
        public void ThrowIfPathTraversalIsDetected(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                throw new ArgumentNullException(nameof(fileName));
            }

            if (fileName.Contains("../") || fileName.Contains("..\\"))
            {
                throw new ArgumentException("Invalid filename");
            }
        }
    }
}
