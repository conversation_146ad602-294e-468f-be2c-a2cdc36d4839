namespace LongRunningJobService.Code
{
    using System;
    using global::LongRunningJobService.Constants;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Models;
    using Microsoft.AspNetCore.Http;

    public class HttpContextEnvironmentContextAccessor : IEnvironmentContextAccessor
    {
        private readonly IHttpContextAccessor httpContextAccessor;

        public HttpContextEnvironmentContextAccessor(IHttpContextAccessor httpContextAccessor)
        {
            this.httpContextAccessor = httpContextAccessor;
        }

        public EnvironmentContext EnvironmentContext
        {
            get => this.TryGetEnvironmentContext(out var environmentContext)
                ? environmentContext
                : throw new InvalidOperationException("The HttpContext is missing or the EnvironmentContext has not been initialized and stored");
            set =>
                this.httpContextAccessor.HttpContext.Items[HttpContextItemKeys.EnvironmentContext] = this.httpContextAccessor.HttpContext != null
                    ? value
                    : throw new InvalidOperationException("The HttpContext is missing.");
        }

        public bool TryGetEnvironmentContext(out EnvironmentContext environmentContext)
        {
            environmentContext = this.httpContextAccessor.HttpContext?.Items[HttpContextItemKeys.EnvironmentContext] as EnvironmentContext;

            return environmentContext != null;
        }
    }
}
