namespace inRiver.Server.Syndication.Service
{
    using inRiver.Server.Syndication.Enums;

    public class MappingSourceResolverService
    {
        public static SyndicationMappingSource GetMappingSource(SyndicationModel syndicationModel, bool applyDsaMapping)
        {
            return applyDsaMapping
                ? SyndicationMappingSource.OutputAdapterDsa
                : syndicationModel.MappingSource;
        }
    }
}
