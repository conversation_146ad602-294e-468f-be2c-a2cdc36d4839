namespace inRiver.Server.Extension
{
    using System;
    using Api.Data.Client.Model;
    using inRiver.Server.Syndication.Enums;
    using Syndication;

    public static class SyndicationIdentifierTypeExtensions
    {
        public static string GetSyndicationIdentifierTypeString(this SyndicationIdentifierType syndicationModelIdentifierType)
        {
            return syndicationModelIdentifierType switch
            {
                SyndicationIdentifierType.FileFormatId => "FileFormatId",
                SyndicationIdentifierType.DynamicFormatId => "DynamicFormatId",
                SyndicationIdentifierType.None => null,
                _ => throw new System.NotSupportedException($"Invalid Value {syndicationModelIdentifierType} for SyndicationIdentifierType")
            };
        }

        public static int GetSyndicationIdentifierByIdentifierType(this SyndicationIdentifierType syndicationModelIdentifierType, SyndicationModel syndicationModel)
        {
            return syndicationModelIdentifierType switch
            {
                SyndicationIdentifierType.FileFormatId => syndicationModel.FileFormatId ?? throw new InvalidOperationException("FileFormatId should be set when IdentifierType is FileFormatId"),
                SyndicationIdentifierType.DynamicFormatId => syndicationModel.DynamicFormatId ?? throw new InvalidOperationException("DynamicFormatId should be set when IdentifierType is DynamicFormatId"),
                SyndicationIdentifierType.None => syndicationModel.Id,
                _ => throw new System.NotSupportedException($"Invalid Value {syndicationModelIdentifierType} for SyndicationIdentifierType")
            };
        }
    }
}
