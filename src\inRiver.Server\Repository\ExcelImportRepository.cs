namespace inRiver.Server.Repository
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Data;
    using System.Diagnostics;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Text;
    using System.Text.RegularExpressions;
    using System.Threading;
    using inriver.Expressions.Client.Constants;
    using inRiver.Core.Constants;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Extension;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Util;
    using inRiver.Server.DataAccess;
    using inRiver.Server.DataAccess.ExpressionUtil;
    using inRiver.Server.DataAccess.ThirdDataLayer;
    using inRiver.Server.EventPublishing;
    using inRiver.Server.Extension;
    using inRiver.Server.Helpers;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;
    using inRiver.Server.Util.IllegalCharacters;
    using inRiver.Validation;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using Telemetry.Metrics;

    public class ExcelImportRepository
    {
        private readonly IDataPersistance dataContext;
        private readonly RequestContext context;
        private ModelRepository modelRepository;
        private DataRepository dataRepository;
        private const string SysIdColumnName = "sys_id";
        private const string SysSegmentIdColumnName = "sys_segmentID";
        private const string EmptyEntityId = "0";
        private const int EvaluatedSegmentId = -1;
        private const int UnsetSegmentId = -2;
        private const string SemicolonSeparator = ";";

        public ExcelImportRepository(RequestContext context)
        {
            this.dataContext = context.DataPersistance;
            this.context = context;
            this.modelRepository = new ModelRepository(context);
            this.dataRepository = new DataRepository(context);
        }

        #region Create Long Running Job

        #endregion

        #region DataImportExcel Data Table Methods

        public string GetExcelModel(string username, string batchId, bool configData)
        {
            return this.dataContext.GetExcelModel(username, batchId, configData);
        }

        public Stream GetImportDataStream(string batchId)
        {
            CloudBlobManager cloudBlobManager = new CloudBlobManager(this.context, "excelimport");
            return cloudBlobManager.GetFileStream(batchId);

        }

        #endregion

        #region Data Staging Table Methods

        public void CreateStagingDataTable(string userName, string batchId, string entityTypeId, inRiver.Core.Models.inRiver.ExcelImport.FileImportConfigurationModel configData)
        {
            using (var importDataStream = GetImportDataStream(batchId))
            {
                importDataStream.Seek(0, SeekOrigin.Begin);
                using (var stagingTable = new DataTable())
                {
                    //We use sqlbulkcopy for loading tables so the order of the columns needs to match
                    // the table layout in sqlserver
                    stagingTable.Columns.Add(new DataColumn("BatchId") { DefaultValue = batchId, AllowDBNull = false });
                    stagingTable.Columns.Add(new DataColumn("inRiverImportColumnId"));
                    stagingTable.PrimaryKey = new[] { stagingTable.Columns["inRiverImportColumnId"] };
                    stagingTable.Columns.Add(new DataColumn("sys_id", typeof(Int32)));
                    stagingTable.Columns.Add(new DataColumn("ImportType"));
                    stagingTable.Columns.Add(new DataColumn("EntityType") { DefaultValue = entityTypeId });
                    stagingTable.Columns.Add(new DataColumn("EntityAsJson"));
                    stagingTable.Columns.Add(new DataColumn("ImportState"));
                    stagingTable.Columns.Add(new DataColumn("ImportStatus"));
                    stagingTable.Columns.Add(new DataColumn("ImportMessage"));
                    stagingTable.Columns.Add(new DataColumn("PostStatus"));
                    stagingTable.Columns.Add(new DataColumn("PostMessage"));

                    using (JsonTextReader reader = new JsonTextReader(new StreamReader(importDataStream)))
                    {
                        var serializer = new JsonSerializer();
                        while (reader.Read())
                        {
                            if (reader.TokenType == JsonToken.StartObject)
                            {
                                // Deserialize each object from the stream individually and process it
                                var jsonObject = serializer.Deserialize<JToken>(reader);

                                var row = stagingTable.NewRow();
                                row["inRiverImportColumnId"] = jsonObject["inRiverImportColumnId"];

                                var columnValuePairs = ExcelImportHelper.CreateColumnValuePairs(jsonObject.ToString(), configData);

                                if (columnValuePairs.ContainsKey("sys_entitytype") &&
                                    !string.IsNullOrWhiteSpace(columnValuePairs["sys_entitytype"]) &&
                                    columnValuePairs["sys_entitytype"] != entityTypeId)
                                {
                                    continue;
                                }

                                var jsonString = JsonConvert.SerializeObject(columnValuePairs);
                                row["EntityAsJson"] = jsonString.ToString();

                                if ((jsonObject["sys_id"] != null) && (jsonObject["sys_id"].HasValues))
                                {
                                    row["sys_id"] = jsonObject["sys_id"];
                                }

                                this.dataContext.InsertRowIntoStagingTable(row);
                                row = null;
                            }
                        }
                    }
                }
            }

        }

        public void SetPostProcessingStatus(string inRiverImportId, string status, string message)
        {
            this.dataContext.SetPostProcessStatus(inRiverImportId, status, message);
        }

        #endregion

        #region ImportValidation Methods

        public bool GetTableWithNextRowToImport(out DataTable table, string batchId)
        {
            table = this.dataContext.GetTableWithRowToProcessFromStagingTable(batchId);

            if (table.Rows.Count == 0)
            {
                return false;
            }

            var row = table.Rows[0];
            row["ImportStatus"] = ImportState.Processing.ToString();
            this.dataContext.SetStagingRow(row);
            return true;

        }

        public bool ProcessImport(ref BlockingCollection<ImportedEntity> queue, string userName, string batchId, CancellationToken cancellationToken)
        {
            bool anyErrors = false;
            string origUser = context.Username;
            var user = dataContext.GetUserByUsername(userName);
            context.Roles = user.Roles.Select(role => role.Name).ToList();
            context.Permissions = user.Permissions.Select(permission => permission.Name).ToList();

            try
            {
                this.context.Username = userName;
                this.context.Log(LogLevel.Information, $"Excel import batch - {batchId}  - Initializing data");

                if (cancellationToken.IsCancellationRequested)
                {
                    return false;
                }

                // First Get the configuration Model and convert from string to Model
                string configurationJsonData = GetExcelModel(userName, batchId, true);
                var configurationData =
                    JsonConvert.DeserializeObject<inRiver.Core.Models.inRiver.ExcelImport.FileImportConfigurationModel>(
                        configurationJsonData);

                List<EntityType> entityTypes = this.modelRepository.GetAllEntityTypes();
                EntityType entityType = entityTypes.Where(field => field.Id == configurationData.EntityTypeId).ToList()
                    .FirstOrDefault();

                var fieldTypes = entityType.FieldTypes.ToDictionary(fieldType => fieldType.Id);
                var entitiesImported = new HashSet<int>();

                if (cancellationToken.IsCancellationRequested)
                {
                    return false;
                }

                // Create the staging table and save it
                CreateStagingDataTable(userName, batchId, configurationData.EntityTypeId, configurationData);

                var categories = this.modelRepository
                    .GetCategoriesForEntityType(configurationData.EntityTypeId)
                    .ToDictionary(category => category.Id);

                var uniqueFieldTypes = entityType.FieldTypes.Where(field => field.Unique).ToList();
                var fieldsWithDefaultValues = entityType.FieldTypes
                    .Where(field => !string.IsNullOrEmpty(field.DefaultValue)).ToList();
                var mandatoryFieldTypes = entityType.FieldTypes.Where(field => field.Mandatory).ToList();
                var localeFieldTypes = entityType.FieldTypes.Where(field => field.DataType == DataType.LocaleString).ToList();
                var readOnlyFieldTypes = entityType.FieldTypes.Where(field => field.ReadOnly).ToList();

                var languages = configurationData.Languages;
                var maxNumberOfInlineCvlValues =
                    int.Parse(this.dataContext.GetServerSetting(ServerConstants.MAX_NUMBER_OF_INLINE_CVL_VALUES));

                var cvlIds = configurationData.Columns
                    .Where(column => column.FieldTypeId != null && fieldTypes.ContainsKey(column.FieldTypeId))
                    .Select(column => fieldTypes[column.FieldTypeId].CVLId).Where(cvlId => cvlId != null).Distinct()
                    .ToList();

                var cvlDictionary = cvlIds.ToDictionary(
                    cvlId => cvlId,
                    cvlId => modelRepository.GetCVL(cvlId));

                var cvlValueDictionary = cvlIds.ToDictionary(
                    cvlId => cvlId,
                    cvlId => modelRepository.GetCVLValuesForCVL(cvlId)
                        .ToDictionary(cvlValue => cvlValue.Key));

                List<CultureInfo> serverLanguages = this.dataContext.GetAllLanguages();

                Entity existingEntity = null;
                Entity importEntity = null;

                this.context.Log(LogLevel.Information, $"Excel import batch - {batchId}  - Initializing data complete");

                var updateSegments = new HashSet<int>();
                var addSegments = new HashSet<int>();
                var changeSegments = new HashSet<int>();
                GetPermissionsForSegments(user, ref addSegments, ref updateSegments, ref changeSegments);

                var segmentExpressions = dataContext.GetExpressionsForEntityTypesByTarget(entityTypes.Select(x => x.Id).ToList(), ExpressionTarget.SEGMENT).ToDictionary(x => x.RefValue, x => x.Data);

                while (this.GetTableWithNextRowToImport(out DataTable table, batchId))
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        var row = table.Rows[0];
                        row["ImportStatus"] = null;
                        this.dataContext.SetStagingRow(row);
                        break;
                    }

                    using (table)
                    {
                        var row = table.Rows[0];
                        try
                        {

                            var validationError = new ImportError();
                            bool fieldSetChanged = false;
                            bool segmentChanged = false;

                            var myEntity = new Entity() { EntityType = entityType };

                            var entityFieldValuePairs = JsonConvert.DeserializeObject<Dictionary<string, string>>(row["EntityAsJson"].ToString());

                            // IF an expression exists for a column, remove the original value and replace it with the expression
                            // then remove the original expression column value.
                            foreach (var kvp in entityFieldValuePairs.ToList())
                            {
                                if (!kvp.Key.EndsWith("_Expression", StringComparison.OrdinalIgnoreCase))
                                {
                                    continue;
                                }

                                var key = kvp.Key[..kvp.Key.LastIndexOf("_", StringComparison.OrdinalIgnoreCase)];
                                if (!fieldTypes.TryGetValue(key, out var fieldType) || !fieldType.ExpressionSupport)
                                {
                                    continue;
                                }

                                var value = Regex.Replace(kvp.Value, @"^'(\s*?=.*)", "$1");
                                if (Utility.StringIsInriverExpression(fieldType.ExpressionSupport, value))
                                {
                                    if (fieldType.DataType == DataType.LocaleString)
                                    {
                                        var columnKey = string.Empty;
                                        foreach (var lang in serverLanguages)
                                        {
                                            columnKey = $"{key}_{lang.Name}";
                                            _ = entityFieldValuePairs.Remove(columnKey);
                                        }

                                        if (!string.IsNullOrEmpty(columnKey))
                                        {
                                            entityFieldValuePairs[columnKey] = value;
                                        }
                                    }
                                    else
                                    {
                                        entityFieldValuePairs[key] = value;
                                    }

                                    _ = entityFieldValuePairs.Remove(kvp.Key);
                                }
                            }

                            var entityFieldValuePairsWithBannedCharacters = entityFieldValuePairs.Where(x => IllegalCharacters.ControlCharacters.Any(x.Value.Contains))
                                .ToDictionary(x => x.Key, y => y.Value);

                            if (entityFieldValuePairsWithBannedCharacters.Count > 0)
                            {
                                RemoveForbiddenCharactersFromRow(entityFieldValuePairsWithBannedCharacters, entityFieldValuePairs);
                            }

                            importEntity = new Entity() { EntityType = entityType };

                            CreateEntityFromValuePairs(
                                entityFieldValuePairs,
                                ref importEntity,
                                entityType,
                                user,
                                configurationData,
                                serverLanguages,
                                cvlDictionary,
                                cvlValueDictionary,
                                segmentExpressions);

                            if (entityFieldValuePairs.ContainsKey("sys_id") && !string.IsNullOrEmpty(entityFieldValuePairs["sys_id"]))
                                row["sys_id"] = entityFieldValuePairs["sys_id"];

                            // The json converts integers into decimals so we need to remove the period zero
                            if (entityFieldValuePairs.ContainsKey(SysIdColumnName) && entityFieldValuePairs
                                                                                       [SysIdColumnName] != null
                                                                                   && !string.IsNullOrEmpty(
                                                                                       entityFieldValuePairs[
                                                                                           SysIdColumnName])
                                                                                   && entityFieldValuePairs[SysIdColumnName]
                                                                                       .EndsWith(".0"))
                            {
                                entityFieldValuePairs[SysIdColumnName] =
                                    entityFieldValuePairs[SysIdColumnName].Substring(0,
                                        entityFieldValuePairs[SysIdColumnName].Length - 2);
                            }

                            existingEntity = FindExistingEntity(entityFieldValuePairs, configurationData, entityTypes,
                                uniqueFieldTypes, ref validationError);

                            // Add readonly fields to the entity so that server extensions can use
                            foreach (var field in readOnlyFieldTypes)
                            {
                                Field newField = importEntity.Fields.FirstOrDefault(f => f.FieldType.Id == field.Id);
                                var existingField = existingEntity?.Fields.FirstOrDefault(f => f.FieldType.Id == field.Id);

                                if (newField == null)
                                {
                                    newField = new Field();
                                    newField.EntityId = existingEntity?.Id ?? 0;
                                    newField.FieldType = field;
                                    newField.Revision = 0;
                                    newField.Data = existingField?.Data;
                                    importEntity.Fields.Add(newField);
                                }
                            }

                            if (validationError.ColumnErrors.Count > 0)
                            {
                                row["ImportStatus"] = ImportState.ValidationError.ToString();
                                row["ImportMessage"] = JsonConvert.SerializeObject(validationError);
                                this.dataContext.SetStagingRow(row);
                                anyErrors = true;
                                continue;
                            }

                            if (existingEntity == null)
                            {
                                if (row["entityType"].ToString() != configurationData.EntityTypeId)
                                {
                                    continue;
                                }

                                existingEntity = Entity.CreateEntity(entityType);

                                if (existingEntity == null)
                                {
                                    row["ImportStatus"] = ImportState.Error;
                                    var importError = new ImportError()
                                    { GeneralError = $"Error initializing the Entity for import" };
                                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                                    this.dataContext.SetStagingRow(row);
                                    anyErrors = true;
                                    continue;
                                }
                                else
                                {
                                    foreach (var field in existingEntity.Fields)
                                    {
                                        field.Data = null;
                                    }
                                }
                            }
                            else
                            {
                                if (existingEntity.EntityType.Id != configurationData.EntityTypeId)
                                {
                                    row["entityType"] = existingEntity.EntityType.Id;
                                    row["ImportStatus"] = ImportState.Ignore;
                                    row["ImportMessage"] = "EntityType is different from import wizard selection.";
                                    this.dataContext.SetImportStatus(row["inRiverImportColumnId"].ToString(),
                                    row["ImportStatus"].ToString(), row["ImportMessage"].ToString());
                                    continue;
                                }
                            }


                            row["sys_id"] = existingEntity.Id;
                            if (existingEntity.Id == 0)
                            {
                                row["ImportType"] = "New";
                                SetDefaultFields(importEntity, fieldsWithDefaultValues, configurationData);

                            }
                            else
                            {
                                importEntity.Id = existingEntity.Id;
                                row["ImportType"] = "Update";

                                if (importEntity.Segment.Id == UnsetSegmentId)
                                {
                                    importEntity.Segment.Id = existingEntity.Segment.Id;
                                }

                                if (entitiesImported.Contains(importEntity.Id))
                                {
                                    row["ImportStatus"] = ImportState.ValidationError;
                                    validationError.GeneralError = string.IsNullOrEmpty(validationError.GeneralError)
                                        ? "Entity has already been updated in File.  Line not processed." :
                                        validationError.GeneralError + " Entity has already been updated in File.  Line not processed.";
                                    row["ImportMessage"] = JsonConvert.SerializeObject(validationError);
                                    this.dataContext.SetStagingRow(row);
                                    anyErrors = true;
                                    continue;
                                }

                            }

                            // If it's a new entity or not clear fields remove any empty locale string fields
                            if ((existingEntity.Id == 0) || (!configurationData.ClearEmptyValues))
                            {
                                foreach (FieldType localFieldType in localeFieldTypes)
                                {
                                    var localeField = importEntity.Fields.Where(field => field.FieldType.Id == localFieldType.Id).ToList();
                                    if (localeField.Count > 0)
                                    {
                                        if (Utility.StringIsInriverExpression(localFieldType.ExpressionSupport, localeField[0].Data as string))
                                        {
                                            continue;
                                        }
                                        else if (LocaleString.IsNullOrEmpty((LocaleString)localeField[0].Data))
                                        {
                                            importEntity.Fields.Remove(localeField[0]);
                                        }
                                    }
                                }
                            }

                            var categoryRestrictionLookup = user.RestrictedFieldPermissions
                                        .Where(restriction => restriction.CategoryId != null)
                                        .ToLookup(restriction => Tuple.Create(restriction.EntityTypeId, restriction.CategoryId));

                            var fieldTypeRestrictionLookup = user.RestrictedFieldPermissions
                                .Where(restriction => restriction.FieldTypeId != null)
                                .ToLookup(restriction => Tuple.Create(restriction.EntityTypeId, restriction.FieldTypeId));

                            int? entitySegmentId = importEntity.Segment?.Id ?? null;

                            var userRolesForSegment = new List<int>();
                            if (entitySegmentId != null)
                            {
                                // get User's Roles associated to the SegmentId of the Entity
                                userRolesForSegment = user.GetRolesForSegmentId(entitySegmentId.Value)
                                    .Select(p => p.Id).ToList();
                            }

                            this.dataContext.SetStagingRow(row);

                            if ((configurationData.RunValidation) &&
                                (row["ImportStatus"].ToString() != ImportState.ValidationError.ToString()))
                            {
                                try
                                {
                                    if (IsEntityValid(ref existingEntity, ref importEntity, entityFieldValuePairs, user,
                                        entityType, ref validationError, configurationData, null,
                                        maxNumberOfInlineCvlValues, categories, cvlDictionary, cvlValueDictionary,
                                        categoryRestrictionLookup, fieldTypeRestrictionLookup, userRolesForSegment))
                                    {
                                        row["ImportStatus"] = ImportState.Validated;
                                        row["ImportMessage"] = DBNull.Value;
                                    }
                                    else
                                    {
                                        row["ImportStatus"] = ImportState.ValidationError;
                                        row["ImportMessage"] = JsonConvert.SerializeObject(validationError);
                                    }
                                }
                                catch (Exception e)
                                {
                                    row["ImportStatus"] = ImportState.ValidationError;
                                    var importError = new ImportError()
                                    { GeneralError = "During Validation Process exception message:" + e.Message };
                                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);

                                }

                                this.dataContext.SetImportStatus(row["inRiverImportColumnId"].ToString(),
                                    row["ImportStatus"].ToString(), row["ImportMessage"].ToString());
                            }

                            var updatedFields = new List<Field>();
                            var revisions = new List<Field>();
                            if ((row["ImportStatus"].ToString() == ImportState.Validated.ToString() ||
                                 configurationData.RunValidation == false) && configurationData.RunImport)
                            {
                                try
                                {
                                    if (row["ImportType"].ToString() == "Update")
                                    {
                                        if (importEntity.Segment.Id == EvaluatedSegmentId || updateSegments.Contains(importEntity.Segment.Id))
                                        {
                                            if (importEntity.Segment.Id != EvaluatedSegmentId && importEntity.Segment.Id != existingEntity.Segment.Id && !changeSegments.Contains(existingEntity.Segment.Id))
                                            {
                                                row["ImportStatus"] = ImportState.ImportError;
                                                var importError = new ImportError()
                                                {
                                                    GeneralError =
                                                        $"User, {userName}, does not have permission to change entity in segment {existingEntity.Segment.Name}."
                                                };
                                                row["ImportMessage"] = JsonConvert.SerializeObject(importError);

                                            }
                                            else
                                            {

                                                importEntity.DisplayName = importEntity.Fields.Any(f => f.FieldType.IsDisplayName) ?
                                                        importEntity.Fields.FirstOrDefault(f => f.FieldType.IsDisplayName) :
                                                        existingEntity.DisplayName;
                                                importEntity.DisplayDescription = importEntity.Fields.Any(f => f.FieldType.IsDisplayDescription) ?
                                                        importEntity.Fields.FirstOrDefault(f => f.FieldType.IsDisplayDescription) :
                                                        existingEntity.DisplayDescription;

                                                updatedFields = this.UpdateEntity(
                                                    ref existingEntity,
                                                    ref importEntity,
                                                    configurationData,
                                                    user,
                                                    row,
                                                    serverLanguages,
                                                    ref fieldSetChanged,
                                                    ref segmentChanged,
                                                    mandatoryFieldTypes,
                                                    revisions,
                                                    categoryRestrictionLookup,
                                                    fieldTypeRestrictionLookup,
                                                    userRolesForSegment);
                                            }
                                        }
                                        else
                                        {

                                            row["ImportStatus"] = ImportState.ImportError;
                                            var importError = new ImportError()
                                            {
                                                GeneralError =
                                                    $"User, {userName}, does not have permission to change entity in segment {importEntity.Segment.Name}."
                                            };
                                            row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                                        }
                                    }
                                    else if (row["ImportType"].ToString() == "New")
                                    {
                                        if (importEntity.Segment.Id == EvaluatedSegmentId || addSegments.Contains(importEntity.Segment.Id))
                                        {
                                            this.AddEntity(ref importEntity, row, userName, configurationData, mandatoryFieldTypes, revisions);
                                            row["sys_id"] = importEntity.Id;
                                        }
                                        else
                                        {
                                            row["ImportStatus"] = ImportState.ImportError;
                                            var importError = new ImportError()
                                            {
                                                GeneralError =
                                                    $"User, {userName}, does not have access to the defined segment."
                                            };
                                            row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                                        }
                                    }
                                    else
                                    {
                                        row["ImportStatus"] = ImportState.ImportError;
                                        var importError = new ImportError()
                                        { GeneralError = $"Unknown import status" + row["ImportType"].ToString() };
                                        row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                                    }

                                }
                                catch (Exception e)
                                {
                                    row["ImportStatus"] = ImportState.ImportError;
                                    var importError = new ImportError()
                                    { GeneralError = "During Import Processing exception message:" + e.Message };
                                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                                }

                                this.dataContext.SetStagingRow(row);
                            }

                            if (row["ImportStatus"].ToString() == ImportState.Imported.ToString())
                            {
                                var importedEntity = new ImportedEntity
                                {
                                    IsNew = row["ImportType"].ToString() == "New",
                                    UpdatedFields = updatedFields,
                                    Entity = importEntity,
                                    InRiverImportColumnId = row["inRiverImportColumnId"].ToString(),
                                    FieldSetChanged = fieldSetChanged,
                                    SegmentChanged = segmentChanged,
                                    PreviousSegmentId = existingEntity.Segment.Id
                                };

                                try
                                {
                                    if (importedEntity.IsNew)
                                    {
                                        this.dataContext.SaveEntityFieldRevisionHistory(importedEntity.Entity.Id, revisions, true, userName);
                                    }
                                    else
                                    {
                                        this.dataContext.SaveUpdatedEntityFieldRevisionHistory(importedEntity.Entity.Id, revisions, existingEntity.Fields, userName);
                                    }
                                }
                                catch (Exception e)
                                {
                                    var message =
                                        $"Entity updated but failed to save entity field revision for entity {importedEntity.Entity.Id}, Message: {e.Message}";
                                    row["ImportStatus"] = ImportState.ImportError;
                                    var importError = new ImportError()
                                    { GeneralError = message };
                                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                                    this.dataContext.SetStagingRow(row);
                                }

                                if (row["ImportStatus"].ToString() == ImportState.Imported.ToString() &&
                                     (configurationData.RunEntityListeners || configurationData.RunChannelListeners || configurationData.RunPostProcessing))
                                {
                                    queue.Add(importedEntity);
                                }

                                entitiesImported.Add(importEntity.Id);
                            }
                        }
                        catch (Exception e)
                        {
                            row["ImportStatus"] = ImportState.ValidationError;
                            var importError = new ImportError()
                            { GeneralError = "General Processing exception message:" + e.Message };
                            row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                            this.dataContext.SetStagingRow(row);
                        }

                        if (row["ImportStatus"].ToString() == ImportState.ImportError.ToString() ||
                            row["ImportStatus"].ToString() == ImportState.ValidationError.ToString())
                        {
                            anyErrors = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                this.context.Log(LogLevel.Error, $"Error processing Import Batch: {userName}, {batchId}", ex);
                throw;
            }
            finally
            {
                queue.CompleteAdding();
                context.Username = origUser;
            }

            return anyErrors;
        }

        public bool IsEntityValid(ref Entity existingEntity,
            ref Entity updatedEntity,
            Dictionary<string, string> entityFieldValuePairs,
            User user,
            EntityType entityType,
            ref ImportError validationError,
            inRiver.Core.Models.inRiver.ExcelImport.FileImportConfigurationModel configuration,
            List<CultureInfo> languages,
            int? maxNumberOfInlineCvlValues,
            IReadOnlyDictionary<string, Category> categories,
            Dictionary<string, CVL> cvlDictionary,
            Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary,
            ILookup<Tuple<string, string>, RestrictedFieldPermission> categoryRestrictionLookup,
            ILookup<Tuple<string, string>, RestrictedFieldPermission> fieldTypeRestrictionLookup,
            List<int> userRolesForSegment
            )
        {
            var isValid = true;
            var errorMessages = new StringBuilder();

            try
            {
                foreach (var columnModel in configuration.Columns)
                {
                    // ignore sys_id
                    if (string.Equals(columnModel.ColumnName, SysIdColumnName, StringComparison.OrdinalIgnoreCase))
                    {
                        continue;
                    }

                    // validate fieldset
                    if (string.Equals(columnModel.ColumnName, FileImportFieldModel.SysFieldSetFieldType, StringComparison.OrdinalIgnoreCase))
                    {
                        var newFieldSetId = updatedEntity.FieldSetId ?? string.Empty;
                        var currentFieldSetId = existingEntity.FieldSetId ?? string.Empty;

                        if (!string.IsNullOrEmpty(newFieldSetId) &&
                            !string.Equals(newFieldSetId, currentFieldSetId, StringComparison.OrdinalIgnoreCase))
                        {
                            var fs = entityType.FieldSets.Find(fieldSet => fieldSet.Id == newFieldSetId);

                            if (fs == null)
                            {
                                isValid = false;
                                _ = errorMessages.AppendLine("Fieldset " + newFieldSetId + " does not exist");
                                validationError.ColumnErrors.Add(columnModel.ColumnName, "Fieldset " + newFieldSetId + " does not exist");
                            }
                            else if (fs.EntityTypeId != existingEntity.EntityType.Id)
                            {
                                isValid = false;
                                _ = errorMessages.AppendLine("Fieldset " + newFieldSetId + " does not belong to selected entity type");
                                validationError.ColumnErrors.Add(columnModel.ColumnName, "Fieldset " + newFieldSetId + " does not belong to selected entity type");
                            }
                        }

                        continue;
                    }

                    int? entitySegmentId = updatedEntity.Segment?.Id ?? null;
                    if (entitySegmentId != null)
                    {
                        // get User's Roles associated to the SegmentId of the Entity
                        userRolesForSegment = user.GetRolesForSegmentId(entitySegmentId.Value)
                            .Select(p => p.Id).ToList();
                    }

                    var fieldType = entityType.FieldTypes.Find(ft => ft.Id == columnModel.FieldTypeId);
                    var newField = updatedEntity.Fields.Find(f => f.FieldType.Id == columnModel.FieldTypeId);

                    if (categoryRestrictionLookup == null)
                    {
                        categoryRestrictionLookup = (ILookup<Tuple<string, string>, RestrictedFieldPermission>)Enumerable.Empty<RestrictedFieldPermission>()
                            .ToLookup(x => Tuple.Create<string, string>(null, null));
                    }

                    if (fieldTypeRestrictionLookup == null)
                    {
                        fieldTypeRestrictionLookup = (ILookup<Tuple<string, string>, RestrictedFieldPermission>)Enumerable.Empty<RestrictedFieldPermission>()
                            .ToLookup(x => Tuple.Create<string, string>(null, null));
                    }

                    var restriction = fieldType == null
                        ? null
                        : categoryRestrictionLookup[Tuple.Create(updatedEntity.EntityType.Id, fieldType.CategoryId)]
                            .FirstOrDefault(a => userRolesForSegment.Contains(a.RoleId))
                          ?? fieldTypeRestrictionLookup[Tuple.Create(fieldType.EntityTypeId, fieldType.Id)]
                            .FirstOrDefault(a => userRolesForSegment.Contains(a.RoleId));

                    // ignore if
                    // field is not in the entity
                    // field is readonly
                    // field is restricted
                    // field is empty and clear empty values is set to false
                    if (fieldType == null || fieldType.ReadOnly || restriction != null || (newField == null && !configuration.ClearEmptyValues))
                    {
                        continue;
                    }

                    // field is empty and ClearEmptyValues is set to true, add field with null data
                    if (newField == null && configuration.ClearEmptyValues && updatedEntity.Id != 0)
                    {
                        newField = new Field
                        {
                            EntityId = updatedEntity.Id,
                            FieldType = fieldType,
                            Revision = 0
                        };
                        updatedEntity.Fields.Add(newField);
                        continue;
                    }



                    var cultureInfo = user.CultureInfo;
                    if (!string.IsNullOrEmpty(columnModel.LanguageCode))
                    {
                        cultureInfo = new CultureInfo(columnModel.LanguageCode);
                    }

                    var newFieldData = newField?.Data?.ToString();
                    if (string.IsNullOrEmpty(newFieldData))
                    {
                        if (configuration.ClearEmptyValues)
                        {
                            if (fieldType.Mandatory)
                            {
                                isValid = false;
                                _ = errorMessages.AppendLine(columnModel.ColumnName + "-" + columnModel.FieldTypeName + " is mandatory");
                                validationError.ColumnErrors.Add(columnModel.ColumnName, columnModel.FieldTypeName + " is mandatory");
                                continue;
                            }

                            // readonly check was already done above
                            if (newField == null)
                            {
                                newField = new Field
                                {
                                    EntityId = updatedEntity.Id,
                                    FieldType = fieldType,
                                    Revision = 0
                                };
                                updatedEntity.Fields.Add(newField);
                            }
                            else
                            {
                                newField.Data = null;
                            }

                            continue;
                        }

                        // remove the field, so it will not be set to null or empty
                        _ = updatedEntity.Fields.Remove(newField);
                        continue;
                    }

                    var dataNoEscape = Regex.Replace(newFieldData, @"^'(\s*?=.*)", "$1");
                    if (fieldType.DataType == DataType.DateTime)
                    {
                        if (!(newField.Data is DateTime))
                        {
                            isValid = false;
                            _ = errorMessages.AppendLine(columnModel.ColumnName + "-Value does not match date format (date format: " + configuration.CurrentLanguage + ")");
                            validationError.ColumnErrors.Add(columnModel.ColumnName,
                                "-Value does not match date format (date format: " + configuration.CurrentLanguage + ")");
                            continue;
                        }
                    }
                    else if (!FieldDataValidation.ValueMatchesDataType(fieldType.DataType, dataNoEscape, fieldType.ExpressionSupport))
                    {
                        isValid = false;
                        _ = errorMessages.AppendLine(columnModel.ColumnName + "-Value does not match type for field (expected " + fieldType.DataType + ")");
                        validationError.ColumnErrors.Add(columnModel.ColumnName, "Value does not match type for field (expected " + fieldType.DataType + ")");
                        continue;
                    }
                    else if (Utility.StringIsInriverExpression(fieldType.ExpressionSupport, dataNoEscape))
                    {
                        var wrapper = new ExpressionWrapper(this.context);
                        if (!wrapper.ParseExpression(dataNoEscape, out var errMsg))
                        {
                            var columnName = columnModel.ColumnName;
                            if (configuration.Columns.Any(x => x.ColumnName == $"{columnModel.ColumnName}_Expression"))
                            {
                                columnName = $"{columnModel.ColumnName}_Expression";
                            }

                            isValid = false;
                            _ = errorMessages.AppendLine(columnName + $"-Failed to parse expression (Error: {errMsg})");
                            validationError.ColumnErrors.Add(columnName, $"Failed to parse expression (Error: {errMsg})");
                            continue;
                        }
                    }

                    if (fieldType.Settings != null)
                    {
                        var regexSetting = fieldType.Settings.FirstOrDefault(setting =>
                            setting.Key.Equals("RegExp", StringComparison.OrdinalIgnoreCase));

                        var regexValidationErrorMessage =
                            GetRegexFieldValidationErrorMessage(regexSetting.Value, fieldType, newField);

                        if (!string.IsNullOrEmpty(regexValidationErrorMessage))
                        {
                            isValid = false;
                            _ = errorMessages.AppendLine(columnModel.ColumnName + "-" + regexValidationErrorMessage);
                            validationError.ColumnErrors.Add(columnModel.ColumnName, regexValidationErrorMessage);
                            continue;
                        }
                    }

                    var data = newField.Data;

                    if (fieldType.DataType == DataType.CVL && !Remoting.Util.Utility.StringIsInriverExpression(fieldType.ExpressionSupport, newFieldData))
                    {
                        var fieldCvl = cvlDictionary == null ? null : cvlDictionary[fieldType.CVLId];
                        if (fieldType.Multivalue)
                        {
                            var dataString = data?.ToString() ?? string.Empty;
                            var cvlValues = dataString.Split(SemicolonSeparator);
                            var hasEmptyValues = cvlValues.Any(string.IsNullOrWhiteSpace);

                            if (hasEmptyValues || !FieldDataValidation.VerifyCvlData(fieldType, cvlValues.ToRemoveEmptyOrNull(), cvlValueDictionaryDictionary))
                            {
                                isValid = false;
                                var errorMessage = $"{columnModel.ColumnName} - Invalid value supplied ({newFieldData})";
                                _ = errorMessages.AppendLine(errorMessage);
                                validationError.ColumnErrors.Add(columnModel.ColumnName, errorMessage);
                            }
                            // If CVL has a parent ID, validate the relationship
                            if (fieldCvl?.ParentId != null)
                            {
                                var parentField = updatedEntity.Fields.FirstOrDefault(f => f.FieldType.CVLId == fieldCvl.ParentId)
                                    ?? existingEntity.Fields.FirstOrDefault(f => f.FieldType.CVLId == fieldCvl.ParentId);

                                if (!FieldDataValidation.VerifyCvlRelationships(fieldType, cvlValues, cvlValueDictionaryDictionary, parentField))
                                {
                                    isValid = false;
                                    _ = errorMessages.AppendLine(columnModel.ColumnName + "-Invalid parent cvl value (" + newFieldData + ")");
                                    validationError.ColumnErrors.Add(columnModel.ColumnName, "Invalid parent cvl value (" + newFieldData + ")");
                                }
                            }


                        }
                        else
                        {
                            newFieldData = this.CleanCvlValue(newFieldData);

                            if (!FieldDataValidation.VerifyCvlData(fieldType, newFieldData, cvlValueDictionaryDictionary))
                            {
                                isValid = false;
                                _ = errorMessages.AppendLine(columnModel.ColumnName + "-Invalid value supplied (" + newFieldData + ")");
                                validationError.ColumnErrors.Add(columnModel.ColumnName, "Invalid value supplied (" + newFieldData + ")");
                            }
                            // If CVL has a parent ID, validate the relationship
                            if (fieldCvl?.ParentId != null)
                            {
                                var parentField = updatedEntity.Fields.FirstOrDefault(f => f.FieldType.CVLId == fieldCvl.ParentId)
                                    ?? existingEntity.Fields.FirstOrDefault(f => f.FieldType.CVLId == fieldCvl.ParentId);

                                if (!FieldDataValidation.VerifyCvlRelationships(fieldType, newFieldData, cvlValueDictionaryDictionary, parentField))
                                {
                                    isValid = false;
                                    _ = errorMessages.AppendLine(columnModel.ColumnName + "-Invalid parent cvl value (" + newFieldData + ")");
                                    validationError.ColumnErrors.Add(columnModel.ColumnName, "Invalid parent cvl value (" + newFieldData + ")");
                                }
                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                isValid = false;
                _ = errorMessages.AppendLine(ex.Message);
            }

            validationError.GeneralError = errorMessages.Length > 0 ? errorMessages.ToString() : string.Empty;
            return isValid;
        }

        private static string GetRegexFieldValidationErrorMessage(string pattern, FieldType fieldType, Field field)
        {
            if (string.IsNullOrWhiteSpace(pattern))
            {
                return string.Empty;
            }

            if (fieldType.DataType != DataType.LocaleString)
            {
                try
                {
                    if (!FieldDataValidation.ValueMatchesRegex(pattern, field.Data?.ToString()))
                    {
                        return $"Value does not match regular expression for field (pattern: { pattern })";
                    }
                }
                catch (RegexMatchTimeoutException e)
                {
                    return $"The regex match timed out. Pattern might be too complex or input too long. (pattern: { pattern })";
                }

                return string.Empty;
            }

            var fieldDataLocaleString = field.Data as LocaleString;
            if (fieldDataLocaleString == null)
            {
                return string.Empty;
            }

            var failedValidationLanguages = new List<string>();
            foreach (var language in fieldDataLocaleString.Languages)
            {
                if (!FieldDataValidation.ValueMatchesRegex(pattern, fieldDataLocaleString[language]))
                {
                    failedValidationLanguages.Add(language.Name);
                }
            }

            var concatenatedLanguages = string.Join(", ", failedValidationLanguages);
            return failedValidationLanguages.Count == 0
                ? string.Empty
                : string.IsNullOrEmpty(concatenatedLanguages)
                    ? $"Value does not match regular expression for field (pattern: {pattern})"
                    : $"Value does not match regular expression for field (pattern: {pattern}) for languages: {concatenatedLanguages})";
        }

        public List<Field> UpdateEntity(ref Entity existingEntity,
            ref Entity updatedEntity,
            FileImportConfigurationModel configurationData,
            User user,
            DataRow row,
            List<CultureInfo> serverLanguages,
            ref bool fieldSetChanged,
            ref bool segmentChanged,
            List<FieldType> mandatoryFieldTypes,
            List<Field> revisions,
            ILookup<Tuple<string, string>, RestrictedFieldPermission> categoryRestrictionLookup,
            ILookup<Tuple<string, string>, RestrictedFieldPermission> fieldTypeRestrictionLookup,
            List<int> userRolesForSegment)
        {
            fieldSetChanged = false;
            segmentChanged = false;

            List<Field> fieldsToAdd = new List<Field>();
            List<Field> fieldsToUpdate = new List<Field>();

            if (updatedEntity.Fields.Count == 0 && existingEntity.Segment.Id == updatedEntity.Segment.Id && existingEntity.FieldSetId == updatedEntity.FieldSetId)
            {

                row["ImportStatus"] = ImportState.NoChanges;
                var importError = new ImportError() { GeneralError = "Values of fields for entity did not change or had no values to update." };
                row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                return null;
            }

            if (!string.IsNullOrWhiteSpace(existingEntity.Locked))
            {
                if (existingEntity.Locked.ToLower() != user.Username)
                {
                    row["ImportStatus"] = ImportState.ImportError;
                    var importError = new ImportError() { GeneralError = $"Entity is locked by user: {existingEntity.Locked}" };
                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                    return null;
                }
            }

            if (configurationData.RunServerExtensions)
            {
                foreach (Field field in existingEntity.Fields)
                {
                    if (!updatedEntity.Fields.Exists(f => f.FieldType.Id == field.FieldType.Id))
                    {
                        updatedEntity.Fields.Add((Field)field.Clone());
                    }
                }

                try
                {
                    CancelUpdateArgument arg = new CancelUpdateArgument();

                    ExtensionManager manager = new ExtensionManager(this.context);
                    updatedEntity = manager.CallServerExtensionsForEntity(updatedEntity, ExtensionEvent.OnUpdate, arg);

                    if (arg.Cancel)
                    {
                        if (string.IsNullOrEmpty(arg.Message))
                        {
                            context.Log(LogLevel.Verbose, $"Import Excel - Event was cancelled by the extension. No reason specified. BatchId: {row["batchId"].ToString()}");
                        }
                        else
                        {
                            context.Log(LogLevel.Verbose, $"Import Excel - Event was cancelled by the extension. BatchId: {row["batchId"].ToString()}" + arg.Message);
                        }
                        row["ImportStatus"] = ImportState.ImportError;
                        var importError = new ImportError() { GeneralError = $"Server Extension Canceled Update. Message:  {arg.Message}" };
                        row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                    }
                }
                catch (Exception ex)
                {
                    this.context.Log(LogLevel.Error, $"Error processing Import Batch: BatchId: {row["batchId"].ToString()}", ex);
                    row["ImportStatus"] = ImportState.ImportError;
                    var importError = new ImportError() { GeneralError = $"Error calling Server extensions.  Error: {ex.Message}" };
                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                }
            }

            if (!configurationData.RunServerExtensions)
            {
                var mandatoryFieldsInExistingEntity = GetMatchingFieldsInEntity(mandatoryFieldTypes, existingEntity);

                CompleteEntityWithFieldsIfMissing(updatedEntity, mandatoryFieldsInExistingEntity);
            }

            // If new entity be sure that all mandatory fields are present
            var errorMessages = new StringBuilder();
            var validationError = new ImportError();
            if (!this.AreMandatoryFieldsPresent(updatedEntity, mandatoryFieldTypes, ref validationError, errorMessages))
            {
                row["ImportStatus"] = ImportState.ImportError;
                var importError = new ImportError() { GeneralError = errorMessages.ToString() };
                row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                return null;

            }

            serverLanguages ??= this.dataContext.GetAllLanguages();

            var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
            if (updatedEntity.Segment.Id == EvaluatedSegmentId)
            {
                if (dataContext.GetExpressionsForEntityType(updatedEntity.EntityType.Id)[ExpressionTargetType.ENTITYMETADATA].TryGetValue(ExpressionTarget.SEGMENT, out var segExpr))
                {
                    expressionWrapper.SaveExpressionsForEntity(updatedEntity.Id, new List<Inriver.Expressions.Dto.DtoExpression> { segExpr });
                }
            }

            var expressionDtoEntity = DtoFactory.DtoFromEntity(updatedEntity);
            existingEntity.LoadLevel = LoadLevel.DataOnly;
            expressionWrapper.EvaluateExpressionsForEntityAndMetadata(expressionDtoEntity, DtoFactory.DtoFromEntity(existingEntity), updatedEntity.Fields.Select(x => x.FieldType));
            var fieldTypeDictionary = updatedEntity.Fields.ToDictionary(f => f.FieldType.Id, f => f.FieldType);

            var upsertEntityDurationMetricStopwatch = Stopwatch.StartNew();
            if (row["ImportStatus"].ToString() != ImportState.ImportError.ToString())
            {
                var excelLocaleStringFields = new List<Field>(); // this will hold the actual value from the excel file
                foreach (var dtoField in expressionDtoEntity.Fields)
                {
                    var field = DtoFactory.FieldFromDto(dtoField, fieldTypeDictionary[dtoField.FieldTypeId]);
                    var fieldType = field.FieldType;

                    var restriction =
                        categoryRestrictionLookup[Tuple.Create(updatedEntity.EntityType.Id, fieldType.CategoryId)]
                            .FirstOrDefault(a => userRolesForSegment.Contains(a.RoleId))
                        ?? fieldTypeRestrictionLookup[Tuple.Create(fieldType.EntityTypeId, fieldType.Id)]
                                .FirstOrDefault(a => userRolesForSegment.Contains(a.RoleId));

                    if (restriction != null)
                    {
                        continue;
                    }

                    var existingField = existingEntity.Fields.Find(f => f.FieldType.Id == field.FieldType.Id);

                    if (field.IsEmpty() && existingField.Revision == 0)
                    {
                        continue;
                    }

                    field.Revision = existingField.Revision + 1;
                    field.EntityId = updatedEntity.Id;

                    if (field.FieldType.DataType == DataType.LocaleString)
                    {
                        var fieldLocaleString = field.Data as LocaleString ?? new LocaleString();
                        var persistedLocaleString = existingField.Data as LocaleString;

                        // Remove non-server languages from current LocaleString
                        foreach (var ci in fieldLocaleString.Languages)
                        {
                            if (!serverLanguages.Contains(ci))
                            {
                                fieldLocaleString.RemoveCulture(ci);
                            }
                        }

                        var excelField = field.Clone() as Field;
                        var excelFieldData = excelField.Data as LocaleString;

                        // Ensure all server languages are present in the LocaleString
                        foreach (var serverLanguage in serverLanguages)
                        {
                            if (this.context.EntityModel == (int)EntityModels.NDL && excelFieldData != null)
                            {
                                if (excelFieldData.ContainsCulture(serverLanguage) &&
                                    string.IsNullOrEmpty(excelFieldData[serverLanguage]) &&
                                    !configurationData.ClearEmptyValues)
                                {
                                    excelFieldData.RemoveCulture(serverLanguage); // do not include empty cells when Clear empty value is false
                                }
                            }

                            if (!fieldLocaleString.ContainsCulture(serverLanguage) ||
                                (string.IsNullOrEmpty(fieldLocaleString[serverLanguage]) &&
                                 !configurationData.ClearEmptyValues))
                            {
                                fieldLocaleString[serverLanguage] = (!LocaleString.IsNullOrEmpty(persistedLocaleString) &&
                                                                persistedLocaleString.ContainsCulture(serverLanguage))
                                                                ? persistedLocaleString[serverLanguage] ?? string.Empty
                                                                : string.Empty;
                            }
                        }

                        if (!LocaleString.IsNullOrEmpty(persistedLocaleString))
                        {
                            excelLocaleStringFields.Add(excelField);
                        }

                        field.Data = fieldLocaleString;

                        var updatedEntityField = updatedEntity.Fields.FirstOrDefault(f => f.FieldType.Id == field.FieldType.Id);
                        if (updatedEntityField != null)
                        {
                            updatedEntityField.Data = fieldLocaleString;
                        }
                    }

                    if (existingField.Revision == 0 && !field.IsEmpty())
                    {
                        fieldsToAdd.Add(field);
                        continue;
                    }

                    if (field.IsEmpty())
                    {
                        if (configurationData.ClearEmptyValues)
                        {
                            var fieldData = field.Data?.ToString();
                            if (string.IsNullOrEmpty(fieldData))
                            {
                                field.Data = null;
                            }
                        }
                        else
                        {
                            continue;
                        }
                    }

                    if (field.ValueHasBeenModified(existingEntity.GetField(field.FieldType.Id).Data))
                    {
                        fieldsToUpdate.Add(field);
                    }
                }

                if (this.context.EntityModel == (int)EntityModels.NDL)
                {
                    if (fieldsToAdd.Count > 0)
                    {
                        fieldsToUpdate.AddRange(fieldsToAdd);
                    }

                    if (fieldsToUpdate.Count > 0)
                    {
                        var fieldsToUpdateClone = fieldsToUpdate.Select(f => f.Clone() as Field).ToList();

                        // use the actual value from the excel file for locale strings
                        foreach (var field in fieldsToUpdateClone.Where(f => f.FieldType.DataType == DataType.LocaleString))
                        {
                            var excelField = excelLocaleStringFields.FirstOrDefault(f => f.FieldType.Id == field.FieldType.Id);
                            if (excelField != null)
                            {
                                field.Data = excelField.Data;
                            }
                        }

                        this.dataContext.UpdateFields(fieldsToUpdateClone);
                        this.dataContext.UpdateEntityChangeSet(updatedEntity.Id, fieldsToUpdate);
                    }
                }
                else if (this.context.EntityModel == 2)
                {
                    if (fieldsToAdd.Count > 0)
                    {
                        fieldsToUpdate.AddRange(fieldsToAdd);
                    }

                    if (fieldsToUpdate.Count > 0)
                    {
                        (this.dataContext as IPMCServer3DLPersistanceAdapter).UpdateEntityFieldsAndChangeSet(updatedEntity.Id, fieldsToUpdate);
                    }
                }
                else
                {
                    if (fieldsToAdd.Count > 0)
                    {
                        this.dataContext.AddFields(fieldsToAdd);
                    }

                    if (fieldsToUpdate.Count > 0)
                    {
                        this.dataContext.UpdateFields(fieldsToUpdate);
                    }

                    var allUpdatedFields = fieldsToUpdate.Concat(fieldsToAdd).ToList();
                    if (allUpdatedFields.Count > 0)
                    {
                        this.dataContext.UpdateEntityChangeSet(updatedEntity.Id, allUpdatedFields);
                    }
                }

                revisions.AddRange(fieldsToAdd);
                revisions.AddRange(fieldsToUpdate);
                revisions.AddRange(expressionWrapper.Revisions);

                var manualSegmentUpdate = existingEntity.Segment != null && updatedEntity.Segment != null && existingEntity.Segment.Id != updatedEntity.Segment.Id && updatedEntity.Segment.Id != -1;
                var expressionSegmentUpdate = updatedEntity.Segment != null &&
                    expressionDtoEntity.Segment != null && existingEntity.Segment != null && expressionDtoEntity.Segment.Id != existingEntity.Segment.Id;
                if (manualSegmentUpdate)
                {
                    this.dataContext.SetSegmentForEntities(new List<int>() { updatedEntity.Id }, updatedEntity.Segment.Id);
                    this.dataContext.DeleteExpressionsIfExists(new List<int> { updatedEntity.Id }, ExpressionTarget.SEGMENT);
                    segmentChanged = true;
                }
                else if (expressionSegmentUpdate)
                {
                    updatedEntity.Segment.Id = expressionDtoEntity.Segment.Id;
                    this.dataContext.SetSegmentForEntities(new List<int>() { updatedEntity.Id }, updatedEntity.Segment.Id);
                    segmentChanged = true;
                }

                string existingFieldSet = string.IsNullOrEmpty(existingEntity.FieldSetId) ? string.Empty : existingEntity.FieldSetId;
                string updatedFieldSet = string.IsNullOrEmpty(updatedEntity.FieldSetId) ? string.Empty : updatedEntity.FieldSetId;

                bool hasFieldSetInColumns = configurationData.Columns.Any(column =>
                    column.ColumnName == FileImportFieldModel.SysFieldSetFieldType);

                if ((string.IsNullOrEmpty(updatedFieldSet) && configurationData.ClearEmptyValues && hasFieldSetInColumns) ||
                (!string.Equals(existingFieldSet, updatedFieldSet, StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(updatedFieldSet)))
                {
                    this.dataContext.SetEntityFieldSet(updatedEntity.Id, updatedEntity.FieldSetId);
                    fieldSetChanged = true;
                }

                if (fieldsToUpdate.Count == 0 && fieldsToAdd.Count == 0 && !segmentChanged && !fieldSetChanged)
                {
                    row["ImportStatus"] = ImportState.NoChanges;
                    var importError = new ImportError() { GeneralError = "Values of fields for entity did not change or had no values to update." };
                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                }
                else
                {

                    row["ImportStatus"] = ImportState.Imported;
                    row["ImportMessage"] = "";
                }
            }
            upsertEntityDurationMetricStopwatch.Stop();

            // Return list of changed fields for extensions
            List<Field> allFields = new List<Field>();
            allFields.AddRange(fieldsToAdd);
            allFields.AddRange(fieldsToUpdate);

            UpsertEntityDurationMetric<ExcelImportRepository>.TrackValue(
                upsertEntityDurationMetricStopwatch.ElapsedMilliseconds,
                allFields,
                updatedEntity.EntityType?.Id,
                this.context.CustomerSafeName,
                this.context.EnvironmentSafeName,
                this.context.EntityModel,
                nameof(UpdateEntity));
            return allFields;
        }

        public void AddEntity(ref Entity newEntity, DataRow row, string userName, FileImportConfigurationModel configurationData, List<FieldType> mandatoryFieldTypes, List<Field> revisions)
        {

            if (newEntity.Fields.Count == 0)
            {
                row["ImportStatus"] = ImportState.ImportError;
                var importError = new ImportError() { GeneralError = $"Fields for entity did not have values to add." };
                row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                return;
            }

            var serverLanguages = this.dataContext.GetAllLanguages();
            foreach (var field in newEntity.Fields.Where(f => f.FieldType.DataType == DataType.LocaleString && !Utility.StringIsInriverExpression(f.FieldType.ExpressionSupport, f.Data as string)))
            {
                var fieldLocaleString = field.Data as LocaleString ?? new LocaleString();

                // Remove non-server languages
                foreach (var ci in fieldLocaleString.Languages)
                {
                    if (!serverLanguages.Contains(ci))
                    {
                        fieldLocaleString.RemoveCulture(ci);
                    }
                }

                // Ensure all server languages are present in the LocaleString
                foreach (var serverLanguage in serverLanguages)
                {
                    if (!fieldLocaleString.ContainsCulture(serverLanguage))
                    {
                        fieldLocaleString[serverLanguage] = string.Empty;
                    }
                }

                field.Data = fieldLocaleString;
            }

            if (configurationData.RunServerExtensions)
            {
                try
                {
                    CancelUpdateArgument arg = new CancelUpdateArgument();

                    ExtensionManager manager = new ExtensionManager(this.context);
                    newEntity = manager.CallServerExtensionsForEntity(newEntity, ExtensionEvent.OnAdd, arg);

                    if (arg.Cancel)
                    {
                        if (string.IsNullOrEmpty(arg.Message))
                        {
                            context.Log(LogLevel.Verbose, $"Import Excel - Event was cancelled by the extension. No reason specified. BatchId: {row["batchId"].ToString()}");
                        }
                        else
                        {
                            context.Log(LogLevel.Verbose, $"Import Excel - Event was cancelled by the extension. BatchId: {row["batchId"].ToString()}" + arg.Message);
                        }
                        row["ImportStatus"] = ImportState.ImportError;
                        var importError = new ImportError() { GeneralError = $"Server Extension Canceled Update. Message:  {arg.Message}" };
                        row["ImportMessage"] = JsonConvert.SerializeObject(importError);

                        return;
                    }


                }
                catch (Exception ex)
                {
                    this.context.Log(LogLevel.Error, $"Error processing Import Batch: BatchId: {row["batchId"].ToString()}", ex);
                    row["ImportStatus"] = ImportState.ImportError;
                    var importError = new ImportError() { GeneralError = $"Error calling Server extensions.  Error: {ex.Message}" };
                    row["ImportMessage"] = JsonConvert.SerializeObject(importError);

                    return;
                }
            }

            newEntity.DisplayName = newEntity.Fields.FirstOrDefault(f => f.FieldType.IsDisplayName);
            newEntity.DisplayDescription = newEntity.Fields.FirstOrDefault(f => f.FieldType.IsDisplayDescription);
            newEntity.CreatedBy = userName;
            newEntity.ModifiedBy = userName;

            // If new entity be sure that all mandatory fields are present
            var errorMessages = new StringBuilder();
            var validationError = new ImportError();
            if (!this.AreMandatoryFieldsPresent(newEntity, mandatoryFieldTypes, ref validationError, errorMessages))
            {
                row["ImportStatus"] = ImportState.ImportError;
                var importError = new ImportError() { GeneralError = errorMessages.ToString() };
                row["ImportMessage"] = JsonConvert.SerializeObject(importError);
                return;

            }

            try
            {
                if (newEntity.Fields.Any(x => x.FieldType.ExpressionSupport))
                {
                    var missingFieldTypes = newEntity.EntityType.FieldTypes.Except(newEntity.Fields.Select(x => x.FieldType));
                    foreach (var ft in missingFieldTypes)
                    {
                        newEntity.Fields.Add(new Field
                        {
                            Data = null,
                            EntityId = newEntity.Id,
                            FieldType = ft,
                        });
                    }
                }

                var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
                var expressions = expressionWrapper.CreateDefaultExpressionsAndEvaluate(newEntity, newEntity.EntityType.FieldTypes);

                var upsertEntityDurationMetricStopwatch = Stopwatch.StartNew();
                var result = this.dataContext.AddEntities(new List<Entity>() { newEntity });
                upsertEntityDurationMetricStopwatch.Stop();

                if (result.Count > 0)
                {
                    expressionWrapper.SaveExpressionsForEntity(result[0].Id, expressions);
                }

                revisions.AddRange(newEntity.Fields);
                revisions.AddRange(expressionWrapper.Revisions);

                UpsertEntityDurationMetric<ExcelImportRepository>.TrackValue(
                    upsertEntityDurationMetricStopwatch.ElapsedMilliseconds,
                    newEntity,
                    this.context.CustomerSafeName,
                    this.context.EnvironmentSafeName,
                    this.context.EntityModel,
                    nameof(AddEntity));
                row["ImportStatus"] = ImportState.Imported;
                row["ImportMessage"] = "";
            }
            catch (Exception e)
            {
                row["ImportStatus"] = ImportState.ImportError;
                var importError = new ImportError() { GeneralError = $"Exception thrown adding entity. Message: {e.Message}" };
                row["ImportMessage"] = JsonConvert.SerializeObject(importError);
            }
        }

        public void PostProcessImport(ref BlockingCollection<ImportedEntity> queue, string userName, string batchId)
        {
            string origUser = context.Username;
            try
            {
                context.Username = userName;
                var user = dataContext.GetUserByUsername(userName);
                context.Roles = user.Roles.Select(role => role.Name).ToList();
                context.Permissions = user.Permissions.Select(permission => permission.Name).ToList();

                this.context.Log(LogLevel.Information, $"Excel import batch - {batchId}  - Initializing data");
                // First Get the configuration Model and convert from string to Model
                string configurationJsonData = GetExcelModel(userName, batchId, true);
                var configurationData =
                    JsonConvert.DeserializeObject<inRiver.Core.Models.inRiver.ExcelImport.FileImportConfigurationModel>(
                        configurationJsonData);

                while (!queue.IsCompleted)
                {

                     if (!queue.TryTake(out var importedEntity, Timeout.InfiniteTimeSpan))
                    {
                        continue;
                    }

                    try
                    {

                        var dtoEntity = DtoFactory.DtoFromEntity(importedEntity.Entity);

                        if (configurationData.RunPostProcessing)
                        {
                            if (importedEntity.Entity.EntityType.Id == "Resource")
                            {
                                try
                                {
                                    this.dataContext.ReCalculateEntityMainPicture(importedEntity.Entity.Id,
                                        "Resource");
                                }
                                catch (Exception e)
                                {
                                    throw new Exception(
                                        $"Failed to recalculate main picture for entity {importedEntity.Entity.Id}: " +
                                        e.Message);
                                }
                            }


                            try
                            {
                                this.dataContext.ReCalculateDisplayValuesForEntity(importedEntity.Entity.Id,
                                    importedEntity.Entity.Fields);
                            }
                            catch (Exception e)
                            {
                                throw new Exception(
                                    $"Failed to recalculate display fields for entity {importedEntity.Entity.Id}: " +
                                    e.Message);
                            }

                            if (importedEntity.Entity.EntityType.Id.Equals("Channel") ||
                                importedEntity.Entity.EntityType.Id.Equals("Publication"))
                            {
                                this.dataContext.ReloadChannel(importedEntity.Entity.Id);
                            }

                            try
                            {
                                new CompletenessRepository(this.context).SetEntityCompleteness(dtoEntity);
                            }
                            catch (Exception e)
                            {
                                throw new Exception(
                                    $"Failed to set entity completeness for entity {importedEntity.Entity.Id}: " +
                                    e.Message);
                            }
                        }

                        if (configurationData.RunEntityListeners)
                        {
                            try
                            {
                                if (importedEntity.IsNew)
                                {
                                    EventPublisher.NotifyEntityAdded(this.context, importedEntity.Entity);
                                }
                                else
                                {
                                    List<string> updatedFieldIds = (from f in importedEntity.UpdatedFields select f.FieldType.Id).ToList();
                                    if (updatedFieldIds != null && updatedFieldIds.Count > 0)
                                    {
                                        EventPublisher.NotifyEntityUpdated(this.context, importedEntity.Entity.Id, importedEntity.Entity.EntityType.Id, string.Join(",", updatedFieldIds.ToArray()));
                                    }

                                    if (importedEntity.FieldSetChanged)
                                    {
                                        EventPublisher.NotifyFieldSetUpdated(this.context, importedEntity.Entity.Id, importedEntity.Entity.EntityType.Id, importedEntity.Entity.FieldSetId);
                                    }

                                    if (importedEntity.SegmentChanged)
                                    {
                                        EventPublisher.NotifySegmentationChanged(this.context, importedEntity.Entity.Id, importedEntity.PreviousSegmentId);
                                    }

                                }
                            }
                            catch (Exception e)
                            {
                                throw new Exception(
                                    $"Failed to run entity listeners for entity {importedEntity.Entity.Id}: " +
                                    e.Message);
                            }
                        }

                        if (configurationData.RunChannelListeners)
                        {
                            try
                            {
                                if (!importedEntity.IsNew)
                                {
                                    var channelRepository = new ChannelRepository(this.context);

                                    channelRepository.UpdateEntity(importedEntity.Entity.Id, importedEntity.UpdatedFields);

                                    if (importedEntity.FieldSetChanged)
                                        channelRepository.UpdateFieldsetForEntity(importedEntity.Entity.Id, importedEntity.Entity.FieldSetId);

                                }
                            }
                            catch (Exception e)
                            {
                                throw new Exception(
                                    $"Failed to run channel listeners for entity {importedEntity.Entity.Id}: " +
                                    e.Message);
                            }
                        }

                        if (configurationData.RunPostProcessing)
                        {
                            try
                            {
                                new SearchManager(this.context).UpdateQuickSearchIndex(dtoEntity, context);
                            }
                            catch (Exception e)
                            {
                                throw new Exception(
                                    $"Failed to add to search index for entity {importedEntity.Entity.Id}: " +
                                    e.Message);
                            }

                            if (!new DataRepository(this.context).CreateContributeEntity(new int[]
                                {importedEntity.Entity.Id}))
                            {
                                throw new Exception(
                                    $"Failed to create contribute entity for entity {importedEntity.Entity.Id}.");
                            }

                            // set post process status as Processed
                            this.SetPostProcessingStatus(importedEntity.InRiverImportColumnId,
                                ImportState.PostProcessed.ToString(), "");

                            importedEntity = null;
                        }
                    }
                    catch (Exception e)
                    {
                        // set post process status as error and set a post message
                        this.SetPostProcessingStatus(importedEntity.InRiverImportColumnId,
                            ImportState.PostProcessingError.ToString(), e.Message);
                        this.context.Log(LogLevel.Error,
                            $"Error post processing Import Batch: {userName}, {batchId}", e);
                    }
                }
            }
            catch (Exception ex)
            {
                this.context.Log(LogLevel.Error, $"Error processing Import Batch: {userName}, {batchId}", ex);
                throw;
            }
            finally
            {
                context.Username = origUser;
            }
        }

        private static bool HasReadonlyRestriction(Dictionary<string, RestrictedFieldPermission> fieldRestrictionDictionary, FieldType fieldType)
        {
            return fieldRestrictionDictionary != null &&
                fieldRestrictionDictionary.ContainsKey(fieldType.Id) &&
                fieldRestrictionDictionary[fieldType.Id] != null &&
                !string.IsNullOrEmpty(fieldRestrictionDictionary[fieldType.Id].RestrictionType) &&
                fieldRestrictionDictionary[fieldType.Id].RestrictionType.Equals("Readonly", StringComparison.OrdinalIgnoreCase);
        }

        private string GetColumnData(Dictionary<string, string> entityFieldValuePairs, string columnName)
        {
            if (!entityFieldValuePairs.ContainsKey(columnName) || entityFieldValuePairs[columnName] == null || string.IsNullOrWhiteSpace(entityFieldValuePairs[columnName]))
            {
                return string.Empty;
            }

            return entityFieldValuePairs[columnName].ToString();
        }

        private Entity CreateEntityFromValuePairs(
            Dictionary<string, string> entityFieldValuePairs,
            ref Entity newEntity,
            EntityType entityType,
            User user,
            FileImportConfigurationModel configuration,
            List<CultureInfo> languages,
            Dictionary<string, CVL> cvlDictionary,
            Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary,
            Dictionary<string, string> segmentExpressions
            )
        {

            try
            {

                List<Field> fieldsToImport = new List<Field>();
                newEntity.Segment = new Segment() { Id = UnsetSegmentId };

                CultureInfo currentCultureInfo = !string.IsNullOrEmpty(configuration.CurrentLanguage) ? new CultureInfo(configuration.CurrentLanguage) : null;


                foreach (var columnModel in configuration.Columns)
                {
                    if (string.IsNullOrEmpty(columnModel.ColumnName))
                    {
                        continue;
                    }
                    var columnData = GetColumnData(entityFieldValuePairs, columnModel.ColumnName);
                    if (string.Equals(columnModel.ColumnName, FileImportFieldModel.SysFieldSetFieldType, StringComparison.OrdinalIgnoreCase))
                    {
                        newEntity.FieldSetId = columnData;

                        continue;
                    }

                    if (string.Equals(columnModel.ColumnName, SysSegmentIdColumnName, StringComparison.OrdinalIgnoreCase))
                    {
                        if (columnData.EndsWith(".0"))
                        {
                            columnData = columnData.Substring(0, columnData.Length - 2);
                        }
                        if (int.TryParse(columnData, out var segmentId))
                        {
                            newEntity.Segment = new Segment() { Id = segmentId };
                        }
                        if (columnData.Trim().Equals("auto", StringComparison.OrdinalIgnoreCase) && segmentExpressions.ContainsKey(entityType.Id))
                        {
                            newEntity.Segment = new Segment() { Id = EvaluatedSegmentId };
                        }
                        else if (configuration.ClearEmptyValues && string.IsNullOrWhiteSpace(columnData))
                        {
                            newEntity.Segment = new Segment() { Id = configuration.DefaultSegmentId };
                        }

                        continue;
                    }


                    Field field = new Field();
                    field.EntityId = newEntity.Id;
                    field.FieldType = entityType.FieldTypes.FirstOrDefault(
                        ft => ft.Id.Equals(columnModel.FieldTypeId, StringComparison.InvariantCultureIgnoreCase));
                    field.Revision = 0;

                    if (field.FieldType == null || field.FieldType.ReadOnly)
                    {
                        continue;
                    }

                    var columnDataNoEscape = Regex.Replace(columnData, @"^'(\s*?=.*)", "$1");
                    if (Utility.StringIsInriverExpression(field.FieldType.ExpressionSupport, columnDataNoEscape))
                    {
                        field.Data = columnDataNoEscape;
                    }
                    else if (field.FieldType.DataType == DataType.LocaleString)
                    {
                        if (columnData == null)
                        {
                            continue;
                        }

                        var localeStringField = fieldsToImport.FirstOrDefault(f => f.FieldType.Id == field.FieldType.Id);

                        LocaleString locale;

                        if (localeStringField != null)
                        {
                            if (Utility.StringIsInriverExpression(field.FieldType.ExpressionSupport, localeStringField.Data as string))
                            {
                                continue; // Expression is already added - no need to process individual locales
                            }

                            locale = localeStringField.Data as LocaleString;

                            if (locale == null)
                            {
                                locale = new LocaleString();
                            }

                            string localeValue = GetLocaleStringValue(columnData, columnModel.LanguageCode);

                            locale[new CultureInfo(columnModel.LanguageCode)] = localeValue;

                            continue;
                        }
                        else
                        {
                            locale = new LocaleString();

                            string localeValue = GetLocaleStringValue(columnData, columnModel.LanguageCode);

                            locale[new CultureInfo(columnModel.LanguageCode)] = localeValue;
                        }

                        field.Data = locale;
                    }
                    else if (field.FieldType.DataType == DataType.CVL)
                    {
                        if (field.FieldType.Multivalue)
                        {
                            columnData = columnData?.ToCleanSeparator() ?? string.Empty;
                            string[] values = columnData?.ToString().Split(SemicolonSeparator) ?? Array.Empty<string>();
                            values = GetValues(field.FieldType, values, user.CultureInfo, cvlValueDictionaryDictionary);
                            field.Data = string.Join(SemicolonSeparator, values);
                        }
                        else
                        {
                            field.Data = GetValue(field.FieldType, columnData, user.CultureInfo, cvlValueDictionaryDictionary);
                        }
                    }
                    else
                    {
                        field.Data = ConvertStringDataToObjectData(columnData, field.FieldType, currentCultureInfo);
                    }

                    fieldsToImport.Add(field);
                }

                newEntity.Fields = fieldsToImport;
            }
            catch (Exception ex)
            {
                newEntity = null;
            }

            return newEntity;
        }

        private string CleanCvlValue(string originalValue)
        {
            originalValue = originalValue.Replace("\r\n", "");
            originalValue = originalValue.Replace("\"", "");
            originalValue = originalValue.Replace(",", SemicolonSeparator);

            return originalValue;
        }

        public static object ConvertStringDataToObjectData(object data, FieldType fieldType, CultureInfo currentCultureInfo)
        {
            if (data == null)
            {
                return null;
            }

            if (data.ToString() == string.Empty)
            {
                return null;
            }

            if (fieldType.DataType == DataType.Integer || fieldType.DataType == DataType.File)
            {
                double tempValue;

                // Note: Json converts int to have a decimal so we need to check if the
                // the decimal is a whole number
                if (double.TryParse(data.ToString(), out tempValue))
                {
                    if (tempValue % 1 == 0)
                    {
                        if (tempValue < int.MaxValue)
                            return (int)tempValue;
                        else
                        {
                            return data;
                        }
                    }
                }
                else
                {
                    return data;
                }

            }

            if (fieldType.DataType == DataType.CVL)
            {
                if (data.GetType() != typeof(JArray))
                {
                    return data;
                }

                var valArray = ((System.Collections.IEnumerable)data).Cast<object>().ToArray();
                return valArray.Length == 0 ? null : string.Join(SemicolonSeparator, valArray);  // treat empty array as null...
            }

            if (fieldType.DataType == DataType.Double)
            {
                double value;

                if (double.TryParse(data.ToString().Replace(",", "."), NumberStyles.Any, CultureInfo.InvariantCulture, out value))
                {
                    return value;
                }

                return data;
            }

            if (fieldType.DataType == DataType.Boolean)
            {
                bool value;

                if (bool.TryParse(data.ToString(), out value))
                {
                    return value;
                }

                if (data.ToString() == "1")
                {
                    return true;
                }

                if (data.ToString() == "0")
                {
                    return false;
                }

                return data;
            }

            if (fieldType.DataType == DataType.DateTime)
            {
                DateTime value;

                double fromOaDate;
                double.TryParse(data.ToString(), out fromOaDate);

                if (fromOaDate > 0)
                {
                    return DateTime.FromOADate(fromOaDate);
                }

                if (currentCultureInfo != null)
                {
                    try
                    {
                        return DateTime.Parse(data.ToString(), currentCultureInfo);
                    }
                    catch (Exception e)
                    {
                        return data;
                    }

                }
                else
                {
                    if (DateTime.TryParse(data.ToString(), out value))
                    {
                        return value;
                    }

                }

                return data;
            }

            return data;
        }

        private string GetValue(
            FieldType fieldType,
            string columndata,
            CultureInfo ci,
            IReadOnlyDictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary)
        {
            if (string.IsNullOrEmpty(columndata))
            {
                return string.Empty;
            }

            if (fieldType.DataType != DataType.CVL)
            {
                return columndata;
            }

            if (!cvlValueDictionaryDictionary.ContainsKey(fieldType.CVLId))
            {
                return string.Empty;
            }

            var cvlValueDictionary = cvlValueDictionaryDictionary[fieldType.CVLId];

            return GetCvlKey(fieldType.CVLId, columndata.Trim(), ci, cvlValueDictionary);
        }

        private string[] GetValues(FieldType fieldType,
            string[] columndata,
            CultureInfo ci,
            IReadOnlyDictionary<string, Dictionary<string, CVLValue>> cvlValueDictionaryDictionary)
        {
            var values = new string[columndata.Length];

            for (var i = 0; i < values.Length; i++)
            {
                values[i] = GetValue(fieldType, columndata[i], ci, cvlValueDictionaryDictionary);
            }

            return values;
        }

        public string GetCvlKey(
            string cvlId,
            string cvlKey,
            CultureInfo cultureInfo,
            IReadOnlyDictionary<string, CVLValue> cvlValueDictionary)
        {
            if (cvlValueDictionary == null)
            {
                return $"[{cvlKey}]";
            }

            string key;
            //Excel cvl import data might be in the following format: "cvlKey | cvlValue"
            if (cvlKey.Contains("|"))
            {
                var keyData = cvlKey.Split('|')[0].Trim();

                key = cvlValueDictionary.SingleOrDefault(cvl =>
                    cvl.Key.Equals(keyData, StringComparison.CurrentCultureIgnoreCase)).Key;
            }
            else
            {
                key = cvlValueDictionary.SingleOrDefault(cvl =>
                cvl.Key.Equals(cvlKey, StringComparison.CurrentCultureIgnoreCase)).Key;
            }

            if (key == null)
            {
                return $"[{cvlKey}]";
            }

            var cvlValue = cvlValueDictionary[key];

            return cvlValue == null ? $"[{cvlKey}]" : cvlValue.Key;
        }

        private static string GetLocaleStringValue(string value, string language)
        {
            if (value == null)
            {
                return string.Empty;
            }

            try
            {
                LocaleString locale = JsonConvert.DeserializeObject<LocaleString>(value);

                return locale[new CultureInfo(language)];
            }
            catch
            {
                return value;
            }
        }

        private Entity FindExistingEntity(Dictionary<string, string> entityFieldValuePair, inRiver.Core.Models.inRiver.ExcelImport.FileImportConfigurationModel configuration, List<EntityType> entityTypes,
            List<FieldType> uniqueFields, ref ImportError validationError)
        {
            DtoEntity entity = null;

            try
            {
                if (entityFieldValuePair.ContainsKey(SysIdColumnName) && entityFieldValuePair[SysIdColumnName] != null &&
                    !string.IsNullOrEmpty(entityFieldValuePair[SysIdColumnName].ToString()) && entityFieldValuePair[SysIdColumnName].ToString() != EmptyEntityId)
                {
                    int entityId;

                    if (int.TryParse(entityFieldValuePair[SysIdColumnName].ToString(), out entityId))
                    {
                        if (entityId != 0)
                            try
                            {
                                entity = context.DataPersistance.GetEntity(entityId);
                                if (entity != null)
                                    entity.Fields = context.DataPersistance.GetFieldsForEntity(entity);

                            }
                            catch (Exception e)
                            {
                                validationError.ColumnErrors.Add(SysIdColumnName, "Sys_Id does not match existing entity.");
                            }
                    }

                }

                foreach (var uniqueField in uniqueFields)
                {
                    var column =
                        configuration.Columns.FirstOrDefault(fieldType => fieldType.FieldTypeId == uniqueField.Id);

                    if (column != null && entityFieldValuePair.ContainsKey(column.ColumnName) && entityFieldValuePair[column.ColumnName] != null &&
                        !string.IsNullOrEmpty(entityFieldValuePair[column.ColumnName].ToString()))
                    {
                        int? id = this.dataContext.GetEntityIdByUniqueValue(column.FieldTypeId,
                            entityFieldValuePair[column.ColumnName].ToString());

                        if (id != null)
                        {
                            if (entity == null)
                            {
                                entity = context.DataPersistance.GetEntity(id.Value);
                                if (entity != null)
                                    entity.Fields = context.DataPersistance.GetFieldsForEntity(entity);

                            }
                            else if (id != entity.Id)
                                validationError.ColumnErrors.Add(uniqueField.Id, "Unique field is associated with different entity.");
                        }


                    }
                }

            }
            catch (Exception ex)
            {
                this.context.Log(LogLevel.Error, "Exception finding existing Entity", ex);
                return null;
            }

            return entity == null ? null : DtoFactory.EntityFromDto(entity, entityTypes, null);
        }

        private void SetDefaultFields(Entity importEntity, List<FieldType> defaultFieldTypes, FileImportConfigurationModel configurationModel)
        {

            if (importEntity.Segment.Id == UnsetSegmentId)
            {
                importEntity.Segment.Id = configurationModel.DefaultSegmentId;
            }

            foreach (var defaultFieldType in defaultFieldTypes)
            {
                var importField = importEntity.Fields.FirstOrDefault(
                    field => field.FieldType.Id.Equals(
                        defaultFieldType.Id, StringComparison.InvariantCultureIgnoreCase));

                if (importField == null)
                {
                    Field field = new Field
                    {
                        EntityId = importEntity.Id,
                        FieldType = defaultFieldType,
                        Revision = 0,
                        Data = this.GetDefaultValue(defaultFieldType)
                    };
                    importEntity.Fields.Add(field);
                }
                else if (importField.Data == null || string.IsNullOrEmpty(importField.Data.ToString()))
                {
                    importField.Data = this.GetDefaultValue(defaultFieldType);
                }

            }
        }

        private string GetDefaultValue(FieldType fieldType)
        {
            var defaultValue = fieldType.DefaultValue;
            if (fieldType.DataType == DataType.String
                && defaultValue.Equals(Entity.NewGuidPlaceholder, StringComparison.OrdinalIgnoreCase))
            {
                return Guid.NewGuid().ToString();
            }

            return defaultValue;
        }

        private bool AreMandatoryFieldsPresent(Entity importEntity, List<FieldType> mandatoryFieldTypes, ref ImportError validationError, StringBuilder errorMessages)
        {
            bool isValid = true;

            foreach (var mandatoryField in mandatoryFieldTypes)
            {
                var importField = importEntity.Fields.Where(
                    field => field.FieldType.Id.Equals(mandatoryField.Id, StringComparison.InvariantCultureIgnoreCase)).ToList();


                if (importField.Count == 0 || importField[0].Data == null || string.IsNullOrWhiteSpace(importField[0].Data.ToString()))
                {
                    validationError.ColumnErrors.Add(mandatoryField.Id, "Mandatory field is is not present.");
                    errorMessages.AppendLine($"{mandatoryField.Id} - mandatory field is not present.");
                    isValid = false;
                }
            }

            return isValid;
        }

        private void GetPermissionsForSegments(User user, ref HashSet<int> addSegments, ref HashSet<int> updateSegments, ref HashSet<int> changeSegments)
        {

            // Get all the content segmentation that are being updated
            var segments = user.GetSegments();

            updateSegments = new HashSet<int>();
            addSegments = new HashSet<int>();
            changeSegments = new HashSet<int>();

            // Loop through all the content Segments and make sure user has access to them
            foreach (var contentSegment in segments)
            {
                var userPermissionsForSegment = user.GetPermissionsForSegmentId(contentSegment.Id);
                foreach (var permission in userPermissionsForSegment)
                {
                    if (permission.Name == "ChangeEntitySegment" && !changeSegments.Contains(contentSegment.Id))
                    {
                        changeSegments.Add(contentSegment.Id);
                    }
                    if (permission.Name == "AddEntity" && !addSegments.Contains(contentSegment.Id))
                    {
                        addSegments.Add(contentSegment.Id);
                    }
                    if (permission.Name == "UpdateEntity" && !updateSegments.Contains(contentSegment.Id))
                    {
                        updateSegments.Add(contentSegment.Id);
                    }
                }
            }
        }

        private static void RemoveForbiddenCharactersFromRow(Dictionary<string, string> fieldValuesWithForbiddenChars, IDictionary<string, string> row)
        {
            foreach (var (key, value) in fieldValuesWithForbiddenChars)
            {
                var containedForbiddenCharacters = IllegalCharacters.ControlCharacters.FindAll(x => value.Contains(x));
                var temporaryValue = value;

                foreach (var character in containedForbiddenCharacters)
                {
                    row[key] = temporaryValue.Replace(character.ToString(), string.Empty);

                    temporaryValue = row[key];
                }
            }
        }

        private static IEnumerable<Field> GetMatchingFieldsInEntity(IEnumerable<FieldType> fieldTypes, Entity entity)
        {
            var fieldTypeIds = fieldTypes.Select(x => x.Id);
            var matchingFields = entity.Fields
                .Where(field => fieldTypeIds.Any(id => id == field.FieldType.Id));

            return matchingFields;
        }

        private static void CompleteEntityWithFieldsIfMissing(Entity updatedEntity, IEnumerable<Field> fields)
        {
            foreach (var mandatoryField in fields)
            {
                if (!updatedEntity.Fields.Exists(field => field.FieldType.Id == mandatoryField.FieldType.Id))
                {
                    updatedEntity.Fields.Add((Field)mandatoryField.Clone());
                }
            }
        }

        #endregion
    }
}
