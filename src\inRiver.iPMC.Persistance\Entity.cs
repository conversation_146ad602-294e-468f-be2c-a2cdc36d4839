namespace inRiver.iPMC.Persistance
{
    using System;
    using System.Collections.Generic;

    public class Entity
    {
        public int Id { get; set; }

        public int Version { get; set; }

        public int ChangeSet { get; set; }

        public EntityType EntityType { get; set; }

        private string _entityTypeId;

        public string EntityTypeId
        {
            get
            {
                var retVal = (string.IsNullOrEmpty(_entityTypeId) && EntityType != null) ? EntityType.Id : _entityTypeId;
                return retVal;
            }
            set => _entityTypeId = value;
        }

        public string Locked { get; set; }

        public List<Field> Fields { get; set; }

        public List<Link> Links { get; set; }

        public DateTime DateCreated { get; set; }

        public string CreatedBy { get; set; }

        public string ModifiedBy { get; set; }

        public string FieldSetId { get; set; }

        public DateTime LastModified { get; set; }

        public LoadLevel LoadLevel { get; set; }

        public int? MainPictureId { get; set; }

        public string MainPictureUrl { get; set; }

        public Field DisplayName { get; set; }

        public Field DisplayDescription { get; set; }

        public int? Completeness { get; set; }

        public Segment Segment { get; set; } = new Segment();
    }
}
