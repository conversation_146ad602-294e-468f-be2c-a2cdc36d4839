namespace LongRunningJob.Core
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Models.inRiver;
    using LongRunningJob.Core.Abstractions;
    using LongRunningJob.Core.Models;
    using Serilog;

    public class JobRunner : IJobRunner
    {
        private readonly ILongRunningJobRepository longRunningJobRepository;
        private readonly ICommandDispatcher<Result> commandDispatcher;
        private readonly ICommandFactory commandFactory;

        public JobRunner(ILongRunningJobRepository longRunningJobRepository, ICommandDispatcher<Result> commandDispatcher, ICommandFactory commandFactory)
        {
            this.longRunningJobRepository = longRunningJobRepository;
            this.commandDispatcher = commandDispatcher;
            this.commandFactory = commandFactory;
        }

        public async Task<bool> RunAsync(LongRunningJob job, CancellationToken cancellationToken)
        {
            Log.Information("Running LRJ");
            try
            {
                var command = this.commandFactory.CreateCommand(job.JobType, job);

                var result = await this.commandDispatcher.DispatchAsync(command, cancellationToken);

                await this.longRunningJobRepository.UpdateStateAsync(job.Id, result.JobState);

                return IsSuccessfulJobState(result.JobState);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error when running LRJ");

                await this.SetJobStateToErrorAsync(job);

                return false;
            }
        }

        private static bool IsSuccessfulJobState(string jobState) => jobState == LongRunningJobsStatus.Finished || jobState == LongRunningJobsStatus.FinishedWithErrors;

        private async Task SetJobStateToErrorAsync(LongRunningJob job)
        {
            try
            {
                await this.longRunningJobRepository.UpdateStateAsync(job.Id, LongRunningJobsStatus.Error);
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Could not set job state to Error after job failed");
            }
        }
    }
}
