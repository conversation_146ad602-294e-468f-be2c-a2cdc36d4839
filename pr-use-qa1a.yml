name: $(Date:yy.MM.dd)$(Rev:.r)-$(Build.SourceBranchName)-PRDeploy
resources:
  repositories:
    - repository: templates
      type: git
      name: SRE/sre_pipeline_templates
      ref: 'refs/heads/main'

trigger:
  - none
  
variables: 
  - name: deploy_stage
    value: 'pmc2-use-qa1a'

stages:
  - template: templates/azure-pipelines-prdeploy.yml
    parameters:
      deploy_stage: ${{ variables.deploy_stage }}

    