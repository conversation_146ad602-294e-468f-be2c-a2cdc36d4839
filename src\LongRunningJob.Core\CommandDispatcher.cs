namespace LongRunningJob.Core
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using LongRunningJob.Core.Abstractions;

    public class CommandDispatcher<TResult> : ICommandDispatcher<TResult>
        where TResult : IResult
    {
        private readonly Dictionary<Type, Func<ICommand, CancellationToken, Task<TResult>>> handlers;

        public CommandDispatcher()
        {
            this.handlers = new Dictionary<Type, Func<ICommand, CancellationToken, Task<TResult>>>();
        }

        public ICommandDispatcher<TResult> RegisterHandler<TCommand>(ICommandHandler<TCommand, TResult> handler)
            where TCommand : ICommand
        {
            Task<TResult> Func(ICommand command, CancellationToken cancellationToken) => handler.HandleAsync((TCommand)command, cancellationToken);

            this.handlers.Add(typeof(TCommand), Func);

            return this;
        }

        public Task<TResult> DispatchAsync<TCommand>(TCommand command, CancellationToken cancellationToken)
            where TCommand : ICommand =>
            !this.handlers.TryGetValue(command.GetType(), out var handler)
                ? throw new NotSupportedException($"No handler registered for {command.GetType().Name}")
                : handler(command, cancellationToken);
    }
}
