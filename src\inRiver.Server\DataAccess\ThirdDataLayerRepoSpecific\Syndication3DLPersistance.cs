namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using System.Collections.Generic;
    using System.Linq;

    /// <summary>
    /// Syndication operations for 3DL.
    /// Because this isn't used in any other repo it resides in this folder to avoid being overwritten
    /// during 3DL update procedures.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public IDictionary<int, IList<iPMC.Persistance.SyndicationRelatedEntityFieldValue>> GetRelatedEntityFieldsForEntity(
            int entityId,
            string fieldTypeId,
            string[] linkTypeIds,
            int[] entityLinkPosition,
            string language)
        {
            var result = InRiverDataApiClient.GetRelatedEntitiesSyndicate(this.GetAuthInfo(), entityId, fieldTypeId, linkTypeIds, entityLinkPosition, language);
            var convertedResult = new Dictionary<int, IList<iPMC.Persistance.SyndicationRelatedEntityFieldValue>>();
            foreach (var entry in result)
            {
                convertedResult.Add(
                    entry.Key,
                    entry.Value.Select(x => new iPMC.Persistance.SyndicationRelatedEntityFieldValue { EntityId = x.EntityId, FieldValue = x.FieldValue }).ToList());
            }

            return convertedResult;
        }
    }
}
