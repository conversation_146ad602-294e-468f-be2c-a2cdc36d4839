namespace inRiver.Configuration.Core.Service
{
    using Microsoft.Azure.KeyVault;
    using Microsoft.Azure.Services.AppAuthentication;
    using System.Threading.Tasks;
    using System;

    public static class KeyVaultSecretReader
    {
        public static async Task<string> GetSecretValueAsync(string keyVaultBaseUrl, string secretName)
        {
            if (string.IsNullOrEmpty(keyVaultBaseUrl))
            {
                throw new ArgumentNullException(nameof(keyVaultBaseUrl),
                    $"{nameof(keyVaultBaseUrl)} must have a value.");
            }

            if (string.IsNullOrEmpty(secretName))
            {
                throw new ArgumentNullException(nameof(secretName), $"{nameof(secretName)} must have a value.");
            }

            var provider = new AzureServiceTokenProvider();
            var keyVaultClient =
                new KeyVaultClient(new KeyVaultClient.AuthenticationCallback(provider.KeyVaultTokenCallback));

            var secret = await keyVaultClient.GetSecretAsync(keyVaultBaseUrl, secretName);

            return secret.Value ?? throw new InvalidOperationException($"{secretName} not found.");
        }
    }
}
