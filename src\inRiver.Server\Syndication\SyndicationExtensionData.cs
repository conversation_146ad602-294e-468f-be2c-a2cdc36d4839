namespace inRiver.Server.Syndication
{
    using System.Collections.Generic;

    public class SyndicationExtensionData
    {
        public string JsonData { get; set; }

        public string SyndicationName { get; set; }

        public int LongRunningJobId { get; set; }

        public int NumberOfEntities { get; set; }

        public IDictionary<string, string> Settings { get; set; }

        public bool EnableCompression { get; set; }

        public IDictionary<string, object> Metadata { get; set; }
    }
}
