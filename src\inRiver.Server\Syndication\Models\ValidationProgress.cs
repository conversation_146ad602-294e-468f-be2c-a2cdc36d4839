namespace inRiver.Server.Syndication.Models
{
    using System;

    public class ValidationProgress
    {
        public int TotalBatches { get; set; }

        public int CurrentBatch { get; set; }

        public int TotalEntities { get; set; }

        public int ProcessedEntities { get; set; }

        public DateTime LastUpdate { get; set; }
        
        public int BatchSize { get; set; }

        public string Status { get; set; }
    }
}
