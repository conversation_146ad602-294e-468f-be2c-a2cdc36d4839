<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Platforms>x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="inRiver.StackEssentials" Version="6.0.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="6.0.0" />
    <PackageReference Include="Polly" Version="7.2.3" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Core.EnvironmentSettings\inRiver.Core.EnvironmentSettings.csproj" />
    <ProjectReference Include="..\inRiver.Core.Redis\inRiver.Core.Redis.csproj" />
    <ProjectReference Include="..\inRiver.Core\inRiver.Core.csproj" />
    <ProjectReference Include="..\inRiver.Server\inRiver.Server.csproj" />
  </ItemGroup>

</Project>
