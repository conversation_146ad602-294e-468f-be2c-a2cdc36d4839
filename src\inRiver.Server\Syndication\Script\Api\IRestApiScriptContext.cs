namespace inRiver.Server.Syndication.Script.Api
{
    public interface IRestApiScriptContext
    {
        string RestApiGet(string endpointAlias, string requestPath = null, object cacheExpiryTimeMinutes = null);

        string RestApiDelete(string endpointAlias, string requestPath = null, object cacheExpiryTimeMinutes = null);

        string RestApiPost(string endpointAlias, string requestPath = null, object body = null, object cacheExpiryTimeMinutes = null);

        string RestApiPut(string endpointAlias, string requestPath = null, object body = null, object cacheExpiryTimeMinutes = null);

        string RestApiPatch(string endpointAlias, string requestPath = null, object body = null, object cacheExpiryTimeMinutes = null);
    }
}
