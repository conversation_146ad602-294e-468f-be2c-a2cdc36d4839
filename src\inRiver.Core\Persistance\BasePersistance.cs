namespace inRiver.Core.Persistance
{
    using System;
    using inRiver.Core.Models;
    using inRiver.Core.Util;
    using inRiver.Log;

    public abstract class BasePersistance
    {
        private readonly ICommonLogging logInstance;
        private readonly ApiCaller caller;

        protected BasePersistance(ICommonLogging logInstance, ApiCaller caller)
        {
            this.logInstance = logInstance;
            this.caller = caller;
            this.LogHelper = new CommonLogHelper(this);
        }

        public ICommonLogging GetCommonLogging()
        {
            if (this.logInstance == null)
                throw new NullReferenceException("ICommonLogging hasn't been set.");

            return this.logInstance;
        }

        public ApiCaller GetApiCaller()
        {
            if (this.caller == null)
                throw new NullReferenceException("ApiCaller hasn't been set.");

            return this.caller;
        }

        public CommonLogHelper LogHelper { get; }
    }
}
