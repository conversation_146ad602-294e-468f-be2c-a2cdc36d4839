<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Dapper" version="2.1.35" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.20.0" />
    <PackageReference Include="Inriver.Expressions.Client" Version="3.1.6" />
    <PackageReference Include="inRiver.Log" version="2.1.1" />
    <PackageReference Include="inRiver.Remoting.iPMC" version="8.19.2" />
    <PackageReference Include="inRiver.StackEssentials" version="6.0.0" />
    <PackageReference Include="LazyCache" Version="2.4.0" />
    <PackageReference Include="Microsoft.Azure.Storage.Blob" Version="9.4.0" />
    <PackageReference Include="Microsoft.ClearScript.V8" version="7.1.7" />
    <PackageReference Include="Microsoft.ClearScript.V8.Native.win-x64" Version="7.1.7" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" version="12.0.2" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="Sendgrid" version="9.1.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Threading.Tasks.Dataflow" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Configuration.Core\inRiver.Configuration.Core.csproj" />
    <ProjectReference Include="..\inRiver.Core\inRiver.Core.csproj" />
    <ProjectReference Include="..\inRiver.Utility.Query\inRiver.Utility.Query.csproj" />
    <ProjectReference Include="..\Telemetry.Metrics\Telemetry.Metrics.csproj" />
    <ProjectReference Include="..\Telemetry\Telemetry.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Util\IllegalCharacters\" />
  </ItemGroup>

</Project>
