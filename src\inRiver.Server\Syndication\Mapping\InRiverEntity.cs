namespace inRiver.Server.Syndication.Mapping
{
    using System.Collections.Generic;

    public class InRiverEntity
    {
        public int? ParentId { get; set; }

        public string EntityTypeId { get; set; }

        public int Id { get; set; }

        public int? MainPictureId { get; set; }

        public string DisplayName { get; set; }

        public string FieldSetId { get; set; }

        public List<InRiverField> Fields { get; set; }

        public List<InRiverEntity> RelatedEntities { get; set; }
    }
}
