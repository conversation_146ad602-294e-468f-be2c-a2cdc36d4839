﻿namespace inRiver.iPMC.Persistance
{
    public class LinkType
    {
        public string Id { get; set; }

        public string SourceEntityTypeId { get; set; }

        public LocaleString SourceName { get; set; }

        public string TargetEntityTypeId { get; set; }

        public LocaleString TargetName { get; set; }

        public string LinkEntityTypeId { get; set; }

        public int Index { get; set; }

        public override string ToString() => string.IsNullOrWhiteSpace(this.Id) ? base.ToString() : this.Id;
    }
}
