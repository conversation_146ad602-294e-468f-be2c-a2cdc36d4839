namespace inRiver.Server.Repository
{
    using System.Collections.Generic;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.Request;

    public class UserRepository
    {
        private readonly IDataPersistance dataContext;

        private readonly RequestContext context;

        public UserRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;
            this.context = context;
        }

        public User GetUserByUsername(string username)
        {
            return string.IsNullOrEmpty(username) ? null : this.dataContext.GetUserByUsername(username);
        }

        public Dictionary<string, string> GetAllUserSettings(string username)
        {
            if (string.IsNullOrEmpty(username))
            {
                context.Log(LogLevel.Warning, "Trying to get all user settings with missing user name");
                throw ErrorUtility.GetArgumentException("GetAllUserSettings", "username", "Trying to get all user settings with missing user name");
            }

            User user = this.dataContext.GetShallowUser(username);

            if (user == null)
            {
                context.Log(LogLevel.Warning, "Trying to get all user settings for non existing user");
                throw ErrorUtility.GetArgumentException("GetAllUserSettings", "username", "Trying to get all user settings for non existing user");
            }

            return this.dataContext.GetAllUserSettings(user.Id);
        }
    }
}
