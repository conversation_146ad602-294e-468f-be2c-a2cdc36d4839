# Introduction 
TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project. 

# Getting Started
TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:
1.	Installation process
2.	Software dependencies
3.	Latest releases
4.	API references

# Build and Test
TODO: Describe and show how to build your code and run the tests. 

# Contribute
TODO: Explain how other users and developers can contribute to make your code better. 

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)

# Metrics
## _requestDuration_
The metric is responsible for the execution time of the actor method. RequestDuration contains custom dimensions:
* Dimension.Environment - contains the customer and environment name in the format "{this.customer}/{this.environment}".
* Dimension.Method - contains the method name for which the execution time is measured. 
* Dimension.JobId - contains the ID of a long running job. 
* Dimension.Success - contains the status of the actor method execution. Enumeration. Possible values: Failed, CustomerFailure, Success. 
> Note: Dimension.Success is also responsible for the successful completion of the entire Long Running job for the following job types: SynchronizeChannel, RebuildChannel, CalculateCompleteness, ExcelExport, ExcelImport, ExcelExportHistory, MassUpdate.
For the RunSyndicate job type, Dimension.Success is responsible for only part of the syndication: getting the data, applying the transformations and exporting the resources. The second part of the syndication is done in the ProcessSyndicationOutputExtensionAsync method in Connect solution.

The metric starts timing in the OnPreActorMethodAsync method overload and saves the metric's value in the OnPostActorMethodAsync method overload.

# **Debug syndication job locally without deploying LRJ application to Service Fabric**
The 'LongRunningJobService.Api project allows to simplify the process of local development and debugging of the Syndication job code without deploying all dependent projects, such as Portal, Portal2UI, Token, Connect, since this consumes a lot of CPU and RAM resources locally.

## Prerequisites
* Ensure your VPN is active

## Run 'LongRunningJobService.Api' project
1. Set 'LongRunningJobService.Api' as startup project
2. Select 'Debug' configuration, 'x64' platform and run the project with 'Development-LocalEuw' or 'Development-LocalUse' profile
> To run the project once with default settings ('Debug', 'x64', 'Development-LocalEuw'), select 'Debug/Start new instance' for 'LongRunningJobService.Api' project

## Send request
```bash
curl --location 'http://localhost:50664/api/LongRunningJobDev/RunSyndicate/localcustomer1/euwodl1/<EMAIL>' \
--header 'Content-Type: application/json' \
--data '{
    "MappingId": 16,
    "ExtensionId": "testCalculationsJson",
    "WorkareaId": "b3095ee1-855f-42d4-9eb5-d8b15c4cf3f6",
    "Id": 1,
    "Name": "Syndicate name",
    "WorkareaName": "Workarea name",
    "MappingName": "Mapping name",
    "EnableSKU": false,
    "IsResourceExportEnabled": false,
    "DisableResourceExportLimitPreCheck": false
}'
```
## Example of request with enabled preview
* LongRunningJob executes the code to validate and create preview data, which can be inspected during debug. The request itself only returns Ok or BadRequest response
```bash
curl --location 'http://localhost:50664/api/LongRunningJobDev/RunSyndicate/localcustomer1/euwndl1/<EMAIL>' \
--header 'Content-Type: application/json' \
--data '{
    "Id": 110,
    "Name": "86430Test",
    "EntityIds": [],
    "WorkareaId": "829c249b-52d2-4c83-8f88-a678317cb850",
    "WorkareaName": "86430Test",
    "MappingId": 183,
    "MappingName": "84630Test",
    "ImageUrl": null,
    "ExtensionId": "CSV",
    "ExtensionDisplayName": "InRiver Csv",
    "OutputFormat": "Csv",
    "DeliveryMethods": null,
    "LinkEntityTypeId": null,
    "EnableSKU": false,
    "CollectionId": null,
    "IsResourceExportEnabled": false,
    "DisableResourceExportLimitPreCheck": false,
    "IsPreviewEnabled": true,
    "RunPreview": true,
    "ChannelId": null
}'
```

> 'customerSafeName', 'environmentSafeName', 'username' in the URL parameters, as well as 'MappingId', 'ExtensionId' and 'WorkareaId' in the request body must be existing values, since they are used to retrieve data from the DB. The remaining data is used to form the job metadata object and can be any values, given the data type.

## Notes
* The Connect must be deployed to the local cluster for the entire syndication job to complete successfully. Otherwise, after the LRJ part is completed, the job will have the "Running" status, which will prevent the next job from being started for the same user.
To avoid this, deploy the Connect to the local cluster or Change the syndication job status in the customer database (pmceuwlocalsql01.database.windows.net -> customer data base -> LongRunningJob table) from 'Running' to 'Finished' or 'Cancelled'
```sql
DELETE FROM [LongRunningJob] WHERE [JobType] = 'RunSyndicate' AND [State] = 'Running'
```
