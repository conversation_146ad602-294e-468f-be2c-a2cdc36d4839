namespace inRiver.Core.EnvironmentSettings
{
    using inRiver.Configuration.Core.Repository;
    using inRiver.Server.Request;
    using Telemetry.Logging;

    public class RepositoryFactory
    {
        public static EnvironmentSettingsRepository GetEnvironmentSettingsRepository(string nameOfService, string userName, RequestContext context)
        {
            Models.ApiCaller apiCaller = new Models.ApiCaller { Module = nameOfService, Username = userName };

            EnvironmentSettingsRepository environmentSettingsRepository = new EnvironmentSettingsRepository(
                new inRiver.Configuration.Core.Persistance.EnvironmentSettingsPersistance(
                    context.ConfigurationConnectionString,
                    context.ReadOnlyConfigDatabaseConnectionString,
                    SerilogCommonLogger.Instance,
                    apiCaller),
                apiCaller);


            return environmentSettingsRepository;
        }
    }
}
