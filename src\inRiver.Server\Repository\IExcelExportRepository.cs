namespace inRiver.Server.Repository
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Remoting.Objects;

    public interface IExcelExportRepository
    {
        Task<bool> BatchExportExcelAsync(ExcelExportModel excelExportModel, int batchSize, CancellationToken cancellationToken);
        EntityType GetExportMainEntityType(List<ExcelExportEntityTypeModel> entityTypeModels);
        void ExcelExportHistory(ExcelExportHistoryModel excelExportHistoryModel, string fileName, CancellationToken cancellationToken);
    }
}
