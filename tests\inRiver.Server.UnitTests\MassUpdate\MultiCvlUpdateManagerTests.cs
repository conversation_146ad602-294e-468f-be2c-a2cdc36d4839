namespace inRiver.Server.UnitTests.MassUpdate
{
    using System;
    using FakeItEasy;
    using FluentAssertions;
    using inRiver.Core.Enum;
    using inRiver.Server.MassUpdate;
    using inRiver.Server.Repository;
    using Xunit;

    public class MultiCvlUpdateManagerTests
    {
        private const int EntityId = 1;

        private const string FieldTypeId = "fieldTypeId";

        private const string NewValue = "value1";

        [Fact]
        public void CalculateMultiCvlValue_UpdateApproachIsNull_ThrowsArgumentException()
        {
            // Arrange
            var multiCvlUpdateManager = A.Fake<MultiCvlUpdateManager>();

            // Act
            Func<string> act = () => multiCvlUpdateManager.CalculateMultiCvlValue(null, NewValue, EntityId, FieldTypeId);

            // Assert
            act.Should().Throw<ArgumentException>();
        }

        [Fact]
        public void CalculateMultiCvlValue_UpdateApproachIsNotDefinedValue_ThrowsArgumentException()
        {
            // Arrange
            var multiCvlUpdateManager = A.Fake<MultiCvlUpdateManager>();

            // Act
            Func<string> act = () => multiCvlUpdateManager.CalculateMultiCvlValue((UpdateApproach)int.MaxValue, NewValue, EntityId, FieldTypeId);

            // Assert
            act.Should()
                .Throw<ArgumentException>()
                .WithMessage("Unsupported UpdateApproach.*")
                .And.ParamName.Should().Be("updateApproach");
        }

        [Theory]
        [InlineData(UpdateApproach.Append, "value1;value3", "value0", "value0;value1;value3")]
        [InlineData(UpdateApproach.Append, "value1;value3", "value3", "value1;value3")]
        [InlineData(UpdateApproach.Remove, "value2", "value1;value2;value3", "value1;value3")]
        [InlineData(UpdateApproach.Remove, "value5", "value1;value2;value3", "value1;value2;value3")]
        [InlineData(UpdateApproach.Replace, "value1;value2;value3", "value2", "value1;value2;value3")]
        public void CalculateMultiCvlValue_ShouldReturnCorrectCalculatedValues(UpdateApproach updateApproach, string newValues, string oldValues, string result)
        {
            // Arrange
            var fieldRepository = A.Fake<IFieldRepository>();
            A.CallTo(() => fieldRepository.GetFieldValue(EntityId, FieldTypeId))
                .Returns(oldValues);
            var multiCvlUpdateManager = A.Fake<MultiCvlUpdateManager>(
                options => options.WithArgumentsForConstructor(
                    () => new MultiCvlUpdateManager(fieldRepository)));

            // Act
            var calculatedValue = multiCvlUpdateManager.CalculateMultiCvlValue(updateApproach, newValues, EntityId, FieldTypeId);

            // Assert
            calculatedValue.Should().Be(result);
        }
    }
}
