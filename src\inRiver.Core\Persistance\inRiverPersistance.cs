namespace inRiver.Core.Persistance
{
    using System;
    using System.Data;
    using System.Data.SqlClient;
    using System.Globalization;
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Objects;
    using inRiver.Core.Persistance.Interfaces;
    using inRiver.Log;
    using Field = Models.inRiver.Field;

    // ReSharper disable once InconsistentNaming
    public partial class inRiverPersistance
        : BaseCorePersistance,
            IinRiverPersistance,
            IJobPersistance
    {
        private const string InternalDateTimeFormat = "yyyy-MM-dd HH:mm:ss";

        private readonly string connectionString;

        public int EnvironmentId { get; private set; }

        /**
         * The use of this constructor should be restricted.
         * Please use inRiver.Core.iPMCPersistanceFactory.GetInstance() to get an instance of IinRiverPersistance.
         * Note: the signature of this constructor has slightly been changed from the original one to catch any
         * unintended usage of this constructor during inRiver.iPMC.Persistance merging from/to master
         */
        internal inRiverPersistance(string connectionString, ICommonLogging logInstance, ApiCaller caller, int environmentId, bool signatureChanged)
            : base(connectionString, logInstance, caller)
        {
            this.EnvironmentId = environmentId;
            this.connectionString = connectionString;
        }

        public Field GetField(int entityId, string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return null;
            }

            Field field = null;

            using (var connection = new SqlConnection(this.connectionString))
            {
                try
                {
                    var command = connection.CreateCommand();

                    command.CommandText =
                        @"SELECT Value, LastModified, Revision, DateCreated, DataType FROM ViewAllEntityFields WHERE Id = @EntityId AND FieldTypeId = @FieldTypeId";
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            field = new Field { FieldTypeId = fieldTypeId, EntityId = entityId };


                            if (!reader.IsDBNull(0))
                            {
                                field.Data = reader.GetString(0);
                            }

                            field.LastModified =
                                reader.GetDateTime(!reader.IsDBNull(1) ? 1 : 3)
                                    .ToString(InternalDateTimeFormat, CultureInfo.InvariantCulture);

                            field.Revision = !reader.IsDBNull(2) ? reader.GetInt32(2) : 0;

                            field.DataType = reader.GetString(4);

                            if (reader.GetString(4) == DataType.DateTime && !string.IsNullOrEmpty(field.Data))
                            {
                                field.Data = Convert.ToDateTime(field.Data).ToString(InternalDateTimeFormat);
                            }
                        }
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    this.LogHelper.UnexpectedError(ex);

                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    throw;
                }
            }

            return field;
        }

        public FieldType AddFieldType(FieldType fieldType, bool generateIndex)
            => throw new NotImplementedException();

        public EntityType AddEntityType(EntityType entityType)
            => throw new NotImplementedException();

        public EntityType UpdateEntityType(EntityType entityType)
            => throw new NotImplementedException();

        public bool DeleteEntityType(string id)
            => throw new NotImplementedException();

        public bool DeleteAllEntityTypes()
            => throw new NotImplementedException();

        public FieldType UpdateFieldType(FieldType fieldType)
            => throw new NotImplementedException();

        public FieldSet AddFieldSet(FieldSet fieldSet)
            => throw new NotImplementedException();

        public FieldSet UpdateFieldSet(FieldSet fieldSet)
            => throw new NotImplementedException();

        public bool DeleteFieldSet(string id)
            => throw new NotImplementedException();

        public bool DeleteAllFieldSets()
            => throw new NotImplementedException();

        public bool DeleteFieldTypeFromFieldSet(string fieldSetId, string fieldTypeId)
            => throw new NotImplementedException();

        public bool AddFieldTypeToFieldSet(string fieldSetId, string fieldTypeId)
            => throw new NotImplementedException();

        public bool AddLanguage(string name)
            => throw new NotImplementedException();

        public bool DeleteAllLanguages()
            => throw new NotImplementedException();

        public bool DeleteLanguage(string name)
            => throw new NotImplementedException();

        public LinkType AddLinkType(LinkType linkType)
            => throw new NotImplementedException();

        public bool DeleteAllLinkTypes()
            => throw new NotImplementedException();

        public bool DeleteLinkType(string id)
            => throw new NotImplementedException();

        public LinkType UpdateLinkType(LinkType linkType)
            => throw new NotImplementedException();
    }
}
