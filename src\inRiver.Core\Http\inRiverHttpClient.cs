namespace inRiver.Core.Http
{
    using System;
    using System.Net.Http;
    using Newtonsoft.Json;

    public class inRiverHttpClient : HttpClient, IHttpClient
    {
        public HttpResponseMessage Get(string requestUri)
        {
            return this.GetAsync(requestUri).Result;
        }

        public T Get<T>(string requestUri)
        {
            var response = this.Get(requestUri);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(response.ReasonPhrase);
            }

            return JsonConvert.DeserializeObject<T>(response.Content.ReadAsStringAsync().Result);
        }

        public HttpResponseMessage Delete(string requestUrl)
        {
            return this.DeleteAsync(requestUrl).Result;
        }

        public HttpResponseMessage PutAsJson<T>(string requestUrl, T value)
        {
            return this.PutAsJsonAsync(requestUrl, value).Result;
        }

        public HttpResponseMessage Send(HttpRequestMessage request)
        {
            return this.SendAsync(request).Result;
        }
    }
}
