namespace inRiver.Server.Completeness.Criteria
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using inRiver.Remoting.Extension;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;

    public class ContainsValueCriteria
    {
        private readonly RequestContext context;

        public ContainsValueCriteria(RequestContext context)
        {
            this.context = context;
        }

        private const string FieldTypeIdKey = "FieldTypeId";
        private const string FieldValueKey = "FieldValue";

        public inRiverContext Context { get; set; }

        public string Name => "Contains Value";

        public List<string> SettingsKeys => new List<string> { FieldTypeIdKey, FieldValueKey };

        public int GetCriteriaCompletenessPercentage(int entityId, List<CompletenessRuleSetting> settings)
        {
            CompletenessRuleSetting fieldTypeSetting = settings.FirstOrDefault(s => s.Key == FieldTypeIdKey);
            CompletenessRuleSetting fieldValueSetting = settings.FirstOrDefault(s => s.Key == FieldValueKey);

            if (fieldTypeSetting == null)
            {
                return 0;
            }

            if (string.IsNullOrEmpty(fieldValueSetting?.Value))
            {
                return 0;
            }

            string fieldTypeId = fieldTypeSetting.Value;

            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return 0;
            }

            object data = this.context.DataPersistance.GetFieldValue(entityId, fieldTypeId);

            if (data == null)
            {
                return 0;
            }

            if (data.ToString().Contains(fieldValueSetting.Value))
            {
                return 100;
            }

            return 0;
        }

        public async Task<int> GetCriteriaCompletenessPercentageAsync(int entityId, IEnumerable<CompletenessRuleSetting> settings)
        {
            var fieldTypeSetting = settings.FirstOrDefault(s => s.Key == FieldTypeIdKey);
            var fieldValueSetting = settings.FirstOrDefault(s => s.Key == FieldValueKey);

            if (fieldTypeSetting == null)
            {
                return 0;
            }

            if (string.IsNullOrEmpty(fieldValueSetting?.Value))
            {
                return 0;
            }

            var fieldTypeId = fieldTypeSetting.Value;

            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return 0;
            }

            var data = await this.context.DataPersistance.GetFieldValueAsync(entityId, fieldTypeId);

            if (data == null)
            {
                return 0;
            }

            if (data.ToString().Contains(fieldValueSetting.Value))
            {
                return 100;
            }

            return 0;
        }
    }
}
