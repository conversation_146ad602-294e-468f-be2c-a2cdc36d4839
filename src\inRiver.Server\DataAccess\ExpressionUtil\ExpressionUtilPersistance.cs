﻿namespace inRiver.Server.DataAccess.ExpressionUtil
{
    using System.Collections.Generic;
    using System.Data.SqlClient;
    using inRiver.Server.Request;

    public static class ExpressionUtilPersistance
    {
        public static HashSet<int> GetExpressionEnabledEnvironments(RequestContext context)
        {
            using (var connection = new SqlConnection(context.ReadOnlyConfigDatabaseConnectionString))
            {
                var result = new HashSet<int>();
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT env.[Id], es.[Name], es.[Value]
                        FROM [dbo].[Environment] env
                        INNER JOIN [dbo].[EnvironmentSettings] es ON es.[EnvironmentId] = env.[Id] OR es.[EnvironmentId] IS NULL
                        WHERE es.[Name] = 'EXPRESSIONS_ENABLED' AND es.[Value] = 'true'";
                    var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        result.Add(reader.GetInt32(0));
                    }
                }

                return result;
            }
        }
    }
}
