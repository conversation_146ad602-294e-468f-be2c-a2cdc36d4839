<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IsServiceFabricServiceProject>True</IsServiceFabricServiceProject>
    <ServerGarbageCollection>True</ServerGarbageCollection>
    <RuntimeIdentifier>win7-x64</RuntimeIdentifier>
    <TargetLatestRuntimePatch>False</TargetLatestRuntimePatch>
    <UpdateServiceFabricManifestEnabled>True</UpdateServiceFabricManifestEnabled>
    <ServicePackagePath>PackageRoot</ServicePackagePath>
    <ServicePackagePrefix>$(MSBuildProjectName)</ServicePackagePrefix>
    <FabActUtilWorkingDir>$(BaseIntermediateOutputPath)\FabActUtilTemp</FabActUtilWorkingDir>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release;Development-LocalUse;Development-LocalEuw</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.ServiceFabric.Actors" Version="6.1.1653" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="3.1.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Core.EnvironmentSettings\inRiver.Core.EnvironmentSettings.csproj" />
    <ProjectReference Include="..\LongRunningJob.Core\LongRunningJob.Core.csproj" />
    <ProjectReference Include="..\LongRunningJobActor.Interfaces\LongRunningJobActor.Interfaces.csproj" />
  </ItemGroup>

</Project>
