namespace inRiver.Core.Util
{
    using System.Collections.Generic;
    using System.Linq;

    public class Utilities
    {
        public static int SqlBatchSize => 500;

        public static List<List<T>> SplitListIntoBatches<T>(List<T> collection)
        {
            return SplitListIntoBatches(collection, SqlBatchSize);
        }

        public static List<List<T>> SplitListIntoBatches<T>(List<T> collection, int batchSize)
        {
            var chunks = new List<List<T>>();
            var chunkCount = collection.Count() / batchSize;

            if (collection.Count % batchSize > 0)
            {
                chunkCount++;
            }

            for (var i = 0; i < chunkCount; i++)
            {
                chunks.Add(collection.Skip(i * batchSize).Take(batchSize).ToList());
            }

            return chunks;
        }
    }
}
