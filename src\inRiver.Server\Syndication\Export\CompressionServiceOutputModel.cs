namespace inRiver.Server.Syndication.Export
{
    using System;

    /// <summary>
    ///     Output model with the results of the compression service.
    /// </summary>
    public class CompressionServiceOutputModel
    {
        /// <summary>
        ///     Name of compressed file.
        /// </summary>
        public string ZipFileName { get; set; }

        /// <summary>
        ///     Size of compressed file in bytes.
        /// </summary>
        public long ZipFileSize { get; set; }

        /// <summary>
        ///     Number of files within a compressed file.
        /// </summary>
        public int TotalZipFiles { get; set; }

        /// <summary>
        ///     The number of files that were not downloaded during compression.
        /// </summary>
        public int NotDownloadedFiles { get; set; }

        /// <summary>
        ///     Compression time in seconds.
        /// </summary>
        public int ElapsedTimeInSeconds { get; set; }

        /// <summary>
        ///     Creation date of the compressed file.
        /// </summary>
        public DateTimeOffset? Created { get; set; }
    }
}
