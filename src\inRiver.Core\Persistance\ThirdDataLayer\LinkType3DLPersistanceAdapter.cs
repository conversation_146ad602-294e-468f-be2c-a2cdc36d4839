﻿namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Core.Models.inRiver;

    /// <summary>
    /// LinkType overrides.
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        public override LinkType AddLinkType(LinkType linkType)
        {
            var result = this._origInRiverPersistance.AddLinkType(linkType);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool DeleteAllLinkTypes()
        {
            var result = this._origInRiverPersistance.DeleteAllLinkTypes();
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override bool DeleteLinkType(string id)
        {
            var result = this._origInRiverPersistance.DeleteLinkType(id);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }

        public override LinkType UpdateLinkType(LinkType linkType)
        {
            var result = this._origInRiverPersistance.UpdateLinkType(linkType);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return result;
        }
    }
}
