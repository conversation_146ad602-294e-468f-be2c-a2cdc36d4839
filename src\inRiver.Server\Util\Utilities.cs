namespace inRiver.Server.Util
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Text;
    using System.Xml;
    using inRiver.Remoting.Objects;

    internal class Utilities
    {
        internal static LocaleString XmlToLocaleString(string xml, List<CultureInfo> serverLanguages)
        {
            if (string.IsNullOrWhiteSpace(xml))
            {
                return new LocaleString();
            }

            if (serverLanguages == null)
            {
                return new LocaleString();
            }

            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var selectNodes = doc.DocumentElement?.SelectNodes("value");
            if (selectNodes != null && (doc.DocumentElement != null && selectNodes.Count == 0))
            {
                return new LocaleString();
            }

            LocaleString localeString = new LocaleString(serverLanguages);

            if (doc.DocumentElement == null)
            {
                return localeString;
            }

            foreach (XmlNode node in doc.DocumentElement.ChildNodes)
            {
                if (string.IsNullOrWhiteSpace(node.Attributes?["language"]?.Value))
                {
                    continue;
                }

                CultureInfo ci = new CultureInfo(node.Attributes["language"].Value);

                if (!serverLanguages.Contains(ci))
                {
                    continue;
                }

                localeString[ci] = node.InnerText;
            }

            return localeString;
        }

        internal static LocaleString XmlToLocaleString(string xml)
        {
            if (string.IsNullOrWhiteSpace(xml))
            {
                return new LocaleString();
            }

            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var selectNodes = doc.DocumentElement?.SelectNodes("value");
            if (selectNodes != null && (doc.DocumentElement != null && selectNodes.Count == 0))
            {
                return new LocaleString();
            }

            LocaleString localeString = new LocaleString();

            if (doc.DocumentElement == null)
            {
                return localeString;
            }

            foreach (XmlNode node in doc.DocumentElement.ChildNodes)
            {
                if (string.IsNullOrWhiteSpace(node.Attributes?["language"]?.Value))
                {
                    continue;
                }

                CultureInfo ci = new CultureInfo(node.Attributes["language"].Value);

                localeString[ci] = node.InnerText;
            }

            return localeString;
        }

        internal static string EntityToMinimalXml(Entity entity)
        {
            if (entity == null)
            {
                return string.Empty;
            }

            StringBuilder resultXml = new StringBuilder();

            resultXml.Append("<Entity><Type>");
            resultXml.Append(entity.EntityType.Id + "</Type><Id>");
            resultXml.Append(entity.Id + "</Id><Locked>");
            resultXml.Append(entity.Locked + "</Locked><Version>");
            resultXml.Append(entity.Version + "</Version><LoadLevel>");
            resultXml.Append((int)entity.LoadLevel + "</LoadLevel><Created>");
            resultXml.Append(entity.DateCreated.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) + "</Created><LastModified>");
            resultXml.Append(entity.LastModified.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) + "</LastModified><ChangeSet>");
            resultXml.Append(entity.ChangeSet + "</ChangeSet><FieldSetId>");
            resultXml.Append(entity.FieldSetId + "</FieldSetId><ModifiedBy>");
            resultXml.Append(entity.ModifiedBy + "</ModifiedBy><CreatedBy>");
            resultXml.Append(entity.CreatedBy + "</CreatedBy><MainPicture>");
            resultXml.Append(entity.MainPictureId + "</MainPicture>");

            if (entity.Completeness.HasValue)
            {
                resultXml.Append("<Completeness>" + entity.Completeness.Value + "</Completeness>");
            }

            resultXml.Append("<DisplayName>");

            if (entity.DisplayName != null)
            {
                resultXml.Append(entity.DisplayName.ToXml() + "</DisplayName>");
            }
            else
            {
                resultXml.Append("</DisplayName>");
            }

            resultXml.Append("<DisplayDescription>");

            if (entity.DisplayDescription != null)
            {
                resultXml.Append(entity.DisplayDescription.ToXml() + "</DisplayDescription>");
            }
            else
            {
                resultXml.Append("</DisplayDescription>");
            }

            if (entity.LoadLevel == LoadLevel.Shallow)
            {
                resultXml.Append("<Fields /><Links /></Entity>");

                return resultXml.ToString();
            }

            resultXml.Append("<Fields>");

            foreach (Field field in entity.Fields)
            {
                if (field.IsEmpty() && field.Revision == 0)
                {
                    continue;
                }

                string fieldXml = field.ToXml();

                resultXml.Append(fieldXml);
            }

            resultXml.Append("</Fields>");

            if (entity.LoadLevel == LoadLevel.DataOnly)
            {
                resultXml.Append("<Links /></Entity>");

                return resultXml.ToString();
            }

            resultXml.Append("<Links>");

            foreach (Link link in entity.Links)
            {
                string linkXml = LinkToXml(link);

                resultXml.Append(linkXml);
            }

            resultXml.Append("</Links></Entity>");

            return resultXml.ToString();
        }

        internal static string LinkToXml(Link link)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("<Link><Type>");
            sb.Append(link.LinkType.Id + "</Type><Id>");
            sb.Append(link.Id + "</Id><Index>");
            sb.Append(link.Index + "</Index><Inactive>");
            sb.Append(link.Inactive + "</Inactive><Source>");
            sb.Append(EntityToMinimalXml(link.Source) + "</Source><Target>");
            sb.Append(EntityToMinimalXml(link.Target) + "</Target>");

            if (link.LinkEntity != null)
            {
                sb.Append("<LinkEntity>");
                sb.Append(EntityToMinimalXml(link.LinkEntity));
                sb.Append("</LinkEntity>");
            }

            sb.Append("</Link>");

            return sb.ToString();
        }
    }
}
