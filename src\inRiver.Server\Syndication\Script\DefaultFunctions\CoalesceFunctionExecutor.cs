namespace inRiver.Server.Syndication.Script.DefaultFunctions
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using inRiver.Core.Util;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using Newtonsoft.Json;

    public class CoalesceFunctionExecutor
    {
        public CoalesceFunctionExecutor(
            TransformationManager transformationManager,
            FoundField foundField,
            InRiverEntity entity,
            object mainValue,
            bool enableSku,
            SkuInjector skuInjector,
            string skuId,
            IEnumerable<CultureInfo> serverLanguages,
            RequestContext context)
        {
            this.TransformationManager = transformationManager;
            this.FoundField = foundField;
            this.Entity = entity;
            this.MainValue = mainValue;
            this.EnableSku = enableSku;
            this.SkuInjector = skuInjector;
            this.SkuId = skuId;
            this.Context = context;
            this.DefaultLanguage = serverLanguages.FirstOrDefault()?.Name;
        }

        private TransformationManager TransformationManager { get; }

        private FoundField FoundField { get; }

        private InRiverEntity Entity { get; }

        private object MainValue { get; }

        private bool EnableSku { get; }

        private SkuInjector SkuInjector { get; }

        private string SkuId { get; }

        private string DefaultLanguage { get; }

        private RequestContext Context { get; }

        public string Execute()
        {
            var values = this.TransformationManager.Values;
            var resultFieldValue = this.GetCoalesceValue(values);

            return resultFieldValue;
        }

        private string GetCoalesceValue(IEnumerable<string> values)
        {
            var fields = GetSortedCoalesceFields(values);

            foreach (var field in fields)
            {
                if (field.Index == 1)
                {
                    var mainFieldValue = this.GetMainFieldValue(field);
                    if (mainFieldValue != null && !string.IsNullOrEmpty(mainFieldValue.ToString()))
                    {
                        return mainFieldValue.ToString();
                    }

                    continue;
                }

                if (field.IsConstant)
                {
                    return field.ConstantValue;
                }

                var foundInRiverField = TransformationManager.GetInRiverField(this.Entity, field.FieldTypeId);
                var fieldValue = TransformationManager.IsFieldOfSpecifiedDataType(foundInRiverField, DataType.CVL)
                    ? this.GetCvlValue(field, field.FieldTypeId, foundInRiverField?.Data)
                    : TransformationManager.GetLocaleStringValue(
                        foundInRiverField,
                        field.FieldTypeId,
                        field.Language ?? this.DefaultLanguage,
                        this.EnableSku,
                        this.SkuInjector,
                        this.SkuId);
                if (fieldValue != null && !string.IsNullOrEmpty(fieldValue.ToString()))
                {
                    return fieldValue.ToString();
                }
            }

            return string.Empty;
        }

        private object GetMainFieldValue(CoalesceFunctionField field)
        {
            var foundInRiverField = TransformationManager.GetInRiverField(this.Entity, this.FoundField?.FieldTypeId);

            if (TransformationManager.IsFieldOfSpecifiedDataType(foundInRiverField, DataType.CVL))
            {
                return this.GetCvlValue(field, this.FoundField?.FieldTypeId, this.MainValue);
            }

            return TransformationManager.IsFieldOfSpecifiedDataType(foundInRiverField, DataType.LocaleString)
                ? TransformationManager.GetLocaleStringMainValue(this.MainValue, field.Language ?? this.DefaultLanguage)
                : TransformationManager.GetTransformedMainValue(this.MainValue);
        }

        private string GetCvlValue(CoalesceFunctionField field, string fieldTypeId, object value)
        {
            var cvlId = this.GetCvlId(field.CvlId, fieldTypeId);

            if (!ExportManager.CvlValuesDictionary.TryGetValue(cvlId, out var cvlValuesDictionary))
            {
                var cvlValues = this.Context.DataPersistance.GetCVLValuesForCVL(cvlId) ?? new List<CVLValue>();
                cvlValuesDictionary = cvlValues
                    .GroupBy(cvl => cvl.Key)
                    .ToDictionary(x => x.Key, x => x.FirstOrDefault());
                _ = ExportManager.CvlValuesDictionary.TryAdd(cvlId, cvlValuesDictionary);
            }

            if (value == null || string.IsNullOrEmpty(value.ToString()))
            {
                return null;
            }

            var keys = value.ToString().Split(';');
            var values = keys.Select(key => {
                if (!cvlValuesDictionary.TryGetValue(key, out var cvlValue))
                {
                    throw new SyndicateException($"Coalesce function - Invalid CVL key: {key}, CVL id: {cvlId}.");
                }

                return cvlValue.Value is LocaleString localeString
                    ? localeString[new CultureInfo(field.Language ?? this.DefaultLanguage)]
                    : cvlValue.Value;
            }).ToList();

            return values.All(cvlValue => string.IsNullOrEmpty(cvlValue?.ToString()))
                ? string.Empty
                : string.Join(';', values.Where(x => !string.IsNullOrEmpty(x?.ToString())).Select(x => x.ToString()));
        }

        private string GetCvlId(string cvlId, string fieldTypeId)
        {
            if (!string.IsNullOrEmpty(cvlId))
            {
                return cvlId;
            }

            if (ExportManager.CvlIdsDictionary.TryGetValue(fieldTypeId, out var cvl))
            {
                return cvl;
            }

            cvl = this.Context.DataPersistance.GetCvlIdForFieldType(fieldTypeId);
            if (string.IsNullOrEmpty(cvl))
            {
                throw new SyndicateException("CVL id is not specified for field of type CVL.");
            }

            _ = ExportManager.CvlIdsDictionary.TryAdd(fieldTypeId, cvl);

            return cvl;
        }

        private static IEnumerable<CoalesceFunctionField> GetSortedCoalesceFields(IEnumerable<string> values)
        {
            var coalesceFields = values.Select(JsonConvert.DeserializeObject<CoalesceFunctionField>).ToList();

            return coalesceFields.OrderBy(x => x.Index).ToList();
        }
    }
}
