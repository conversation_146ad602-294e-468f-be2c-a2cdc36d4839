namespace inRiver.Server.DataAccess
{
    using System.Collections.Generic;
    using System.Data;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using inRiver.iPMC.Persistance;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Query;
    using inRiver.Server.Request;
    using FieldType = inRiver.Remoting.Objects.FieldType;
    using SystemQuery = inRiver.Remoting.Query.SystemQuery;

    // ReSharper disable once InconsistentNaming
    [SuppressMessage("StyleCop.CSharp.NamingRules", "SA1300:ElementMustBeginWithUpperCaseLetter", Justification = "Reviewed. Suppression is OK here.")]
    public partial class inRiverPersistance : IDataPersistance
    {
        private readonly RequestContext context;

        protected readonly IContentSegmentPermissionProvider contentSegmentProvider;

        /**
         * The use of this constructor should be restricted.
         * Please use inRiver.Server.DataAccess.IPMCServerPersistanceFactory.GetInstance() to get an instance of IDataPersistance (inRiverPersistance).
         * Note: the signature of this constructor has slightly been changed from the original one to catch any
         * unintended usage of this constructor during inRiver.iPMC.Persistance merging from/to master
         */
        internal inRiverPersistance(RequestContext context, bool signatureChanged)
        {
            this.context = context;
            this.contentSegmentProvider = new ContentSegmentPermissionProvider(context);
        }

        public string ConnectionString => this.context.ConnectionString;

        public string ConfigurationConnectionString => this.context.ConfigurationConnectionString;

        public string ReadonlyConfigConnectionString => this.context.ReadOnlyConfigDatabaseConnectionString;

        public List<int> SearchEntityBySystemQueryAndDataQuery(SystemQuery querySystemQuery, Query queryDataQuery)
        {
            throw new System.NotImplementedException();
        }

        public DataTable GetEntitiesAsTable(
            List<int> entityIds, string entityType,
            List<FieldType> fields,
            inRiver.iPMC.Persistance.ContentSegmentationEnum segmentationOption,
            CancellationToken cancellationToken)
        {
            throw new System.NotImplementedException();
        }
    }
}
