namespace inRiver.Server.Syndication.Service
{
    using System;

    public class MappingResolverService 
    {
        public static int GetMappingId(SyndicationModel syndicationModel, bool applyDsaMapping) =>
            applyDsaMapping
                ? syndicationModel.DsaMappingId ?? throw new ArgumentNullException(nameof(syndicationModel.DsaMappingId))
                : syndicationModel.MappingId;
    }
}
