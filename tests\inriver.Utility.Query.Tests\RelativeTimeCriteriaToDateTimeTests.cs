namespace inriver.Utility.Query.Tests
{
    using System;
    using System.Globalization;
    using System.Threading;
    using inRiver.Remoting.Util;
    using Xunit;

    public class RelativeTimeCriteriaToDateTimeTests : RelativeTimeCriteriaTestBase
    {
        public const int MaxAllowedDiffInSeconds = 1;

        private static readonly TestExpectation[] CorrectData =
        {
          new TestExpectation("-5-m", duration: new TimeSpan(hours: 0, minutes: -5, 0), utcOffset: new TimeSpan(hours: 1, 0, 0)),
          new TestExpectation("-1-d", new TimeSpan(days: -1, 0, 0, 0), utcOffset: new TimeSpan(hours: 12, 0, 0)),
          new TestExpectation("52-w", new TimeSpan(days: 7 * 52, 0, 0, 0), new TimeSpan(hours: -8, 0, 0)),
          new TestExpectation("99999-h", new TimeSpan(hours: 99999, 0, 0), utcOffset: new TimeSpan(hours: 1, 0, 0)),
          new TestExpectation("+600-m", duration: new TimeSpan(hours: 0, minutes: 600, seconds: 0), utcOffset: new TimeSpan(hours: 0, 0, 0)),
          new TestExpectation("-99999-w", new TimeSpan(days: -1 * 99999 * 7, hours: 0, 0, 0), utcOffset: new TimeSpan(hours: 14, 0, 0)),
          new TestExpectation("-100-y", yearDuration: -100, monthDuration: 0, utcOffset: new TimeSpan(hours: 9, 0, 0)),
          new TestExpectation("+1000-y", yearDuration: +1000, monthDuration: 0, utcOffset: new TimeSpan(hours: -10, 0, 0)),
          new TestExpectation("+12000-M", yearDuration: 0, monthDuration: 12000, utcOffset: new TimeSpan(hours: -11, 0, 0)),
          new TestExpectation("12-M", yearDuration: 0, monthDuration: 12, utcOffset: new TimeSpan(hours: 0, 0, 0)),
        };

        private static readonly TestExpectation[] CorrectDataLocalTime =
        {
          new TestExpectation("-1-w", new TimeSpan(days: -1 * 7, 0, 0, 0)),
          new TestExpectation("6-M", yearDuration: 0, monthDuration: 6),
          new TestExpectation("52-d", new TimeSpan(days: 52, 0, 0, 0)),
          new TestExpectation("99999-h", new TimeSpan(hours: 99999, 0, 0)),
          new TestExpectation("-800-y", yearDuration: -800, monthDuration: 0),
          new TestExpectation("+1000-y", yearDuration: 1000, monthDuration: 0),
          new TestExpectation("+12000-M", yearDuration: 0, monthDuration: 12000),
          new TestExpectation("-99999-w", new TimeSpan(days: -1 * 99999 * 7, 0, 0, 0)),
        };

        private static readonly TestExpectation[] CorrectSystemUtcData =
{
          new TestExpectation("-5-m", duration: new TimeSpan(hours: 0, minutes: -5, 0)),
          new TestExpectation("-1-d", new TimeSpan(days: -1, 0, 0, 0)),
          new TestExpectation("52-w", new TimeSpan(days: -7 * 52, 0, 0, 0)),
          new TestExpectation("99999-h", new TimeSpan(hours: -99999, 0, 0)),
          new TestExpectation("+600-m", duration: new TimeSpan(hours: 0, minutes: 600, seconds: 0)),
          new TestExpectation("-99999-w", new TimeSpan(days: -1 * 99999 * 7, hours: 0, 0, 0)),
          new TestExpectation("-100-y", yearDuration: -100, monthDuration: 0),
          new TestExpectation("+1000-y", yearDuration: +1000, monthDuration: 0),
          new TestExpectation("+12000-M", yearDuration: 0, monthDuration: 12000),
          new TestExpectation("12-M", yearDuration: 0, monthDuration: -12),
        };

        [Fact]
        public void When_CorrectIntervalValue_Then_ExpectedDateTimeIsReturned()
        {
            var accumulatedAssert = true;
            foreach (var testModel in CorrectData)
            {
                var timeZone = CreateTimeZone(testModel.UtcOffset);
                var returnedDateTime = TimeIntervalQueryUtil.GetDatetimeFromIntervalValue(testModel.QueryValue, timeZone);
                var expectedDateTime = testModel.ExpectedUtcDateTime();
                var diff = Math.Abs(Math.Round((expectedDateTime - returnedDateTime).TotalSeconds, 0));
                accumulatedAssert &= diff <= MaxAllowedDiffInSeconds;
            }

            Assert.True(accumulatedAssert);
        }

        [Fact]
        public void When_CorrectCriteria_Then_ExpectedDateTimeIsReturned()
        {
            var accumulatedAssert = true;
            foreach (var testModel in CorrectDataLocalTime)
            {
                var criteria = new inRiver.Remoting.Query.Criteria
                {
                    Value = testModel.QueryValue
                };
                TimeIntervalQueryUtil.GetCriteriaValueFromDatetimeInterval(criteria, useUTC: false);
                var returnedDateTime = DateTime.ParseExact((string)criteria.Value, TimeIntervalQueryUtil.DateTimeFormatString, Thread.CurrentThread.CurrentCulture);

                var expectedDateTime = testModel.ExpectedLocalDateTime();
                var diff = Math.Abs(Math.Round((returnedDateTime - expectedDateTime).TotalSeconds, 0));
                accumulatedAssert &= diff <= MaxAllowedDiffInSeconds;
            }

            Assert.True(accumulatedAssert);
        }

        [Fact]
        public void When_CorrectSystemQuery_Then_ExpectedDateTimeIsReturned()
        {
            var accumulatedAssert = true;
            foreach (var testModel in CorrectSystemUtcData)
            {
                var systemQuery = new inRiver.Remoting.Query.SystemQuery
                {
                    IntervalValueCreated = testModel.QueryValue
                };

                TimeIntervalQueryUtil.GetSystemQueryValueFromDatetimeInterval(systemQuery);
                var returnedDateTime = systemQuery.Created.Value;
                var expectedDateTime = testModel.ExpectedUtcDateTime();
                var diff = Math.Abs(Math.Round((expectedDateTime - returnedDateTime).TotalSeconds, 0));
                accumulatedAssert &= diff <= MaxAllowedDiffInSeconds;
            }

            Assert.True(accumulatedAssert);
        }

        [Theory]
        [InlineData("-6000-y", 12)]
        [InlineData("+5000-y", 11)]
        [InlineData("+13000-M", 10)]
        [InlineData("-6000-y", 9)]
        [InlineData("+5000-y", 8)]
        [InlineData("+13000-M", 7)]
        public void WhenExtremeIntervalValueThenExceptionIsThrown(string intervalQueryValue, object hoursOffsetUTC)
        {
            var timeZone = CreateTimeZone((int)hoursOffsetUTC);
            var exception = Assert.Throws<ArgumentOutOfRangeException>(() => TimeIntervalQueryUtil.GetDatetimeFromIntervalValue(intervalQueryValue, timeZone));
            Assert.NotNull(exception);
            Assert.True(exception.Message.ToLower().IndexOf("range") >= 0);
        }

        [Theory]
        [InlineData("-1w")]
        [InlineData("- 1 w")]
        [InlineData("999123d")]
        [InlineData("000000")]
        [InlineData("6days")]
        [InlineData("+4-min")]
        [InlineData("+4-M-3-d")]
        public void WhenInvalidIntervalValueThenExceptionIsThrown(string intervalQueryValue)
        {
            var exception = Assert.Throws<FormatException>(() => TimeIntervalQueryUtil.GetDatetimeFromIntervalValue(intervalQueryValue, TimeZoneInfo.Utc));

            Assert.NotNull(exception);
        }

        private static TimeZoneInfo CreateTimeZone(TimeSpan spanOffset)
        {
            var name = $"GMT{spanOffset.Hours}";
            return TimeZoneInfo.CreateCustomTimeZone(name, spanOffset, name, name);
        }

        private static TimeZoneInfo CreateTimeZone(int hoursOffset)
        {
            var name = hoursOffset > -1 ? $"GMT+{hoursOffset}" : $"GMT{hoursOffset}";
            var offset = new TimeSpan(hoursOffset, 00, 00);
            return TimeZoneInfo.CreateCustomTimeZone(name, offset, name, name);
        }
    }
}
