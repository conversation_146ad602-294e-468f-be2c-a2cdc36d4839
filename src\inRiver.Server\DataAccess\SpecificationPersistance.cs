namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Data.SqlTypes;
    using System.Globalization;
    using System.Linq;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Error;
    using Newtonsoft.Json;
    using Serilog;
    using Utilities = Util.Utilities;

    // ReSharper disable once InconsistentNaming
    public partial class inRiverPersistance
    {
        #region Specification Field Type

        public SpecificationFieldType GetSpecificationFieldType(string id)
        {
            SpecificationFieldType specificationFieldType = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "SELECT [Id], [CategoryId], [CVLId], [Mandatory], [DefaultValue], [MultiValue], [Name], [DataType], [Index], [Format], [Unit], [AdditionalData], [EntityId], [OwnerEntityId], [Disabled] FROM [ViewSpecificationFieldTypes] WHERE Id = @Id AND EntityId = OwnerEntityId";
                    command.Parameters.Add("@Id", SqlDbType.VarChar, 64).Value = id;
                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            specificationFieldType = new SpecificationFieldType();

                            specificationFieldType.Id = reader.GetString(0);
                            specificationFieldType.CategoryId = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                specificationFieldType.CVLId = reader.GetString(2);
                            }

                            specificationFieldType.Mandatory = reader.GetBoolean(3);

                            if (!reader.IsDBNull(4))
                            {
                                specificationFieldType.DefaultValue = reader.GetString(4);
                            }

                            specificationFieldType.Multivalue = reader.GetBoolean(5);

                            SqlXml xmlName = reader.GetSqlXml(6);
                            specificationFieldType.Name = Utilities.XmlToLocaleString(xmlName.Value);

                            specificationFieldType.DataType = reader.GetString(7);
                            specificationFieldType.Index = reader.GetInt32(8);

                            if (!reader.IsDBNull(9))
                            {
                                specificationFieldType.Format = reader.GetString(9);
                            }

                            if (!reader.IsDBNull(10))
                            {
                                specificationFieldType.Unit = reader.GetString(10);
                            }

                            if (!reader.IsDBNull(11))
                            {
                                specificationFieldType.AdditionalData = reader.GetString(11);
                            }

                            specificationFieldType.EntityId = reader.GetInt32(12);
                            specificationFieldType.OwnerEntityId = reader.GetInt32(13);

                            if (!reader.IsDBNull(14))
                            {
                                specificationFieldType.Disabled = reader.GetBoolean(14);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting specification field type " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting specification field type " + id, ex);
                }

                if (specificationFieldType == null)
                {
                    return null;
                }
            }

            return specificationFieldType;
        }

        public List<SpecificationFieldType> GetSpecificationFieldTypes(List<string> ids)
        {
            List<SpecificationFieldType> specificationFieldTypes = new List<SpecificationFieldType>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    string combinedIds = string.Join("','", ids.ToArray());

                    command.CommandText = "SELECT [Id], [CategoryId], [CVLId], [Mandatory], [DefaultValue], [MultiValue], [Name], [DataType], [Index], " +
                                          "[Format], [Unit], [AdditionalData], [EntityId], [OwnerEntityId], [Disabled] " +
                                          $"FROM [ViewSpecificationFieldTypes] WHERE Id IN ('{combinedIds}') AND EntityId = OwnerEntityId";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            SpecificationFieldType specificationFieldType = new SpecificationFieldType();

                            specificationFieldType.Id = reader.GetString(0);
                            specificationFieldType.CategoryId = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                specificationFieldType.CVLId = reader.GetString(2);
                            }

                            specificationFieldType.Mandatory = reader.GetBoolean(3);

                            if (!reader.IsDBNull(4))
                            {
                                specificationFieldType.DefaultValue = reader.GetString(4);
                            }

                            specificationFieldType.Multivalue = reader.GetBoolean(5);

                            SqlXml xmlName = reader.GetSqlXml(6);
                            specificationFieldType.Name = Utilities.XmlToLocaleString(xmlName.Value);

                            specificationFieldType.DataType = reader.GetString(7);
                            specificationFieldType.Index = reader.GetInt32(8);

                            if (!reader.IsDBNull(9))
                            {
                                specificationFieldType.Format = reader.GetString(9);
                            }

                            if (!reader.IsDBNull(10))
                            {
                                specificationFieldType.Unit = reader.GetString(10);
                            }

                            if (!reader.IsDBNull(11))
                            {
                                specificationFieldType.AdditionalData = reader.GetString(11);
                            }

                            specificationFieldType.EntityId = reader.GetInt32(12);
                            specificationFieldType.OwnerEntityId = reader.GetInt32(13);

                            if (!reader.IsDBNull(14))
                            {
                                specificationFieldType.Disabled = reader.GetBoolean(14);
                            }

                            specificationFieldTypes.Add(specificationFieldType);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting specification field types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting specification field types", ex);
                }

                if (!specificationFieldTypes.Any())
                {
                    return null;
                }
            }

            return specificationFieldTypes;
        }

        /// <summary>
        /// Get all Specification FieldType Ids from ImportHistoryField for suppliers ids.
        /// </summary>
        /// <param name="supplierIds"></param>
        /// <returns> List of strings represented by Specification Field Type Ids from xConnectSupplierImportHistoryField</returns>
        public List<string> GetSpecFieldTypeIdsFromImportedHistory(List<int> supplierIds)
        {
            List<string> result = new List<string>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    using (SqlCommand command = connection.CreateCommand())
                    {
                        command.CommandText =
                            "SELECT distinct field.[FieldType] as specFieldType FROM [xConnectSupplierImportHistory] supplier " +
                            "inner join [xConnectSupplierImportHistoryEntity] entity " +
                            "ON entity.BatchId = supplier.BatchId " +
                            "inner join [xConnectSupplierImportHistoryField] field " +
                            "ON entity.Id = field.ImportedEntityId " +
                            $"WHERE supplier.SupplierId IN ({string.Join(",", supplierIds)}) AND [IsSpecificationField] = 1";

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(reader.GetString(reader.GetOrdinal("specFieldType")));
                            }

                            connection.Close();
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting specification field types");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when getting specification field types", ex);
                }
            }

            return result;
        }

        /// <summary>
        /// Get all Specification FieldType Ids from ImportHistoryField for entity id.
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns> List of strings represented by Specification Field Type Ids from xConnectSupplierImportHistoryField</returns>
        public List<string> GetSpecFieldTypeIdsFromImportedHistoryByEntityId(int entityId)
        {
            List<string> result = new List<string>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    using (SqlCommand command = connection.CreateCommand())
                    {
                        command.CommandText =
                            "SELECT distinct field.[FieldType] as specFieldType FROM [xConnectSupplierImportHistory] supplier " +
                            "inner join [xConnectSupplierImportHistoryEntity] entity " +
                            "ON entity.BatchId = supplier.BatchId " +
                            "inner join [xConnectSupplierImportHistoryField] field " +
                            "ON entity.Id = field.ImportedEntityId " +
                            "WHERE entity.EntityId = @EntityId AND [IsSpecificationField] = 1";

                        command.Parameters.Add("@EntityId", SqlDbType.Int).Value = entityId;

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(reader.GetString(reader.GetOrdinal("specFieldType")));
                            }

                            connection.Close();
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting specification field types by entity id");
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when getting specification field types by entity id", ex);
                }
            }

            return result;
        }

        #endregion

        public void LinkSpecification(DtoLink link)
        {
            if (link.Source.EntityTypeId == "Specification" && link.Target.EntityTypeId == "Specification")
            {
                this.AddSpecificationInheritance(link.Source.Id, link.Target.Id);
            }
            else if (link.Target.EntityTypeId == "Specification")
            {
                this.AddSpecificationEntityMapping(link.Source.Id, link.Target.Id);
            }
        }

        public void UnlinkSpecification(DtoLink link)
        {
            if (link.Source.EntityTypeId == "Specification" && link.Target.EntityTypeId == "Specification")
            {
                this.RemoveSpecificationInheritance(link.Source.Id, link.Target.Id);
            }
            else if (link.Target.EntityTypeId == "Specification")
            {
                this.DeleteSpecificationFieldsForEntity(link.Source.Id);
            }
        }

        // Exclude all disabled fields

        #region Specification Field

        public List<SpecificationField> GetSpecificationFieldsForEntity(int entityId)
        {
            List<SpecificationField> fields = new List<SpecificationField>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, CategoryId, SpecificationEntityId, EntityId, DataType, CVLId, Mandatory, DefaultValue, MultiValue, Name, [Index], Format, Unit, AdditionalData, Value, LastModified, OverridenMandatory, OverridenIndex FROM [ViewSpecificationFields] WHERE EntityId = @EntityId";
                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            SpecificationField field = new SpecificationField();
                            field.SpecificationFieldType = new SpecificationFieldType();

                            field.SpecificationFieldType.Id = reader.GetString(0);
                            field.SpecificationFieldType.CategoryId = reader.GetString(1);
                            field.SpecificationFieldType.EntityId = reader.GetInt32(2);
                            field.SpecificationFieldType.DataType = reader.GetString(4);

                            if (!reader.IsDBNull(5))
                            {
                                field.SpecificationFieldType.CVLId = reader.GetString(5);
                            }

                            field.SpecificationFieldType.Mandatory = reader.GetBoolean(6);

                            if (!reader.IsDBNull(7))
                            {
                                field.SpecificationFieldType.DefaultValue = reader.GetString(7);
                            }

                            field.SpecificationFieldType.Multivalue = reader.GetBoolean(8);

                            SqlXml xmlName = reader.GetSqlXml(9);

                            field.SpecificationFieldType.Name = Utilities.XmlToLocaleString(xmlName.Value);

                            field.SpecificationFieldType.Index = reader.GetInt32(10);

                            if (!reader.IsDBNull(11))
                            {
                                field.SpecificationFieldType.Format = reader.GetString(11);
                            }

                            if (!reader.IsDBNull(12))
                            {
                                field.SpecificationFieldType.Unit = reader.GetString(12);
                            }

                            if (!reader.IsDBNull(13))
                            {
                                field.SpecificationFieldType.AdditionalData = reader.GetString(13);
                            }

                            field.EntityId = reader.GetInt32(3);

                            if (!reader.IsDBNull(14))
                            {
                                object value = reader.GetValue(14);

                                field.Data = this.SetFieldData(value, field.SpecificationFieldType);
                            }

                            if (!reader.IsDBNull(15))
                            {
                                field.LastModified = reader.GetDateTime(15);
                            }

                            if (!reader.IsDBNull(16))
                            {
                                field.SpecificationFieldType.Mandatory = reader.GetBoolean(16);
                            }

                            if (!reader.IsDBNull(17))
                            {
                                field.SpecificationFieldType.Index = reader.GetInt32(17);
                            }

                            field.Formatted = !string.IsNullOrEmpty(field.SpecificationFieldType.Format);

                            fields.Add(field);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting specification fields for entity");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            return fields;
        }

        public SpecificationField GetSpecificationField(int entityId, string specificationFieldTypeId)
        {
            SpecificationField field = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, CategoryId, SpecificationEntityId, EntityId, DataType, CVLId, Mandatory, DefaultValue, MultiValue, Name, [Index], Format, Unit, AdditionalData, Value, LastModified, OverridenMandatory, OverridenIndex FROM [ViewSpecificationFields] WHERE EntityId = @EntityId AND Id = @Id";
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.Add("@Id", SqlDbType.VarChar, 64).Value = specificationFieldTypeId;

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            field = new SpecificationField();
                            field.SpecificationFieldType = new SpecificationFieldType();

                            field.SpecificationFieldType.Id = reader.GetString(0);
                            field.SpecificationFieldType.CategoryId = reader.GetString(1);
                            field.SpecificationFieldType.EntityId = reader.GetInt32(2);
                            field.SpecificationFieldType.DataType = reader.GetString(4);

                            if (!reader.IsDBNull(5))
                            {
                                field.SpecificationFieldType.CVLId = reader.GetString(5);
                            }

                            field.SpecificationFieldType.Mandatory = reader.GetBoolean(6);

                            if (!reader.IsDBNull(7))
                            {
                                field.SpecificationFieldType.DefaultValue = reader.GetString(7);
                            }

                            field.SpecificationFieldType.Multivalue = reader.GetBoolean(8);

                            SqlXml xmlName = reader.GetSqlXml(9);

                            field.SpecificationFieldType.Name = Utilities.XmlToLocaleString(xmlName.Value);

                            field.SpecificationFieldType.Index = reader.GetInt32(10);

                            if (!reader.IsDBNull(11))
                            {
                                field.SpecificationFieldType.Format = reader.GetString(11);
                            }

                            if (!reader.IsDBNull(12))
                            {
                                field.SpecificationFieldType.Unit = reader.GetString(12);
                            }

                            if (!reader.IsDBNull(13))
                            {
                                field.SpecificationFieldType.AdditionalData = reader.GetString(13);
                            }

                            field.EntityId = reader.GetInt32(3);

                            if (!reader.IsDBNull(14))
                            {
                                object value = reader.GetValue(14);

                                field.Data = this.SetFieldData(value, field.SpecificationFieldType);
                            }

                            if (!reader.IsDBNull(15))
                            {
                                field.LastModified = reader.GetDateTime(15);
                            }

                            if (!reader.IsDBNull(16))
                            {
                                field.SpecificationFieldType.Mandatory = reader.GetBoolean(16);
                            }

                            if (!reader.IsDBNull(17))
                            {
                                field.SpecificationFieldType.Index = reader.GetInt32(17);
                            }

                            field.Formatted = !string.IsNullOrEmpty(field.SpecificationFieldType.Format);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting specification field " + specificationFieldTypeId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting specification field " + specificationFieldTypeId, ex);
                }
            }

            return field;
        }

        #endregion

        #region Specification Categories

        #endregion

        #region private methods

        private void AddSpecificationInheritance(int specificationEntityId, int specificationEntityOwnerId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "INSERT INTO SpecificationEntityInheritance(SpecificationEntityId, OwnerSpecificationEntityId) " +
                                            "VALUES (@SpecificationEntityId, @OwnerSpecificationEntityId); " +
                                            "INSERT INTO SpecificationEntityFieldTypeMapping ([SpecificationEntityId],[SpecificationFieldTypeId] ,[Disabled]) " +
                                            "SELECT @SpecificationEntityId, SpecificationFieldTypeId, [Disabled] " +
                                            "FROM SpecificationEntityFieldTypeMapping " +
                                            "WHERE SpecificationEntityId = @OwnerSpecificationEntityId";

                    command.Parameters.AddWithValue("@SpecificationEntityId", specificationEntityId);
                    command.Parameters.AddWithValue("@OwnerSpecificationEntityId", specificationEntityOwnerId);

                    connection.Open();

                    command.ExecuteNonQuery();

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when adding specification entity field type mapping");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when adding specification entity field type mapping", ex);
                }
            }
        }

        private void AddSpecificationEntityMapping(int entityId, int specificationEntityId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "INSERT INTO SpecificationEntityMapping(SpecificationEntityId, EntityId) VALUES (@SpecificationEntityId, @EntityId)";

                    command.Parameters.AddWithValue("@SpecificationEntityId", specificationEntityId);
                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();

                    command.ExecuteNonQuery();

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when adding specification entity mapping");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when adding specification entity mapping", ex);
                }
            }
        }

        private object SetFieldData(object value, SpecificationFieldType specificationFieldType)
        {
            if (specificationFieldType == null)
            {
                return value;
            }

            string dataType = specificationFieldType.DataType;

            if (value == DBNull.Value || value == null)
            {
                return null;
            }

            if (dataType == DataType.String)
            {
                return value.ToString();
            }

            if (dataType == DataType.CVL)
            {
                return value.ToString();
            }

            if (dataType == DataType.LocaleString)
            {
                return JsonConvert.DeserializeObject<LocaleString>(value.ToString());
            }

            if (dataType == DataType.Double)
            {
                return double.Parse(value.ToString().Replace(",", "."), CultureInfo.InvariantCulture);
            }

            if (dataType == DataType.Integer)
            {
                return Convert.ToInt32(value);
            }

            if (dataType == DataType.Boolean)
            {
                bool result;

                if (bool.TryParse(value.ToString(), out result))
                {
                    return result;
                }

                return Convert.ToBoolean(Convert.ToInt32(value));
            }

            if (dataType == DataType.DateTime)
            {
                return DateTime.ParseExact(value.ToString(), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            }

            if (dataType == DataType.Xml)
            {
                return value.ToString();
            }

            if (dataType == DataType.File)
            {
                return Convert.ToInt32(value);
            }

            return value;
        }

        private static object SetFieldData(object value, string dataType)
        {
            if (string.IsNullOrEmpty(dataType))
            {
                return value;
            }

            if (value == DBNull.Value || value == null)
            {
                return null;
            }

            if (dataType == DataType.String)
            {
                return value.ToString();
            }

            if (dataType == DataType.CVL)
            {
                return value.ToString();
            }

            if (dataType == DataType.LocaleString)
            {
                return JsonConvert.DeserializeObject<LocaleString>(value.ToString());
            }

            if (dataType == DataType.Double)
            {
                return double.Parse(value.ToString().Replace(",", "."), CultureInfo.InvariantCulture);
            }

            if (dataType == DataType.Integer)
            {
                return Convert.ToInt32(value);
            }

            if (dataType == DataType.Boolean)
            {
                bool result;

                if (bool.TryParse(value.ToString(), out result))
                {
                    return result;
                }

                return Convert.ToBoolean(Convert.ToInt32(value));
            }

            if (dataType == DataType.DateTime)
            {
                return Convert.ToDateTime(value);
            }

            if (dataType == DataType.Xml)
            {
                return value.ToString();
            }

            if (dataType == DataType.File)
            {
                return Convert.ToInt32(value);
            }

            return value;
        }

        private void RemoveSpecificationInheritance(int specificationEntityId, int specificationEntityOwnerId)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();

                command.CommandText = "RemoveSpecificationInheritance";
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@SpecificationEntityId", specificationEntityId);
                command.Parameters.AddWithValue("@OwnerSpecificationEntityId", specificationEntityOwnerId);

                connection.Open();
                command.ExecuteNonQuery();
                connection.Close();
            }
            catch (Exception ex)
            {
                if (connection.State != ConnectionState.Closed)
                {
                    connection.Close();
                }

                Log.Error(ex, "An unexpected error occurred when removing specification inheritance");
                throw ErrorUtility.GetDataAccessException("An unexpected error occurred when removing specification inheritance", ex);
            }
        }

        private void DeleteSpecificationFieldsForEntity(int entityId)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();

                command.CommandText = "DELETE FROM SpecificationEntityMapping WHERE EntityId = @EntityId; DELETE FROM SpecificationFieldValue WHERE EntityId = @EntityId;";

                command.Parameters.AddWithValue("@EntityId", entityId);

                connection.Open();
                command.ExecuteNonQuery();
                connection.Close();
            }
            catch (Exception ex)
            {
                if (connection.State != ConnectionState.Closed)
                {
                    connection.Close();
                }

                Log.Error(ex, "An unexpected error occurred when deleting specification field values for entity " + entityId);
                throw ErrorUtility.GetDataAccessException("An unexpected error occurred when deleting specification field values for entity " + entityId, ex);
            }
        }

        #endregion
    }
}
