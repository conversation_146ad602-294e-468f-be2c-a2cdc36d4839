namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using inRiver.Api.Data.Client;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using System;
    using System.Collections.Generic;
    using System.Text;

    /// <summary>
    /// Contains Entity related operations.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public void UpdateEntityFieldsAndChangeSet(int entityId, List<Field> fieldsToUpdate)
        {
            var dtoFields = fieldsToUpdate.ConvertAll(new Converter<Field, DtoField>(FieldToDtoField));
            InRiverDataApiClient.UpdateEntityFieldsAndChangeSet(this.GetAuthInfo(), entityId, dtoFields);
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
        }
    }
}
