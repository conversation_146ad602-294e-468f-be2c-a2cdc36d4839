namespace inRiver.Server.Syndication.Export
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Util;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication.Constants;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Helpers;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Script;
    using inRiver.Server.Syndication.Script.Api;
    using inRiver.Server.Syndication.Script.DefaultFunctions;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Serilog;

    public class ExportManager
    {
        private readonly IDictionary<string, Exception> corruptedScripts = new Dictionary<string, Exception>();

        private readonly RequestContext context;

        private readonly SyndicationModel syndicationModel;

        private readonly IEntityFieldService entityFieldService;

        private readonly IResourceExportService resourceExportService;

        private readonly IResourceFieldService resourceFieldService;

        private readonly IJobMetadataManager jobMetadataManager;

        private IScriptContextDealer scriptDealer;

        // This dictionary was created to save cvl values for concatenate function.
        // Dictionary is used in this case to improve performance when processing large amount of entities.
        // Key - CVL id. Value - dictionary that contains CVLKey as dictionary key, and CVLValue object as value.
        public static ConcurrentDictionary<string, Dictionary<string, CVLValue>> CvlValuesDictionary { get; set; }

        // This dictionary was created to save cvl ids for specified field type.
        // Key - Field type id. Value - CVL id.
        public static ConcurrentDictionary<string, string> CvlIdsDictionary { get; set; }

        public ExportManager(
            MapContainer mapContainer,
            RequestContext context,
            SyndicationModel syndicationModel,
            IResourceExportService resourceExportService,
            IJobMetadataManager jobMetadataManager)
        {
            this.MapContainer = mapContainer;
            this.context = context;
            this.syndicationModel = syndicationModel;
            this.resourceExportService = resourceExportService;
            this.jobMetadataManager = jobMetadataManager;
            this.resourceFieldService = new ResourceFieldService();
            this.entityFieldService = new EntityFieldService();
            CvlValuesDictionary = new ConcurrentDictionary<string, Dictionary<string, CVLValue>>();
            CvlIdsDictionary = new ConcurrentDictionary<string, string>();
        }

        private MapContainer MapContainer { get; }

        public List<ExportContainer> GetExportContainers(IList<InRiverEntity> entities, List<CultureInfo> languages, CancellationToken cancellationToken)
        {
            var resultErrors = new Dictionary<string, SyndicationExportContainerExceptionModel>();
            var exportContainers = new List<ExportContainer>();
            var skuSettingsManager = new SkuSettingsManager(this.syndicationModel.EnableSKU, this.context, this.MapContainer);
            var scriptFields = new List<ScriptField>();

            ParallelTaskExecutor.Execute<InRiverEntity, IList<(ExportContainer exportContainer, List<ScriptField> scriptFields)>>(
                entities,
                transform: entity => {
                    try
                    {
                        var containers = new List<(ExportContainer exportContainer, List<ScriptField> scriptFields)>();
                        if (skuSettingsManager.ShouldProcessSkuField())
                        {
                            containers.AddRange(this.PopulateWithSkuFields(entity, skuSettingsManager, languages));
                        }
                        else
                        {
                            var container = this.GetExportContainerFromEntity(entity, skuSettingsManager.IsSkuEnabled, null, string.Empty, languages);
                            containers.Add(container);
                        }

                        return containers;
                    }
                    catch (SyndicateException e)
                    {
                        AddErrorMessage(resultErrors, e.InnerException, entity.Id, e.Message);
                        return null;
                    }
                    catch (Exception ex)
                    {
                        AddErrorMessage(resultErrors, ex, entity.Id);
                        return null;
                    }
                },
                action: result => {
                    if (result == null)
                    {
                        return;
                    }

                    exportContainers.AddRange(result.Select(x => x.exportContainer));
                    scriptFields.AddRange(result.SelectMany(x => x.scriptFields));
                },
                cancellationToken);

            using (this.scriptDealer = new ScriptContextDealer(this.context, languages, skuSettingsManager))
            {
                this.ProcessScriptFields(scriptFields, exportContainers, resultErrors, cancellationToken);
            }

            if (resultErrors.Any())
            {
                this.LogExportContainerErrors(resultErrors, skuSettingsManager.IsSkuEnabled);
            }

            return exportContainers;
        }

        private static void AddErrorMessage(IDictionary<string, SyndicationExportContainerExceptionModel> resultErrors, Exception ex, int entityId, string fieldTypeExceptionMessage = "")
        {
            var message = ex?.Message ?? string.Empty;
            message += fieldTypeExceptionMessage;

            if (resultErrors.TryGetValue(message, out var value))
            {
                value.EntityIds.Add(entityId);
            }
            else
            {
                resultErrors.Add(message, new SyndicationExportContainerExceptionModel
                {
                    Exception = ex,
                    FieldTypeExceptionMessage = fieldTypeExceptionMessage,
                    EntityIds = new List<int> { entityId }
                });
            }
        }

        private void LogExportContainerErrors(IDictionary<string, SyndicationExportContainerExceptionModel> resultErrors, bool enableSKU)
        {
            var resultErrorMessage = string.Empty;

            foreach (var error in resultErrors.Values)
            {
                var fieldTypeException = string.IsNullOrEmpty(error.FieldTypeExceptionMessage) ? string.Empty : $", field: {error.FieldTypeExceptionMessage}";
                var entityIds = string.Join(", ", error.EntityIds);
                resultErrorMessage += $"Exception occurred in entities: {entityIds}{fieldTypeException}. Message: {error.Exception.Message} \n\n";
                Log.Error(
                    error.Exception,
                    "Syndication failed when trying to populate Export Containers: {customerSafeName}/{environmentSafeName}. SKU enabled: {enableSKU}",
                    this.context?.CustomerSafeName,
                    this.context?.EnvironmentSafeName,
                    enableSKU);
            }

            _ = this.jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.Message, resultErrorMessage);
            _ = this.jobMetadataManager.TryAdd(LongRunningJobMetaDataKeys.FinishedWithErrors, true);
        }

        private List<(ExportContainer exportContainer, List<ScriptField> scriptFields)> PopulateWithSkuFields(InRiverEntity entity, SkuSettingsManager skuSettingsManager, IList<CultureInfo> languages)
        {
            var skuDataString = entity.Fields.Find(x => x.FieldType.FieldTypeId == skuSettingsManager.EntitySkuField)?.Data?.ToString() ?? string.Empty;
            var skuInjector = SkuInjector.CreateSkuInjector(skuDataString, string.Empty, skuSettingsManager.SchemaSkuFields.ToList());
            var containers = new List<(ExportContainer exportContainer, List<ScriptField> scriptFields)>();

            if (!string.IsNullOrEmpty(skuDataString) && skuInjector != null)
            {
                var skuOutputFieldNames = skuInjector.GetSkuOutputFieldNames();
                foreach (var skuId in skuInjector.GetAllSkuIds())
                {
                    containers.Add(this.GetExportContainerFromEntity(entity, skuSettingsManager.IsSkuEnabled, skuInjector, skuId, languages.ToList()));
                }
            }
            else
            {
                containers.Add(this.GetExportContainerFromEntity(entity, skuSettingsManager.IsSkuEnabled, skuInjector, string.Empty, languages.ToList()));
            }

            return containers;
        }

        private void ProcessScriptFields(
            IEnumerable<ScriptField> scriptFields,
            IEnumerable<ExportContainer> exportContainers,
            IDictionary<string, SyndicationExportContainerExceptionModel> resultErrors,
            CancellationToken cancellationToken)
        {
            var exportContainersDictionary = exportContainers.ToList()
                .GroupBy(exportContainer => exportContainer.Id)
                .ToDictionary(x => x.Key, x => x.ToList());

            foreach (var scriptField in scriptFields)
            {
                cancellationToken.ThrowIfCancellationRequested();
                try
                {
                    if (!exportContainersDictionary.TryGetValue(scriptField.Entity.Id, out var entityExportContainers))
                    {
                        continue;
                    }

                    var exportContainerFieldProjection = scriptField.IsResourceExport && this.resourceExportService.IsResourceExportAllowed
                        ? entityExportContainers.SelectMany(container => container.ResourceExportFields as IEnumerable<IMapField>)
                        : entityExportContainers.SelectMany(container => container.Fields as IEnumerable<IMapField>);
                    var exportContainerField = exportContainerFieldProjection.FirstOrDefault(field => field.MapFieldUniqueId == scriptField.MapFieldUniqueId);
                    if (exportContainerField == null)
                    {
                        continue;
                    }

                    // Check if this script was already defined as corrupted and rethrow existing exception instead of processing this script again.
                    // We need to save exception to use this exception message as field value
                    var isCurrentScriptCorrupted = this.corruptedScripts.TryGetValue(scriptField.Script, out var fieldException);
                    if (!isCurrentScriptCorrupted)
                    {
                        try
                        {
                            var data = ScriptManager.Execute(
                                scriptField.Script,
                                scriptField.Args,
                                scriptField.Values,
                                this.scriptDealer.GetScriptContext(scriptField.Entity, scriptField.SkuId));

                            if (scriptField.Script != null && data != null)
                            {
                                ScriptManager.ValidateScriptResult(scriptField.Script, data);
                            }

                            exportContainerField.Data = data ?? scriptField.DefaultValue;
                        }
                        catch (Exception exception)
                        {
                            this.SaveAsCorruptedScript(scriptField.Script, exception);
                            exportContainerField.Data = exception.Message;
                        }
                    }
                    else
                    {
                        exportContainerField.Data = fieldException.Message;
                    }
                }
                catch (SyndicateException e)
                {
                    AddErrorMessage(resultErrors, e.InnerException, scriptField.Entity.Id, e.Message);
                }
                catch (Exception ex)
                {
                    AddErrorMessage(resultErrors, ex, scriptField.Entity.Id);
                }
            }
        }

        private void SaveAsCorruptedScript(string script, Exception exception)
        {
            // Scripts containing a SysId mask must be executed even if previous runs failed, since the result of executing such scripts may be different.
            var scriptContainsSysIdMask = script.Contains(MaskReplacementService.EntitySystemIdMask, StringComparison.InvariantCultureIgnoreCase);
            if (scriptContainsSysIdMask)
            {
                return;
            }

            _ = this.corruptedScripts.TryAdd(script, exception);
        }

        private (ExportContainer exportContainer, List<ScriptField> scriptFields) GetExportContainerFromEntity(
            InRiverEntity entity,
            bool enableSKU,
            SkuInjector skuInjector,
            string skuId,
            List<CultureInfo> serverLanguages)
        {
            var exportContainer = new ExportContainer
            {
                RelatedIds = entity.RelatedEntities?.Select(p => p.Id).ToList(),
                Id = entity.Id,
                Source = $"{entity.DisplayName} ({entity.Id})",
                Fields = new List<ExportField>(),
                ResourceExportFields = new List<ResourceExportField>(),
                ShouldSerializeResources = true, // needed to serialize the resource data correctly once saving to blobs in SyndicationRepository.GetExportContainers
            };

            var scriptFields = new List<ScriptField>();

            var entityFields = entity.Fields.Concat((entity.RelatedEntities != null)
                                                ? entity.RelatedEntities.SelectMany(y => y.Fields)
                                                : new List<InRiverField>()).ToList();
            if (this.resourceExportService.IsResourceExportAllowed)
            {
                var (resourceScriptFields, resourceExportFields)
                    = this.HandleResourceFieldsForEntity(entity, entityFields, serverLanguages);
                scriptFields.AddRange(resourceScriptFields);
                exportContainer.ResourceExportFields.AddRange(resourceExportFields);
            }

            foreach (var mapField in this.MapContainer.MapFields)
            {
                try
                {
                    var stopWatch = new Stopwatch();
                    stopWatch.Start();

                    object data;

                    var uniqueId = Guid.NewGuid();

                    var foundField = this.GetFoundField(enableSKU, mapField, skuInjector, entityFields, skuId);
                    var mainValue = this.GetMainValue(foundField, serverLanguages);

                    if (!string.IsNullOrEmpty(mapField.Script) || (!string.IsNullOrEmpty(mapField.Converter) && (CheckIfConverterEquals(mapField.Converter, StandardFunctions.Coalesce) || CheckIfConverterEquals(mapField.Converter, StandardFunctions.SystemID))))
                    {
                        var transformationManager = new TransformationManager(mapField.Args, this.context);

                        if (CheckIfConverterEquals(mapField.Converter, StandardFunctions.Concatenate))
                        {
                            var concatenateExecutor = new ConcatenateFunctionExecutor(
                                transformationManager,
                                foundField,
                                entity,
                                mainValue,
                                enableSKU,
                                skuInjector,
                                skuId,
                                serverLanguages,
                                this.context);
                            data = concatenateExecutor.Execute();
                        }
                        else if (CheckIfConverterEquals(mapField.Converter, StandardFunctions.BeforeAfter))
                        {
                            var beforeAfterExecutor = new BeforeAfterFunctionExecutor(this.context, transformationManager, mainValue, mapField.Enumerations.Any());
                            data = beforeAfterExecutor.Execute();
                        }
                        else if (CheckIfConverterEquals(mapField.Converter, StandardFunctions.Coalesce))
                        {
                            var coalesceExecutor = new CoalesceFunctionExecutor(
                                transformationManager,
                                foundField,
                                entity,
                                mainValue,
                                enableSKU,
                                skuInjector,
                                skuId,
                                serverLanguages,
                                this.context);
                            data = coalesceExecutor.Execute();
                        }
                        else if (CheckIfConverterEquals(mapField.Converter, StandardFunctions.SystemID))
                        {
                            data = entity.Id;
                        }
                        else if (CheckIfConverterEquals(mapField.Converter, StandardFunctions.CvlValue))
                        {
                            var cvlValues = this.GetCvlValues(transformationManager.Args, mainValue, serverLanguages);
                            data = this.GetCVLValuesDataFormat(cvlValues);
                        }
                        else
                        {
                            var values = this.GetScriptValues(transformationManager, mapField, foundField, entity, mainValue, enableSKU, skuInjector, skuId, serverLanguages, out var args);
                            scriptFields.Add(new ScriptField(isResourceExport: false)
                            {
                                Args = args.ToArray(),
                                Values = values.ToArray(),
                                Entity = entity,
                                SkuId = skuId,
                                FieldTypeId = foundField?.FieldTypeId,
                                MapFieldUniqueId = uniqueId,
                                Script = mapField.Script,
                                DefaultValue = mapField.MapFieldType.DefaultValue
                            });
                            data = null;
                        }
                    }
                    else
                    {
                        data = mainValue;
                    }

                    var exportField = new ExportField
                    {
                        Id = mapField.MapFieldTypeId,
                        Data = data ?? mapField.MapFieldType.DefaultValue,
                        Path = $"{mapField.MapPath}/{mapField.MapFieldTypeId}",
                        MapFieldType = mapField.MapFieldType,
                        UnitType = mapField.UnitType,
                        UnitValue = mapField.UnitValue ?? mapField.UnitDefaultValue,
                        Source = exportContainer.Source,
                        FieldTypeId = foundField?.FieldTypeId,
                        SortOrder = mapField.SortOrder,
                        MapFieldUniqueId = uniqueId,
                        MappingFormatFieldId = mapField.MappingFormatFieldId,
                    };

                    exportContainer.Fields.Add(exportField);
                    stopWatch.Stop();
                    Debug.WriteLine($"{mapField.MapFieldType} --> {stopWatch.Elapsed.TotalMilliseconds.ToString("F02")}ms");
                }
                catch (Exception ex)
                {
                    throw new SyndicateException($"Source: {mapField.Source.FieldTypeId ?? "Not selected"}, Target: {mapField.MapFieldTypeId}", ex);
                }
            }

            this.OrderContainerFields(exportContainer);

            return (exportContainer, scriptFields);
        }

        private void OrderContainerFields(ExportContainer exportContainer) => exportContainer.Fields = exportContainer.Fields.OrderBy(x => x.SortOrder).ToList();

        private (IList<ScriptField> ScriptFields, IList<ResourceExportField> ResourceExportFields) HandleResourceFieldsForEntity(
            InRiverEntity entity,
            IList<InRiverField> entityFields,
            IList<CultureInfo> serverLanguages)
        {
            var scriptFields = new List<ScriptField>();
            var resourceExportFields = new List<ResourceExportField>();
            foreach (var resourceField in this.MapContainer.MapResourceFields)
            {
                var uniqueId = Guid.NewGuid();
                var fieldTypeId = resourceField.FieldTypeId;

                // Use Image instead of ImageUrl fieldTypeId if there is no custom function to access resource metadata.
                if (string.IsNullOrEmpty(resourceField.Args) && this.resourceFieldService.IsResourceFieldType(resourceField.FieldTypeId, ResourceFieldTypes.ImageUrl))
                {
                    fieldTypeId = this.resourceFieldService.GetImageUrlTypeFromImageFieldType(resourceField.FieldTypeId);
                }

                var fields = this.entityFieldService.GetFieldsByFieldTypeId(entityFields, fieldTypeId);
                var foundField = this.entityFieldService.CreateFoundField(
                    fields.Where(x => x != null).Select(x => x.Data).ToList(),
                    fieldTypeId);
                if (foundField == null)
                {
                    continue;
                }

                var mainValue = this.GetMainValue(foundField, serverLanguages.ToList());
                if (!string.IsNullOrEmpty(resourceField.Args))
                {
                    var transformationManager = new TransformationManager(resourceField.Args, this.context);
                    scriptFields.Add(new ScriptField(isResourceExport: true)
                    {
                        Args = transformationManager.Args.ToArray<object>(),
                        Values = this.resourceExportService.GetScriptValues(transformationManager, entity, mainValue),
                        Entity = entity,
                        FieldTypeId = foundField.FieldTypeId,
                        MapFieldUniqueId = uniqueId,
                        Script = resourceField.Script
                    });
                    mainValue = null;
                }

                resourceExportFields.Add(new ResourceExportField
                {
                    MapFieldUniqueId = uniqueId,
                    Data = mainValue
                });
            }

            return (scriptFields, resourceExportFields);
        }

        private List<object> GetScriptValues(
            TransformationManager transformationManager,
            MapField mapField,
            FoundField foundField,
            InRiverEntity entity,
            object mainValue,
            bool enableSKU,
            SkuInjector skuInjector,
            string skuId,
            List<CultureInfo> serverLanguages,
            out List<object> args)
        {
            var values = new List<object>();
            args = new List<object>();

            if (!mapField.IsCustom)
            {
                if (TransformationManager.IsFieldLocaleString(entity, foundField))
                {
                    if (CheckIfConverterEquals(mapField.Converter, StandardFunctions.ChooseLanguage))
                    {
                        return GetValuesForChooseLanguage(transformationManager, mainValue, values);
                    }
                }
            }

            // all custom function's and some default function's values will get here.
            if (mainValue != null)
            {
                values.Add(mainValue);
            }

            values.AddRange(transformationManager.GetValues(entity, enableSKU, skuInjector, skuId));
            args.AddRange(transformationManager.Args);

            return values;
        }

        private static List<object> GetValuesForChooseLanguage(TransformationManager transformationManager, object mainValue, List<object> values)
        {
            object[] valueArray;
            if (mainValue is object[] value)
            {
                valueArray = value;
            }
            else
            {
                valueArray = new object[1];
                valueArray[0] = mainValue;
            }

            var scriptValueList = new List<object>();
            foreach (var item in valueArray)
            {
                var dict = (Dictionary<string, string>)item;
                if (dict == null || transformationManager.Args.Length <= 0)
                {
                    continue;
                }

                var lang = transformationManager.Args[0];

                if (dict.ContainsKey(lang))
                {
                    scriptValueList.Add(dict[lang]);
                }
            }

            values.Add(scriptValueList.Count() == 1 ? scriptValueList[0] : scriptValueList);
            return values;
        }

        private static bool CheckIfConverterEquals(string converter, string functionName) => converter.Equals(functionName, StringComparison.InvariantCultureIgnoreCase);
        
        private object GetMainValue(FoundField foundFields, List<CultureInfo> serverLanguages)
        {
            if (foundFields == null)
            {
                return null;
            }
            List<object> fieldDataList;
            if (foundFields.Data is List<Object>)
            {
                fieldDataList = (List<Object>)foundFields.Data;
            }
            else
            {
                fieldDataList = new List<Object>();
                fieldDataList.Add(foundFields.Data);
            }

            object[] mainValue = new object[fieldDataList.Count()];

            for (var i = 0; i < fieldDataList?.Count(); i++)
            {
                var fieldData = fieldDataList[i];
                if (fieldData != null && fieldData != null && fieldData.GetType() == typeof(LocaleString))
                {
                    mainValue[i] = ((LocaleString)fieldData).ToDictionary(serverLanguages);
                }
                else
                {
                    mainValue[i] = fieldData;
                }
            }
            return mainValue.Length == 1 ? mainValue[0] : mainValue;
        }

        private FoundField GetFoundField(bool enableSKU, MapField mapField, SkuInjector skuInjector, List<InRiverField> fields, string skuId)
        {
            FoundField foundField;
            var isSkuFieldDataType = mapField.MapFieldType.FieldDataType != null && mapField.MapFieldType.FieldDataType.Equals(SkuConstants.SkuFieldDataType, StringComparison.InvariantCultureIgnoreCase);
            if (enableSKU && isSkuFieldDataType)
            {
                if (string.IsNullOrEmpty(skuId))
                {
                    return null;
                }

                foundField = new FoundField
                {
                    Data = skuInjector.GetValueForSkuOutputFieldName(skuId, mapField.Source.FieldTypeId),
                    FieldTypeId = mapField.Source.FieldTypeId
                };
            }
            else
            {
                foundField = this.GetFieldValue(mapField, fields);
            }

            return foundField;
        }

        private FoundField GetFieldValue(MapField mapField, IEnumerable<InRiverField> fields)
        {
            var foundCollection = this.entityFieldService.GetFieldsByFieldTypeId(fields.ToList(), mapField.Source.FieldTypeId);
            if (foundCollection == null)
            {
                return null;
            }

            var data = this.entityFieldService.GetMappedData(mapField, foundCollection);

            return this.entityFieldService.CreateFoundField(data.ToList(), mapField.Source.FieldTypeId);
        }

        private IEnumerable<string> GetCvlValues(string[] args, object mainValue, List<CultureInfo> serverLanguages)
        {
            var cvlId = args[0];
            var delimiter = args[1];
            var selectedLanguage = args.Length >= 3 ? args[2] : string.Empty;
            var resultValues = new List<string>();

            if (mainValue != null)
            {
                if (mainValue is Array mainValueAsArray)
                {
                    foreach (var value in mainValueAsArray)
                    {
                        if (value == null)
                        {
                            continue;
                        }

                        resultValues.Add(this.GetAndJoinCVLValueByKeys(value, cvlId, selectedLanguage, serverLanguages, delimiter));
                    }
                }
                else
                {
                    resultValues.Add(this.GetAndJoinCVLValueByKeys(mainValue, cvlId, selectedLanguage, serverLanguages, delimiter));
                }

            }

            return resultValues.Where(v => !string.IsNullOrEmpty(v));
        }

        private string GetAndJoinCVLValueByKeys(object value, string cvlId, string selectedLanguage, List<CultureInfo> serverLanguages, string delimiter)
        {
            const char cvlKeysSplit = ';';
            var keys = value.ToString().Split(cvlKeysSplit);
            var cvlValues = this.context.DataPersistance.GetCVLValueByKeys(keys, cvlId, selectedLanguage, serverLanguages);
            if (cvlValues == null) //Check if the English version must be brought.
            {
                cvlValues = new List<CVLValue>();
            }

            return string.Join(delimiter, cvlValues.Select(x => x.Value));
        }

        private object GetCVLValuesDataFormat(IEnumerable<string> values) => values.Count() > 1 ? (object)values : values.FirstOrDefault();
    }
}
