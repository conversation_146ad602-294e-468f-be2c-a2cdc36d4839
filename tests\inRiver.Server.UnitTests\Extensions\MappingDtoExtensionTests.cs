namespace inRiver.Server.UnitTests.Extensions
{
    using System.Collections.Generic;
    using System.Text.Json;
    using inRiver.Server.Extension;
    using Xunit;

    public class MappingDtoExtensionTests
    {
        [Fact]
        public void GetEnumerations_WithValidList_ReturnsEnumerations()
        {
            // Arrange
            var mapping = JsonSerializer.Deserialize<Dictionary<string, object>>(
                @"{
                    ""Enumerations"": [
                        {""FieldValue"": ""value1""},
                        {""FieldValue"": ""value2""},
                        {""FieldValue"": ""value3""}
                    ]
                }");

            // Act
            var enumerations = MappingDtoExtension.GetEnumerations(mapping);

            // Assert
            Assert.Equal(3, enumerations.Count);
            Assert.Equal("value1", enumerations[0].FieldValue);
            Assert.Equal("value2", enumerations[1].FieldValue);
            Assert.Equal("value3", enumerations[2].FieldValue);
        }

        [Fact]
        public void GetEnumerations_WithNotValidList_ReturnsEmptyLis()
        {
            // Arrange
            var mapping = JsonSerializer.Deserialize<Dictionary<string, object>>(
                @"{
                    ""Enumerations"": [
                        {""WrongField"": ""value1""},
                        {""WrongField"": ""value2""},
                        {""WrongField"": ""value3""}
                    ]
                }");

            // Act
            var enumerations = MappingDtoExtension.GetEnumerations(mapping);

            // Assert
            Assert.Empty(enumerations);
        }

        [Fact]
        public void GetEnumerations_WhenKeyMissing_ReturnsEmptyList()
        {
            // Arrange
            var mapping = JsonSerializer.Deserialize<Dictionary<string, object>>(
                @"{
                    ""AnotherField"": """"
                }");

            // Act
            var enumerations = MappingDtoExtension.GetEnumerations(mapping);

            // Assert
            Assert.Empty(enumerations);
        }

        [Fact]
        public void GetEnumerations_WhenValueIsNotList_ReturnsEmptyList()
        {
            // Arrange
            var mapping = JsonSerializer.Deserialize<Dictionary<string, object>>(
                @"{
                    ""Enumerations"": ""value1;value2;value3""
                }");

            // Act
            var enumerations = MappingDtoExtension.GetEnumerations(mapping);

            // Assert
            Assert.Empty(enumerations);
        }

        [Fact]
        public void TryGetFormatFieldId_WhenKeyMissing_ShouldReturnDefaultValue()
        {
            // Arrange
            var mapping = JsonSerializer.Deserialize<Dictionary<string, object>>(
                @"{
                    ""AnotherField"": """"
                }");

            // Act
            var formatFieldId = MappingDtoExtension.TryGetFormatFieldId(mapping);

            // Assert
            Assert.Equal(-1, formatFieldId);
        }

        [Fact]
        public void TryGetFormatFieldId_WhenValueIsString_ShouldReturnDefaultValue()
        {
            // Arrange
            var mapping = JsonSerializer.Deserialize<Dictionary<string, object>>(
                @"{
                    ""FormatFieldId"": ""string value""
                }");

            // Act
            var formatFieldId = MappingDtoExtension.TryGetFormatFieldId(mapping);

            // Assert
            Assert.Equal(-1, formatFieldId);
        }

        [Fact]
        public void TryGetFormatFieldId_WhenValueIsNumber_ShouldReturnNumber()
        {
            // Arrange
            var mapping = JsonSerializer.Deserialize<Dictionary<string, object>>(
                @"{
                    ""FormatFieldId"": ""123""
                }");

            // Act
            var formatFieldId = MappingDtoExtension.TryGetFormatFieldId(mapping);

            // Assert
            Assert.Equal(123, formatFieldId);
        }
    }
}
