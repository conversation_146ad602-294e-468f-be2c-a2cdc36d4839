namespace inRiver.Core.Models.inRiver
{
    using System;
    using System.Collections.Generic;

    public class EntityType
        : IIdentifierAsStringInterface, ICloneable
    {
        public EntityType()
        {
            this.LinkTypes = new List<LinkType>();
        }

        public EntityType(string id)
        {
            this.Id = id;
            this.LinkTypes = new List<LinkType>();
            this.FieldTypes = new List<FieldType>();
        }

        public string Id { get; set; }

        public LocaleString Name { get; set; }

        public bool IsLinkEntityType { get; set; }

        public List<FieldType> FieldTypes { get; set; }

        public List<LinkType> LinkTypes { get; set; }

        public List<FieldSet> FieldSets { get; set; }

        public object Clone()
        {
            EntityType cloned = new EntityType(this.Id)
            {
                Name = this.Name,
                IsLinkEntityType = this.IsLinkEntityType,
                FieldTypes = this.FieldTypes,
                LinkTypes = this.LinkTypes,
                FieldSets = this.FieldSets
            };

            return cloned;
        }

        public override string ToString()
        {
            if (string.IsNullOrWhiteSpace(this.Id))
            {
                return base.ToString();
            }

            return this.Id;
        }
    }
}
