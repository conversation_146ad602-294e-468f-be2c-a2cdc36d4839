namespace inRiver.Core.Extension.Augmenta
{
    using System;
    using Enum.Augmenta;

    internal static class StringExtensions
    {
        public static EntityCommandName ToEntityCommandName(this string extensionEvent)
        {
            switch (extensionEvent)
            {
                case "OnAdd":
                    return EntityCommandName.CreateEntity;
                case "OnUpdate":
                    return EntityCommandName.UpdateEntity;
                case "OnDelete":
                    return EntityCommandName.DeleteEntity;
                case "OnCreateVersion":
                    return EntityCommandName.CreateNewVersionForEntity;
                case "OnLock":
                    return EntityCommandName.LockEntity;
                case "OnUnlock":
                    return EntityCommandName.UnlockEntity;
                default:
                    throw new ArgumentException($"Unknown extension event: {extensionEvent}");
            }
        }

        public static LinkCommandName ToLinkCommandName(this string extensionEvent)
        {
            switch (extensionEvent)
            {
                case "OnLink":
                    return LinkCommandName.CreateLink;
                case "OnUnlink":
                    return LinkCommandName.DeleteLink;
                case "OnLinkUpdate":
                    return LinkCommandName.UpdateSortOrder;
                default:
                    throw new ArgumentException($"Unknown extension event: {extensionEvent}");
            }
        }
    }
}
