﻿namespace inRiver.Core.Persistance.ThirdDataLayer
{
    using inRiver.Api.Data.Client;

    /// <summary>
    /// CVL overrides.
    /// </summary>
    internal partial class iPMC3DLPersistanceAdapter : IPMCPersistanceAdaptor
    {
        public override bool DeleteCVLValue(int cvlValueId)
        {
            InRiverDataApiClient.DeleteCVLValueById(this.authInfo, cvlValueId, force: true);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return true;
        }

        public override bool DeleteAllCVLValuesForCVL(string cvlId)
        {
            InRiverDataApiClient.DeleteAllCVLValuesForCVL(this.authInfo, cvlId, deactivatedOnly: false, force: true);
            InRiverDataApiClient.InvalidateModelCache(this.authInfo);
            InRiverDataApiClient.InvalidateDataCache(this.authInfo);
            return true;
        }
    }
}
