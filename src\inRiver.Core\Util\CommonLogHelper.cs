
namespace inRiver.Core.Util
{
    using System;
    using System.Runtime.CompilerServices;

    using inRiver.Core.Persistance;

    public class CommonLogHelper
    {
        internal BasePersistance Parent;

        public CommonLogHelper(BasePersistance parent)
        {
            this.Parent = parent;
        }

        public void Debug(
            string message, 
            [CallerMemberName] string memberName = "", 
            [CallerFilePath] string filePath = "",
            [CallerLineNumber] int lineNo = 0)
        {
            if (this.Parent.GetCommonLogging() == null) return;

            this.Parent.GetCommonLogging()
                .Debug(
                    message,
                    this.Parent.GetApiCaller().Module,
                    this.Parent.GetApiCaller().Username,
                    memberName,
                    filePath,
                    lineNo);
        }

        public void Information(
            string message, 
            [CallerMemberName] string memberName = "", 
            [CallerFilePath] string filePath = "", 
            [CallerLineNumber] int lineNo = 0)
        {
            if (this.Parent.GetCommonLogging() == null) return;

            this.Parent.GetCommonLogging()
                .Information(
                    message,
                    this.Parent.GetApiCaller().Module,
                    this.Parent.GetApiCaller().Username,
                    memberName,
                    filePath,
                    lineNo);
        }

        public void Error(
            string message, 
            Exception ex, 
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string filePath = "", 
            [CallerLineNumber] int lineNo = 0)
        {
            if (this.Parent.GetCommonLogging() == null) return;

            this.Parent.GetCommonLogging()
                .Error(
                    message, 
                    ex, 
                    this.Parent.GetApiCaller().Module, 
                    this.Parent.GetApiCaller().Username, 
                    true,
                    memberName,
                    filePath,
                    lineNo);
        }

        public void UnexpectedError(
            Exception ex, 
            [CallerMemberName] string memberName = "", 
            [CallerFilePath] string filePath = "", 
            [CallerLineNumber] int lineNo = 0)
        {
            if (this.Parent.GetCommonLogging() == null) return;

            this.Parent.GetCommonLogging()
                .Error(
                    "Unexpected error.", 
                    ex, 
                    this.Parent.GetApiCaller().Module, 
                    this.Parent.GetApiCaller().Username,
                    true,
                    memberName,
                    filePath,
                    lineNo);
        }
    }
}
