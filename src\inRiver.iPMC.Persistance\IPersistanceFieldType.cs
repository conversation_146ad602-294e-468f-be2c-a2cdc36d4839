namespace inRiver.iPMC.Persistance
{
    using System.Collections.Generic;

    public interface IPersistanceFieldType
    {
        FieldType GetFieldType(string id, bool includeSettings = false);

        List<FieldType> GetAllFieldTypes(bool includeSettings = false);

        Dictionary<string, List<FieldType>> GetAllFieldTypesGrouped();

        List<FieldType> GetFieldTypesForEntityType(string entityTypeId, bool includeSettings = false);

        Dictionary<string, string> GetFieldTypeSettings(string fieldTypeId);

        Dictionary<string, Dictionary<string, string>> GetAllFieldTypeSettings();
    }
}
