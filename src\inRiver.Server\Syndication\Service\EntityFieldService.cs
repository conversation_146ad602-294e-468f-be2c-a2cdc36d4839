namespace inRiver.Server.Syndication.Service
{
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Service.Interfaces;

    public class EntityFieldService : IEntityFieldService
    {
        public IEnumerable<InRiverField> GetFieldsByFieldTypeId(IList<InRiverField> fields, string fieldTypeId)
            => fields.Where(x => x.FieldType.FieldTypeId == fieldTypeId);

        public IEnumerable<object> GetMappedData(MapField mapField, IEnumerable<InRiverField> foundCollection)
        {
            var data = new List<object>();

            foreach (var found in foundCollection)
            {
                if (found.Data != null && mapField.Enumerations != null && mapField.Enumerations.Count > 0)
                {
                    var result = mapField.Enumerations.GetMatchingEnumValuesString(found.Data.ToString());
                    data.Add(result);
                }
                else
                {
                    data.Add(found.Data);
                }
            }

            return data;
        }

        public FoundField CreateFoundField(IList<object> foundFieldsData, string fieldTypeId)
        {
            if (foundFieldsData == null)
            {
                return null;
            }

            var returnValue = new FoundField
            {
                FieldTypeId = fieldTypeId,
            };

            if (foundFieldsData.Count == 0 || foundFieldsData.All(i => i == null))
            {
                returnValue = null;
            }
            else if (foundFieldsData.Count > 1)
            {
                returnValue.Data = foundFieldsData;
            }
            else if (foundFieldsData.Count == 1)
            {
                returnValue.Data = foundFieldsData[0];
            }

            return returnValue;
        }
    }
}
