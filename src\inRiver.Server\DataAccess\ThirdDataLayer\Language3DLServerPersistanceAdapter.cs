﻿namespace inRiver.Server.DataAccess.ThirdDataLayer
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using inRiver.Api.Data.Client;

    /// <summary>
    /// Language overrides.
    /// </summary>
    internal partial class IPMCServer3DLPersistanceAdapter : IPMCServerPersistanceAdapter
    {
        public override bool AddLanguage(CultureInfo cultureInfo)
        {
            var result = _origInRiverPersistance.AddLanguage(cultureInfo);
            InRiverDataApiClient.InvalidateModelCache(this.GetAuthInfo());
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return result;
        }

        public override bool DeleteAllLanguages()
        {
            var result = _origInRiverPersistance.DeleteAllLanguages();
            InRiverDataApiClient.InvalidateModelCache(this.GetAuthInfo());
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return result;
        }

        public override bool DeleteLanguage(CultureInfo cultureInfo)
        {
            var result = _origInRiverPersistance.DeleteLanguage(cultureInfo);
            InRiverDataApiClient.InvalidateModelCache(this.GetAuthInfo());
            InRiverDataApiClient.InvalidateDataCache(this.GetAuthInfo());
            return result;
        }
    }
}
