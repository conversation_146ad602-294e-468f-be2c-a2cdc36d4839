namespace inRiver.Server.UnitTests.Syndicate
{
    using System.Collections.Generic;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Constants;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Service;
    using Xunit;

    public class ExportContainersValidatorTests
    {
        [Fact]
        public void Validate_MandatoryFormatFileField_HasData_ShouldNotReturnAnyWarningAndErrors()
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        Mandatory = true,
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = "value",
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            Assert.Equal(0, result.Rows.Count);
        }

        [Fact]
        public void Validate_MandatoryFormatFileField_HasNoData_ShouldReturnError()
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        Mandatory = true,
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = null,
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            foreach (var row in result.Rows)
            {
                Assert.Equal(1, row.EntityId);
                Assert.Equal(0, row.Warnings.Count);

                Assert.Collection(
                    row.Errors,
                    item => {
                        Assert.Equal("field1", item.FieldId);
                        Assert.Equal(ValidationErrorTypes.MandatoryField.Value, item.ErrorType);
                    });
            }
        }

        [Fact]
        public void Validate_RecommendedFormatFileField_HasData_ShouldNotReturnAnyWarningAndErrors()
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        Recommended = true,
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = "value",
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            Assert.Equal(0, result.Rows.Count);
        }

        [Fact]
        public void Validate_RecommendedFormatFileField_HasNoData_ShouldReturnWarning()
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        Recommended = true,
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = null,
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            foreach (var row in result.Rows)
            {
                Assert.Equal(1, row.EntityId);
                Assert.Collection(
                    row.Warnings,
                    item => {
                        Assert.Equal("field1", item.FieldId);
                        Assert.Equal(ValidationErrorTypes.RecommendedField.Value, item.ErrorType);
                    });
                Assert.Equal(0, row.Errors.Count);
            }
        }

        [Fact]
        public void Validate_RecommendedAndMandatoryFormatFileField_HasNoData_ShouldReturnWarningAndError()
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        Mandatory = true,
                        Recommended = true,
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = null,
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            foreach (var row in result.Rows)
            {
                Assert.Equal(1, row.EntityId);
                Assert.Collection(
                    row.Warnings,
                    item => {
                        Assert.Equal("field1", item.FieldId);
                        Assert.Equal(ValidationErrorTypes.RecommendedField.Value, item.ErrorType);
                    });
                Assert.Collection(
                    row.Errors,
                    item => {
                        Assert.Equal("field1", item.FieldId);
                        Assert.Equal(ValidationErrorTypes.MandatoryField.Value, item.ErrorType);
                    });
            }
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("1A")]
        public void Validate_NumberFormatFileField_HasInvalidData_ShouldReturnError(object fieldData)
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        DataType = "Number"
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = fieldData,
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            foreach (var row in result.Rows)
            {
                Assert.Equal(1, row.EntityId);
                Assert.Collection(
                    row.Errors,
                    item => {
                        Assert.Equal("field1", item.FieldId);
                        Assert.Equal(ValidationErrorTypes.NumberField.Value, item.ErrorType);
                    });
            }
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1.2)]
        [InlineData("12.3")]
        public void Validate_NumberFormatFileField_HasValidData_ShouldNotReturnAnyWarningAndErrors(object fieldData)
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        DataType = "Number"
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = fieldData,
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            Assert.Equal(0, result.Rows.Count);
        }

        [Theory]
        [InlineData("")]
        [InlineData(";")]
        [InlineData("1blue")]
        [InlineData("blue;white;")]
        [InlineData("blue;white;red")]
        [InlineData("blue;white;blue")]
        public void Validate_EnumFormatFileField_HasInvalidData_ShouldReturnError(object fieldData)
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        DataType = "Enum"
                    },
                    Enumerations = new List<MapEnumeration>
                    {
                        new MapEnumeration
                        {
                            EnumValue = "white"
                        },
                        new MapEnumeration
                        {
                            EnumValue = "blue"
                        }
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = fieldData,
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            Assert.Equal(1, result.Rows.Count);
            foreach (var row in result.Rows)
            {
                Assert.Equal(1, row.EntityId);
                Assert.Collection(
                    row.Errors,
                    item => {
                        Assert.Equal("field1", item.FieldId);
                        Assert.Equal(ValidationErrorTypes.EnumField.Value, item.ErrorType);
                    });
            }
        }

        [Theory]
        [InlineData(null)] // Unmapped field
        [InlineData("white")]
        [InlineData("blue")]
        [InlineData("blue;white")]
        public void Validate_EnumFormatFileField_HasValidData_ShouldNotReturnAnyWarningAndErrors(object fieldData)
        {
            // Arrange
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1",
                    MapFieldType = new MapFieldType
                    {
                        DataType = "Enum"
                    },
                    Enumerations = new List<MapEnumeration>
                    {
                        new MapEnumeration
                        {
                            EnumValue = "white"
                        },
                        new MapEnumeration
                        {
                            EnumValue = "blue"
                        }
                    }
                },
            };
            var exportContainers = new List<ExportContainer>
            {
                new ExportContainer
                {
                    Id = 1,
                    Fields = new List<ExportField>
                    {
                        new ExportField
                        {
                            Id = "field1",
                            Data = fieldData,
                            FieldTypeId = "field1"
                        }
                    }
                },
            };
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.Validate(formatFileFields, exportContainers);

            // Assert
            Assert.Equal(0, result.Rows.Count);
        }

        [Fact]
        public void GetUpdatedProgress_FirstBatch_ShouldReturnUpdatedProgressCorrectly()
        {
            // Arrange
            const int batchSize = 10;
            const int currentBatch = 1;
            const int totalEntities = 95;
            const int currentEntities = 10;
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.GetUpdatedProgress(batchSize, currentBatch, totalEntities, currentEntities);

            // Assert
            Assert.Equal(10, result.TotalBatches);
            Assert.Equal(1, result.CurrentBatch);
            Assert.Equal(95, result.TotalEntities);
            Assert.Equal(10, result.ProcessedEntities);
            Assert.Equal(10, result.BatchSize);
            Assert.Equal(ValidationStatus.InProgress, result.Status);
        }

        [Fact]
        public void GetUpdatedProgressLastBatch_ShouldReturnUpdatedProgressCorrectly()
        {
            // Arrange
            const int batchSize = 10;
            const int currentBatch = 10;
            const int totalEntities = 95;
            const int currentEntities = 5;
            var validator = new ExportContainersValidator();

            // Act
            var result = validator.GetUpdatedProgress(batchSize, currentBatch, totalEntities, currentEntities);

            // Assert
            Assert.Equal(10, result.TotalBatches);
            Assert.Equal(10, result.CurrentBatch);
            Assert.Equal(95, result.TotalEntities);
            Assert.Equal(95, result.ProcessedEntities);
            Assert.Equal(10, result.BatchSize);
            Assert.Equal(ValidationStatus.Finished, result.Status);
        }

        [Theory]
        [InlineData(-100, null)]
        [InlineData(-1, null)]
        [InlineData(0, 0)]
        [InlineData(1, 1)]
        [InlineData(100, 100)]
        public void GetMappingFormatFieldId_ShouldReturnCorrectMappingFormatFieldId(int currentMappingFormatFieldId, int? expectedMappingFormatFieldId)
        {
            // Arrange
            var validator = new ExportContainersValidator();
            var mapField = new MapField
            {
                MappingFormatFieldId = currentMappingFormatFieldId,
            };

            // Act
            var result = validator.GetMappingFormatFieldId(mapField);

            // Assert
            Assert.Equal(expectedMappingFormatFieldId, result);
        }

        [Fact]
        public void GetFormatFileField_WhenDuplicatesExistForDynamicFormatFile_ShouldThrowException()
        {
            // Arrange
            var validator = new ExportContainersValidator();
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1"
                },
                new MapField
                {
                    MapFieldTypeId = "field1"
                },
                new MapField
                {
                    MapFieldTypeId = "field2"
                }
            };
            var exportContainerField = new ExportField { Id = "field1" };

            // Act + Assert
            Assert.Throws<DynamicMapFieldTypeIdNotUniqueException>(() =>
                validator.GetFormatFileField(formatFileFields, exportContainerField, isDynamicFormatFile: true));
        }

        [Fact]
        public void GetFormatFileField_WhenThereAreNoDuplicatesForDynamicFormatFile_ShouldReturnField()
        {
            // Arrange
            var validator = new ExportContainersValidator();
            var formatFileFields = new List<MapField>
            {
                new MapField
                {
                    MapFieldTypeId = "field1"
                },
                new MapField
                {
                    MapFieldTypeId = "field2"
                },
                new MapField
                {
                    MapFieldTypeId = "field3"
                }
            };
            var exportContainerField = new ExportField { Id = "field1" };

            // Act
            var field = validator.GetFormatFileField(formatFileFields, exportContainerField, isDynamicFormatFile: true);

            // Assert
            Assert.Equal("field1", field.MapFieldTypeId);
        }
    }
}
