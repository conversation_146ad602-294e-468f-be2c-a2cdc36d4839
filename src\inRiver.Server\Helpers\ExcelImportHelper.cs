namespace inRiver.Server.Helpers
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using Newtonsoft.Json;

    public static class ExcelImportHelper
    {
        public static IDictionary<string, string> CreateColumnValuePairs(string jsonString, FileImportConfigurationModel configurationModel)
        {
            var currentCultureInfo = !string.IsNullOrEmpty(configurationModel.CurrentLanguage) ?
                new CultureInfo(configurationModel.CurrentLanguage) : CultureInfo.InvariantCulture;
            var indexValuePairs = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonString);
            var columnValuePairs = new Dictionary<string, string>();

            foreach (var index in indexValuePairs.Keys.Where(index => index != "-1"))
            {
                const char tabChar = '\u0009';
                var stringValue = indexValuePairs[index] is double number ?
                    number.ToString(currentCultureInfo) :
                    indexValuePairs[index].ToString();
                var value = stringValue.Replace(tabChar.ToString(), string.Empty);
                var column = configurationModel.Columns.FirstOrDefault(a => a.Index.ToString().Equals(index));
                columnValuePairs.Add(column != null ? column.ColumnName : index, value);
            }

            return columnValuePairs;
        }
    }
}
