namespace inRiver.Server.UnitTests.Syndicate
{
    using System;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Service;
    using Xunit;

    public class MappingResolverServiceTests
    {
        [Fact]
        public void GetMappingId_ApplyDsaMappingTrueAndDsaMappingIdNotNull_ShouldReturnDsaMappingId()
        {
            // Arrange
            var model = new SyndicationModel
            {
                DsaMappingId = 42,
                MappingId = 99
            };
            var applyDsaMapping = true;

            // Act
            var result = MappingResolverService.GetMappingId(model, applyDsaMapping);

            // Assert
            Assert.Equal(42, result);
        }

        [Fact]
        public void GetMappingId_ApplyDsaMappingTrueAndDsaMappingIdNull_ShouldThrowArgumentNullException()
        {
            // Arrange
            var model = new SyndicationModel
            {
                DsaMappingId = null,
                MappingId = 99
            };
            var applyDsaMapping = true;

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => MappingResolverService.GetMappingId(model, applyDsaMapping));
        }

        [Fact]
        public void GetMappingId_ApplyDsaMappingFalse_ShouldReturnMappingId()
        {
            // Arrange
            var model = new SyndicationModel
            {
                DsaMappingId = 42,
                MappingId = 99
            };
            var applyDsaMapping = false;

            // Act
            var result = MappingResolverService.GetMappingId(model, applyDsaMapping);

            // Assert
            Assert.Equal(99, result);
        }
    }
}
